{"name": "archer-client", "version": "1.0.0", "description": "archer-client", "main": "", "directories": {}, "dependencies": {"js-base64": "^3.7.7", "md5": "^2.3.0", "moment-timezone": "^0.5.43", "pako": "^2.1.0"}, "devDependencies": {"@types/md5": "^2.3.5", "@types/pako": "^2.0.3", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0", "eslint": "^8.31.0", "eslint-config-alloy": "^4.8.0", "npm-run-all": "^4.1.5", "typescript": "^4.9.4"}, "scripts": {"tsc": "tsc -p .", "eslint": "npm-run-all -s eslint-check-macos eslint-log", "eslint-check-macos": "eslint . --ext .ts -o  ./temp/eslint/eslint.json  -f json ; exit 0", "eslint-check": "eslint . --ext .ts -o  ./temp/eslint/eslint.json  -f json", "eslint-log": "node tools/eslint-log/app.js", "build-proto": "node tools/build-proto/app.js", "build-data": "node tools/build-data/app.js", "export-xlsx": "node tools/export-xlsx/app.js", "clean-project": "node tools/clean-project/app.js -p . -d", "check-project": "node tools/check-project/app.js -p .", "build-spine-event": "node tools/build-spine-event/app.js"}, "scriptsComments": {"build-proto": "npm run build-proto -- -b develop", "build-data": "npm run build-data -- -b client -v trunk", "export-xlsx": "npm run export-xlsx -- -v tags/V3"}, "repository": {"type": "git", "url": "git@*************:archer/client/archer-client.git"}, "keywords": [], "author": "nsn game frontend development department", "license": "ISC"}