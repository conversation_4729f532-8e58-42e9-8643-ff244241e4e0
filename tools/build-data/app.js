/*
 * @Author: <PERSON>yF<PERSON>
 * @Date: 2023-08-28 15:39:30
 * @Last Modified by: <PERSON>yF<PERSON>
 * @Last Modified time: 2024-10-11 17:26:49
 */

const fs = require("fs-extra");
const path = require("path");
const commandLineArgs = require("command-line-args");
const exec = require("await-exec");

const XlsxUtils = require("./src/xlsx-utils");
const XlsxParser = require("./src/xlsx-parser");
const ClientExporter = require("./src/client-export");
const { SERVER_FLAG, CLIENT_FLAG, DESIGN_FLAG } = require("./src/constant");
const ErrorList = require("./src/error-list");
const ClientCopy = require("./src/client-copy");
const Logger = require("./src/logger");
const ServerExporter = require("./src/server-export");
const ServerCopy = require("./src/server-copy");

class Task {
    config = null;
    options = null;
    xlsxPath = "";

    /**
     * 检查环境
     * @returns
     */
    checkEnv() {
        const config = fs.readJsonSync(path.join(__dirname, "config.json"));
        if (!config) {
            Logger.error("配置不存在");
            return false;
        }
        if (!config.xlsxPath) {
            Logger.error("请配置xlsxPath");
            return false;
        }

        const optionDefinitions = [
            { name: "version", alias: "v", type: String },
            { name: "build", alias: "b", type: String },
            { name: "noUpdate", alias: "n", type: Boolean },
        ];
        const options = commandLineArgs(optionDefinitions);
        if (!options.version) {
            Logger.error("请输入配置版本，使用-v指定版本");
            return false;
        }
        if (!options.build) {
            Logger.error("请输入构建类型，使用-b指定构建类型，只能为client|server|all|check");
            return false;
        }

        if (options.build !== "check") {
            if (!config.tempPath) {
                Logger.error("请配置tempPath");
                return false;
            }
        }

        this.config = config;
        this.options = options;
        this.xlsxPath = path.join(config.xlsxPath, options.version);

        return true;
    }

    /**
     * 更新svn
     */
    async updateSvn() {
        Logger.log("svn开始更新");
        const cmdStr = `svn update ${this.xlsxPath}`;
        await exec(cmdStr);
        Logger.log("svn更新完成");
        if (!fs.existsSync(this.xlsxPath)) {
            Logger.error("配置路径不存在：" + this.xlsxPath);
            return false;
        }
        return true;
    }

    /**
     * 预加载配置
     */
    async preloadXlsx() {
        await XlsxUtils.preloadXlsx(this.xlsxPath);
    }

    async checkXlsx() {
        Logger.log("开始检测");
        let files = XlsxUtils.getFiles(this.xlsxPath);
        const parsers = {};
        for (const file of files) {
            const parser = new XlsxParser();
            await parser.read(file);
            parser.check();
            parsers[path.basename(file)] = parser;
        }
        const errs = ErrorList.get();
        if (errs.length) {
            errs.forEach((e) => {
                Logger.error(e);
            });
            Logger.error("检测未通过");
            return null;
        } else {
            Logger.log("检测通过");
            return parsers;
        }
    }

    /**
     * 导出客户端配置
     */
    async convertClient(parsers) {
        Logger.log("开始转换客户端配置");

        Logger.log("清理数据");
        const copy = new ClientCopy(this.config);
        copy.clear();

        Logger.log("导出数据");
        for (const key in parsers) {
            if (key.startsWith(SERVER_FLAG) || key.startsWith(DESIGN_FLAG)) {
                continue;
            }
            new ClientExporter(parsers[key], this.config).execute();
        }
        copy.execute();
        Logger.log("转换客户端配置完成");
    }

    /**
     * 导出服务端配置
     */
    async convertServer(parsers) {
        Logger.log("开始转换服务端端配置");

        Logger.log("清理数据");
        const copy = new ServerCopy(this.config);
        copy.clear();

        Logger.log("导出数据");
        for (const key in parsers) {
            if (key.startsWith(CLIENT_FLAG) || key.startsWith(DESIGN_FLAG)) {
                continue;
            }
            new ServerExporter(parsers[key], this.config).execute();
        }
        copy.execute();
        Logger.log("转换服务端端配置完成");
    }

    async execute() {
        if (!this.checkEnv()) {
            return;
        }
        if (!this.options.noUpdate) {
            if (!(await this.updateSvn())) {
                return;
            }
        } else {
            Logger.log("跳过svn更新");
        }
        await this.preloadXlsx();
        const parsers = await this.checkXlsx();
        if (!parsers) {
            return;
        }
        switch (this.options.build) {
            case "client":
                await this.convertClient(parsers);
                break;
            case "server":
                await this.convertServer(parsers);
                break;
            case "all":
                await this.convertClient(parsers);
                await this.convertServer(parsers);
            case "check":
                break;
            default:
                break;
        }
    }
}

new Task().execute();
