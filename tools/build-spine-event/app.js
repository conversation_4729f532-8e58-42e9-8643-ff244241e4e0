const spine = require("./spine-webgl");
const fs = require("fs-extra");
const path = require("path");
const rd = require("rd");

const SPINE_DIR = [
    "assets/resources/spine/archer",
    "assets/resources/spine/monster",
    "assets/resources/spine/pet",
    "assets/resources/spine/lead",
    "assets/resources/spine/tank",
    "assets/resources/spine/wing",
    "assets/resources/spine/weapon",
    "assets/resources/spine/skillEffect",
];
const DST_PATH = "assets/resources/data/spineEvent.json";

class Task {
    /**
     * 递归获取文件
     * @param {*} types
     * @param {*} dirs
     * @param {*} excludeDirs
     * @returns
     */
    getFileList(types, dirs, excludeDirs) {
        let fileList = [];
        for (let dir of dirs) {
            types.forEach((type) => {
                let r = RegExp(`(\.` + type + `)$`);
                rd.eachFileFilterSync(dir, r, (f) => {
                    let isExclude = false;
                    for (let i = 0; i < excludeDirs.length; i++) {
                        if (f.includes(excludeDirs[i])) {
                            isExclude = true;
                            break;
                        }
                    }
                    if (!isExclude) {
                        fileList.push(f);
                    }
                });
            });
        }
        return fileList;
    }

    /**
     * 加载骨骼文件
     * @param {*} filePath
     * @returns
     */
    async loadSkeletonFile(filePath) {
        try {
            const data = {};
            const buffer = await fs.promises.readFile(filePath);
            const atlasText = await fs.promises.readFile(
                path.join(path.dirname(filePath), path.basename(filePath, path.extname(filePath)) + ".atlas"),
                "utf8"
            );
            const atlas = new spine.TextureAtlas(atlasText, () => {
                return new spine.FakeTexture(new spine.TextureRegion());
            });
            const atlasLoader = new spine.AtlasAttachmentLoader(atlas);
            const binary = new spine.SkeletonBinary(atlasLoader);
            const skeletonData = binary.readSkeletonData(buffer);
            for (const e1 of skeletonData.animations) {
                data[e1.name] = data[e1.name] || { duration: Number.parseFloat(e1.duration.toFixed(3)), event: {} };
                for (const e2 of e1.timelines) {
                    if (e2 instanceof spine.EventTimeline) {
                        for (const e3 of e2.events) {
                            data[e1.name].event[e3.data.name] = data[e1.name].event[e3.data.name] || [];
                            const time = Number.parseFloat(e3.time.toFixed(3));
                            if (!data[e1.name].event[e3.data.name].includes(time)) {
                                data[e1.name].event[e3.data.name].push(time);
                            }
                        }
                    }
                }
            }
            return data;
        } catch (error) {
            console.log(filePath);
            console.error("解析失败:", error);
        }
    }

    async execute() {
        const dirs = SPINE_DIR.map((v) => path.join(__dirname, "../../", v));
        const list = this.getFileList([".skel"], dirs, []);
        const data = {};
        for (const e of list) {
            const eventData = await this.loadSkeletonFile(e);
            if (Object.keys(eventData).length) {
                data[path.basename(e, path.extname(e))] = eventData;
            }
        }
        const target = path.join(__dirname, "../../" + DST_PATH);
        fs.writeJSONSync(target, data);
    }
}

new Task().execute();
