/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-12-22 09:18:46
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-07-04 19:00:56
 */

import Platform from "../../../nsn/platform/Platform";
import Logger from "../../../nsn/util/Logger";
import { PayType } from "../../../protobuf/proto";
import User from "../../core/User";
import TBRecharge from "../../data/parser/TBRecharge";
import { SDK } from "../../lib/dataNexus/index.js";
import { ISDKReporterCreateRoleData, ISDKReporterLoginData, ISDKReporterPurchaseData } from "./ISDKReporter";
import SDKReporterBase from "./SDKReporterBase";

const USER_ACTION_SET_ID = 1215306506;
const SECRET_KEY = "abcf9f499b62534d544d1a8d2875d8cc";
const WECHAT_MINIGAME_APP_ID = "wxa2cec6b7487a63bd";

export default class DataNexus extends SDKReporterBase {
    private sdk: SDK = null;

    public constructor() {
        super();
        this.initSdk();
    }

    private initSdk(): void {
        const isWechatMinigame = Platform.getInstance().isWechatMinigame();
        if (!isWechatMinigame) {
            return;
        }
        try {
            this.sdk = new SDK({
                // eslint-disable-next-line @typescript-eslint/naming-convention
                user_action_set_id: USER_ACTION_SET_ID,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                secret_key: SECRET_KEY,
                appid: WECHAT_MINIGAME_APP_ID,
            });
        } catch (error) {
            Logger.error("DataNexus", JSON.stringify(error));
        }
    }

    /**
     * 设置openId
     */
    private setOpenId(): void {
        const thirdId = User.getInstance().getThirdId();
        this.sdk && thirdId && this.sdk.setOpenId(thirdId);
    }

    /**
     * 游戏启动
     */
    public start(): void {
        this.setOpenId();
        this.sdk && this.sdk.onAppStart();
    }

    /**
     * 上报登录
     * @param data
     */
    public login(data: ISDKReporterLoginData): void {
        // 这里比较特殊，实际上不是上报登录事件，而是设置openId
        // 这样能保证不管玩家是调用微信登录还是快速登录都能正常设置openId
        this.setOpenId();
    }

    /**
     * 注册埋点
     * @param data
     */
    public register(data: ISDKReporterLoginData): void {
        // 注册也需要正常设置openId
        this.setOpenId();
        this.sdk && this.sdk.onRegister();
    }

    /**
     * 支付埋点
     * @param data
     */
    public purchase(data: ISDKReporterPurchaseData): void {
        const { rechargeId, payType } = data;
        if (rechargeId <= 10) {
            return;
        }

        // 安卓支付马上上报，这里不再上报
        // 点券不上报
        if (payType === PayType.Ticket || payType === PayType.WechatMini) {
            return;
        }
        const rechargeData = TBRecharge.getInstance().getDataById(rechargeId);
        if (rechargeData.price <= 0) {
            return;
        }
        this.sdk && this.sdk.onPurchase(rechargeData.price * 100);
    }

    /**
     * 上报创角
     * @param data
     * @returns
     */
    public createRole(data: ISDKReporterCreateRoleData): void {
        this.sdk && this.sdk.onCreateRole(data.roleName);
    }

    /**
     * 获取sdk
     * @returns
     */
    public getSdk(): SDK {
        return this.sdk;
    }
}
