import {
    ActivityGameReward,
    ActivityNewTrialFree,
    ActivitySign,
    ArcherActivateReward,
    ArcherGroupActivateReward,
    ArcherResetLevel,
    BagAddProps,
    BagOpenRandomBox,
    BagOpenSelectBox,
    BagUseProp,
    BoxProgressBatchReward,
    BoxProgressReward,
    CdkeyTake,
    CollectionGameOpenReward,
    CollectionLevelReward,
    DungeonBossSweetReward,
    DungeonCloudSweetReward,
    DungeonThiefSweetReward,
    FriendBatchTakeAwardAndThumbsUpAll,
    FriendTakeThumbsUpAward,
    GameSwitchGet,
    GuideAdd,
    IdleEarningsTake,
    IdleEarningsTakeHours,
    LeadSkinActivateReward,
    LeadSkinReset,
    MagicalResetLevel,
    MailBatchTake,
    MailTake,
    MiniTakeAward,
    PetFreeReceiveEgg,
    PetResetLevel,
    PlayerBindReward,
    PlotBatchReceiveOrder,
    PlotReceive,
    PlotReceiveOther,
    PowerPeakReward,
    PowerPeakRewardRet,
    PowerPeakWorshipReward,
    PowerPeakWorshipRewardRet,
    PrivilegeBatchReceiveDayRwd,
    ProgressReceive,
    RechargeGiftReceivePack,
    RechargeNoticeRet,
    RechargePrePay,
    RechargeReceivePack,
    RechargeReceiveTotalReward,
    ShopPurchase,
    TaskActivityBigReward,
    TaskBatchTakeAward,
    UnionActiveReward,
    UnionBossReward,
    UnionDonateReward,
    UnionHelpAll,
    UnionTreasureShopPurchase,
    WelfareReceiveRwd,
} from "../../protobuf/proto";

export const REWARD_CONFIG = [
    BagAddProps.prototype.clazzName,
    RechargeNoticeRet.prototype.clazzName,
    GameSwitchGet.prototype.clazzName,
    UnionTreasureShopPurchase.prototype.clazzName,
    UnionBossReward.prototype.clazzName,
    UnionActiveReward.prototype.clazzName,
    UnionDonateReward.prototype.clazzName,
    ShopPurchase.prototype.clazzName,
    TaskBatchTakeAward.prototype.clazzName,
    RechargeReceivePack.prototype.clazzName,
    ActivitySign.prototype.clazzName,
    RechargeGiftReceivePack.prototype.clazzName,
    FriendTakeThumbsUpAward.prototype.clazzName,
    FriendBatchTakeAwardAndThumbsUpAll.prototype.clazzName,
    CdkeyTake.prototype.clazzName,
    WelfareReceiveRwd.prototype.clazzName,
    MailTake.prototype.clazzName,
    MailBatchTake.prototype.clazzName,
    MagicalResetLevel.prototype.clazzName,
    ProgressReceive.prototype.clazzName,
    PlotReceive.prototype.clazzName,
    PlotReceiveOther.prototype.clazzName,
    PlotBatchReceiveOrder.prototype.clazzName,
    ActivityGameReward.prototype.clazzName,
    PlayerBindReward.prototype.clazzName,
    LeadSkinActivateReward.prototype.clazzName,
    LeadSkinReset.prototype.clazzName,
    ArcherResetLevel.prototype.clazzName,
    ArcherActivateReward.prototype.clazzName,
    ArcherGroupActivateReward.prototype.clazzName,
    PetResetLevel.prototype.clazzName,
    PetFreeReceiveEgg.prototype.clazzName,
    IdleEarningsTake.prototype.clazzName,
    IdleEarningsTakeHours.prototype.clazzName,
    DungeonBossSweetReward.prototype.clazzName,
    DungeonCloudSweetReward.prototype.clazzName,
    RechargeReceiveTotalReward.prototype.clazzName,
    PrivilegeBatchReceiveDayRwd.prototype.clazzName,
    RechargePrePay.prototype.clazzName,
    ActivityNewTrialFree.prototype.clazzName,
    BagOpenRandomBox.prototype.clazzName,
    BagOpenSelectBox.prototype.clazzName,
    BoxProgressReward.prototype.clazzName,
    BoxProgressBatchReward.prototype.clazzName,
    CollectionLevelReward.prototype.clazzName,
    CollectionGameOpenReward.prototype.clazzName,
    PowerPeakWorshipRewardRet.prototype.clazzName,
    PowerPeakRewardRet.prototype.clazzName,
    MiniTakeAward.prototype.clazzName,
    PowerPeakWorshipReward.prototype.clazzName,
    PowerPeakReward.prototype.clazzName,
    TaskActivityBigReward.prototype.clazzName,
    UnionHelpAll.prototype.clazzName,
    BagUseProp.prototype.clazzName,
    DungeonThiefSweetReward.prototype.clazzName,
];

export const REWARD_TIPS_CONFIG = [GuideAdd.prototype.clazzName];
