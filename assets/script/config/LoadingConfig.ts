/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-28 20:42:46
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 16:11:16
 */

import { TB_MAP } from "../data/parser/TBMap";
import { ClientConfigName } from "../game/ArcherClientConfig";

/**
 * 加载比例分布
 */
export const LOADING_PERCENT = {
    DATA: 0.8,
    SCENE: 0.04,
    ATLAS: 0.04,
    PREFAB: 0.04,
    ITEM: 0.04,
    CLIENT_CONFIG: 0.04,
};

export const PRELOAD_DATA = Object.keys(TB_MAP);
export const PRELOAD_SCENE = "GameScene";
export const PRELOAD_ATLAS: string[] = [];
export const PRELOAD_PREFAB: string[] = [
    "ui/BaseHome",
    "ui/UISelectLead",
    "prefab/home/<USER>",
    "prefab/combat/PrefabCombatScene",
    "prefab/home/<USER>",
];
export const PRELOAD_ITEM: [string, number][] = [
    ["prefab/combat/PrefabCombatMonsterItem", 10],
    ["prefab/combat/PrefabCombatShowItem1", 10],
    ["prefab/combat/PrefabCombatShowItem2", 10],
    ["prefab/combat/PrefabCombatDamageItem", 20],
];
export const PRELOAD_CLIENT_CONFIG: [string, string][] = [["data/spineEvent", ClientConfigName.SpineData]];

export const LOADING_SUPPORT_REMOTE_DATA = false;
