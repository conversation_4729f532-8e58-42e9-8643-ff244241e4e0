/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-30 17:45:57
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-07-17 17:33:55
 */

import Reporter from "../../../../nsn/core/Reporter";
import Etadpu, { EtadpuEvent } from "../../../../nsn/etadpu/Etadpu";
import UI from "../../../../nsn/ui/UI";
import Logger from "../../../../nsn/util/Logger";
import TextUtils from "../../../../nsn/util/TextUtils";
import Tips from "../../../../nsn/util/Tips";
import { PayType } from "../../../../protobuf/proto";
import Login from "../../../core/Login";
import User from "../../../core/User";
import Recharge from "../../../game/Recharge";
import DataNexus from "../../../sdk/reporter/DataNexus";
import i18n from "../../i18n/I18n";
import { WECHAT_MINIGAME_SHARE_IMAGE_URL, WECHAT_MINIGAME_SHARE_IMAGE_URL_ID } from "../../platform/PlatformConfig";
import { REPORTER_ID } from "../../ReporterConfig";
import ChannelBase from "../base/ChannelBase";
import { LoginType } from "../base/ChannelType";
import { IPayData } from "../base/IChannelSdk";

// 支付id
const MIDAS_OFFER_ID = "1450391866";

// 评价链接
const WECHAT_EVALUATE_LINK = "TWFRCqV5WeM2AkMXhKwJ03MhfPOieJfAsvXKUbWvQFQtLyyA5etMPabBehga950uzfZcH3Vi3QeEh41xRGEVFw";

// 游戏圈链接
const WECHAT_MINIGAME_GAME_CLUB_LINK =
    "-SSEykJvFV3pORt5kTNpS9v8ojbBqqUcymrjfoWlI1Kps0gQILuu5uFt0dvwsqAYcP-TgdqBlsdrKQCTlCg9sQHCLAN-4BmrAmwJAR8TpwJ4t72qr1LcSnaMOTgdV-eJPtAW9-2HhME5-lCrV5JqXXgb20ekFuTEcovMMKs0d-JcY-LI6t4x1OLAFicKeIeu77hyOwxZq8aTe3CAgKsZ8SUUPaquU1kf2q4cEjG-F1-KRbMEyJaPeERuhmlAbHBCvrMF5Vzb3tq8av98KgQuHvHwhTm1_vTey7X7d78BY3dTCUVjUyY2siadoFkPlzHLar-eu5VsspHco60pB1DZiw";

const IOS_CUSTOMER_SERVICE_PAY1 = "https://aquapic.3669yx.com/archer/wechat/image/pay1.jpg";
const IOS_CUSTOMER_SERVICE_PAY2 = "https://aquapic.3669yx.com/archer/wechat/image/pay2.jpg";

/**
 * 支付结果类型
 */
enum PayResultType {
    Success = "SUCCESS",
    Recharge = "RECHARGE",
    Error = "ERROR",
    Retry = "RETRY",
}

export default class WechatMinigame extends ChannelBase {
    protected registerHandler(): void {
        super.registerHandler();

        wx.showShareMenu({
            withShareTicket: true,
            menus: ["shareAppMessage", "shareTimeline"],
        });
        this.onShareAppMessage();
        this.onShareTimeline();
    }

    /**
     * 登录
     * @param type
     */
    public login(type: LoginType): void {
        switch (type) {
            case LoginType.WechatMinigame:
                wx.login({
                    success: (res) => {
                        if (res.code) {
                            this.setLoginSuccess(res.code);
                        } else {
                            this.setLoginFailed(i18n.channel0002);
                        }
                    },
                });
                break;
            default:
                break;
        }
    }

    /**
     * 登录成功
     * @param data
     */
    public setLoginSuccess(data: string): void {
        UI.getInstance().hideLoading();
        Reporter.logTriggerEvent(REPORTER_ID.TRIGGER.LOGIN.BY_WECHAT_MINIGAME_SUCCESS);
        Login.getInstance().loginByWechatMinigame(data);
    }

    /**
     * 登录失败
     * @param data
     */
    public setLoginFailed(data: string): void {
        UI.getInstance().hideLoading();
        Reporter.logTriggerEvent(REPORTER_ID.TRIGGER.LOGIN.BY_WECHAT_MINIGAME_FAILED);
        Tips.getInstance().show(data);
    }

    /**
     * 支付
     * @param data
     */
    public pay(data: IPayData): void {
        Logger.info("微信小游戏", "支付");
        switch (data.payType) {
            case PayType.WechatMini:
                switch (data.orderData) {
                    case PayResultType.Recharge:
                        const info = wx.getSystemInfoSync();
                        wx.checkSession({
                            success: () => {
                                // session_key 未过期，并且在本生命周期一直有效
                                wx.requestMidasPayment({
                                    mode: "game",
                                    offerId: MIDAS_OFFER_ID,
                                    currencyType: "CNY",
                                    env: 0,
                                    buyQuantity: data.price,
                                    // @ts-ignore
                                    platform: info.platform,
                                    success: (res) => {
                                        this.setDeliverProduct(data.orderId + "#" + data.price * 100);
                                        // 安卓支付马上上报
                                        DataNexus.getInstance()
                                            .getSdk()
                                            .onPurchase(data.price * 100);
                                    },
                                    fail: (res) => {
                                        if (res.errCode === -2) {
                                            Tips.getInstance().show(i18n.channel0004);
                                        } else {
                                            Tips.getInstance().show(TextUtils.format(i18n.channel0008, res.errCode));
                                        }
                                    },
                                });
                            },
                            fail() {
                                // session_key 已经失效，需要重新执行登录流程
                                Tips.getInstance().show(i18n.pay0003);
                            },
                        });
                        break;
                    case PayResultType.Retry:
                        this.setDeliverProduct(data.orderId + "#" + data.price * 100);
                        break;
                    case PayResultType.Error:
                        Tips.getInstance().show(i18n.pay0004);
                        break;
                    case PayResultType.Success:
                    default:
                        break;
                }
                break;
            case PayType.WechatJS:
                const sessionFrom = { ...data, action: "pay", scene: "" };
                wx.showModal({
                    title: "支付提示",
                    content: '即将打开客服聊天界面，输入"cz"或者"充值"可以获取支付链接',
                    showCancel: false,
                    confirmText: "我知道了",
                    complete: () => {
                        wx.openCustomerServiceConversation({
                            sessionFrom: JSON.stringify(sessionFrom),
                            showMessageCard: true,
                            sendMessageTitle: i18n.game0001,
                            sendMessagePath: IOS_CUSTOMER_SERVICE_PAY2,
                            sendMessageImg: IOS_CUSTOMER_SERVICE_PAY1,
                            success: ({ path, query }) => {},
                            fail: () => {},
                            complete: () => {
                                Logger.info("微信小游戏", "成功打开客服对话");
                            },
                        });
                    },
                });
                break;
            default:
                break;
        }
    }

    /**
     * 发放道具
     * @param data
     */
    public setDeliverProduct(data: string): void {
        Logger.info("微信小游戏", "发放道具: " + data);
        const [orderId, price] = data.split("#");
        const payload = {
            openId: User.getInstance().getThirdId(),
            price,
            payItem: "",
        };
        Recharge.getInstance().sendRechargeVerify(PayType.WechatMini, orderId, "", JSON.stringify(payload));
    }

    /**
     * 检测更新
     */
    public checkUpdate(): void {
        Logger.info("微信小游戏", "开始检测更新");
        const info = wx.getSystemInfoSync();
        if (info.platform === "windows" || info.platform === "mac") {
            Logger.info("微信小游戏", "桌面平台，不检测更新");
            Etadpu.getInstance().emit(EtadpuEvent.UpdateSkip);
            return;
        }
        wx.showLoading({
            title: i18n.etadpu0015,
            mask: true,
        });
        Promise.race([
            new Promise<void>((resolve, reject) => {
                const updateManager = wx.getUpdateManager();
                // 请求完新版本信息的回调
                updateManager.onCheckForUpdate((res) => {
                    if (res.hasUpdate) {
                        wx.showLoading({
                            title: i18n.etadpu0017,
                            mask: true,
                        });
                        Logger.info("微信小游戏", "检测到更新");
                        Etadpu.getInstance().emit(EtadpuEvent.ShowMsg, i18n.etadpu0017);
                    } else {
                        wx.hideLoading({});
                        Logger.info("微信小游戏", "没有检测到更新");
                        Etadpu.getInstance().emit(EtadpuEvent.ShowMsg, i18n.etadpu0020);
                        Etadpu.getInstance().emit(EtadpuEvent.UpdateSuccess);
                    }
                    resolve();
                });

                // 新版本已经下载好，调用 applyUpdate 应用新版本并重启
                updateManager.onUpdateReady(() => {
                    wx.hideLoading({});
                    Logger.info("微信小游戏", "更新完成");
                    Etadpu.getInstance().emit(EtadpuEvent.ShowMsg, i18n.etadpu0018);
                    updateManager.applyUpdate();
                    setTimeout(() => {
                        // 特殊情况下如果未重启，则重新打开
                        Logger.info("微信小游戏", "超时触发更新完成");
                        Etadpu.getInstance().emit(EtadpuEvent.UpdateSuccess);
                    }, 3000);
                    resolve();
                });

                // 新版本下载失败
                updateManager.onUpdateFailed(() => {
                    wx.hideLoading({});
                    Logger.info("微信小游戏", "更新失败");
                    Etadpu.getInstance().emit(EtadpuEvent.ShowMsg, i18n.etadpu0019);
                    Etadpu.getInstance().emit(EtadpuEvent.UpdateSuccess);
                    resolve();
                });
            }),
            new Promise<void>((resolve, reject) => {
                setTimeout(resolve, 5000);
            }),
        ]);
    }

    /**
     * 跳转评价
     * @param data
     */
    public openEvaluate(): void {
        if (!wx.createPageManager) {
            Tips.getInstance().show(i18n.sdk0005);
            return;
        }

        wx.createPageManager().show({
            openlink: WECHAT_EVALUATE_LINK,
        });
    }

    /**
     * 打开游戏圈
     */
    public openGameClub(): void {
        const info = wx.getSystemInfoSync();
        if (info.platform === "windows" || info.platform === "mac") {
            Tips.getInstance().show("暂不支持电脑端打开游戏圈，请通过手机打开并领取奖励");
            return;
        }

        if (!wx.createPageManager) {
            Tips.getInstance().show(i18n.sdk0005);
            return;
        }

        const pageManager = wx.createPageManager();
        pageManager
            .load({
                openlink: WECHAT_MINIGAME_GAME_CLUB_LINK,
            })
            .then((res) => {
                Logger.info("微信小游戏", "打开游戏圈成功");
                // 加载成功后按需显示
                pageManager.show();
            })
            .catch((err) => {
                Logger.info(
                    "微信小游戏",
                    "打开游戏圈失败：" + err.errMsg + "#" + err.errCode + "#" + JSON.stringify(err.errInfo)
                );
                Tips.getInstance().show(err.errMsg);
            });
    }

    /**
     * 好友分享
     */
    public onShareAppMessage(): void {
        wx.onShareAppMessage(() => {
            Logger.info("微信小游戏", "分享给好友");
            const sdk = DataNexus.getInstance().getSdk();
            sdk.track("SHARE", {
                target: "APP_MESSAGE",
            });
            return {
                title: i18n.game0002,
                imageUrlId: WECHAT_MINIGAME_SHARE_IMAGE_URL_ID,
                imageUrl: WECHAT_MINIGAME_SHARE_IMAGE_URL,
                query: "",
            };
        });
    }

    /**
     * 朋友圈分享
     */
    public onShareTimeline(): void {
        wx.onShareTimeline(() => {
            Logger.info("微信小游戏", "分享朋友圈");
            const sdk = DataNexus.getInstance().getSdk();
            sdk.track("SHARE", {
                target: "TIME_LINE",
            });
            return {
                title: i18n.game0002,
                imageUrlId: WECHAT_MINIGAME_SHARE_IMAGE_URL_ID,
                imageUrl: WECHAT_MINIGAME_SHARE_IMAGE_URL,
                query: "",
            };
        });
    }

    /**
     * 检测场景值为桌面入口
     * @returns
     */
    public isEnterFromDesktop(): boolean {
        const info = wx.getEnterOptionsSync();
        if (!info) {
            return false;
        }
        return info.scene === 1023 || info.scene === 1186;
    }

    /**
     * 检测场景值为收藏入口
     * @returns
     */
    public isEnterFromFavorite(): boolean {
        const info = wx.getEnterOptionsSync();
        if (!info) {
            return false;
        }
        return info.scene === 1103 || info.scene === 1104 || info.scene === 1257;
    }

    /**
     * 检测场景值为微信分享群聊入口
     * @returns
     */
    public isEnterFromWechatShare(): boolean {
        const info = wx.getEnterOptionsSync();
        if (!info) {
            return false;
        }
        return info.shareTicket && info.chatType === 3;
    }
}
