import { IUIConfig, UIType } from "../../nsn/ui/UIType";

export const UI_CONFIG: IUIConfig[] = [
    { name: "BaseHome", type: UIType.Base, isBlockInputEvents: false, desc: "主界面" },
    { name: "BaseLogin", type: UIType.Base, desc: "登录界面" },

    { name: "PopupActivityExpeditionSettings", type: UIType.Popup, desc: "活动-游历-设置" },
    { name: "PopupActivityFundInfo", type: UIType.Popup, desc: "活动-基金详情" },
    { name: "PopupActivitySprint", type: UIType.Popup, desc: "活动-限时竞速" },
    { name: "PopupActivityPersonSprint", type: UIType.Popup, desc: "活动-个人竞速" },
    { name: "PopupActivityMatchGameEnd", type: UIType.Popup, desc: "咕嘟药水-游戏结束" },
    { name: "PopupAccountLogin", type: UIType.Popup, desc: "账号登录" },
    { name: "PopupActivityTimeBackPoster", type: UIType.Popup, desc: "时空回溯-海报入口" },
    { name: "PopupActivityUnion", type: UIType.Popup, desc: "公会-活动入口" },
    { name: "PopupActivityUnionDefenseAchievement", type: UIType.Popup, desc: "公会对决-挑战成就" },
    { name: "PopupActivityUnionDefenseChallenge", type: UIType.Popup, desc: "公会对决-挑战" },
    { name: "PopupActivityUnionDefenseDayLog", type: UIType.Popup, desc: "公会对决-日志" },
    { name: "PopupActivityUnionDefenseTreasury", type: UIType.Popup, desc: "公会对决-宝库" },
    { name: "PopupActivityUnionDefenseUnionDetails", type: UIType.Popup, desc: "公会对决-公会信息" },
    { name: "PopupActivityUnionDefenseResultReward", type: UIType.Popup, desc: "公会对决-结算奖励" },
    { name: "PopupAgreeProtocol", type: UIType.Popup, desc: "同意协议" },
    { name: "PopupArenaLog", type: UIType.Popup, desc: "竞技场-挑战记录" },
    { name: "PopupArenaOpponent", type: UIType.Popup, desc: "竞技场-对手" },
    { name: "PopupArenaReward", type: UIType.Popup, desc: "竞技场-奖励" },
    { name: "PopupArrowDetails", type: UIType.Popup, desc: "主页面-箭矢详情页" },
    { name: "PopupAttrShowSystem", type: UIType.Popup, desc: "属性展示-系统" },
    { name: "PopupAttrShowTotal", type: UIType.Popup, desc: "属性展示-总" },
    { name: "PopupBag", type: UIType.Popup, desc: "背包" },
    { name: "PopupBless", type: UIType.Popup, desc: "祝福系统" },
    { name: "PopupBindReward", type: UIType.Popup, desc: "账号绑定奖励" },
    { name: "PopupBulletin", type: UIType.Popup, desc: "公告" },
    { name: "PopupBox", type: UIType.Popup, desc: "宝箱" },
    { name: "PopupChat", type: UIType.Popup, desc: "聊天" },
    { name: "PopupChildMarriage", type: UIType.Popup, desc: "知己-王储联姻" },
    { name: "PopupCollectionBookInfoRewardShow", type: UIType.Popup, desc: "藏品-图鉴奖励预览" },
    { name: "PopupCollectionChange", type: UIType.Popup, desc: "藏品-转化" },
    { name: "PopupCollectionInfo", type: UIType.Popup, desc: "藏品-升级升星详情" },
    { name: "PopupCollectionNotOwn", type: UIType.Popup, desc: "藏品-未拥有" },
    { name: "PopupCollectionDrawRewardPreview", type: UIType.Popup, desc: "藏品-抽奖预览" },
    { name: "PopupCollectionGroupPreview", type: UIType.Popup, desc: "藏品-套装预览" },
    { name: "PopupCombatLogSet", type: UIType.Popup, desc: "战斗日志设置" },
    { name: "PopupConfidantChildApply", type: UIType.Popup, desc: "知己-王储申请" },
    { name: "PopupConfidantChildRaise", type: UIType.Popup, desc: "知己-王储提亲" },
    { name: "PopupConfidantLevelInfo", type: UIType.Popup, desc: "知己-知己等级信息" },
    { name: "PopupDailyTask", type: UIType.Popup, desc: "日常任务" },
    { name: "PopupDeleteAccount", type: UIType.Popup, desc: "删除账号" },
    { name: "PopupDeleteAccountConfirm", type: UIType.Popup, desc: "删除账号确认" },
    { name: "PopupDungeonBoxLevel", type: UIType.Popup, desc: "副本入口-丰裕宝匣-等级" },
    { name: "PopupDungeonEquipBeInvited", type: UIType.Popup, desc: "装备副本-被邀请" },
    { name: "PopupDungeonEquipInvite", type: UIType.Popup, desc: "装备副本-邀请" },
    { name: "PopupDungeonResultEquip", type: UIType.Popup, desc: "副本结果-装备" },
    { name: "PopupDungeonRewardPreview", type: UIType.Popup, desc: "副本-奖励预览" },
    { name: "PopupDungeonRewardMain", type: UIType.Popup, desc: "主线副本-奖励" },
    { name: "PopupEquipIncome", type: UIType.Popup, desc: "装备收益" },
    { name: "PopupEquipInfo", type: UIType.Popup, desc: "装备-详情" },
    { name: "PopupEquipStrengthen", type: UIType.Popup, desc: "装备-强化" },
    { name: "PopupEquipSuit", type: UIType.Popup, desc: "装备-套装" },
    { name: "PopupExchangeCode", type: UIType.Popup, desc: "兑换码" },
    { name: "PopupFirstRecharge", type: UIType.Popup, desc: "首充礼包" },
    { name: "PopupForge", type: UIType.Popup, desc: "锻造台" },
    { name: "PopupFriend", type: UIType.Popup, desc: "好友" },
    { name: "PopupFund", type: UIType.Popup, desc: "充值好礼-基金" },
    { name: "PopupGameClub", type: UIType.Popup, desc: "微信游戏圈" },
    { name: "PopupGamePlayPreview", type: UIType.Popup, desc: "玩法预告" },
    { name: "PopupGameServer", type: UIType.Popup, desc: "选择服务器" },
    { name: "PopupHomeAutoSet", type: UIType.Popup, desc: "主界面-自动设置" },
    { name: "PopupItemUse", type: UIType.Popup, desc: "使用道具" },
    { name: "PopupMailLogin", type: UIType.Popup, desc: "邮箱登录" },
    { name: "PopupMagicInfo", type: UIType.Popup, desc: "魔法系统-升级升星界面" },
    { name: "PopupMagicWear", type: UIType.Popup, desc: "魔法系统-魔法佩戴" },
    { name: "PopupMagicReplaced", type: UIType.Popup, desc: "魔法系统-魔法替换" },
    { name: "PopupMagicDock", type: UIType.Popup, desc: "魔法系统-魔法坞" },
    { name: "PopupMagicDraw", type: UIType.Popup, desc: "魔法抽奖-抽奖页面" },
    { name: "PopupMagicRewardPreview", type: UIType.Popup, desc: "魔法抽奖-奖励预览" },
    { name: "PopupMail", type: UIType.Popup, desc: "邮件" },
    { name: "PopupMiningAuto", type: UIType.Popup, desc: "挖矿系统-自动挖掘" },
    { name: "PopupMiningResearch", type: UIType.Popup, desc: "科技树" },
    { name: "PopupIdleReward", type: UIType.Popup, desc: "挂机收益" },
    { name: "PopupParkConfirm", type: UIType.Popup, desc: "停车场-保护费确认" },
    { name: "PopupPartnerDraw", type: UIType.Popup, desc: "伙伴抽奖-抽奖界面" },
    { name: "PopupPartnerRewardPreview", type: UIType.Popup, desc: "伙伴抽奖-奖励预览" },
    { name: "PopupPersonalized", type: UIType.Popup, desc: "个性化" },
    { name: "PopupParkIncome", type: UIType.Popup, desc: "停车场-收益" },
    { name: "PopupParkInfo", type: UIType.Popup, desc: "停车场-信息" },
    {
        name: "PopupParkOrder",
        type: UIType.Popup,
        desc: "停车场-订单",
    },
    { name: "PopupParkOrderInfo", type: UIType.Popup, desc: "停车场-订单信息" },
    { name: "PopupParkOtherIncome", type: UIType.Popup, desc: "停车场-他人收益" },
    {
        name: "PopupParkSearch",
        type: UIType.Popup,
        desc: "停车场-搜索",
    },
    { name: "PopupParkReady", type: UIType.Popup, desc: "停车场-准备" },
    {
        name: "PopupParkRecord",
        type: UIType.Popup,
        desc: "停车场-记录",
    },
    { name: "PopupParkSet", type: UIType.Popup, desc: "停车场-设置" },
    {
        name: "PopupParkSkin",
        type: UIType.Popup,
        desc: "停车场-皮肤",
    },
    { name: "PopupParkSkinInfo", type: UIType.Popup, desc: "停车场-皮肤信息" },
    { name: "PopupParkShowTank", type: UIType.Popup, desc: "停车场-展示战车" },
    { name: "PopupPartnerGroupInfo", type: UIType.Popup, desc: "伙伴-羁绊详情" },
    { name: "PopupPartnerInfo", type: UIType.Popup, desc: "伙伴-详情" },
    { name: "PopupPetEntryConfirm", type: UIType.Popup, desc: "宠物-词条确认" },
    { name: "PopupPetRefine", type: UIType.Popup, desc: "宠物-洗练" },
    { name: "PopupPetSwitch", type: UIType.Popup, desc: "宠物-切换" },
    { name: "PopupPetTeam", type: UIType.Popup, desc: "宠物-上阵" },
    { name: "PopupPetUpStar", type: UIType.Popup, desc: "宠物-吞噬" },
    { name: "PopupPlayerInfo", type: UIType.Popup, desc: "个人信息" },
    { name: "PopupPowerPeakRecord", type: UIType.Popup, desc: "王权之巅-记录" },
    { name: "PopupPowerFund", type: UIType.Popup, desc: "王权基金" },
    { name: "PopupPowerRoad", type: UIType.Popup, desc: "王权之路" },
    { name: "PopupPrivilegeCard", type: UIType.Popup, desc: "充值好礼-特权卡" },
    { name: "PopupPowerUpgrade", type: UIType.Popup, desc: "王权系统-爵位晋升" },
    { name: "PopupRank", type: UIType.Popup, desc: "排行榜" },
    { name: "PopupRechargeGift", type: UIType.Popup, desc: "充值好礼" },
    { name: "PopupRechargePackChoose", type: UIType.Popup, desc: "充值好礼-礼包自选" },
    { name: "PopupRealNameCert", type: UIType.Popup, desc: "实名认证" },
    { name: "PopupRedPacket", type: UIType.Popup, desc: "红包" },
    { name: "PopupRedPacketGive", type: UIType.Popup, desc: "红包-发出" },
    { name: "PopupRedPacketOpen", type: UIType.Popup, desc: "红包-开启" },
    { name: "PopupRewardPreview", type: UIType.Popup, desc: "奖励预览" },
    { name: "PopupShop", type: UIType.Popup, desc: "通用商店" },
    { name: "PopupSignIn", type: UIType.Popup, desc: "签到" },
    { name: "PopupSkinInfo", type: UIType.Popup, desc: "圣装-详情" },
    { name: "PopupSkinStory", type: UIType.Popup, desc: "圣装-故事" },
    { name: "PopupSocial", type: UIType.Popup, desc: "社交" },
    { name: "PopupTankStarPreview", type: UIType.Popup, desc: "战车满星预览" },
    { name: "PopupTabPrivilege", type: UIType.Popup, desc: "特权卡" },
    { name: "PopupTelLogin", type: UIType.Popup, desc: "手机验证码登录" },
    { name: "PopupUnionApplyList", type: UIType.Popup, desc: "公会-申请列表" },
    { name: "PopupUnionBoss", type: UIType.Popup, desc: "公会-boss" },
    { name: "PopupUnionCreate", type: UIType.Popup, desc: "公会-创建" },
    { name: "PopupUnionDonate", type: UIType.Popup, desc: "公会-捐赠" },
    { name: "PopupUnionEdit", type: UIType.Popup, desc: "公会-编辑" },
    { name: "PopupUnionHelp", type: UIType.Popup, desc: "公会-互助" },
    { name: "PopupUnionTask", type: UIType.Popup, desc: "公会-任务" },
    { name: "PopupUnionTreasureShop", type: UIType.Popup, desc: "公会-砍价商店" },
    { name: "PopupUnionRank", type: UIType.Popup, desc: "公会-排行榜" },
    { name: "PopupVipCustomerService", type: UIType.Popup, desc: "vip客服" },
    { name: "PopupVipServicePush", type: UIType.Popup, desc: "专属客服推送" },
    { name: "PopupWeaponSkin", type: UIType.Popup, desc: "神器-幻化" },
    { name: "PopupWeaponStarPreview", type: UIType.Popup, desc: "神器-满星预览" },
    { name: "PopupWingEnchant", type: UIType.Popup, desc: "背饰-附魔" },
    { name: "PopupWingSkin", type: UIType.Popup, desc: "背饰-幻化" },
    { name: "PopupWingStarPreview", type: UIType.Popup, desc: "背饰-满星预览" },
    { name: "PopupWxFavorite", type: UIType.Popup, desc: "添加好礼" },
    { name: "PopupWv", type: UIType.Popup, desc: "打开网页" },

    { name: "FloatAccountBind", type: UIType.Float, desc: "账号绑定" },
    { name: "FloatAccountEmailOperation", type: UIType.Float, desc: "邮箱操作" },
    { name: "FloatActivityExpeditionHatGame", type: UIType.Float, desc: "活动-游历-游历-小游戏-魔术戏法" },
    { name: "FloatActivityExpeditionSuccess", type: UIType.Float, desc: "活动-游历-游历成功" },
    { name: "FloatActivityExpeditionTreasureGame", type: UIType.Float, desc: "活动-游历-小游戏-幸运宝藏" },
    { name: "FloatActivityExpeditionTurntableGame", type: UIType.Float, desc: "活动-游历-小游戏-幸运转盘" },
    { name: "FloatActivityHamsterParkourGameOver", type: UIType.Float, desc: "仓鼠冲刺-游戏结束" },
    { name: "FloatActivityHamsterParkourGamePause", type: UIType.Float, desc: "仓鼠冲刺-游戏暂停" },
    { name: "FloatActivityUnionDefenseChallengeResult", type: UIType.Float, desc: "公会对决-挑战扫荡结算" },
    { name: "FloatActivityUnionDefenseResult", type: UIType.Float, desc: "公会对决-领奖期结算" },
    { name: "FloatActivityUnionDefenseRewardNotice", type: UIType.Float, desc: "公会对决-奖励公示" },
    { name: "FloatActivityUnionDefenseRewardPreview", type: UIType.Float, desc: "公会对决-奖励预览" },
    { name: "FloatAlert", type: UIType.Float, desc: "通用单按钮弹窗" },
    { name: "FloatArenaEntry", type: UIType.Float, desc: "竞技场-入场动画" },
    { name: "FloatBagExchange", type: UIType.Float, desc: "背包转换" },
    { name: "FloatChatFriendMore", type: UIType.Float, desc: "聊天-好友-更多" },
    { name: "FloatChatSelect", type: UIType.Float, desc: "聊天选择" },
    { name: "FloatChildPreview", type: UIType.Float, desc: "知己-王储预览" },
    { name: "FloatCollectionGameReward", type: UIType.Float, desc: "藏品玩法奖励" },
    { name: "FloatCollectionPreviewDetail", type: UIType.Float, desc: "藏品系统-藏品预览" },
    { name: "FloatCollectionUpStar", type: UIType.Float, desc: "藏品系统-升星成功" },
    { name: "FloatCombatPvpParkResult", type: UIType.Float, desc: "pvp战斗-停车场结果" },
    { name: "FloatCommonGift", type: UIType.Float, desc: "通用礼包" },
    { name: "FloatConfidantDialog", type: UIType.Float, desc: "知己-知己对话" },
    { name: "FloatConfidantGetChild", type: UIType.Float, desc: "知己-获得王储" },
    { name: "FloatConfidantUpgrade", type: UIType.Float, desc: "知己-知己升级" },
    { name: "FloatDungeonChapterMain", type: UIType.Float, desc: "主线副本-章节" },
    { name: "FloatDungeonEnd", type: UIType.Float, desc: "副本-结束" },
    { name: "FloatDungeonEndUnion", type: UIType.Float, desc: "公会副本-结束" },
    { name: "FloatDungeonFailure", type: UIType.Float, desc: "副本-失败" },
    { name: "FloatDungeonFailureArena", type: UIType.Float, desc: "竞技场副本-失败" },
    { name: "FloatDungeonPause", type: UIType.Float, desc: "副本-暂停" },
    { name: "FloatDungeonSkipLevelMain", type: UIType.Float, desc: "主线副本-跳过关卡" },
    { name: "FloatDungeonSuccess", type: UIType.Float, desc: "副本-成功" },
    { name: "FloatDungeonSuccessArena", type: UIType.Float, desc: "竞技场副本-成功" },
    { name: "FloatDiyBox", type: UIType.Float, desc: "自选宝箱" },
    { name: "FloatDiyBoxConfirm", type: UIType.Float, desc: "自选宝箱-确认" },
    { name: "FloatEquipMaster", type: UIType.Float, desc: "装备=强化大师" },
    { name: "FloatEquipReplace", type: UIType.Float, desc: "装备替换" },
    { name: "FloatFakeAd", type: UIType.Float, desc: "虚拟广告" },
    { name: "FloatFirstRechargeGift", type: UIType.Float, desc: "首充-赠礼" },
    { name: "FloatForgeAccelerate", type: UIType.Float, desc: "锻造-道具加速" },
    { name: "FloatForgePromote", type: UIType.Float, desc: "锻造-升阶成功" },
    { name: "FloatForgeAttrShow", type: UIType.Float, desc: "锻造台-属性展示" },
    { name: "FloatLinkGift", type: UIType.Float, desc: "链式礼包" },
    { name: "FloatInfo", type: UIType.Float, desc: "通用双按钮弹窗" },
    { name: "FloatItemBox", type: UIType.Float, desc: "道具信息-宝箱类型" },
    { name: "FloatItemDetail", type: UIType.Float, desc: "道具信息-普通类型" },
    { name: "FloatItemSource", type: UIType.Float, desc: "道具信息-来源" },
    { name: "FloatMailDetail", type: UIType.Float, desc: "邮件-详情" },
    { name: "FloatMagicAllUpStar", type: UIType.Float, desc: "魔法-一键升星" },
    { name: "FloatMagicAllPromote", type: UIType.Float, desc: "魔法-一键升阶" },
    { name: "FloatMagicDrawCardDetail", type: UIType.Float, desc: "魔法抽奖-魔法详情" },
    { name: "FloatMagicPreview", type: UIType.Float, desc: "魔法系统-兑换预览" },
    { name: "FloatMagicPromote", type: UIType.Float, desc: "魔法系统-进阶" },
    { name: "FloatMagicPromoteShow", type: UIType.Float, desc: "魔法系统-进阶展示" },
    { name: "FloatMagicUpgrade", type: UIType.Float, desc: "魔法系统-升星成功" },
    { name: "FloatMagicWear", type: UIType.Float, desc: "魔法系统-确认佩戴" },
    { name: "FloatMiningResearchComplete", type: UIType.Float, desc: "采矿研究所-研究完成" },
    { name: "FloatMiningWatchAdvertisement", type: UIType.Float, desc: "采矿-广告确认弹窗" },
    { name: "FloatMultiPay", type: UIType.Float, desc: "支付窗口" },
    { name: "FloatNewCollection", type: UIType.Float, desc: "首次获得-新藏品" },
    { name: "FloatNewLeadSkin", type: UIType.Float, desc: "首次获得-新主角圣装" },
    { name: "FloatNewMagic", type: UIType.Float, desc: "首次获得-新魔法" },
    { name: "FloatNewPet", type: UIType.Float, desc: "宠物-获得感" },
    { name: "FloatNewPartner", type: UIType.Float, desc: "首次获得-新伙伴" },
    { name: "FloatNewTank", type: UIType.Float, desc: "首次获得-新战车" },
    { name: "FloatNewWeapon", type: UIType.Float, desc: "首次获得-新神器" },
    { name: "FloatNewWing", type: UIType.Float, desc: "首次获得-新背饰" },
    { name: "FloatNsnHelp", type: UIType.Float, desc: "客服" },
    { name: "FloatOtherPlayerInfo", type: UIType.Float, desc: "查看-他人信息" },
    { name: "FloatIdleRewardPreview", type: UIType.Float, desc: "挂机收益-预览" },
    { name: "FloatIdleRewardQuick", type: UIType.Float, desc: "挂机收益-快速受益" },
    { name: "FloatParkRename", type: UIType.Float, desc: "停车场-重命名" },
    { name: "FloatParkUpgrade", type: UIType.Float, desc: "停车场-升级" },
    { name: "FloatPartnerAllUpStar", type: UIType.Float, desc: "伙伴-一键升星" },
    { name: "FloatPartnerUpgrade", type: UIType.Float, desc: "伙伴-升星" },
    { name: "FloatPartnerDrawCardDetail", type: UIType.Float, desc: "伙伴预览-伙伴详情" },
    { name: "FloatPetEntryPreview", type: UIType.Float, desc: "宠物-词条预览" },
    { name: "FloatPetEntryTips", type: UIType.Float, desc: "宠物-词条详情" },
    { name: "FloatPetUpStar", type: UIType.Float, desc: "宠物-吞噬成功" },
    { name: "FloatPetRefine", type: UIType.Float, desc: "宠物-洗练结果" },
    { name: "FloatPlayerRename", type: UIType.Float, desc: "玩家改名" },
    { name: "FloatPlayerReport", type: UIType.Float, desc: "玩家举报" },
    { name: "FloatPowerUpgrade", type: UIType.Float, desc: "王权系统-突破" },
    { name: "FloatPowerPromote", type: UIType.Float, desc: "王权系统-晋升" },
    { name: "FloatRandomBox", type: UIType.Float, desc: "随机宝箱" },
    { name: "FloatRechargeInfo", type: UIType.Float, desc: "代币提示框" },
    { name: "FloatReward", type: UIType.Float, desc: "获得奖励" },
    { name: "FloatRewardTips", type: UIType.Float, desc: "奖励浮窗" },
    { name: "FloatRule", type: UIType.Float, desc: "规则" },
    { name: "FloatShare", type: UIType.Float, desc: "分享" },
    { name: "FloatShopItemDetail", type: UIType.Float, desc: "商店购买详情" },
    { name: "FloatSkillSet", type: UIType.Float, desc: "技能设置" },
    { name: "FloatSkinPreviewDetail", type: UIType.Float, desc: "圣装预览-圣装详情" },
    { name: "FloatSkinFatePreview", type: UIType.Float, desc: "主角-天命预览" },
    { name: "FloatSkillTips", type: UIType.Float, desc: "圣装-技能描述" },
    { name: "FloatSkinUpgrade", type: UIType.Float, desc: "圣装-升星" },
    { name: "FloatTankExpand", type: UIType.Float, desc: "首次获得-战车扩张" },
    { name: "FloatTankFatePreview", type: UIType.Float, desc: "战车-天命预览" },
    { name: "FloatTankPreviewDetail", type: UIType.Float, desc: "战车预览-战车详情" },
    { name: "FloatTankUpStar", type: UIType.Float, desc: "战车-升星成功" },
    { name: "FloatTextTips", type: UIType.Float, desc: "文本浮窗" },
    { name: "FloatTravelReward", type: UIType.Float, desc: "知己-游历奖励" },
    { name: "FloatUnionBossLog", type: UIType.Float, desc: "公会-boss-日志" },
    { name: "FloatUnionFlag", type: UIType.Float, desc: "公会-旗帜" },
    { name: "FloatUnionInfo", type: UIType.Float, desc: "公会-详情" },
    { name: "FloatUnionOpMenu", type: UIType.Float, desc: "公会-操作菜单" },
    { name: "FloatUnionRename", type: UIType.Float, desc: "公会-改名" },
    { name: "FloatUnionTreasureShopList", type: UIType.Float, desc: "公会-未议价名单" },
    { name: "FloatUnionRankDetails", type: UIType.Float, desc: "公会-排行榜-详情" },
    { name: "FloatVideo", type: UIType.Float, desc: "视频-播放" },
    { name: "FloatWeaponFatePreview", type: UIType.Float, desc: "神器-天命预览" },
    { name: "FloatWeaponPreviewDetail", type: UIType.Float, desc: "神器-神器详情" },
    { name: "FloatWeaponUpStar", type: UIType.Float, desc: "神器-升星" },
    { name: "FloatWingFatePreview", type: UIType.Float, desc: "背饰-天命预览" },
    { name: "FloatWingPreviewDetail", type: UIType.Float, desc: "背饰-背饰详情" },
    { name: "FloatWingUpStar", type: UIType.Float, desc: "背饰-升星" },

    { name: "UIActivityExpedition", type: UIType.UI, desc: "游历" },
    { name: "UIActivityExpeditionGame", type: UIType.UI, desc: "游历-游戏" },
    { name: "UIActivityMatchGame", type: UIType.UI, desc: "活动-咕嘟药水" },
    { name: "UIActivityMatchGameStart", type: UIType.UI, desc: "咕嘟药水-游戏" },
    { name: "UIActivityHamsterParkour", type: UIType.UI, desc: "仓鼠冲刺-入口界面" },
    { name: "UIActivityHamsterParkourGame", type: UIType.UI, desc: "仓鼠冲刺-游戏中" },
    { name: "UIActivityNewbieTrial", type: UIType.UI, desc: "新手试炼" },
    { name: "UIActivityOpeningCelebration", type: UIType.UI, desc: "开服庆典" },
    { name: "UIActivitySprintInfo", type: UIType.UI, desc: "活动-限时竞速-详情" },
    { name: "UIActivityPersonSprintInfo", type: UIType.UI, desc: "活动-个人竞速-详情" },
    { name: "UIActivityTankTreasure", type: UIType.UI, desc: "战车夺宝" },
    { name: "UIActivityTimeBack", type: UIType.UI, desc: "时空回溯" },
    { name: "UIActivityTimeBackDraw", type: UIType.UI, desc: "时空回溯-抽中大奖" },
    { name: "UIActivityUnionDefense", type: UIType.UI, desc: "公会对决-主界面" },
    { name: "UIActivityUnionDefenseStart", type: UIType.UI, desc: "公会对决" },
    { name: "UIActivityWeaponDrawCard", type: UIType.UI, desc: "神器抽卡-主页面" },
    { name: "UIActivityWingDrawCard", type: UIType.UI, desc: "背饰抽卡-主页面" },
    { name: "UIArena", type: UIType.UI, desc: "竞技场" },
    { name: "UICollection", type: UIType.UI, desc: "藏品系统" },
    { name: "UICollectionGame", type: UIType.UI, desc: "藏品玩法" },
    { name: "UIConfidant", type: UIType.UI, desc: "知己" },
    { name: "UIConfidantChildInfo", type: UIType.UI, desc: "知己-王储信息" },
    { name: "UIConfidantInfo", type: UIType.UI, desc: "知己-知己信息" },
    { name: "UIConfidantMarriage", type: UIType.UI, desc: "知己-联姻" },
    { name: "UICrazyDraw", type: UIType.UI, desc: "狂送x抽" },
    { name: "UIDungeonBoss", type: UIType.UI, desc: "副本入口-不竭之境" },
    { name: "UIDungeonBox", type: UIType.UI, desc: "副本入口-丰裕宝匣" },
    { name: "UIDungeonBoxGame", type: UIType.UI, desc: "副本入口-丰裕宝匣-玩法" },
    { name: "UIDungeonCloud", type: UIType.UI, desc: "副本入口-米德云端" },
    { name: "UIDungeonCombatArena", type: UIType.UI, desc: "竞技场副本" },
    { name: "UIDungeonCombatBoss", type: UIType.UI, desc: "boss副本" },
    { name: "UIDungeonCombatCloud", type: UIType.UI, desc: "云端副本" },
    { name: "UIDungeonCombatPark", type: UIType.UI, desc: "停车场副本" },
    { name: "UIDungeonCombatTest", type: UIType.UI, desc: "测试副本" },
    { name: "UIDungeonCombatThief", type: UIType.UI, desc: "怪盗副本" },
    { name: "UIDungeonCombatTower", type: UIType.UI, desc: "爬塔副本" },
    { name: "UIDungeonCombatTrial", type: UIType.UI, desc: "试炼副本" },
    { name: "UIDungeonCombatUnion", type: UIType.UI, desc: "公会副本" },
    { name: "UIDungeonCombatUnionDefense", type: UIType.UI, desc: "公会攻防战副本" },
    { name: "UIDungeonThief", type: UIType.UI, desc: "副本入口-怪盗积德" },
    { name: "UIDungeonTower", type: UIType.UI, desc: "副本入口-天虹之塔" },
    { name: "UIGamePlay", type: UIType.UI, desc: "玩法" },
    { name: "UIGrowth", type: UIType.UI, desc: "养成入口" },
    { name: "UIHomeLand", type: UIType.UI, desc: "家园入口" },
    { name: "UILead", type: UIType.UI, desc: "主角" },
    { name: "UIMagicDrawCard", type: UIType.UI, desc: "魔法抽奖" },
    { name: "UIMall", type: UIType.UI, desc: "商城" },
    { name: "UIMining", type: UIType.UI, desc: "挖矿" },
    { name: "UIPark", type: UIType.UI, desc: "停车场" },
    { name: "UIPartner", type: UIType.UI, desc: "伙伴" },
    { name: "UIPartnerDrawCard", type: UIType.UI, desc: "伙伴抽奖" },
    { name: "UIPet", type: UIType.UI, desc: "宠物" },
    { name: "UIPower", type: UIType.UI, desc: "王权系统-主界面" },
    { name: "UIPowerPeak", type: UIType.UI, desc: "王权之巅" },
    { name: "UIRank", type: UIType.UI, desc: "排行榜" },
    { name: "UISelectLead", type: UIType.UI, desc: "选择主角" },
    { name: "UISkin", type: UIType.UI, desc: "圣装" },
    { name: "UITank", type: UIType.UI, desc: "战车" },
    { name: "UITotalRecharge", type: UIType.UI, desc: "累天累充" },
    { name: "UIUnion", type: UIType.UI, desc: "公会" },
    { name: "UIUnionHall", type: UIType.UI, desc: "公会-大厅" },
    { name: "UIUnionList", type: UIType.UI, desc: "公会-列表" },
];
