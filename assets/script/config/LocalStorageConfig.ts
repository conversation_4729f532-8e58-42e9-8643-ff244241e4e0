/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-27 14:13:12
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-16 15:38:37
 */

/** 所有的本地存储KEY写在这里 */
export const enum LocalStorageKey {
    // 语言
    Language = "nsn-language",

    // 音频
    AudioMusic = "audio-music",
    AudioEffect = "audio-effect",

    // 设置
    SettingMusic = "setting-music", // 音乐
    SettingEffect = "setting-effect", // 音效
    SettingMusicVolume = "setting-music-volume", // 音乐音量
    SettingEffectVolume = "setting-effect-volume", // 音效音量

    // 账号相关
    DeviceId = "device-id",
    GuestId = "guest-id",
    Account = "account",
    Password = "password",
    Email = "email",
    EmailPassword = "email-password",
    LoginType = "login-type",
    Tel = "telephone",
    Protocol = "protocol",
    WechatDeviceId = "wechat-device-id",
    ThirdId = "third-id",

    // 弹窗
    PopupForever = "popup-forever", // 终身弹窗
    PopupDaily = "popup-daily", // 每日弹窗
    PopupWeekly = "popup-weekly", // 每周弹窗
    PopupDuration = "popup-duration", // 每时间段弹窗
    PopupTodayNo = "popup-today-no", // 今天不弹

    // 红点
    RedPointDaily = "red-point-daily", // 每日红点
    RedPointWeekly = "red-point-weekly", // 每周红点
    RedPointForever = "red-point-forever", // 终身红点

    // 开关
    AdventureAutoDoubleRebirth = "adventure-auto-double-rebirth", // 冒险-自动双倍重生
    MakeArrowAutoMakeArrow = "make-arrow-auto-make-arrow", // 制作弓箭-自动制作弓箭

    // 任务
    TaskProgress = "task-progress", // 任务进度

    // 挖矿
    MiningAutoSettings = "mining-auto-settings", // 挖矿规则设置

    // 公告
    ReadBulletin = "read-bulletin", // 已读公告

    // 充值
    RechargeInRegisterDate = "recharge-in-register-date", // 注册当日是否有充值

    // 好友
    FriendChatList = "friend-chat-list", // 聊天列表
    FriendChatMsg = "friend-chat-msg", // 聊天记录
    FriendUnreadList = "friend-unread-list", // 未读信息
    FriendAddRefreshTime = "friend-add-refresh-time", // 推荐好友刷新时间

    // 社群福利
    Social = "social",

    // 个性化
    PersonalizedNewLead = "personalized-new-lead", // 新角色
    PersonalizedNewTank = "personalized-new-tank", // 新战车
    PersonalizedNewWeapon = "personalized-new-weapon", // 新神器
    PersonalizedNewWing = "personalized-new-wing", // 新背饰
    PersonalizedNewPet = "personalized-new-pet", // 新宠物
    PersonalizedNewTitle = "personalized-new-title", // 新称号
    PersonalizedNewAvatarFrame = "personalized-new-avatar-frame", // 新头像框
    PersonalizedNewAvatar = "personalized-new-avatar", // 新头像

    // 主角
    LeadNew = "lead-new", // 新角色

    // 装备
    EquipNew = "equip-new", // 新装备

    // 战斗
    CombatSettingData = "combat-setting-data", // 设置数据
    CombatLogSettingData = "combat-log-setting-data", // 日志设置数据

    // 战车
    TankNewInfos = "tank-new-infos", // 是否新战车

    // 锻造台
    ForgeNewInfos = "forge-new-infos", // 是否新锻造台
    ForgeAllUpgrade = "forge-all-upgrade", // 锻造台一键升级

    // 宝箱
    BoxSkinAni = "box-skin-ani", // 宝箱是否跳过动画

    // 抽卡相关
    CollectionSkinAni = "collection-skin-ani", // 抽卡跳过动画

    // vip客服推送
    VipServicePush = "vip-service-push",
}

/**
 * 所有的不被清理的全局KEY写在这里
 * 目前只支持全局KEY
 */
export const LOCAL_STORAGE_DONT_CLEAR_KEY = [LocalStorageKey.DeviceId];
