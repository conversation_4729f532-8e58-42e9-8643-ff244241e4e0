/* eslint-disable @typescript-eslint/naming-convention */

export const ZHS_SERVER = {
    "activityGetInfoRet": {
        "0": "默认"
    },
    "activitySignRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "暂无奖励可领取",
        "3": "奖励已领取",
        "4": "活动未开启",
        "5": "参数错误",
        "6": "操作太频繁"
    },
    "activityGameStartRet": {
        "0": "默认",
        "1": "不在活动期间",
        "2": "道具不足",
        "3": "配置不存在"
    },
    "activityGameEndRet": {
        "0": "默认",
        "1": "不在活动期间",
        "2": "找不到关卡",
        "3": "道具不足",
        "4": "配置不存在"
    },
    "activityGameRewardRet": {
        "0": "默认",
        "1": "不在活动期间",
        "2": "找不到关卡",
        "3": "进度未达到",
        "4": "奖励已领取",
        "5": "配置不存在",
        "6": "奖励不存在"
    },
    "activityGameTaskEndRet": {
        "0": "默认",
        "1": "不在活动期间",
        "2": "配置不存在",
        "4": "参数错误"
    },
    "activityNewTrialFreeRet": {
        "0": "默认",
        "1": "奖励已领取",
        "2": "配置不存在",
        "3": "不在活动期间"
    },
    "activityNewTrialRewardRet": {
        "0": "默认",
        "1": "不在活动期间",
        "2": "配置不存在",
        "3": "关卡已达上限",
        "4": "道具不足"
    },
    "adsInitRet": {
        "0": "默认"
    },
    "adsWatchRet": {
        "0": "默认",
        "1": "未找到广告配置",
        "2": "冷却中，请稍后重试",
        "3": "广告次数已看完"
    },
    "adsSkipRet": {
        "0": "默认",
        "1": "未找到广告配置",
        "2": "冷却中，请稍后重试",
        "3": "广告次数已看完",
        "4": "该广告不可跳过",
        "5": "道具不足"
    },
    "adsAwardClaimRet": {
        "0": "默认"
    },
    "archerInitRet": {
        "0": ""
    },
    "archerBattleRet": {
        "0": "",
        "1": "伙伴不存在",
        "2": "伙伴不可重复上阵",
        "3": "上阵位置不存在"
    },
    "archerUpgradeRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "伙伴不存在",
        "3": "等级已达上限",
        "4": "参数错误",
        "5": "星级不足"
    },
    "archerResetLevelRet": {
        "0": "默认",
        "1": "伙伴不存在"
    },
    "archerIncrStarRet": {
        "0": "默认",
        "1": "资源不足",
        "3": "伙伴不存在",
        "4": "星级已达上限"
    },
    "archerBatchIncrStarRet": {
        "0": "默认"
    },
    "archerActivateRewardRet": {
        "0": "默认",
        "1": "奖励已领取",
        "2": "伙伴不存在"
    },
    "archerGroupActivateRewardRet": {
        "0": "默认",
        "1": "奖励已领取",
        "2": "未满足条件",
        "3": "配置不存在"
    },
    "archerCreateRet": {
        "0": "默认"
    },
    "arenaRefreshRet": {
        "0": "默认",
        "1": "请求太频繁"
    },
    "arenaChallengeRet": {
        "0": "默认",
        "1": "挑战玩家不在挑战列表",
        "2": "资源不足",
        "3": "已定榜，不可再挑战",
        "4": "战斗异常"
    },
    "arenaGetLogRet": {
        "0": "默认"
    },
    "arrowGroupInitRet": {
        "0": ""
    },
    "arrowGroupUnlockBattleCellRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "资源不足",
        "3": "参数错误",
        "4": "该格子已解锁",
        "5": "弓箭手不存在",
        "6": "弓箭手已被使用"
    },
    "arrowGroupUnlockArrowCellRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "资源不足",
        "3": "参数错误",
        "4": "该格子已解锁"
    },
    "arrowGroupSyncRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "资源不足",
        "3": "参数错误",
        "4": "箭矢合成异常"
    },
    "arrowGroupGetAttributeRet": {
        "0": "默认"
    },
    "bagInitRet": {
        "0": "默认"
    },
    "bagOpenRandomBoxRet": {
        "0": "",
        "1": "道具不足",
        "2": "配置不存在"
    },
    "bagOpenSelectBoxRet": {
        "0": "",
        "1": "宝箱不足",
        "2": "奖励配置不存在",
        "3": "开启数量错误",
        "4": "自选奖励不匹配"
    },
    "bagBatchExchangeRet": {
        "0": "",
        "1": "配置不存在",
        "2": "道具不足"
    },
    "bagUsePropRet": {
        "0": "",
        "1": "配置不存在",
        "2": "道具不足",
        "3": "参数错误",
        "4": "道具类型错误"
    },
    "blessInitRet": {
        "0": ""
    },
    "blessStartRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "请先看广告"
    },
    "boxInitRet": {
        "0": ""
    },
    "boxOpenRet": {
        "0": "",
        "1": "道具不足",
        "2": "配置不存在"
    },
    "boxProgressRewardRet": {
        "0": "",
        "1": "未达到领取条件"
    },
    "boxProgressBatchRewardRet": {
        "0": "",
        "1": "未达到领取条件"
    },
    "bulletinBoardPopupRet": {
        "0": "默认"
    },
    "cdkeyTakeRet": {
        "0": "默认",
        "1": "兑换码不存在",
        "2": "兑换码未生效",
        "3": "该渠道不可兑换此兑换码",
        "4": "兑换码已被领取",
        "5": "兑换码已兑换完"
    },
    "chatGetChannelMessagesRet": {
        "0": "默认"
    },
    "chatSendChannelRet": {
        "0": "默认",
        "1": "频道不存在",
        "2": "消息内容包含敏感信息"
    },
    "chatBroadcastRet": {
        "0": "默认"
    },
    "chatSendRet": {
        "0": "默认",
        "1": "找不到玩家",
        "2": "对方拒绝了陌生人聊天",
        "3": "已被对方拉入黑名单",
        "4": "已被拉入黑名单",
        "5": "消息内容包含敏感信息"
    },
    "chatGetOfflineMessageRet": {
        "0": "默认"
    },
    "collectionInitRet": {
        "0": ""
    },
    "collectionUnlockRet": {
        "0": "",
        "1": "配置不存在",
        "2": "道具不足",
        "3": "藏品已解锁"
    },
    "collectionUpgradeRet": {
        "0": "",
        "1": "道具不足",
        "2": "等级已达最高",
        "3": "藏品不存在",
        "4": "配置不存在"
    },
    "collectionUpgradeStarRet": {
        "0": "",
        "1": "道具不足",
        "2": "星级已达最高",
        "3": "藏品不存在",
        "4": "配置不存在"
    },
    "collectionReceiveExpRet": {
        "0": "",
        "2": "配置不存在",
        "3": "藏品不存在"
    },
    "collectionLevelRewardRet": {
        "0": ""
    },
    "collectionGroupRewardRet": {
        "0": "默认",
        "1": "奖励已领取",
        "2": "未满足条件",
        "3": "配置不存在"
    },
    "collectionCreateNoticeRet": {
        "0": ""
    },
    "collectionGameInitRet": {
        "0": ""
    },
    "collectionGameOpenRet": {
        "0": "",
        "1": "参数错误",
        "2": "格子已开启",
        "4": "体力不足"
    },
    "collectionGameOpenRewardRet": {
        "0": "",
        "1": "没有奖励可领取"
    },
    "collectionGameUpgradeRet": {
        "0": "",
        "1": "大奖不存在",
        "2": "没有升级次数了"
    },
    "collectionGameBigRewardRet": {
        "0": "",
        "1": "大奖不存在",
        "2": "还未升级结束"
    },
    "collectionGameNextBarrierRet": {
        "0": "",
        "1": "大奖还未领取",
        "2": "大奖还没有开出"
    },
    "collectionGameWatchAdRet": {
        "0": "",
        "1": "请先看广告",
        "2": "配置不存在"
    },
    "confidantInitRet": {
        "0": ""
    },
    "confidantPrincessUnlockRet": {
        "0": "",
        "1": "配置不存在",
        "2": "道具不足",
        "3": "不能通过解锁获得",
        "4": "已拥有"
    },
    "confidantCreatePrincessNotice": {
        "0": ""
    },
    "confidantPrincessUpgradeRet": {
        "0": "",
        "1": "配置不存在",
        "2": "未拥有",
        "3": "等级已达上限",
        "4": "道具不足"
    },
    "confidantPrincessMarryRet": {
        "0": "",
        "1": "配置不存在",
        "2": "未拥有",
        "3": "亲密度等级不足",
        "4": "已结婚",
        "5": "道具不足"
    },
    "confidantPrincessFavorRet": {
        "0": "",
        "1": "配置不存在",
        "2": "未拥有知己",
        "4": "还未结婚",
        "5": "道具不足",
        "6": "没有宠幸对象"
    },
    "confidantTravelingRet": {
        "0": "",
        "1": "体力不足"
    },
    "confidantExchangeStrengthRet": {
        "0": "",
        "1": "道具不足"
    },
    "confidantChildModifyNameRet": {
        "0": "",
        "1": "子嗣不存在",
        "2": "命名不规范"
    },
    "confidantChildUpgradeRet": {
        "0": "",
        "1": "子嗣不存在",
        "2": "等级已达上限",
        "3": "道具不足"
    },
    "confidantChildPublishRet": {
        "0": "",
        "1": "子嗣不存在",
        "2": "子嗣已提亲",
        "3": "等级不足"
    },
    "confidantChildCancelProposeRet": {
        "0": "",
        "1": "子嗣不存在",
        "2": "子嗣还未提亲"
    },
    "confidantChildSearchRet": {
        "0": "",
        "1": "找不到匹配的玩家，请重新输入"
    },
    "confidantChildMarryApplyRet": {
        "0": "",
        "1": "子嗣不存在",
        "2": "子嗣等级还未满级",
        "3": "子嗣已联姻",
        "4": "子嗣提亲中，不可操作",
        "5": "联姻对象不存在",
        "6": "联姻对象还未满级",
        "7": "联姻对象已联姻",
        "8": "同性不能联姻"
    },
    "confidantChildMarryApplyNotice": {
        "0": ""
    },
    "confidantChildDealApplyRet": {
        "0": "",
        "1": "子嗣不存在",
        "2": "子嗣等级还未满级",
        "3": "子嗣已联姻",
        "4": "子嗣提亲中，不可操作",
        "5": "申请已过期",
        "6": "联姻对象已联姻"
    },
    "confidantChildDealApplyNotice": {
        "0": ""
    },
    "confidantChildBatchRejectApplyRet": {
        "0": "",
        "1": "子嗣不存在"
    },
    "httpError": {
        "0": "默认成功",
        "1": "当前服务器已爆满!",
        "2": "服务器还未准备好，请稍后重试~",
        "3": "服务器维护中",
        "4": "链接参数异常",
        "5": "登录过期，请重新登录",
        "6": "登录校验错误，请重新登录",
        "7": "发现版本更新，点击确定重新启动游戏进行更新",
        "8": "账号已被限制登录",
        "9": "账号已有登录状态，请稍后重试",
        "10": "发现新版本，请跳转应用商店更新",
        "11": "请先实名认证",
        "12": "未成年不可进入游戏",
        "101": "登录验证失效",
        "102": "服务器丢失了",
        "103": "登录验证失败",
        "104": "操作太频繁，请稍后重试",
        "105": "账号不存在",
        "106": "密码错误",
        "107": "登录已过期，请重新登录",
        "108": "验证码获取失败",
        "109": "手机不能为空",
        "110": "密码不能为空",
        "111": "验证码不能为空",
        "112": "验证码错误",
        "113": "账号已注册，无法重复注册",
        "114": "账号未注册",
        "115": "登录渠道异常，请联系客服",
        "116": "当日短信发送总量超限，请联系客服",
        "117": "短信余额不足，请联系客服",
        "118": "非法手机号，请填写正确手机号",
        "119": "手机短信发送频率超限,请稍后重试",
        "120": "获取苹果验证公钥失败",
        "121": "苹果登陆授权 idToken 已过期",
        "122": "非法的苹果登陆授权 idToken",
        "123": "苹果登陆授权 idToken 验证失败",
        "124": "该手机已绑定，请使用手机登录",
        "125": "请求获取信息失败",
        "126": "账号登录失败",
        "127": "登录信息验证失败",
        "128": "授权登录平台错误",
        "129": "邮箱不能为空",
        "130": "邮箱服务异常",
        "131": "邮箱登录类型不能为空",
        "132": "未设置登录密码",
        "133": "无法翻译语言",
        "134": "口令错误",
        "135": "推广渠道编号不能为空",
        "136": "账号不能为空",
        "137": "账号已存在",
        "138": "登录状态失效，请关闭游戏重新登录",
        "139": "登录验证失败",
        "140": "无法获取配置属性",
        "1001": "邮箱不能为空",
        "1002": "邮箱已被使用",
        "1003": "邮箱验证码不能为空",
        "1004": "邮箱验证码错误",
        "1005": "邮箱未被绑定，无法设置密码",
        "1006": "邮箱登入密码不能为空",
        "1007": "邮箱地址不正确",
        "1008": "facebook账号已被绑定",
        "1009": "google账号已被绑定",
        "1010": "apple账号已被绑定",
        "1011": "您已绑定apple账号",
        "1012": "您未绑定此登录方式",
        "1013": "请先解绑再绑定",
        "1014": "只有一种登入方式时无法解绑",
        "1015": "apple账号不能解绑",
        "1016": "请求微信获取用户信息出错",
        "1101": "实名认证不通过，请核对身份证号和姓名！",
        "1102": "验证失败：认证系统异常",
        "1103": "验证失败：内部错误",
        "1104": "账号不存在",
        "1105": "账号已实名认证",
        "1106": "该身份证已经被其他账号绑定",
        "1107": "当日错误验证达3次，需次日方可再次验证"
    },
    "drawCardInitRet": {
        "0": ""
    },
    "drawCardDrawRet": {
        "0": "",
        "1": "奖池不存在",
        "2": "道具不足",
        "3": "请先看广告",
        "4": "配置不存在",
        "5": "活动未开启",
        "6": "免费次数不足"
    },
    "dungeonBossInitRet": {
        "0": ""
    },
    "dungeonBossRewardRet": {
        "0": "",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "参数错误",
        "4": "关卡已过"
    },
    "dungeonBossSweetRewardRet": {
        "0": "",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "还未通关，不可扫荡",
        "4": "参数错误"
    },
    "dungeonBoxInitRet": {
        "0": ""
    },
    "dungeonBoxUpgradeRet": {
        "0": "",
        "1": "消耗的体力不足"
    },
    "dungeonBoxStartRet": {
        "0": "",
        "1": "参数错误",
        "2": "资源不足",
        "3": "副本已开始"
    },
    "dungeonBoxOpenRet": {
        "0": "",
        "1": "游戏未开始",
        "2": "该位置已翻开"
    },
    "dungeonCloudInitRet": {
        "0": ""
    },
    "dungeonCloudRewardRet": {
        "0": "",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "参数错误",
        "4": "关卡已过"
    },
    "dungeonCloudSweetRewardRet": {
        "0": "",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "还未通关，不可扫荡",
        "4": "参数错误"
    },
    "dungeonThiefInitRet": {
        "0": ""
    },
    "dungeonThiefRewardRet": {
        "0": "",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "参数错误",
        "4": "战斗异常"
    },
    "dungeonThiefSweetRewardRet": {
        "0": "",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "参数错误",
        "4": "战斗异常"
    },
    "dungeonTowerInitRet": {
        "0": ""
    },
    "dungeonTowerRewardRet": {
        "0": "",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "参数错误",
        "4": "战斗异常"
    },
    "dungeonTowerFailRet": {
        "0": "",
        "1": "资源不足",
        "2": "配置不存在"
    },
    "dungeonTowerBoxRewardRet": {
        "0": "",
        "1": "没有奖励可领取"
    },
    "equipCreateRet": {
        "0": ""
    },
    "equipInitRet": {
        "0": ""
    },
    "equipReplaceRet": {
        "0": "",
        "1": "装备不存在",
        "2": "装备已穿戴"
    },
    "equipDecomposeRet": {
        "0": "",
        "1": "装备不存在",
        "2": "穿戴的装备不能分解"
    },
    "equipWatchAdGotFatigueRet": {
        "0": "",
        "1": "请先看广告",
        "2": "配置不存在"
    },
    "equipUsePropGotFatigueRet": {
        "0": "",
        "1": "道具不足",
        "2": "配置不存在"
    },
    "equipSuitActivateRet": {
        "0": "",
        "1": "不满足升阶条件",
        "2": "配置不存在"
    },
    "equipStrengthenRet": {
        "0": "",
        "1": "道具不足",
        "2": "强化已达上限",
        "3": "配置不存在"
    },
    "equipBatchStrengthenRet": {
        "0": ""
    },
    "equipMasterActivateRet": {
        "0": "",
        "1": "不满足激活条件",
        "2": "配置不存在"
    },
    "equipCreateNoticeRet": {
        "0": ""
    },
    "equipFatigueNoticeRet": {
        "0": ""
    },
    "equipDungeonInitRet": {
        "0": ""
    },
    "equipDungeonCreateTeamRet": {
        "0": ""
    },
    "equipDungeonJoinTeamRet": {
        "0": "",
        "1": "队伍不存在",
        "2": "战斗已开始",
        "3": "队伍已满",
        "4": "配置不存在",
        "5": "副本还未解锁"
    },
    "equipDungeonJoinTeamNoticeRet": {
        "0": ""
    },
    "equipDungeonQuitTeamRet": {
        "0": "",
        "1": "没有加入队伍",
        "2": "队伍不存在",
        "3": "战斗已开始"
    },
    "equipDungeonQuitTeamNoticeRet": {
        "0": ""
    },
    "equipDungeonHurryRet": {
        "0": "",
        "1": "队伍不存在",
        "2": "操作太频繁",
        "3": "不在队伍里",
        "4": "战斗已开始"
    },
    "equipDungeonHurryNoticeRet": {
        "0": ""
    },
    "equipDungeonKickPeopleRet": {
        "0": "",
        "1": "队伍不存在",
        "2": "玩家已不在队伍",
        "3": "不能踢自己",
        "4": "战斗已开始",
        "5": "不是房主"
    },
    "equipDungeonKickPeopleNoticeRet": {
        "0": ""
    },
    "equipDungeonBattleStartRet": {
        "0": "",
        "1": "队伍不存在",
        "2": "不是房主",
        "3": "战斗已开始",
        "4": "配置不存在",
        "5": "副本还未解锁",
        "6": "协助成员道具不足",
        "7": "道具不足"
    },
    "equipDungeonBattleStartNoticeRet": {
        "0": ""
    },
    "equipDungeonBattleSyncRet": {
        "0": "",
        "1": "队伍不存在",
        "2": "战斗还未开始",
        "3": "战斗已结束",
        "4": "不在队伍",
        "5": "关卡信息错误"
    },
    "equipDungeonBattleEndRet": {
        "0": "",
        "1": "队伍不存在",
        "2": "战斗还未开始",
        "3": "战斗已结束",
        "4": "不在队伍"
    },
    "equipDungeonReceiveTicketRet": {
        "0": "",
        "1": "未到领取时间"
    },
    "equipDungeonSearchRet": {
        "0": ""
    },
    "equipDungeonBatchInviteRet": {
        "0": "",
        "2": "队伍不存在",
        "3": "战斗已开始",
        "4": "不是房主"
    },
    "equipDungeonInviteNoticeRet": {
        "0": ""
    },
    "equipDungeonShareChannelRet": {
        "0": "",
        "1": "副本还未解锁",
        "2": "队伍不存在",
        "3": "战斗已开始",
        "4": "不是房主",
        "5": "配置不存在"
    },
    "expeditionStartRet": {
        "0": "默认",
        "1": "道具不足",
        "2": "参数错误",
        "3": "上一次挑战未完成",
        "4": "活动未开启"
    },
    "expeditionEventCompleteRet": {
        "0": "默认",
        "2": "参数错误",
        "4": "活动未开启",
        "5": "事件不存在",
        "6": "事件已结束"
    },
    "forgeInitRet": {
        "0": ""
    },
    "forgeCultivateUpgradeRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "等级已达上限",
        "4": "传参错误"
    },
    "forgeDressSkinRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "皮肤未拥有"
    },
    "forgeUpgradeRet": {
        "0": "默认",
        "2": "不满足升阶条件",
        "3": "正在升阶中",
        "4": "等级已达上限"
    },
    "forgeWatchAdSpeedRet": {
        "0": "默认",
        "1": "请先看广告",
        "2": "升阶已完成"
    },
    "forgeUsePropSpeedRet": {
        "0": "默认",
        "1": "道具不足",
        "2": "升阶已完成"
    },
    "forgeCompleteRet": {
        "0": "默认",
        "1": "升阶时间还没到",
        "2": "升阶已完成"
    },
    "forgeCompleteShowRet": {
        "0": "默认",
        "2": "升阶未完成"
    },
    "forgeCreateSkinNoticeRet": {
        "0": ""
    },
    "forgeBeHelpedRet": {
        "0": "",
        "1": "升阶已完成，无需求助",
        "2": "已经求助过",
        "3": "未加入联盟"
    },
    "friendInitRet": {
        "0": "默认"
    },
    "friendGetRecommendsRet": {
        "0": "默认"
    },
    "friendFindRet": {
        "0": "默认",
        "1": "找不到玩家"
    },
    "friendApplyRet": {
        "0": "默认",
        "1": "好友数量已达上限",
        "2": "找不到玩家",
        "3": "已被对方拉入黑名单",
        "4": "已好友无需申请",
        "5": "已被拉入黑名单",
        "6": "不可添加自己为好友",
        "7": "跨服好友暂未开启",
        "8": "暂不可添加跨区好友"
    },
    "friendDealWithApplicationRet": {
        "0": "默认",
        "1": "不是待处理状态",
        "2": "好友数量已达上限",
        "3": "对方好友数量已达上限"
    },
    "friendBatchDealWithApplicationRet": {
        "0": "默认",
        "1": "暂无好友申请可处理",
        "2": "好友位不足，请手动处理",
        "3": "对方好友数量已达上限"
    },
    "friendSetBlacklistRet": {
        "0": "默认",
        "1": "设置失败",
        "2": "找不到玩家",
        "3": "默认好友不可拉黑"
    },
    "friendSetAnonChatRet": {
        "0": "默认"
    },
    "friendThumbsUpRet": {
        "0": "默认",
        "1": "非好友无法赠礼",
        "2": "找不到玩家",
        "3": "已赠礼",
        "4": "赠礼已达上限"
    },
    "friendTakeThumbsUpAwardRet": {
        "0": "默认",
        "1": "好友还未给你送礼",
        "2": "找不到玩家",
        "3": "奖励已领取",
        "4": "收礼次数已达上限"
    },
    "friendBatchTakeAwardAndThumbsUpAllRet": {
        "0": "默认",
        "1": "已全部领取和赠送",
        "2": "赠礼次数已达上限",
        "3": "收礼次数已达上限"
    },
    "friendDeleteRet": {
        "0": "默认",
        "1": "找不到玩家",
        "2": "非好友无法删除",
        "3": "默认好友不可删除"
    },
    "friendApplyNoticeRet": {
        "0": "默认"
    },
    "friendThumbsUpNoticeRet": {
        "0": "默认"
    },
    "friendDeleteNoticeRet": {
        "0": "默认"
    },
    "gameLogGetRet": {
        "0": "成功"
    },
    "gameSwitchInitRet": {
        "0": "成功"
    },
    "gameSwitchGetRet": {
        "0": "成功",
        "1": "等级不足",
        "2": "开服时间未到",
        "3": "重复领取"
    },
    "guideInitRet": {
        "0": ""
    },
    "guideAddRet": {
        "0": "",
        "1": "配置不存在",
        "2": "奖励已领取"
    },
    "idleEarningsInitRet": {
        "0": ""
    },
    "idleEarningsTakeRet": {
        "0": "",
        "1": "没有挂机奖励"
    },
    "idleEarningsTakeHoursRet": {
        "0": "",
        "1": "请先观看广告",
        "2": "领取次数不足",
        "3": "道具不足"
    },
    "idleEarningsGetRewardRet": {
        "0": ""
    },
    "leadSkinInitRet": {
        "0": ""
    },
    "leadSkinDressRet": {
        "0": "默认",
        "1": "未拥有圣装"
    },
    "leadSkinMagicalRet": {
        "0": "默认",
        "1": "幻化的形象不存在"
    },
    "leadSkinUpgradeRet": {
        "0": "默认",
        "1": "道具不足",
        "2": "未拥有圣装",
        "3": "等级已达上限",
        "4": "参数错误"
    },
    "leadSkinResetRet": {
        "0": "默认",
        "1": "未拥有圣装"
    },
    "leadSkinIncrStarRet": {
        "0": "默认",
        "1": "资源不足",
        "3": "星级已达上限",
        "4": "未拥有圣装"
    },
    "leadSkinActivateRewardRet": {
        "0": "默认",
        "1": "奖励已领取",
        "2": "未拥有圣装"
    },
    "leadSkinCreateRet": {
        "0": "默认"
    },
    "leadSkinSelectRet": {
        "0": "默认",
        "1": "参数错误",
        "2": "已选角"
    },
    "magicalInitRet": {
        "0": ""
    },
    "magicalBattleRet": {
        "0": "",
        "1": "魔法未拥有",
        "2": "位置错误"
    },
    "magicalBatchBattleRet": {
        "0": "",
        "1": "魔法未拥有",
        "2": "位置错误"
    },
    "magicalUpgradeRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "魔法未拥有",
        "3": "等级已达上限",
        "4": "参数错误",
        "5": "星级不足"
    },
    "magicalResetLevelRet": {
        "0": "默认",
        "1": "魔法未拥有"
    },
    "magicalIncrStarRet": {
        "0": "默认",
        "1": "资源不足",
        "3": "魔法未拥有",
        "4": "星级已达上限"
    },
    "magicalBatchIncrStarRet": {
        "0": "默认"
    },
    "magicalAnalyzeRet": {
        "0": "默认",
        "1": "魔法未拥有"
    },
    "magicalCreateRet": {
        "0": "默认"
    },
    "mailInitRet": {
        "0": "默认"
    },
    "mailTakeRet": {
        "0": "默认",
        "1": "邮件不存在",
        "2": "邮件已过期",
        "3": "邮件已领取",
        "4": "没有附件可领取"
    },
    "mailBatchTakeRet": {
        "0": "默认",
        "1": "没有附件可领取"
    },
    "mailReadRet": {
        "0": "默认",
        "1": "邮件不存在",
        "2": "邮件已过期"
    },
    "mailRemoveRet": {
        "0": "默认",
        "1": "邮件不存在",
        "2": "邮件已过期",
        "3": "附件还未领取"
    },
    "mailBatchRemoveRet": {
        "0": "默认"
    },
    "mailNotifyRet": {
        "0": "默认"
    },
    "mapBarrierInitRet": {
        "0": ""
    },
    "mapBarrierSyncRet": {
        "0": "默认",
        "1": "关卡已达上限",
        "2": "过关条件不满足",
        "3": "战斗异常"
    },
    "mapBarrierMonsterDropRet": {
        "0": "默认"
    },
    "mapBarrierPropDropRet": {
        "0": "默认",
        "1": "配置不存在"
    },
    "miniInitRet": {
        "0": "默认"
    },
    "miniUpdateMomentsInfoRet": {
        "0": "默认",
        "1": "授权已过期，请重新授权登录"
    },
    "miniTakeAwardRet": {
        "0": "默认",
        "1": "奖励不存在",
        "2": "奖励已领取"
    },
    "miningInitRet": {
        "0": "默认"
    },
    "miningStartRet": {
        "0": "默认",
        "1": "道具不足",
        "2": "操作失败"
    },
    "miningTakeAwardRet": {
        "0": "默认",
        "1": "奖励不存在或者已领取"
    },
    "netPong": {
        "0": "默认"
    },
    "petInitRet": {
        "0": ""
    },
    "petBattleRet": {
        "0": "",
        "1": "宠物不存在"
    },
    "petMagicalRet": {
        "0": "",
        "1": "宠物不存在"
    },
    "petUpgradeRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "宠物不存在",
        "3": "等级已达上限",
        "4": "参数错误",
        "5": "星级不足"
    },
    "petResetLevelRet": {
        "0": "默认",
        "1": "宠物不存在"
    },
    "petSynthesisRet": {
        "0": "默认",
        "1": "宠物不存在",
        "2": "品质不同不可合成",
        "3": "最高品质的宠物不可合成",
        "4": "上阵宠物不可合成"
    },
    "petIncrStarRet": {
        "0": "默认",
        "1": "资源不足",
        "3": "宠物不存在",
        "4": "等级已达上限",
        "5": "宠物品质不足",
        "6": "不可吞噬等级高于自己的",
        "7": "上阵宠物不可被吞噬",
        "8": "不能吞噬自己",
        "9": "请先选择词条"
    },
    "petIncrStarUsePropRet": {
        "0": "默认",
        "1": "资源不足",
        "3": "宠物不存在",
        "4": "星级已达上限",
        "5": "宠物品质不足",
        "6": "请先选择词条"
    },
    "petRefineRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "宠物不存在",
        "3": "星级满后才可洗炼",
        "4": "位置不存在",
        "5": "请先选择词条"
    },
    "petSelectEntryRet": {
        "0": "默认",
        "1": "宠物不存在",
        "2": "词条不存在",
        "3": "该位置无词条可选择",
        "4": "请选择词条"
    },
    "petFreeReceiveEggRet": {
        "0": "默认",
        "1": "领取时间还未到"
    },
    "petOpenEggRet": {
        "0": "",
        "1": "道具不足",
        "2": "配置不存在",
        "3": "宠物已达上限，请先腾出空间"
    },
    "petCreateRet": {
        "0": "默认"
    },
    "playerInitRet": {
        "0": "默认",
        "1": "当前服务器已爆满!",
        "2": "服务器还未准备好，请稍后重试~",
        "3": "服务器维护中",
        "4": "链接参数异常",
        "5": "登录过期，请重新登录",
        "6": "登录校验错误，请重新登录",
        "7": "发现版本更新，点击确定重新启动游戏进行更新",
        "8": "账号已被限制登录",
        "9": "账号已有登录状态，请稍后重试",
        "10": "发现新版本，请跳转应用商店更新"
    },
    "playerLoginOutRet": {
        "0": "有其他设备登录",
        "1": "连接异常，请联系客服",
        "2": "服务器临时维护",
        "3": "服务器例行维护",
        "4": "账号维护,请稍后重试~",
        "5": "尊敬的玩家您好，根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》、《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》要求，未成年人用户仅可在周五、周六、周日和法定节假日的20时至21时体验游戏。"
    },
    "playerReconnectRet": {
        "0": "有其他设备登录"
    },
    "playerUpgradeRet": {
        "0": ""
    },
    "playerInfoReplaceRet": {
        "0": "",
        "1": "道具不存在",
        "2": "道具未解锁",
        "3": "道具已过期"
    },
    "playerInfoQueryRet": {
        "0": "",
        "1": "角色ID不存在"
    },
    "playerRenameRet": {
        "0": "默认",
        "1": "道具不足",
        "2": "名称不符合规范",
        "3": "昵称已存在"
    },
    "playerBindRewardRet": {
        "0": "默认",
        "1": "请先绑定",
        "2": "凭证已过期"
    },
    "plotInitRet": {
        "0": ""
    },
    "plotGetRet": {
        "0": "",
        "1": "停车场不存在",
        "2": "只能访问别人的停车场"
    },
    "plotGetLogRet": {
        "0": ""
    },
    "plotSearchRet": {
        "0": ""
    },
    "plotModifyNameRet": {
        "0": "",
        "1": "命名不规范",
        "2": "一天只有一次改名机会哦~"
    },
    "plotCollectRet": {
        "0": "",
        "1": "停车场不存在",
        "2": "收藏数量已达上限",
        "3": "不能收藏自己的停车场"
    },
    "plotModifyManageRet": {
        "0": "",
        "1": "参数错误",
        "2": "管理费设置不合理"
    },
    "plotModifyShowRet": {
        "0": "",
        "1": "未拥有战车"
    },
    "plotModifyDressRet": {
        "0": "",
        "1": "未拥有装扮"
    },
    "plotUpgradeDressRet": {
        "0": "",
        "1": "未拥有装扮",
        "2": "等级已达上限",
        "3": "资源不足",
        "4": "配置不存在"
    },
    "plotUnlockDressRet": {
        "0": "",
        "1": "装扮已解锁",
        "2": "资源不足"
    },
    "plotStopRet": {
        "0": "",
        "1": "战车未拥有",
        "2": "车位已满",
        "3": "该车位已停放车子",
        "4": "留点位置给其他玩家吧~",
        "5": "车位不存在",
        "6": "战车已停放",
        "7": "该车位战斗还未结束，无法停车"
    },
    "plotStopOtherRet": {
        "0": "",
        "1": "停车场不存在",
        "2": "战车未拥有",
        "3": "车位已满",
        "4": "该车位已停放车子",
        "5": "不能同时停在多个车位",
        "6": "未交管理费车位车主无法帮忙驻守",
        "7": "车位不存在",
        "8": "不符合停车场主人设置的管理条件",
        "9": "战车已停放",
        "10": "该车位战斗还未结束，无法停车"
    },
    "plotReceiveRet": {
        "0": "",
        "1": "车位不存在",
        "2": "当前车位正在战斗中，无法收车",
        "3": "车辆已被收回",
        "4": "玩家未交管理费，不能帮忙收车",
        "5": "未到收车时间"
    },
    "plotReceiveOtherRet": {
        "0": "",
        "1": "停车场不存在",
        "2": "车位不存在",
        "3": "车辆已被收回",
        "5": "当前车位正在战斗中，无法收车",
        "6": "没有收车权限"
    },
    "plotAutoReceiveRet": {
        "0": "",
        "1": "车位不存在",
        "2": "当前车位正在战斗中，无法自动收车",
        "3": "车辆已被收回",
        "4": "玩家未交管理费，不能帮忙收车",
        "5": "未到自动收车时间"
    },
    "plotAutoReceiveOtherRet": {
        "0": "",
        "1": "停车场不存在",
        "2": "车位不存在",
        "3": "车辆已被收回",
        "5": "当前车位正在战斗中，无法自动收车",
        "6": "没有自动收车权限",
        "7": "未到自动收车时间"
    },
    "plotStartSnatchRet": {
        "0": "",
        "1": "停车场不存在",
        "2": "车位不存在",
        "3": "当前车位正在战斗中，无法抢夺",
        "4": "车辆已被收回",
        "5": "无法抢夺自己的车位",
        "6": "玩家已交管理费无法抢夺",
        "7": "停车保护中，不可抢夺",
        "8": "抢夺次数不足",
        "9": "无法抢夺停车场主人车位"
    },
    "plotEndSnatchRet": {
        "0": "",
        "1": "停车场不存在",
        "2": "车位不存在",
        "3": "抢夺已过期",
        "4": "战斗已结束",
        "5": "车辆已被收回",
        "6": "战斗异常"
    },
    "plotSnatchShareRet": {
        "0": "",
        "1": "停车场不存在",
        "2": "车位不存在",
        "3": "未加入公会",
        "4": "车辆已被收回"
    },
    "plotStopOtherNoticeRet": {
        "0": ""
    },
    "plotReceiveNoticeRet": {
        "0": ""
    },
    "plotIncrExpNoticeRet": {
        "0": ""
    },
    "plotReceiveOtherNoticeRet": {
        "0": ""
    },
    "plotStartSnatchNoticeRet": {
        "0": ""
    },
    "plotRefreshOrderRet": {
        "0": "",
        "1": "请先看广告",
        "2": "资源不足"
    },
    "plotBatchAcceptOrderRet": {
        "0": "",
        "1": "订单不存在",
        "2": "今日接取的订单已达上限",
        "3": "战车不存在",
        "4": "参数错误",
        "5": "战车已接取订单"
    },
    "plotBatchSpeedUpOrderRet": {
        "0": "",
        "1": "订单不存在",
        "2": "资源不足",
        "3": "参数错误",
        "4": "请先接受订单"
    },
    "plotBatchReceiveOrderRet": {
        "0": "",
        "1": "订单不存在",
        "2": "订单收货时间还未到",
        "4": "参数错误",
        "5": "请先接受订单"
    },
    "popUpsInitRet": {
        "0": "默认"
    },
    "popUpsPushNoticeRet": {
        "0": "默认"
    },
    "powerInitRet": {
        "0": ""
    },
    "powerUpgradeRet": {
        "0": "",
        "1": "传参错误",
        "2": "道具不足"
    },
    "powerBreakOutRet": {
        "0": "",
        "1": "等级不足，无法突破",
        "2": "道具不足"
    },
    "powerPromotionRet": {
        "0": "",
        "1": "不满足晋升条件"
    },
    "powerPeakGetRet": {
        "0": ""
    },
    "powerPeakRewardRet": {
        "0": "",
        "1": "奖励已领取",
        "2": "不满足领取条件",
        "3": "配置不存在"
    },
    "powerPeakWorshipRewardRet": {
        "0": "",
        "1": "奖励已领取"
    },
    "powerPeakNotify": {
        "0": ""
    },
    "privilegeInitRet": {
        "0": "默认"
    },
    "privilegeBatchReceiveDayRwdRet": {
        "0": "默认"
    },
    "privilegeUpdateNoticeRet": {
        "0": "默认"
    },
    "progressInitRet": {
        "0": "默认"
    },
    "progressUpdateRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "参数错误",
        "3": "不在活动时间内"
    },
    "progressReceiveRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "参数错误",
        "3": "不在活动时间内",
        "4": "进度值未达到",
        "5": "奖励已领取",
        "6": "未找到奖励"
    },
    "randomShopInitRet": {
        "0": "默认"
    },
    "randomShopRefreshRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "免费刷新次数不足",
        "3": "刷新次数不足",
        "4": "道具不足",
        "5": "请先看广告"
    },
    "randomShopBatchBuyRet": {
        "0": "默认",
        "1": "道具不足",
        "2": "商品不存在",
        "3": "商品数量不足",
        "4": "配置不存在"
    },
    "rankGetRet": {
        "0": "默认"
    },
    "rankUnionGetRet": {
        "0": "默认"
    },
    "rankReceiveRet": {
        "0": "默认",
        "1": "活动未开启",
        "2": "活动未结束",
        "3": "不满足领奖名次",
        "4": "已领取"
    },
    "rechargeGetInfoRet": {
        "0": "默认"
    },
    "rechargeNoticeRet": {
        "0": "默认"
    },
    "rechargePrePayRet": {
        "0": "默认",
        "1": "请求太频繁",
        "2": "配置不存在",
        "3": "充值已达上限",
        "4": "商品不售卖",
        "5": "商品无法购买",
        "6": "消耗道具不足",
        "7": "活动未开启",
        "8": "活动已结束",
        "9": "请自选礼包道具",
        "10": "支付暂未开放",
        "11": "未成年人充值"
    },
    "rechargeVerifyRet": {
        "0": "默认",
        "1": "订单不存在",
        "2": "奖励已发放",
        "3": "校验失败"
    },
    "rechargeGetPackInfoRet": {
        "0": "默认"
    },
    "rechargeReceivePackRet": {
        "0": "默认",
        "1": "没有可领取礼包",
        "2": "配置不存在",
        "4": "未购买",
        "5": "活动未开启"
    },
    "rechargeGiftReceivePackRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "未购买指定礼包",
        "3": "未达到指定领取天数",
        "4": "礼包奖励已领取"
    },
    "rechargeReceiveTotalRewardRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "不满足领取条件",
        "3": "奖励已领取",
        "4": "参数错误"
    },
    "redPacketInitRet": {
        "0": "默认"
    },
    "redPacketSendRet": {
        "0": "默认",
        "1": "红包不存在",
        "2": "配置不存在",
        "3": "红包数量有误"
    },
    "redPacketSendNoticeRet": {
        "0": "默认"
    },
    "redPacketReceiveRet": {
        "0": "默认",
        "1": "红包不存在",
        "2": "手慢了，红包已被领取完毕",
        "3": "红包已过期",
        "4": "红包已领取",
        "5": "今日领取红包次数已达上限"
    },
    "redPacketLookRet": {
        "0": "默认",
        "1": "红包不存在"
    },
    "redPacketCreateNoticeRet": {
        "0": "默认"
    },
    "reportUploadRet": {
        "0": ""
    },
    "selectPackGetRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "活动未开启",
        "3": "活动已结束"
    },
    "selectPackRecordRet": {
        "0": "默认",
        "1": "配置不存在",
        "2": "活动未开启",
        "3": "活动已结束",
        "4": "自选道具异常"
    },
    "settingInitRet": {
        "0": ""
    },
    "settingSetRet": {
        "0": ""
    },
    "shopGetRet": {
        "0": ""
    },
    "shopPurchaseRet": {
        "0": "",
        "1": "购买所需资源不足",
        "2": "已达购买上限",
        "3": "未达解锁要求",
        "4": "商品不存在",
        "5": "活动未开启",
        "6": "奖励领取次数不足",
        "7": "活动已结束",
        "8": "商品数量错误",
        "9": "广告不存在"
    },
    "talentTreeInitRet": {
        "0": "默认"
    },
    "talentTreeUpgradeRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "等级已达上限",
        "4": "请先完成前置研究",
        "5": "上一次研究还未完成",
        "6": "找不到研究点"
    },
    "talentTreeCompleteRet": {
        "0": "默认",
        "2": "研究时间还未到",
        "3": "研究已完成"
    },
    "talentTreeAdQuickenRet": {
        "0": "",
        "1": "配置不存在",
        "2": "研究已完成",
        "3": "请先看广告"
    },
    "talentTreePropQuickenRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "研究已完成"
    },
    "talentTreeBeHelpedRet": {
        "0": "",
        "1": "研究已完成，无需求助",
        "2": "已经求助过",
        "3": "未加入联盟"
    },
    "tankInitRet": {
        "0": ""
    },
    "tankUpgradeLevelRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "等级已达上限"
    },
    "tankUpgradeStarRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "等级已达上限",
        "4": "战车不存在",
        "5": "普通战车不可升星"
    },
    "tankBattleRet": {
        "0": "默认",
        "1": "战车不存在"
    },
    "tankMagicalRet": {
        "0": "默认",
        "1": "幻化的形象不存在"
    },
    "taskInitRet": {
        "0": ""
    },
    "taskStatisticsRet": {
        "0": ""
    },
    "taskUpdateRet": {
        "0": ""
    },
    "taskTakeAwardRet": {
        "0": "",
        "1": "任务线不存在",
        "2": "任务还未完成",
        "3": "奖励已领取",
        "4": "礼包未解锁",
        "5": "活动未开启"
    },
    "taskBatchTakeAwardRet": {
        "0": "",
        "1": "任务线不存在",
        "2": "任务还未完成",
        "3": "奖励已领取",
        "4": "礼包未解锁",
        "5": "活动未开启"
    },
    "taskActivityBigRewardRet": {
        "0": "",
        "1": "活动不存在",
        "2": "任务线不存在",
        "3": "任务还未完成",
        "4": "奖励已领取",
        "5": "活动未开启",
        "6": "无奖励可领取"
    },
    "unionInitRet": {
        "0": "",
        "1": "未加入公会"
    },
    "unionCurrGetRet": {
        "0": "",
        "1": "未加入公会"
    },
    "unionModuleGetRet": {
        "0": ""
    },
    "unionGetRet": {
        "0": "",
        "1": "公会不存在"
    },
    "unionCreateRet": {
        "0": "",
        "1": "道具不足",
        "2": "名称重复",
        "3": "公会名称不合规",
        "4": "公告内容长度过长",
        "5": "宣言内容长度过长",
        "6": "宣言内容不合规"
    },
    "unionSearchRet": {
        "0": ""
    },
    "unionGetApplyListRet": {
        "0": "",
        "1": "权限不足"
    },
    "unionChangeNameRet": {
        "0": "",
        "1": "名称重复",
        "2": "字数超过上限",
        "3": "权限不足",
        "4": "道具不足",
        "5": "公会名称不合规"
    },
    "unionSettingRet": {
        "0": "",
        "1": "权限不足",
        "2": "内容不合规",
        "4": "宣言内容长度过长",
        "5": "宣言内容不合规"
    },
    "unionApplySettingRet": {
        "0": "",
        "1": "条件设置不符合要求",
        "2": "权限不足"
    },
    "unionApplySubmitRet": {
        "0": "成功",
        "1": "角色已经加入公会",
        "2": "公会人数已满",
        "3": "没有满足随机加入的公会",
        "4": "请勿重复申请",
        "5": "每日申请人数达到上限",
        "6": "等级不满足公会要求",
        "7": "申请加入异常",
        "8": "参数不合法",
        "9": "公会已解散",
        "10": "不能加入其他服的公会",
        "11": "申请冷却中"
    },
    "unionReviewRet": {
        "0": "成功",
        "1": "角色已经加入公会",
        "2": "今日允许加入数量超出上限",
        "3": "公会人数已满",
        "4": "权限不足",
        "5": "角色已经加入其他公会",
        "6": "类型不存在",
        "7": "传参错误",
        "8": "不在同一个公会，无法操作"
    },
    "unionKickOutRet": {
        "0": "成功",
        "1": "不能逐出自己",
        "2": "权限不足",
        "3": "不是同一个公会",
        "4": "玩家已退出公会",
        "5": "盟主不能被逐出"
    },
    "unionOwnerChangeRet": {
        "0": "成功",
        "1": "不能转给自己",
        "2": "权限不足",
        "3": "不是同一个公会",
        "4": "该成员近期未上线，无法任命",
        "5": "传参错误"
    },
    "unionPositionSettingRet": {
        "0": "成功",
        "1": "不能给自己",
        "2": "权限不足",
        "3": "职位人数已达上限",
        "4": "不是同一个公会",
        "5": "传参错误"
    },
    "unionDismissRet": {
        "0": "成功",
        "1": "权限不足",
        "2": "公会正在攻防战中，暂时不可解散"
    },
    "unionLeaveRet": {
        "0": "成功",
        "1": "盟主无法离开"
    },
    "unionActiveRewardGetRet": {
        "0": "成功",
        "1": "未加入公会"
    },
    "unionActiveRewardRet": {
        "0": "",
        "1": "重复领取",
        "2": "未找到对应奖励",
        "3": "未加入公会",
        "4": "活跃度未达到不可领取"
    },
    "unionBossGetRet": {
        "0": "成功",
        "1": "未开启"
    },
    "unionBossAttackRet": {
        "0": "成功",
        "1": "未开启",
        "2": "未加入公会",
        "3": "攻击次数不足",
        "4": "攻击人数过多，请稍后重试",
        "5": "战斗异常"
    },
    "unionBossRewardRet": {
        "0": "成功",
        "1": "没有可以领取的奖励",
        "2": "未加入公会",
        "3": "参数错误"
    },
    "unionBossRankRet": {
        "0": "成功"
    },
    "unionTreasureShopGetRet": {
        "0": "成功",
        "1": "重复砍价"
    },
    "unionTreasureShopBargainRet": {
        "0": "成功",
        "1": "重复砍价",
        "2": "当前砍价人数过多，请稍后重试",
        "3": "不在所属公会"
    },
    "unionTreasureShopPurchaseRet": {
        "0": "成功",
        "1": "资源不足",
        "2": "重复购买",
        "3": "未加入公会"
    },
    "unionDonateGetRet": {
        "0": "成功"
    },
    "unionDonateRewardRet": {
        "0": "",
        "1": "未加入公会",
        "2": "次数不足",
        "3": "道具不足"
    },
    "unionHelpGetRet": {
        "0": "成功"
    },
    "unionHelpAllRet": {
        "0": "成功",
        "1": "未加入公会"
    },
    "unionHelpHelpNoticeRet": {
        "0": "成功"
    },
    "unionHelpAskForNoticeRet": {
        "0": "成功"
    },
    "unionHelpDelNoticeRet": {
        "0": "成功"
    },
    "unionSiegeGetRet": {
        "0": "成功"
    },
    "unionSiegeStartChallengeRet": {
        "0": "",
        "1": "请选择对手公会的玩家进行挑战",
        "2": "当前玩家正在战斗中，无法挑战",
        "3": "玩家已被挑战",
        "4": "挑战次数不足",
        "5": "不在挑战时间内",
        "6": "未加入公会",
        "7": "操作太频繁",
        "8": "参数错误",
        "9": "因上场战斗异常退出，需休息一会才可重新挑战",
        "10": "挑战成员不是防守成员",
        "11": "不是参战人员"
    },
    "unionSiegeStartChallengeNoticeRet": {
        "0": ""
    },
    "unionSiegeEndChallengeRet": {
        "0": "",
        "1": "挑战信息错误",
        "2": "挑战已过期",
        "3": "不在挑战时间内",
        "4": "参数错误",
        "5": "操作太频繁",
        "6": "未加入公会",
        "7": "挑战未开始",
        "9": "战斗异常"
    },
    "unionSiegeEndChallengeNoticeRet": {
        "0": ""
    },
    "unionSiegeSweetChallengeRet": {
        "0": "",
        "1": "请选择对手公会的玩家进行扫荡",
        "3": "不满足扫荡条件",
        "4": "挑战次数不足",
        "5": "参数错误",
        "6": "不在挑战时间内",
        "7": "操作太频繁",
        "8": "未加入公会",
        "9": "不是参战人员",
        "10": "上一次战斗还未结束",
        "11": "挑战成员不是防守成员"
    },
    "unionSiegeGetLogRet": {
        "0": "成功",
        "1": "未加入公会",
        "2": "操作太频繁"
    },
    "unionSiegeGetMyLogRet": {
        "0": "成功",
        "1": "未加入公会",
        "2": "操作太频繁",
        "3": "未参战"
    },
    "unionSiegeRankRet": {
        "0": "",
        "1": "未加入公会",
        "2": "操作太频繁",
        "3": "不在活动期间"
    },
    "unionSiegeRewardRet": {
        "0": "",
        "1": "未加入公会",
        "2": "未参赛",
        "3": "奖励已领取",
        "4": "不在领奖期",
        "5": "操作太频繁"
    },
    "unionSiegeBoxRewardRet": {
        "0": "",
        "1": "未加入公会",
        "2": "未参赛",
        "3": "奖励已领取",
        "4": "不在领奖期",
        "5": "请先领取结算奖励",
        "6": "操作太频繁",
        "7": "轮空公会没有宝箱奖励"
    },
    "unionKickOutBroadcastRet": {
        "0": "成功"
    },
    "unionActiveRewardBroadcastRet": {
        "0": "成功"
    },
    "unionBossProgressBroadcastRet": {
        "0": "成功"
    },
    "unionTreasureShopBargainBroadcastRet": {
        "0": "成功"
    },
    "weaponInitRet": {
        "0": ""
    },
    "weaponUpgradeLevelRet": {
        "0": "默认",
        "1": "资源不足",
        "3": "等级已达上限"
    },
    "weaponUpgradeStarRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "配置不存在",
        "3": "等级已达上限",
        "4": "神器不存在",
        "5": "普通神器不可升星"
    },
    "weaponBattleRet": {
        "0": "默认",
        "1": "神器不存在"
    },
    "weaponMagicalRet": {
        "0": "默认",
        "1": "幻化的形象不存在"
    },
    "weaponShowRet": {
        "0": "默认"
    },
    "welfareInitRet": {
        "0": "默认"
    },
    "welfareReceiveRwdRet": {
        "0": "默认",
        "1": "已领取",
        "2": "配合不存在"
    },
    "wingsInitRet": {
        "0": ""
    },
    "wingsBatchUpgradeLevelRet": {
        "0": "默认",
        "1": "羽毛不存在",
        "2": "配置不存在",
        "3": "等级已达上限",
        "4": "资源不足",
        "5": "参数错误"
    },
    "wingsUpgradeStarRet": {
        "0": "默认",
        "1": "资源不足",
        "2": "星级已达上限",
        "3": "背饰不存在",
        "4": "普通背饰不可升星"
    },
    "wingsLineupRet": {
        "0": "默认",
        "1": "背饰不存在"
    },
    "wingsMagicalRet": {
        "0": "默认",
        "1": "背饰不存在"
    },
    "wingsShowRet": {
        "0": "默认"
    },
    "wingsEnchantUpgradeRet": {
        "0": "默认",
        "1": "配置不存在",
        "3": "道具不足",
        "4": "等级已达上限"
    }
}