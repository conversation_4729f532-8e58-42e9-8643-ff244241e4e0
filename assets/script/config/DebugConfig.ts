/*
 * @Author: <PERSON>y<PERSON><PERSON>
 * @Date: 2023-11-09 19:27:30
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-23 10:20:06
 */

import Channel from "../../nsn/config/Channel";
import Review from "../../nsn/config/Review";
import Server from "../../nsn/config/Server";
import Version from "../../nsn/config/Version";
import Whitelist from "../../nsn/config/Whitelist";
import Language from "../../nsn/core/Language";
import Loader from "../../nsn/core/Loader";
import LocalStorage from "../../nsn/core/LocalStorage";
import Socket from "../../nsn/core/Socket";
import Data from "../../nsn/data/Data";
import EtadpuConfig from "../../nsn/etadpu/EtadpuConfig";
import Platform from "../../nsn/platform/Platform";
import Profiler from "../../nsn/recycle/Profiler";
import Recycle from "../../nsn/recycle/Recycle";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import Tips from "../../nsn/util/Tips";
import { BagUpdateRet, IItemInfo, IShowItemInfo, ItemInfo, NetworkResult } from "../../protobuf/proto";
import { GameServer } from "../core/GameServer";
import TBItem from "../data/parser/TBItem";
import { TB_MAP } from "../data/parser/TBMap";
import { RECHARGE_ID } from "../data/parser/TBRecharge";
import Bag from "../game/Bag";
import { DungeonType } from "../game/Combat";
import CombatSetting, { CombatSettingId } from "../game/CombatSetting";
import Equip from "../game/Equip";
import GameClub from "../game/GameClub";
import GameSwitch from "../game/GameSwitch";
import MakeArrow from "../game/MakeArrow";
import Player, { PLAYER_INIT_TYPE } from "../game/Player";
import Recharge from "../game/Recharge";
import AdSdk from "../sdk/ad/AdSdk";
import { LoginType } from "./channel/base/ChannelType";
import Nsn from "./channel/channel/Nsn";
import WechatMinigame from "./channel/channel/WechatMinigame";
import { AndroidCallMethod } from "./platform/PlatformCallMethod";

const PROFILER_DEBUG_NODE_PREFAB_URI = "prefab/debug/PrefabProfiler";

/**
 * 页签
 */
export enum DebugUITab {
    Common,
    UI,
    Guide,
    Data,
    Order,
    Others,
    Code,
}

export const DEBUG_UI_TAB_NAME = ["通用", "界面", "引导", "配置", "订单", "其他", "调试"];

/**
 * 调试功能
 */
export enum DebugFunctionType {
    SystemInfo,
    OffLine,
    GuestLogin,
    AccountLogin,
    EtadpuNode1,
    EtadpuNode2,
    EtadpuNode3,
    Recycle,
    GC,
    Profiler,
    HideFps,
    RemoteDataNames,
    TestBattle,
    SyncPlayerInfo,
    QueryMissOrder,
    GotoAppStore,
    GetSignHash,
    ShowWhitelistServer,
    ShowAd,
    FakeBag,
    Mail,
    GetItem,
    GetLocalStorageData,
    ShowWv,
    GetEquip,
    CombatLog,
    HideEquipInfo,
    GetServerAttrData,
    TestPay,
    AnalysisData,
    OpenGameClub,
    GetGameClubData,
    Evaluate,
    GameSwitch,
    Share,
    DungeonTest,
}

/**
 * 调试界面使用场景
 */
export enum DebugSceneType {
    Login,
    Game,
    All,
}

/**
 * 通用调试功能配置
 */
export interface ICommonDebugFunction {
    name: string;
    scene?: DebugSceneType;
    id: DebugFunctionType;
    execute: (btn: cc.Node) => void;
    text?: () => string;
}

/**
 * 属性调试功能配置
 */
export interface IAttributeDebugFunction {
    name: string;
    id: DebugFunctionType;
    execute: () => void;
}

/**
 * 其他调试功能配置
 */
export interface IOthersDebugFunction {
    name: string;
    id: DebugFunctionType;
    args: string[];
    execute: (text1: string, text2: string, text3: string) => boolean;
}

export const COMMON_DEBUG_FUNCTION: ICommonDebugFunction[] = [
    {
        name: "系统信息",
        scene: DebugSceneType.All,
        id: DebugFunctionType.SystemInfo,
        execute: () => {
            let msg = "";
            msg += `应用版本：${Platform.getInstance().getAppVersion()}\n`;
            msg += `游戏版本：${Version.getInstance().getGameVersion()}\n`;
            msg += `配置版本：${Version.getInstance().getDataVersion()}\n`;
            msg += `白名单：${Whitelist.getInstance().getWhitelist() ? "是" : "否"}\n`;
            msg += `审核服：${Review.getInstance().getPassReview() ? "否" : "是"}\n`;
            msg += `设备性能：${Profiler.getInstance().getDeviceLevelName()}\n`;
            msg += `语言：${Language.getInstance().getLanguage()}\n`;
            msg += `引擎语言：${Language.getInstance().getSystemLanguage()}\n`;
            msg += `服务器：${Server.getInstance().getName()}\n`;
            msg += `游戏区服：${GameServer.getInstance().getCurrentName()}\n`;
            msg += `当前热更节点：${EtadpuConfig.getInstance().getName()}\n`;
            msg += `设备渠道id：${Platform.getInstance().getChannelId()}（${Channel.getInstance()
                .getConfig()
                .getName()}）\n`;
            msg += `设备id：${Platform.getInstance().getDeviceId()}\n`;
            msg += `设备类型：${Platform.getInstance().getDeviceType()}\n`;
            msg += `操作系统：${Platform.getInstance().getDeviceSystemType()}\n`;
            msg += `系统版本：${Platform.getInstance().getSystemVersion()}\n`;
            if (cc.sys.isNative) {
                msg += `文件读写路径：${jsb.fileUtils ? jsb.fileUtils.getWritablePath() : ""}`;
            }
            UI.getInstance().open("FloatRule", { title: "系统信息", content: msg });
        },
    },
    {
        name: "Profiler",
        scene: DebugSceneType.All,
        id: DebugFunctionType.Profiler,
        execute: () => {
            const debugNode1 = cc.find("Canvas/debugNode");
            let isFind = false;
            for (const child of debugNode1.children) {
                if (child.name === "PrefabProfiler") {
                    child.destroy();
                    isFind = true;
                    break;
                }
            }
            if (!isFind) {
                Loader.getInstance().loadPrefab(PROFILER_DEBUG_NODE_PREFAB_URI, (prefab: cc.Prefab) => {
                    if (!cc.isValid(debugNode1)) {
                        return;
                    }
                    const node = Loader.getInstance().instantiate(prefab);
                    debugNode1.addChild(node);
                });
            }
        },
    },
    {
        name: "显隐FPS",
        scene: DebugSceneType.All,
        id: DebugFunctionType.HideFps,
        execute: (btn: cc.Node) => {
            cc.debug.setDisplayStats(!cc.debug.isDisplayStats());
            const isShow = cc.debug.isDisplayStats();
            const text = btn.getChildByName("text");
            CocosExt.setLabelText(text, isShow ? "隐藏FPS" : "显示FPS");
        },
        text: () => {
            const isShow = cc.debug.isDisplayStats();
            return isShow ? "隐藏FPS" : "显示FPS";
        },
    },
    {
        name: "游客登录",
        scene: DebugSceneType.Login,
        id: DebugFunctionType.GuestLogin,
        execute: () => {
            Nsn.getInstance().login(LoginType.Guest);
        },
    },
    {
        name: "账号登录",
        scene: DebugSceneType.Login,
        id: DebugFunctionType.AccountLogin,
        execute: () => {
            UI.getInstance().open("PopupAccountLogin");
        },
    },
    {
        name: "切换备用节点1",
        scene: DebugSceneType.All,
        id: DebugFunctionType.EtadpuNode1,
        execute: () => {
            EtadpuConfig.getInstance().setId(0);
            Tips.getInstance().info("切换到默认节点");
        },
    },
    {
        name: "切换备用节点2",
        scene: DebugSceneType.All,
        id: DebugFunctionType.EtadpuNode2,
        execute: () => {
            EtadpuConfig.getInstance().setId(1);
            Tips.getInstance().info("切换到备用节点1");
        },
    },
    {
        name: "切换备用节点3",
        scene: DebugSceneType.All,
        id: DebugFunctionType.EtadpuNode3,
        execute: () => {
            EtadpuConfig.getInstance().setId(2);
            Tips.getInstance().info("切换到备用节点2");
        },
    },
    {
        name: "断线重连",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.OffLine,
        execute: () => {
            Socket.getInstance().getSocket().close();
        },
    },
    {
        name: "手动回收资源",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.Recycle,
        execute: () => {
            Recycle.getInstance().release();
            Tips.getInstance().info("回收资源");
        },
    },
    {
        name: "手动GC",
        scene: DebugSceneType.All,
        id: DebugFunctionType.GC,
        execute: () => {
            Recycle.getInstance().gc();
            Tips.getInstance().info("gc成功");
        },
    },
    {
        name: "远程读表",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.RemoteDataNames,
        execute: () => {
            cc.log(Data.getInstance().getRemoteNames());
        },
    },
    {
        name: "同步玩家数据",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.SyncPlayerInfo,
        execute: () => {
            Player.getInstance().sendPlayerInit(PLAYER_INIT_TYPE.SYNC);
        },
    },
    {
        name: "查询掉单信息",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.QueryMissOrder,
        execute: () => {
            Channel.getInstance().getSdk().getUnConsumePurchase();
        },
    },
    {
        name: "前往应用市场",
        scene: DebugSceneType.All,
        id: DebugFunctionType.GotoAppStore,
        execute: () => {
            Channel.getInstance().getSdk().gotoAppStore();
        },
    },
    {
        name: "输出apk签名hash",
        scene: DebugSceneType.All,
        id: DebugFunctionType.GetSignHash,
        execute: () => {
            AndroidCallMethod.callNativeMethod("getHashKey", "()Ljava/lang/String;");
        },
    },
    {
        name: "白名单服",
        scene: DebugSceneType.Login,
        id: DebugFunctionType.ShowWhitelistServer,
        execute: (btn: cc.Node) => {
            const isWhitelist = Whitelist.getInstance().getWhitelist();
            Whitelist.getInstance().setWhitelist(!isWhitelist);
            GameServer.getInstance().setCurrent("");

            const text = btn.getChildByName("text");
            CocosExt.setLabelText(text, !isWhitelist ? "退出白名单" : "进入白名单");
        },
        text: () => {
            const isWhitelist = Whitelist.getInstance().getWhitelist();
            return isWhitelist ? "退出白名单" : "进入白名单";
        },
    },
    {
        name: "播放广告",
        scene: DebugSceneType.All,
        id: DebugFunctionType.ShowAd,
        execute: () => {
            AdSdk.getInstance().showAd({
                success: () => {
                    Tips.getInstance().show("广告播放成功");
                },
                loadFailed: (data: string) => {
                    Tips.getInstance().show("广告加载失败：" + data);
                },
                showFailed: (data: string) => {
                    Tips.getInstance().show("广告播放失败：" + data);
                },
                complete: () => {
                    Tips.getInstance().show("广告播放完成");
                },
            });
        },
    },
    {
        name: "道具填充",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.FakeBag,
        execute: () => {
            const list = TBItem.getInstance().getList();
            const item: IItemInfo[] = [];
            for (const e of list) {
                item.push({
                    itemInfoId: e.id,
                    num: 9999,
                });
            }
            const costItem: IItemInfo[] = [];
            const gotItem: IItemInfo[] = [];
            const srcReq = "";
            const showItem: IShowItemInfo[] = [];
            const result = NetworkResult.Success;
            const data = BagUpdateRet.create({ result, item, costItem, showItem, gotItem, srcReq });
            Bag.getInstance().bagUpdateRet(data);
        },
    },
    {
        name: "战斗日志",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.CombatLog,
        execute: () => {
            UI.getInstance().open("PopupCombatLogSet");
        },
    },
    {
        name: "打开邮件",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.Mail,
        execute: () => {
            UI.getInstance().open("PopupMail");
        },
    },
    {
        name: "显隐装备信息",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.HideEquipInfo,
        execute: (btn: cc.Node) => {
            CombatSetting.getInstance().setSettingState(CombatSettingId.EquipHideInfo);
            const isShow = CombatSetting.getInstance().getSettingState(CombatSettingId.EquipHideInfo);
            btn.getChildByName("text").label(isShow ? "显示装备信息" : "隐藏装备信息");
        },
        text: () => {
            const isShow = CombatSetting.getInstance().getSettingState(CombatSettingId.EquipHideInfo);
            return isShow ? "显示装备信息" : "隐藏装备信息";
        },
    },
    {
        name: "获取服务端属性数据",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.GetServerAttrData,
        execute: () => {
            MakeArrow.getInstance().sendGetAttrData();
        },
    },
    {
        name: "测试支付",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.TestPay,
        execute: () => {
            Recharge.getInstance().prePay(RECHARGE_ID.TEST);
        },
    },
    {
        name: "分析配置",
        scene: DebugSceneType.All,
        id: DebugFunctionType.AnalysisData,
        execute: () => {
            function getSize(obj: any): number {
                const jsonString = JSON.stringify(obj);
                return jsonString ? jsonString.length * 2 : 0;
            }
            function formatBytes(bytes: number): string {
                const KB = 1024;
                const MB = KB * 1024;

                if (bytes < MB) {
                    return (bytes / KB).toFixed(2) + " KB";
                } else {
                    return (bytes / MB).toFixed(2) + " MB";
                }
            }
            const data = Data.getInstance().getAllConfig();
            const info: { name: string; size: number; length: number; optimize: boolean; showSize: string }[] = [];
            for (const name in data) {
                const size = getSize(TB_MAP[name].getInstance());
                const length = TB_MAP[name].getInstance().getCount();
                info.push({ name, size, length, optimize: false, showSize: formatBytes(size) });
            }
            const count = info.length * 0.2;
            let totalSize = 0;
            let totalCount = 0;
            info.sort((a, b) => {
                return b.size - a.size;
            });
            info.forEach((v, i) => {
                v.optimize = i <= count;
                totalSize += v.size;
                totalCount += v.length;
            });
            cc.log("配置以大小排序，总：" + formatBytes(totalSize) + "，行数：" + totalCount);
            cc.log(info);
        },
    },
    {
        name: "打开游戏圈",
        scene: DebugSceneType.All,
        id: DebugFunctionType.OpenGameClub,
        execute: () => {
            const isWechatMinigame = Platform.getInstance().isWechatMinigame();
            if (!isWechatMinigame) {
                Tips.getInstance().show("微信小游戏环境下可用");
                return;
            }
            WechatMinigame.getInstance().openGameClub();
        },
    },
    {
        name: "获取游戏圈数据",
        scene: DebugSceneType.All,
        id: DebugFunctionType.GetGameClubData,
        execute: () => {
            GameClub.getInstance().request();
        },
    },
    {
        name: "打开评价",
        scene: DebugSceneType.All,
        id: DebugFunctionType.Evaluate,
        execute: () => {
            Channel.getInstance().getSdk().openEvaluate();
        },
    },
    {
        name: "系统开关",
        text: () => {
            return GameSwitch.getInstance().isEnable() ? "关闭系统开关" : "开启系统开关";
        },
        scene: DebugSceneType.Game,
        id: DebugFunctionType.GameSwitch,
        execute: (btn: cc.Node) => {
            const enable = GameSwitch.getInstance().isEnable();
            GameSwitch.getInstance().setEnable(!enable);
            const text = btn.getChildByName("text");
            CocosExt.setLabelText(text, GameSwitch.getInstance().isEnable() ? "关闭系统开关" : "开启系统开关");
        },
    },
    {
        name: "分享",
        scene: DebugSceneType.All,
        id: DebugFunctionType.Share,
        execute: () => {
            UI.getInstance().open("FloatShare");
        },
    },
    {
        name: "测试副本",
        scene: DebugSceneType.Game,
        id: DebugFunctionType.DungeonTest,
        execute: () => {
            UI.getInstance().open("UIDungeonCombatTest", {
                type: DungeonType.Test,
                levelId: 1,
            });
        },
    },
];

export const OTHERS_DEBUG_FUNCTION: IOthersDebugFunction[] = [
    {
        name: "获取道具",
        id: DebugFunctionType.GetItem,
        args: ["道具id", "道具数量"],
        execute: (text1: string, text2: string, text3: string) => {
            if (!text1) {
                Tips.getInstance().show("请输入道具id");
                return false;
            }
            const ids = text1.split(",").map((v) => parseInt(v));
            const count = parseInt(text2);
            const items = [];
            for (const id of ids) {
                const item = ItemInfo.create({ itemInfoId: id, num: count });
                items.push(item);
            }
            Bag.getInstance().sendBagAddProps(items);
            return true;
        },
    },
    {
        name: "读取本地存储数据",
        id: DebugFunctionType.GetLocalStorageData,
        args: ["存储key", "全局"],
        execute: (text1: string, text2: string, text3: string) => {
            if (!text1) {
                Tips.getInstance().show("请输入存储key");
                return false;
            }
            // @ts-ignore
            const data = LocalStorage.getInstance().getItem(text1, !!text2);
            UI.getInstance().open("FloatRule", { title: text1, content: data });
            return true;
        },
    },
    {
        name: "打开网页",
        id: DebugFunctionType.ShowWv,
        args: ["网址"],
        execute: (text1: string, text2: string, text3: string) => {
            if (!text1) {
                Tips.getInstance().show("请输入网址");
                return false;
            }
            UI.getInstance().open("PopupWv", { url: text1 });
            return true;
        },
    },
    {
        name: "获取装备",
        id: DebugFunctionType.GetEquip,
        args: ["装备id", "装备等级", "装备数量"],
        execute: (text1: string, text2: string, text3: string) => {
            if (!text1) {
                Tips.getInstance().show("装备id");
                return false;
            }
            if (!text1) {
                Tips.getInstance().show("装备等级");
                return false;
            }
            if (!text1) {
                Tips.getInstance().show("装备数量");
                return false;
            }
            Equip.getInstance().sendEquipCreate(parseInt(text1), parseInt(text2), parseInt(text3) || 1);
            return true;
        },
    },
];
