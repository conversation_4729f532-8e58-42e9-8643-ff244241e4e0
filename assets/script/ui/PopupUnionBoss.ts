/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:39:15
 */

import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import Time, { HOUR_TO_SECOND } from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import {
    UnionBossAttackRet,
    UnionBossProgressBroadcastRet,
    UnionBossRankRet,
    UnionBossReward,
    UnionBossRewardRet,
} from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import { EnumUnionPara } from "../data/base/BaseUnion";
import { RANK_ID } from "../data/parser/TBRank";
import TBUnion from "../data/parser/TBUnion";
import TBUnionLevel from "../data/parser/TBUnionLevel";
import { DungeonType } from "../game/Combat";
import Union from "../game/Union";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";
import TweenUtil from "../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

// 更新时间
const UPDATE_DT = 1;

enum RewardBoxType {
    Little,
    Big,
}

@ccclass
export default class PopupUnionBoss extends I18nComponent {
    @property(cc.Node)
    rank: cc.Node = null;
    @property(cc.Node)
    hurt: cc.Node = null;
    @property(cc.Node)
    reward1: cc.Node = null;
    @property([cc.Node])
    reward2: cc.Node[] = [];

    @property(cc.Node)
    btnFight: cc.Node = null;
    @property(cc.Node)
    btnFightText: cc.Node = null;
    @property(cc.Label)
    lbtTime: cc.Label = null;
    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private dt: number = 0;

    protected onLoad(): void {
        this.updateUI();
    }

    protected registerHandler(): void {
        Union.getInstance().on(
            [
                UnionBossAttackRet.prototype.clazzName,
                UnionBossProgressBroadcastRet.prototype.clazzName,
                UnionBossRewardRet.prototype.clazzName,
                UnionBossRankRet.prototype.clazzName,
            ],
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateInfo();
        this.updateReward();
        this.updateTime();
    }

    protected update(dt: number): void {
        this.dt += dt;
        if (this.dt < UPDATE_DT) {
            return;
        }
        this.dt -= UPDATE_DT;
        this.updateTime();
    }

    private updateInfo(): void {
        const unionInfo = Union.getInstance().getInfo();
        const unionLevel = TBUnionLevel.getInstance().getLevelByExp(unionInfo.exp);
        const unionLevelData = TBUnionLevel.getInstance().getDataById(unionLevel);
        ItemUtils.refreshView(this.reward1, this.prefabItem, unionLevelData.challengeReward);

        const totalCount = TBUnion.getInstance().getValueByPara(EnumUnionPara.BossChallengeCount);
        const roleAttackTimes = Union.getInstance().getRoleAttackTimes();
        this.btnFightText.label(TextUtils.format(i18n.union0075, totalCount - roleAttackTimes, totalCount));

        this.hurt.label(NumberUtils.format(Union.getInstance().getBossScore()));
        const rank = Union.getInstance().getBossRank();
        if (rank) {
            this.rank.label(TextUtils.format(i18n.rank0001, rank));
        } else {
            this.rank.label(i18n.rank0014);
        }
    }

    private updateReward(): void {
        const unionInfo = Union.getInstance().getInfo();
        const unionLevel = TBUnionLevel.getInstance().getLevelByExp(unionInfo.exp);
        const unionLevelData = TBUnionLevel.getInstance().getDataById(unionLevel);
        const unReceiveBossReward = Union.getInstance().getUnReceiveBossReward();
        const canReceiveBossReward = Union.getInstance().getCanReceiveBossReward();
        const receiveBossReward = Union.getInstance().getReceiveBossReward();

        const level1 = this.reward2[0].child("level");
        const count1 = this.reward2[0].child("count");
        const limit1 = this.reward2[0].child("limit");
        const box1 = this.reward2[0].child("box");
        level1.label("Lv." + unionLevel);
        count1.label("x" + unReceiveBossReward.smallBox);
        limit1.label(TextUtils.format(i18n.union0074, receiveBossReward.smallBox + "/" + unionLevelData.chestNumber));
        box1.color = canReceiveBossReward.smallBox <= 0 ? "#4d4d4d" : "#ffffff";
        box1.getComponent(cc.Button).interactable = canReceiveBossReward.smallBox > 0;
        TweenUtil.swing(box1, canReceiveBossReward.smallBox > 0);

        const level2 = this.reward2[1].child("level");
        const count2 = this.reward2[1].child("count");
        const limit2 = this.reward2[1].child("limit");
        const box2 = this.reward2[1].child("box");
        level2.label("Lv." + unionLevel);
        count2.label("x" + unReceiveBossReward.bigBox);
        limit2.label(TextUtils.format(i18n.union0074, receiveBossReward.bigBox + "/" + unionLevelData.bigChestNumber));
        box2.color = canReceiveBossReward.bigBox <= 0 ? "#4d4d4d" : "#ffffff";
        box2.getComponent(cc.Button).interactable = canReceiveBossReward.bigBox > 0;
        TweenUtil.swing(box2, canReceiveBossReward.bigBox > 0);
    }

    private updateTime(): void {
        const thisToday = Time.getInstance().getTodayZero();
        const endPointTime = TBUnion.getInstance().getValueByPara(EnumUnionPara.BossChallengeEndTime);
        const endTime = thisToday + endPointTime * HOUR_TO_SECOND * 1000;
        const timeGap = endTime - Time.getInstance().now();
        if (timeGap > 0) {
            this.lbtTime.string = TextUtils.format(
                i18n.union0055,
                TimeFormat.getInstance().getTextByDuration(
                    endTime - Time.getInstance().now(),
                    TimeDurationFormatType.D_H_M_S_2
                )
            );
            const totalCount = TBUnion.getInstance().getValueByPara(EnumUnionPara.BossChallengeCount);
            const lastCount = totalCount - Union.getInstance().getRoleAttackTimes();
            this.btnFight.getComponent(GrayComp).gray = lastCount <= 0;
            this.btnFight.getComponent(cc.Button).interactable = lastCount > 0;
        } else {
            this.lbtTime.string = i18n.union0047;
            this.btnFight.getComponent(GrayComp).gray = true;
            this.btnFight.getComponent(cc.Button).interactable = false;
        }
    }

    protected onClickRank(): void {
        UI.getInstance().open("PopupRank", RANK_ID.UNION_BOSS);
    }

    protected onClickReward(sender: cc.Event.EventTouch, data: string): void {
        const unionInfo = Union.getInstance().getInfo();
        const unionLevel = TBUnionLevel.getInstance().getLevelByExp(unionInfo.exp);
        const unionLevelData = TBUnionLevel.getInstance().getDataById(unionLevel);
        const type = parseInt(data);
        switch (type) {
            case RewardBoxType.Little:
                ItemUtils.showInfo(unionLevelData.chest[0][0]);
                break;
            case RewardBoxType.Big:
                ItemUtils.showInfo(unionLevelData.bigChest[0][0]);
                break;
            default:
                break;
        }
    }

    protected onClickGetReward(sender: cc.Event.EventTouch, data: string): void {
        const type = parseInt(data);
        switch (type) {
            case RewardBoxType.Little:
                Union.getInstance().sendUnionBossReward(UnionBossReward.ReceiveType.Small);
                break;
            case RewardBoxType.Big:
                Union.getInstance().sendUnionBossReward(UnionBossReward.ReceiveType.Big);
                break;
            default:
                break;
        }
    }

    protected onClickLog(): void {
        UI.getInstance().open("FloatUnionBossLog");
    }

    protected onClickFight(): void {
        UI.getInstance().open("UIDungeonCombatUnion", {
            type: DungeonType.Union,
            levelId: 1,
        });
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
