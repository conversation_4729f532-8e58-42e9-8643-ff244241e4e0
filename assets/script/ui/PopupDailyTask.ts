/* 任务
 * @Author: wangym
 * @Date: 2024-03-27 10:14:59
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-25 09:47:11
 */
import ButtonGroup from "../../nsn/comp/ui/ButtonGroup";
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";

enum UIType {
    DailyTask,
    WeeklyTask,
    WeekFund,
}

const TAB_ENUM = [UIType.DailyTask, UIType.WeeklyTask, UIType.WeekFund];

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupDailyTask extends I18nComponent {
    @property([cc.Node])
    btns: cc.Node[] = [];
    @property(cc.Node)
    content: cc.Node = null;
    @property([cc.Prefab])
    prefabs: cc.Prefab[] = [];
    @property(cc.Node)
    buttonGroup: cc.Node = null;

    private curTab: UIType = UIType.DailyTask;
    private nodes: cc.Node[] = [];

    protected onLoad(): void {
        for (let i = 0; i < this.btns.length; i++) {
            this.btns[i].button(TAB_ENUM[i]);
        }

        this.updateUI();
        this.buttonGroup.getComponent(ButtonGroup).setDefaultIndex(this.curTab);
    }

    private updateUI(): void {
        if (!cc.isValid(this.nodes[this.curTab])) {
            this.nodes[this.curTab] = Loader.getInstance().instantiate(this.prefabs[this.curTab]);
            this.nodes[this.curTab].parent = this.content;
        }
        for (let i = 0; i < this.nodes.length; i++) {
            if (cc.isValid(this.nodes[i])) {
                this.nodes[i].active = i === this.curTab;
            }
        }
    }

    protected onClickTab(sender: cc.Event.EventTouch, tab: UIType): void {
        if (this.curTab === tab) {
            return;
        }
        this.curTab = tab;
        this.updateUI();
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
