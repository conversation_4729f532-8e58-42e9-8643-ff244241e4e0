/*
 * @Author: chenx
 * @Date: 2024-09-18 14:45:28
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:40:22
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import Time from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import {
    BagUpdateRet,
    DungeonTowerBoxReward,
    DungeonTowerBoxRewardRet,
    DungeonTowerReward,
    DungeonTowerRewardRet,
    IItemInfo,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumDungeonType } from "../data/base/BaseDungeon";
import { EnumUniversalPara } from "../data/base/BaseUniversal";
import TBDungeon from "../data/parser/TBDungeon";
import TBDungeonTower from "../data/parser/TBDungeonTower";
import { GAME_SWITCH_ID } from "../data/parser/TBGameSwitch";
import { RANK_ID } from "../data/parser/TBRank";
import TBUniversal from "../data/parser/TBUniversal";
import Bag from "../game/Bag";
import { DungeonType } from "../game/Combat";
import CombatScore from "../game/CombatScore";
import CombatSetting, { CombatSettingId } from "../game/CombatSetting";
import DungeonTower from "../game/DungeonTower";
import GameSwitch from "../game/GameSwitch";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class UIDungeonTower extends I18nComponent {
    @property(cc.Node)
    nodeTitle: cc.Node = null;
    @property(cc.Node)
    title: cc.Node = null;
    @property(cc.Node)
    monster: cc.Node = null;

    @property(cc.Node)
    combatScore: cc.Node = null;
    @property(cc.Node)
    rewards: cc.Node = null;

    @property(cc.Node)
    btnAd: cc.Node = null;
    @property(cc.Node)
    btnAdText: cc.Node = null;
    @property(cc.Node)
    btnReward: cc.Node = null;
    @property(cc.Node)
    btnRewardText: cc.Node = null;

    @property(cc.Sprite)
    spCombatIcon: cc.Sprite = null; // 战斗按钮icon
    @property(cc.Sprite)
    spCombatIcon2: cc.Sprite = null; // 战斗按钮icon
    @property(cc.Node)
    costIcon: cc.Node = null;
    @property(cc.Node)
    costCount: cc.Node = null;
    @property(cc.Node)
    time: cc.Node = null;
    @property(cc.Node)
    red: cc.Node = null;
    @property(cc.Node)
    nodeCheckboxIcon: cc.Node = null; // 勾选框icon

    @property(cc.Node)
    nodeBoxAni: cc.Node = null; // 宝箱动画
    @property(sp.Skeleton)
    spineBox: sp.Skeleton = null; // 宝箱

    @property(cc.Node)
    nodeSeckillAni: cc.Node = null; // 秒杀动画
    @property(sp.Skeleton)
    spineSeckill: sp.Skeleton = null; // 秒杀

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private dt: number = 1;
    private rewardData: IItemInfo[] = null; // 奖励数据
    private isSeckill: boolean = false; // 是否秒杀
    private isPlayingAni: boolean = false; // 是否正在播放动画

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spCombatIcon,
                url: "texture/syncUI/dungeon/btnInstanceFight1",
            },
            {
                sprite: this.spCombatIcon2,
                url: "texture/syncUI/dungeon/btnInstanceFight3",
            },
            {
                sprite: this.nodeTitle,
                url: "texture/syncUI/dungeon/spTHZTSystemTitle",
            },
        ];
    }

    protected onLoad(): void {
        this.updateUI();
    }

    protected onEnable(): void {
        this.updateCheckboxState();
        this.requestReward();
    }

    protected registerHandler(): void {
        // 背包-更新
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                switch (data.srcReq) {
                    case DungeonTowerReward.prototype.clazzName:
                        this.rewardData = data.gotItem;
                        break;
                    case DungeonTowerBoxReward.prototype.clazzName:
                        this.playBosAni(data.gotItem);
                        break;
                    default:
                        break;
                }
            },
            this
        );
        // 爬塔副本-过关
        DungeonTower.getInstance().on(
            DungeonTowerRewardRet.prototype.clazzName,
            (data: DungeonTowerRewardRet) => {
                if (data.isSkip) {
                    !this.isPlayingAni && this.playSeckillAni(this.rewardData);
                } else {
                    this.updateUI();
                }
            },
            this
        );
        // 爬塔副本-领取宝箱奖励
        DungeonTower.getInstance().on(
            DungeonTowerBoxRewardRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        const dungeonId = DungeonTower.getInstance().getCurDungeonId();
        const data = TBDungeonTower.getInstance().getDataById(dungeonId);
        this.title.label(TextUtils.format(i18n.dungeon0043, data.layer, data.grade));
        SpineUtils.setMonster(this.monster, data.monster[data.monster.length - 1][0][0], "wait");
        this.combatScore.label(NumberUtils.format(data.resPower));
        ItemUtils.refreshView(this.rewards, this.prefabItem, data.reward);

        const rewardDungeon = TBDungeonTower.getInstance().getNextRewardDungeonData(dungeonId);
        if (rewardDungeon.boxReward) {
            this.btnReward.active = true;
            this.btnRewardText.label(TextUtils.format(i18n.dungeon0044, rewardDungeon.grade - data.grade + 1));
        } else {
            this.btnReward.active = false;
        }

        this.isSeckill = false;
        const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.DUNGEON_TOWER_SECKILL);
        if (result) {
            const para: number = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.Annihilate);
            const score = CombatScore.getInstance().getScore();
            this.isSeckill = score >= data.resPower * (1 + para);
        }
        this.spCombatIcon.node.active = !this.isSeckill;
        this.spCombatIcon2.node.active = this.isSeckill;
        const dungeonData = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonTower);
        ImageUtils.setItemIcon(this.costIcon, dungeonData.cost[0][0]);
        this.red.active = Bag.getInstance().isEnough(dungeonData.cost[0][0], dungeonData.cost[0][1]);
    }

    protected update(dt: number): void {
        this.dt += dt;
        if (this.dt < 1) {
            return;
        }
        this.dt = 0;
        this.updateTime();
    }

    private updateTime(): void {
        const data = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonTower);
        const [costId, costCount] = data.cost[0];
        const [maxCount, recoverTime] = data.recoveryTime;
        const count = Bag.getInstance().getItemCountById(costId);
        this.costCount.richText(
            TextUtils.format(
                count >= costCount ? i18n.common0072 : i18n.common0073,
                NumberUtils.format(count, 1, 0),
                NumberUtils.format(maxCount, 1, 0)
            )
        );

        if (count >= maxCount) {
            this.time.active = false;
        } else {
            this.time.active = true;
            const now = Time.getInstance().now();
            const lastRecoverTime = DungeonTower.getInstance().getLastRecoverTime();
            const gap = Math.max(lastRecoverTime + recoverTime * 1000 - now, 0);
            this.time.label(
                TextUtils.format(
                    i18n.dungeon0045,
                    TimeFormat.getInstance().getTextByDuration(gap, TimeDurationFormatType.MM_SS),
                    1
                )
            );
        }
    }

    private requestReward(): void {
        const hasReward = DungeonTower.getInstance().hasReward();
        if (hasReward) {
            DungeonTower.getInstance().sendDungeonTowerBoxReward();
        }
    }

    /**
     * 播放秒杀动画
     * @param rewardData 奖励数据
     */
    private playSeckillAni(rewardData: IItemInfo[]): void {
        this.isPlayingAni = true;
        this.nodeSeckillAni.active = true;
        this.spineSeckill.node.opacity = 255;
        this.spineSeckill.setCompleteListener(() => {
            this.spineSeckill.setCompleteListener(null);

            this.spineSeckill.node.opacity = 0;
            this.spineSeckill.clearTracks();
        });
        this.spineSeckill.setAnimation(0, "wait", false);
        const spineMonster = this.monster.getComponent(sp.Skeleton);
        spineMonster.setCompleteListener(() => {
            spineMonster.setCompleteListener(null);

            this.nodeSeckillAni.active = false;
            this.isPlayingAni = false;

            this.updateUI();
            UI.getInstance().open("FloatReward", {
                gotItem: rewardData,
                cb: () => {
                    if (cc.isValid(this.node)) {
                        this.updateCheckboxState();
                        this.requestReward();
                    }
                },
            });
        });
        spineMonster.setAnimation(0, "die", false);
    }

    /**
     * 播放宝箱动画
     * @param rewardData 奖励数据
     */
    private playBosAni(rewardData: IItemInfo[]): void {
        this.nodeBoxAni.active = true;
        cc.tween(this.nodeBoxAni)
            .to(0.2, { opacity: 200 })
            .delay(1.13)
            .call(() => {
                this.nodeBoxAni.active = false;
                this.nodeBoxAni.opacity = 0;
                this.spineBox.node.opacity = 0;
                this.spineBox.clearTrack(0);

                UI.getInstance().open("FloatReward", { gotItem: rewardData });
            })
            .start();
        this.spineBox.node.opacity = 255;
        this.spineBox.setAnimation(0, "wait", false);
    }

    /**
     * 更新勾选框状态
     */
    private updateCheckboxState(): void {
        this.nodeCheckboxIcon.active = CombatSetting.getInstance().getSettingState(
            CombatSettingId.DungeonTowerAutoNextLevel
        );
    }

    protected onClickRank(): void {
        UI.getInstance().open("PopupRank", RANK_ID.DUNGEON_TOWER);
    }

    protected onClickReward(): void {
        const dungeonId = DungeonTower.getInstance().getCurDungeonId();
        const data = TBDungeonTower.getInstance().getDataById(dungeonId);

        const rewardDungeon = TBDungeonTower.getInstance().getNextRewardDungeonData(dungeonId);
        if (rewardDungeon.boxReward) {
            UI.getInstance().open("PopupDungeonRewardPreview", {
                text: TextUtils.format(i18n.dungeon0046, rewardDungeon.grade - data.grade + 1),
                rewards: rewardDungeon.boxReward.map((v) => {
                    return {
                        itemInfoId: v[0],
                        num: v[1],
                    };
                }),
            });
        }
    }

    /**
     * 战斗
     */
    protected onClickCombat(): void {
        const data = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonTower);
        const [costId, costCount] = data.cost[0];
        if (!Bag.getInstance().isEnough(costId, costCount)) {
            UI.getInstance().open("FloatItemSource", costId);
            return;
        }
        const levelId = DungeonTower.getInstance().getCurDungeonId();
        if (this.isSeckill) {
            let seckillLevelNum = 1;
            let levelInfo = TBDungeonTower.getInstance().getDataById(levelId);
            const layer = levelInfo.layer;
            const para: number = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.Annihilate);
            const score = CombatScore.getInstance().getScore();
            while (true) {
                levelInfo = TBDungeonTower.getInstance().getDataById(levelInfo.nextId);
                if (levelInfo.layer !== layer) {
                    break;
                }
                if (score < levelInfo.resPower * (1 + para)) {
                    break;
                }

                seckillLevelNum++;
                if (levelInfo.id === levelInfo.nextId) {
                    break;
                }
            }
            DungeonTower.getInstance().sendDungeonTowerReward(true, seckillLevelNum);
            return;
        }

        UI.getInstance().open("UIDungeonCombatTower", {
            type: DungeonType.Tower,
            levelId: DungeonTower.getInstance().getCurDungeonId(),
        });
    }

    /**
     * 设置勾选框状态
     */
    protected onClickSetCheckboxState(): void {
        CombatSetting.getInstance().setSettingState(CombatSettingId.DungeonTowerAutoNextLevel);
        this.updateCheckboxState();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
