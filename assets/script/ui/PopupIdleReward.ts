/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-22 09:15:30
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import Time, { HOUR_TO_SECOND } from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import Tips from "../../nsn/util/Tips";
import {
    IdleEarningsGetRewardRet,
    IdleEarningsTakeHoursRet,
    IdleEarningsTakeRet,
    IItemInfo,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumEconomyAttributeType } from "../data/base/BaseEconomyAttribute";
import { EnumIdleEarningsTotalPara } from "../data/base/BaseIdleEarningsTotal";
import { EnumPrivilegeConfigType } from "../data/base/BasePrivilegeConfig";
import { EnumPrivilegeGroupSource } from "../data/base/BasePrivilegeGroup";
import TBEconomyAttribute from "../data/parser/TBEconomyAttribute";
import TBForgeLevel from "../data/parser/TBForgeLevel";
import TBFundMarkup from "../data/parser/TBFundMarkup";
import TBIdleEarningsTotal from "../data/parser/TBIdleEarningsTotal";
import TBMainBarrier from "../data/parser/TBMainBarrier";
import TBPrivilegeConfig from "../data/parser/TBPrivilegeConfig";
import TBPrivilegeGroup from "../data/parser/TBPrivilegeGroup";
import { RECHARGE_ID } from "../data/parser/TBRecharge";
import DungeonMain from "../game/DungeonMain";
import EconomyAttribute from "../game/EconomyAttribute";
import IdleReward from "../game/IdleReward";
import MakeArrow from "../game/MakeArrow";
import Privilege from "../game/Privilege";
import Recharge from "../game/Recharge";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupIdleReward extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null;
    @property(cc.Label)
    lbtTime: cc.Label = null;
    @property(cc.Label)
    lbtLimitTime: cc.Label = null;
    @property(cc.Node)
    production: cc.Node = null;
    @property(ListView)
    rewardList: ListView = null;
    @property(cc.Node)
    rewardLayout: cc.Node = null;
    @property(cc.Node)
    noneReward: cc.Node = null;
    @property(cc.Node)
    btnQuickText: cc.Node = null;

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private dt: number = 1;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/syncUI/idleReward/spExplorationTitleTxt",
            },
        ];
    }

    protected onLoad(): void {
        this.updateUI();
        this.requestData();
    }

    protected onDestroy(): void {
        UI.getInstance().hideSoftLoading();
    }

    protected registerHandler(): void {
        IdleReward.getInstance().on(
            IdleEarningsTakeHoursRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );

        IdleReward.getInstance().on(
            IdleEarningsTakeRet.prototype.clazzName,
            () => {
                this.updateUI();
                this.updateReward([]);
            },
            this
        );

        IdleReward.getInstance().on(
            IdleEarningsGetRewardRet.prototype.clazzName,
            (rewards: IItemInfo[]) => {
                this.updateReward(rewards);
            },
            this
        );
    }

    private requestData(): void {
        UI.getInstance().showSoftLoading();
        IdleReward.getInstance().sendIdleEarningsGetReward();
    }

    private updateUI(): void {
        const maxIdleTime =
            parseFloat(TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.ProfitLimitTime)) *
            1000;
        this.lbtLimitTime.string = TextUtils.format(
            i18n.idle0001,
            TimeFormat.getInstance().getTextByDuration(maxIdleTime, TimeDurationFormatType.D_H)
        );

        const barrierId = DungeonMain.getInstance().getLevelId();
        const { idlePrestigeEarnings } = TBMainBarrier.getInstance().getDataById(barrierId);
        const idleEarnTime = parseFloat(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.ProfitTime)
        );

        ImageUtils.setRichTextItemIcons(this.production, ["prop" + idlePrestigeEarnings[0][0]], () => {
            const otherAttr = EconomyAttribute.getInstance().getOtherAttr();
            this.production.richText(
                TextUtils.format(
                    i18n.idle0002,
                    "prop" + idlePrestigeEarnings[0][0],
                    NumberUtils.format((idlePrestigeEarnings[0][1] / idleEarnTime) * HOUR_TO_SECOND) +
                        (otherAttr[EnumEconomyAttributeType.ReputationBonus].value
                            ? "(+" +
                              (NumberUtils.format(
                                  (idlePrestigeEarnings[0][1] / idleEarnTime) *
                                      HOUR_TO_SECOND *
                                      otherAttr[EnumEconomyAttributeType.ReputationBonus].value
                              ) +
                                  ")")
                            : "")
                )
            );
        });

        let freeCount = parseInt(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.FreeProfitNum)
        );
        freeCount += this.getPrivilegeFreeCount();
        const costCount = parseInt(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.CostProfitNum)
        );
        const freeTimes = IdleReward.getInstance().getFreeTimes();
        const costTimes = IdleReward.getInstance().getCostTimes();
        this.btnQuickText.label(freeCount + costCount - freeTimes - costTimes + "/" + (freeCount + costCount));
    }

    private updateReward(rewards: IItemInfo[]): void {
        if (rewards.length) {
            const earningsTime = IdleReward.getInstance().getEarningsTime();
            const now = Time.getInstance().now();
            const idleEarnTime = parseFloat(
                TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.ProfitTime)
            );
            const hasGotReward = IdleReward.getInstance().hasGotReward();
            if (now - earningsTime < idleEarnTime * 1000 && hasGotReward) {
                this.rewardList.node.active = false;
                this.rewardLayout.active = false;
                this.noneReward.active = true;
            } else {
                if (rewards.length <= 4) {
                    this.rewardList.node.active = false;
                    this.rewardLayout.active = true;
                    this.noneReward.active = false;
                    ItemUtils.refreshView(this.rewardLayout, this.prefabItem, rewards);
                } else {
                    this.rewardList.node.active = true;
                    this.rewardLayout.active = false;
                    this.noneReward.active = false;
                    this.rewardList.setListData(rewards);
                }
            }
        } else {
            this.rewardList.node.active = false;
            this.rewardLayout.active = false;
            this.noneReward.active = true;
        }
    }

    protected update(dt: number): void {
        this.dt += dt;
        if (this.dt < 1) {
            return;
        }
        this.dt -= 1;

        const earningsTime = IdleReward.getInstance().getEarningsTime();
        const maxIdleTime =
            parseFloat(TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.ProfitLimitTime)) *
            1000;
        const now = Time.getInstance().now();
        this.lbtTime.string = TextUtils.format(
            i18n.idle0009,
            TimeFormat.getInstance().getTextByDuration(
                Math.min(now - earningsTime, maxIdleTime),
                TimeDurationFormatType.D_H_M_S_2
            )
        );
    }

    private getPrivilegeFreeCount(): number {
        let count = 0;
        const privileges = TBPrivilegeConfig.getInstance().getDataListByType(EnumPrivilegeConfigType.QuickAFKTimes);
        for (const e of privileges) {
            if (Privilege.getInstance().hasPrivilege(e.id)) {
                count += e.para.value;
            }
        }
        return count;
    }

    protected onClickTips(sender: cc.Event.EventTouch): void {
        // 特权卡
        const info: {
            id: number;
            source: EnumPrivilegeGroupSource;
            desc: string;
            value: number;
            hasPrivilege: boolean;
        }[] = [];
        const list = TBPrivilegeGroup.getInstance().getList();
        const ecoAttrData = TBEconomyAttribute.getInstance().getDataByType(EnumEconomyAttributeType.ReputationBonus);
        for (const e1 of list) {
            const { source, privilege, desc } = e1;
            for (const e2 of privilege) {
                const data = TBPrivilegeConfig.getInstance().getDataById(e2);
                if (data.type !== EnumPrivilegeConfigType.EconomyAttribute) {
                    continue;
                }
                if (data.para.attr[0].economyAttribute !== ecoAttrData.id) {
                    continue;
                }
                info.push({
                    id: e1.id,
                    source,
                    desc,
                    value: data.para.attr[0].value,
                    hasPrivilege: Privilege.getInstance().hasPrivilege(data.id),
                });
            }
        }
        const showData: {
            id: number;
            source: EnumPrivilegeGroupSource;
            desc: string;
            value: number;
            hasPrivilege: boolean;
        }[] = [];
        for (const e of info) {
            if (e.source === EnumPrivilegeGroupSource.SourceForgeBed) {
                if (e.hasPrivilege) {
                    const find = showData.find((v) => v.source === e.source && v.hasPrivilege);
                    if (find) {
                        find.value += e.value;
                    } else {
                        showData.push({
                            id: e.id,
                            source: e.source,
                            desc: e.desc,
                            value: e.value,
                            hasPrivilege: e.hasPrivilege,
                        });
                    }
                }
            } else {
                showData.push(e);
            }
        }
        const hasForgePrivilege = showData.find(
            (v) => v.hasPrivilege && v.source === EnumPrivilegeGroupSource.SourceForgeBed
        );
        if (!hasForgePrivilege) {
            const forgeLevelList = TBForgeLevel.getInstance().getList();
            const minPrivilegeGroupId = forgeLevelList.filter((v) => v.privilegeGroup).map((v) => v.privilegeGroup)[0];
            const find = info.find((v) => v.id === minPrivilegeGroupId);
            if (find) {
                showData.push(find);
            }
        }
        showData.sort((a, b) => Number(b.hasPrivilege) - Number(a.hasPrivilege));
        let text = "";
        for (const e of showData) {
            text += `<color=${e.hasPrivilege ? "#93ffa5" : "#CFBBAF"}>${e.desc}${(e.value * 100).toFixed(0)}%</c>\n`;
        }

        // 王权基金
        const fundList = TBFundMarkup.getInstance().getList();
        const isRecharge = Recharge.getInstance().isRecharged(RECHARGE_ID.POWER_FUND);
        if (!isRecharge) {
            text += `<color=#CFBBAF>${i18n.powerFund0004}${fundList[0].economyAttribute[0][1].toFixed(0)}%</c>`;
        } else {
            let value = 0;
            for (const e of fundList) {
                const isEnough = MakeArrow.getInstance().getForgeIronCostCounts() >= e.cost;
                if (isEnough) {
                    value = e.economyAttribute[0][1];
                }
            }
            if (value <= 0) {
                text += `<color=#CFBBAF>${i18n.powerFund0004}${(fundList[0].economyAttribute[0][1] * 100).toFixed(
                    0
                )}%</c>`;
            } else {
                text += `<color=#93ffa5>${i18n.powerFund0004}${(value * 100).toFixed(0)}%</c>`;
            }
        }

        const pos = sender.target.convertToWorldSpaceAR(cc.v2());
        UI.getInstance().open("FloatTextTips", {
            pos,
            text,
            offset: cc.v2(0, 120),
            arrow: false,
        });
    }

    protected onClickGet(): void {
        const earningsTime = IdleReward.getInstance().getEarningsTime();
        const now = Time.getInstance().now();
        const idleEarnTime = parseFloat(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.ProfitTime)
        );

        const hasGotReward = IdleReward.getInstance().hasGotReward();
        if (now - earningsTime < idleEarnTime * 1000 && hasGotReward) {
            Tips.getInstance().show(i18n.idle0003);
            return;
        }
        IdleReward.getInstance().sendIdleEarningsTake();
    }

    protected onClickQuick(): void {
        let freeCount = parseInt(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.FreeProfitNum)
        );
        freeCount += this.getPrivilegeFreeCount();
        const costCount = parseInt(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.CostProfitNum)
        );
        const freeTimes = IdleReward.getInstance().getFreeTimes();
        const costTimes = IdleReward.getInstance().getCostTimes();
        if (freeTimes + costTimes >= freeCount + costCount) {
            Tips.getInstance().show(i18n.idle0004);
            return;
        }
        UI.getInstance().open("FloatIdleRewardQuick");
    }

    protected onClickPreview(): void {
        UI.getInstance().open("FloatIdleRewardPreview");
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
