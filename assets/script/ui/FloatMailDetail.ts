/*
 * @Author: Jrrend
 * @Date: 2024-03-16 11:10:50
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-18 17:13:47
 */

import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TimeFormat from "../../nsn/util/TimeFormat";
import {
    MailBatchRemoveRet,
    MailBatchTakeRet,
    MailNotifyRet,
    MailReadRet,
    MailRemoveRet,
    MailTakeRet,
} from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import Mail from "../game/Mail";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatMailDetail extends I18nComponent {
    @property(ListView)
    itemListView: ListView = null;
    @property(cc.Label)
    spTitle: cc.Label = null;

    @property(cc.Node)
    scvNode1: cc.Node = null; // 可领取奖励时滑动区
    @property(cc.Node)
    scvNode2: cc.Node = null; // 无可领取奖励时滑动区
    @property(cc.Node)
    lbtDetail1: cc.Node = null;
    @property(cc.Node)
    lbtDetail2: cc.Node = null;
    @property(cc.Node)
    lbtSend: cc.Node = null; // 发送者
    @property(cc.Node)
    sendTime: cc.Node = null; // 发送时间
    @property(cc.Node)
    receiveNode: cc.Node = null;
    @property(cc.Node)
    delNode: cc.Node = null;
    @property(cc.Node)
    bottomNode: cc.Node = null;
    @property(cc.Node)
    line: cc.Node = null;

    private mailId: string = "";

    protected onLoad(): void {
        this.mailId = this.args;
        this.updateUI();
    }

    protected registerHandler(): void {
        Mail.getInstance().on(
            [
                MailReadRet.prototype.clazzName,
                MailTakeRet.prototype.clazzName,
                MailRemoveRet.prototype.clazzName,
                MailBatchTakeRet.prototype.clazzName,
                MailBatchRemoveRet.prototype.clazzName,
                MailNotifyRet.prototype.clazzName,
            ],
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        const info = Mail.getInstance().getMailById(this.mailId);
        this.spTitle.string = info.title;
        this.receiveNode.active = info.annex.length > 0;
        this.receiveNode.getComponent(GrayComp).gray = info.annex.length > 0 && info.isTaked;
        this.receiveNode.getComponent(cc.Button).interactable = info.annex.length > 0 && !info.isTaked;
        this.receiveNode
            .child("text")
            .label(info.annex.length > 0 && info.isTaked ? i18n.leadGrowth0006 : i18n.common0030);
        this.delNode.active = info.annex.length === 0 || info.isTaked;
        this.line.active = info.annex.length > 0;
        if (info.annex.length <= 0) {
            this.lbtSend.y = -970;
            this.sendTime.y = -1010;
            this.scvNode1.active = false;
            this.scvNode2.active = true;
            this.bottomNode.active = false;
            this.lbtDetail2.label(info.content);
        } else {
            this.lbtSend.y = -752;
            this.sendTime.y = -792;
            this.scvNode1.active = true;
            this.scvNode2.active = false;
            this.bottomNode.active = true;
            this.lbtDetail1.label(info.content);
            this.itemListView.setListData(
                info.annex.map((e) => {
                    return {
                        info,
                        reward: e,
                    };
                })
            );
        }
        this.lbtSend.label(i18n.mail00001);
        this.sendTime.label(TimeFormat.getInstance().getTextFromNow(info.sendAt));
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }

    protected onClickReceive(): void {
        Mail.getInstance().sendMailTake(this.mailId);
    }

    protected onClickDel(): void {
        Mail.getInstance().sendMailRemove(this.mailId);
        this.onClickClose();
    }
}
