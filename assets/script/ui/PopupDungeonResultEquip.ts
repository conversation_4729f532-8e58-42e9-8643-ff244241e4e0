/*
 * @Author: chenx
 * @Date: 2024-09-10 18:22:18
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-11-29 17:30:06
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import { IItemInfo, IPlayerInfo } from "../../protobuf/proto";
import DungeonEquip from "../game/DungeonEquip";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";

/**
 * 界面参数
 */
export interface IPopupDungeonResultEquipArgs {
    playerData: IPlayerInfo; // 玩家数据
    playerData2: IPlayerInfo; // 玩家数据-队友
    maxDps: number; // 最大秒伤
    maxDps2: number; // 最大秒伤-队友
    passTime: number; // 通关时间(s)
    rewardData: IItemInfo[]; // 奖励
}

const { ccclass, property } = cc._decorator;

/**
 * 副本结果-装备
 */
@ccclass
export default class PopupDungeonResultEquip extends I18nComponent {
    @property(cc.Sprite)
    spTitle: cc.Sprite = null; // 标题
    @property(cc.Label)
    lbtTime: cc.Label = null; // 通关时间
    @property(ListView)
    listReward: ListView = null; // 奖励列表

    @property(cc.Node)
    nodeDps: cc.Node = null; // 秒伤
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    nodeMvpTag: cc.Node = null; // mvpTag
    @property(cc.Label)
    lbtDps: cc.Label = null; // 秒伤
    @property(cc.ProgressBar)
    prgDps: cc.ProgressBar = null; // 秒伤

    @property(cc.Node)
    nodeDps2: cc.Node = null; // 秒伤-队友
    @property(cc.Label)
    lbtName2: cc.Label = null; // 名称-队友
    @property(cc.Node)
    nodeMvpTag2: cc.Node = null; // mvpTag-队友
    @property(cc.Label)
    lbtDps2: cc.Label = null; // 秒伤-队友
    @property(cc.ProgressBar)
    prgDps2: cc.ProgressBar = null; // 秒伤-队友

    initData: IPopupDungeonResultEquipArgs = null; // 初始数据

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/syncUI/reward/spInstanceResultTitle2",
            },
        ];
    }

    protected onLoad(): void {
        this.initData = this.args;
        this.init();
    }

    /**
     * 初始化
     */
    private init(): void {
        this.lbtTime.string = TimeFormat.getInstance().getTextByDuration(
            this.initData.passTime * 1000,
            TimeDurationFormatType.HH_MM_SS
        );
        this.listReward.scrollView.stopAutoScroll();
        this.listReward.setListData(this.initData.rewardData);
        const isShowDps2 = !!this.initData.playerData2;
        !isShowDps2 && (this.nodeDps.y = 0);
        this.nodeDps2.active = isShowDps2;
        if (!isShowDps2) {
            this.lbtName.string = this.initData.playerData.name;
            this.nodeMvpTag.active = true;
            this.lbtDps.string = NumberUtils.format(this.initData.maxDps, 1, 0);
            this.prgDps.progress = 1;
        } else {
            const isMvp = this.initData.maxDps >= this.initData.maxDps2;
            this.lbtName.string = isMvp ? this.initData.playerData.name : this.initData.playerData2.name;
            this.nodeMvpTag.active = true;
            this.lbtDps.string = isMvp
                ? NumberUtils.format(this.initData.maxDps, 1, 0)
                : NumberUtils.format(this.initData.maxDps2, 1, 0);
            const progressDps = this.initData.maxDps / (this.initData.maxDps + this.initData.maxDps2);
            const progressDps2 = this.initData.maxDps2 / (this.initData.maxDps + this.initData.maxDps2);
            this.prgDps.progress = isMvp ? progressDps : progressDps2;
            this.lbtName2.string = isMvp ? this.initData.playerData2.name : this.initData.playerData.name;
            this.nodeMvpTag2.active = false;
            this.lbtDps2.string = isMvp
                ? NumberUtils.format(this.initData.maxDps2, 1, 0)
                : NumberUtils.format(this.initData.maxDps, 1, 0);
            this.prgDps2.progress = isMvp ? progressDps2 : progressDps;
        }
    }

    /**
     * 监听渲染事件-奖励列表
     * @param nodeItem 列表item
     * @param index 列表index
     */
    protected onRenderEvent(nodeItem: cc.Node, index: number): void {
        index = Math.abs(index);
        const rewardData = this.initData.rewardData[index];
        nodeItem.button(rewardData.itemInfoId);
        ImageUtils.setItemQuality(nodeItem.child("spBg"), rewardData.itemInfoId);
        ImageUtils.setItemIcon(nodeItem.child("spIcon"), rewardData.itemInfoId);
        nodeItem.child("lbtCount").label(rewardData.num !== 1 ? rewardData.num + "" : "");
    }

    /**
     * 道具信息
     * @param event 事件
     * @param itemId 道具id
     */
    protected onClickItemInfo(event: cc.Event.EventTouch, itemId: number): void {
        itemId && ItemUtils.showInfo(itemId);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
        UI.getInstance().close();
        UI.getInstance().close();
        DungeonEquip.getInstance().sendCreateTeam();
    }
}
