import LoadingProgress from "../../nsn/comp/ui/LoadingProgress";
import Channel from "../../nsn/config/Channel";
import Review from "../../nsn/config/Review";
import Token from "../../nsn/config/Token";
import Version from "../../nsn/config/Version";
import Whitelist from "../../nsn/config/Whitelist";
import { IHttpResp } from "../../nsn/core/Http";
import Language from "../../nsn/core/Language";
import Loader from "../../nsn/core/Loader";
import Loading, { LoadingType } from "../../nsn/core/Loading";
import LocalStorage from "../../nsn/core/LocalStorage";
import Reporter from "../../nsn/core/Reporter";
import Data from "../../nsn/data/Data";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import Minigame from "../../nsn/platform/Minigame";
import Platform from "../../nsn/platform/Platform";
import UI from "../../nsn/ui/UI";
import Logger from "../../nsn/util/Logger";
import TextUtils from "../../nsn/util/TextUtils";
import Tips from "../../nsn/util/Tips";
import { HttpError, PlayerInitRet } from "../../protobuf/proto";
import { LocalStorageKey } from "../config/LocalStorageConfig";
import { REPORTER_ID } from "../config/ReporterConfig";
import i18n from "../config/i18n/I18n";
import AntiAddiction from "../core/AntiAddiction";
import Game from "../core/Game";
import { GameProtocol } from "../core/GameProtocol";
import { GameServer, GameServerEvent, GameServerStatus } from "../core/GameServer";
import Login, { LoginConnectScene, LoginEvent } from "../core/Login";
import { Sku } from "../core/Sku";
import TBMainBarrier from "../data/parser/TBMainBarrier";
import TBMainMap from "../data/parser/TBMainMap";
import TBTank from "../data/parser/TBTank";
import DebugEntry from "../debug/DebugEntry";
import Bulletin from "../game/Bulletin";
import DungeonMain from "../game/DungeonMain";
import Player from "../game/Player";
import Tank from "../game/Tank";
import NsnHelp, { NsnHelpEntranceId } from "../sdk/NsnHelp";
import SDKReporter from "../sdk/reporter/SDKReporter";

const { ccclass, property } = cc._decorator;

/**
 * 界面状态
 */
enum UIType {
    Login,
    GetServer,
    Start,
    Entering,
}

@ccclass
export default class BaseLogin extends I18nComponent {
    @property(cc.Node)
    nodeLogo: cc.Node = null;
    @property(cc.Mask)
    nodeLogoLight: cc.Mask = null;
    @property(cc.Node)
    progressNode: cc.Node = null;
    @property(cc.Label)
    lbtVersion: cc.Label = null;
    @property(cc.Node)
    nodeServer: cc.Node = null;
    @property(cc.Label)
    lbtServerName: cc.Label = null;
    @property(cc.Sprite)
    spServerStatus: cc.Sprite = null;
    @property(cc.Node)
    btnLogin: cc.Node = null;
    @property(cc.Node)
    btnStart: cc.Node = null;
    @property(cc.Node)
    btnLogout: cc.Node = null;
    @property(cc.Node)
    btnNotice: cc.Node = null;
    @property(cc.Node)
    btnService: cc.Node = null;
    @property(cc.Node)
    btnFix: cc.Node = null;
    @property(cc.Node)
    copyright: cc.Node = null;
    @property(cc.Node)
    copyrightText: cc.Node = null;
    @property(cc.Node)
    protocol: cc.Node = null;
    @property(cc.Toggle)
    protocolToggle: cc.Toggle = null;
    @property([cc.SpriteFrame])
    spStatus: cc.SpriteFrame[] = [];

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.nodeLogo,
                url: "texture/syncUI/login/spLoginLogo",
            },
            {
                sprite: this.nodeLogoLight,
                url: "texture/syncUI/login/spLoginLogo",
            },
            {
                sprite: this.btnStart,
                url: "texture/syncUI/login/btnLoginStarGame",
            },
        ];
    }

    protected onLoad(): void {
        this.registerHandler();
        this.initUI();

        const token = Token.getInstance().getData();
        if (token) {
            Login.getInstance().checkToken((success: boolean, result: IHttpResp) => {
                if (success) {
                    this.updateUI(UIType.GetServer);
                    GameServer.getInstance().requestGameServer();
                    Login.getInstance().requestAccountBindInfo();
                    SDKReporter.getInstance().login();
                } else {
                    this.updateUI(UIType.Login);
                }
            });
        } else {
            if (Channel.getInstance().getConfig().supportAutoLogin()) {
                Login.getInstance().loginByGuest();
            } else {
                this.updateUI(UIType.Login);
            }
        }

        this.node.getComponent(LoadingProgress).init([
            i18n.loading0001, // 加载本地配置
            i18n.loading0002, // 加载远程配置
            i18n.loading0004, // 加载场景
            i18n.loading0005, // 加载合图
            i18n.loading0006, // 加载预制体
            i18n.loading0007, // 加载节点
            i18n.loading0008, // 加载客户端配置
            i18n.loading0009, // 连接服务器
        ]);
    }

    protected start(): void {
        const logoutResult = Player.getInstance().getLogoutResult();
        if (logoutResult) {
            UI.getInstance().open("FloatAlert", { text: logoutResult });
            Player.getInstance().setLogoutResult(null);
        }
    }

    private registerHandler(): void {
        Login.getInstance().on(
            LoginEvent.LoginSuccess,
            (result: IHttpResp) => {
                this.updateUI(UIType.GetServer);
                GameServer.getInstance().requestGameServer();
                Login.getInstance().requestAccountBindInfo();
                const { param } = result.data;
                const { isNewUser } = param;
                if (isNewUser === "true") {
                    SDKReporter.getInstance().register();
                } else {
                    SDKReporter.getInstance().login();
                }
            },
            this
        );
        Login.getInstance().on(
            LoginEvent.ToggleProtocol,
            () => {
                this.protocolToggle.check();
                this.onToggleProtocol(this.protocolToggle);
            },
            this
        );
        Login.getInstance().on(
            LoginEvent.Connect,
            () => {
                this.connect();
            },
            this
        );
        GameServer.getInstance().on(
            GameServerEvent.SelectServer,
            () => {
                const server = GameServer.getInstance().getCurrent();
                this.lbtServerName.string = GameServer.getInstance().getCurrentName();
                switch (server.status) {
                    case GameServerStatus.Maintenance:
                        this.spServerStatus.spriteFrame = this.spStatus[3];
                        break;
                    case GameServerStatus.Busy:
                        this.spServerStatus.spriteFrame = this.spStatus[1];
                        break;
                    case GameServerStatus.Normal:
                        this.spServerStatus.spriteFrame = this.spStatus[0];
                        break;
                    case GameServerStatus.Hot:
                        this.spServerStatus.spriteFrame = this.spStatus[2];
                        break;
                    default:
                        break;
                }
            },
            this
        );
        GameServer.getInstance().on(
            GameServerEvent.UnSelectServer,
            () => {
                this.lbtServerName.string = i18n.server0004;
                this.spServerStatus.spriteFrame = null;
            },
            this
        );
        GameServer.getInstance().on(
            GameServerEvent.RequestSuccess,
            () => {
                if (Channel.getInstance().getConfig().supportAutoStartGame()) {
                    this.onClickStart();
                } else {
                    this.updateUI(UIType.Start);
                }
            },
            this
        );
        GameServer.getInstance().on(
            GameServerEvent.RequestFailed,
            (errMsg: string) => {
                UI.getInstance().open("FloatInfo", {
                    text: errMsg,
                    confirmText: i18n.common0001,
                    confirmCb: () => {
                        GameServer.getInstance().requestGameServer();
                    },
                    cancelCb: () => {
                        this.updateUI(UIType.Login);
                    },
                });
            },
            this
        );
    }

    private initUI(): void {
        this.btnFix.active = Review.getInstance().getPassReview();
        const copyright = Channel.getInstance().getConfig().getCopyRight();
        if (copyright) {
            this.copyrightText.label(copyright);
        }
        const supportProtocol = Channel.getInstance().getConfig().supportProtocol();
        if (supportProtocol) {
            const isChecked = LocalStorage.getInstance().getItem(LocalStorageKey.Protocol, true) === "1";
            this.protocolToggle.isChecked = isChecked;
        }
        this.lbtVersion.string = TextUtils.format(i18n.login0001, Version.getInstance().getGameVersion());

        cc.tween(this.btnStart)
            .repeatForever(cc.tween().to(1.5, { scale: 1.03 }).to(1.5, { scale: 0.97 }))
            .start();
    }

    private updateUI(type: UIType): void {
        switch (type) {
            case UIType.Login:
                this.btnLogin.active = true;
                this.nodeServer.active = false;
                this.btnStart.active = false;
                this.progressNode.active = false;
                this.protocol.active = Channel.getInstance().getConfig().supportProtocol();

                this.btnLogout.active = false;
                this.btnNotice.active = false;
                this.btnService.active = Channel.getInstance().getConfig().supportNsnHelp();
                this.copyright.active = !!Channel.getInstance().getConfig().getCopyRight();
                break;
            case UIType.GetServer:
                this.btnLogin.active = false;
                this.nodeServer.active = false;
                this.btnStart.active = false;
                this.progressNode.active = false;
                this.protocol.active = false;

                this.btnLogout.active = true;
                this.btnNotice.active = false;
                this.btnService.active = Channel.getInstance().getConfig().supportNsnHelp();
                this.copyright.active = !!Channel.getInstance().getConfig().getCopyRight();
                break;
            case UIType.Start:
                this.btnLogin.active = false;
                this.nodeServer.active = true;
                this.btnStart.active = true;
                this.progressNode.active = false;
                this.protocol.active = false;

                this.btnLogout.active = true;
                this.btnNotice.active = true;
                this.btnService.active = Channel.getInstance().getConfig().supportNsnHelp();
                this.copyright.active = !!Channel.getInstance().getConfig().getCopyRight();
                break;
            case UIType.Entering:
                this.btnLogin.active = false;
                this.nodeServer.active = false;
                this.btnStart.active = false;
                this.progressNode.active = true;
                this.protocol.active = false;

                this.btnLogout.active = false;
                this.btnNotice.active = false;
                this.btnService.active = false;
                this.copyright.active = false;
                break;
            default:
                break;
        }
    }

    protected onClickStart(): void {
        Reporter.logClickEvent(REPORTER_ID.CLICK.START.GAME);
        const server = GameServer.getInstance().getCurrent();
        if (!server) {
            Tips.getInstance().show(i18n.server0005);
            return;
        }
        if (Channel.getInstance().getConfig().supportSdkRealNameCert()) {
            Channel.getInstance().getSdk().queryRealNameInfo();
        } else {
            this.connect();
        }
    }

    private connect(): void {
        const server = GameServer.getInstance().getCurrent();
        Login.getInstance().connect(
            {
                serverId: server.serverLogicId,
                language: Language.getInstance().getLanguage(),
                isWhiteDevice: Whitelist.getInstance().getWhitelist(),
                isRealName: AntiAddiction.getInstance().isRealName(),
                isAdult: AntiAddiction.getInstance().isAdult(),
                scene: LoginConnectScene.Login,
            },
            (success: boolean, result: IHttpResp) => {
                if (success) {
                    this.updateUI(UIType.Entering);
                    Loading.getInstance().start({
                        socketUrl: server.url,
                        httpUrl: server.httpUrl,
                        serverId: server.serverLogicId,
                        socketParams: [result.data.socketToken],
                    });
                } else {
                    switch (parseInt(result.code)) {
                        case HttpError.ErrorResult.RealNameFirst:
                            UI.getInstance().open("PopupRealNameCert");
                            break;
                        case HttpError.ErrorResult.AdultAllowOnly:
                            UI.getInstance().open("FloatAlert", { text: i18n.antiAddiction0003 });
                            break;
                        case HttpError.ErrorResult.LowClientVer:
                            const isWechatMinigame = Platform.getInstance().isWechatMinigame();
                            if (isWechatMinigame) {
                                wx.showModal({
                                    title: i18n.common0041,
                                    content: i18n.common0086,
                                    showCancel: false,
                                    confirmText: i18n.common0013,
                                    complete: () => {
                                        Minigame.getInstance().restart();
                                    },
                                });
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        );
    }

    protected onClickServer(): void {
        UI.getInstance().open("PopupGameServer");
    }

    protected onProgressStart(): void {
        Reporter.logTriggerEvent(REPORTER_ID.TRIGGER.LOADING.START);
    }

    protected onProgress(progress: number, type: LoadingType): void {
        switch (type) {
            case LoadingType.RemoteData:
                Logger.info("远程读表", JSON.stringify(Data.getInstance().getRemoteNames()));
                Sku.getInstance().query();
                break;
            default:
                break;
        }
    }

    protected async onProgressEnd(): Promise<void> {
        Reporter.logTriggerEvent(REPORTER_ID.TRIGGER.LOADING.SUCCESS);
        await this.preloadRes();
        cc.director.loadScene("GameScene");
    }

    protected onProgressError(errMsg: string): void {
        Reporter.logTriggerEvent(REPORTER_ID.TRIGGER.LOADING.FAILED, errMsg);
        switch (errMsg) {
            case i18n.playerInitRet[PlayerInitRet.ErrorResult.LowClientVer]:
                UI.getInstance().open("FloatInfo", {
                    text: errMsg,
                    confirmText: i18n.common0013,
                    confirmCb: () => {
                        Game.getInstance().end();
                    },
                    cancelCb: () => {
                        this.updateUI(UIType.Start);
                    },
                });
                break;
            case i18n.playerInitRet[PlayerInitRet.ErrorResult.ForceUpdateNewAppVer]:
                UI.getInstance().open("FloatInfo", {
                    text: errMsg,
                    confirmText: i18n.common0013,
                    confirmCb: () => {
                        this.updateUI(UIType.Start);
                        Channel.getInstance().getSdk().gotoAppStore();
                    },
                    cancelCb: () => {
                        this.updateUI(UIType.Start);
                    },
                });
                break;
            default:
                UI.getInstance().open("FloatInfo", {
                    text: errMsg,
                    confirmText: i18n.common0001,
                    confirmCb: () => {
                        this.connect();
                    },
                    cancelCb: () => {
                        this.updateUI(UIType.Start);
                    },
                });
                break;
        }
    }

    /**
     * 预加载资源
     */
    private async preloadRes(): Promise<void> {
        Logger.info("游戏加载", "预加载资源...");

        const promise: Promise<void>[] = [];

        // 主线副本-背景
        const levelId = DungeonMain.getInstance().getLevelId();
        const levelInfo = TBMainBarrier.getInstance().getDataById(levelId);
        const mapInfo = TBMainMap.getInstance().getDataById(levelInfo.map);
        if (mapInfo.res[1] && mapInfo.res[1].length !== 0) {
            promise.push(
                new Promise<void>((resolve) => {
                    Loader.getInstance().loadSpriteFrame(
                        `texture/dungeonSceneBg/main/spMIMap${mapInfo.res[0][0]}Far${mapInfo.res[1][0]}`,
                        () => resolve(),
                        () => resolve()
                    );
                })
            );
        }
        if (mapInfo.res[2] && mapInfo.res[2].length !== 0) {
            promise.push(
                new Promise<void>((resolve) => {
                    Loader.getInstance().loadSpriteFrame(
                        `texture/dungeonSceneBg/main/spMIMap${mapInfo.res[0][0]}Mid${mapInfo.res[2][0]}`,
                        () => resolve(),
                        () => resolve()
                    );
                })
            );
        }
        if (mapInfo.res[3] && mapInfo.res[3].length !== 0) {
            promise.push(
                new Promise<void>((resolve) => {
                    Loader.getInstance().loadSpriteFrame(
                        `texture/dungeonSceneBg/main/spMIMap${mapInfo.res[0][0]}Near${mapInfo.res[3][0]}`,
                        () => resolve(),
                        () => resolve()
                    );
                })
            );
        }

        // 战车
        const tankId = Tank.getInstance().getTeamId();
        const tankInfo = TBTank.getInstance().getDataById(tankId);
        promise.push(
            new Promise<void>((resolve) => {
                Loader.getInstance().loadSpine(
                    `spine/tank/efTank${tankInfo.res}`,
                    () => resolve(),
                    () => resolve()
                );
            })
        );

        await Promise.allSettled(promise);
    }

    protected onToggleProtocol(toggle: cc.Toggle): void {
        LocalStorage.getInstance().setItem(LocalStorageKey.Protocol, toggle.isChecked ? "1" : "0", true);
    }

    protected onClickUserProtocol(): void {
        GameProtocol.openUserProtocol();
    }

    protected onCLickSensitiveProtocol(): void {
        GameProtocol.openSensitiveProtocol();
    }

    /**
     * 点击公告
     */
    protected onClickBulletin(): void {
        Bulletin.getInstance().request(() => {
            const bi = Bulletin.getInstance().getBulletinInfo();
            if (!bi.length) {
                Tips.getInstance().show(i18n.bulletin0001);
            } else {
                UI.getInstance().open("PopupBulletin");
            }
        });
    }

    /**
     * 登出
     */
    protected onClickLogout(): void {
        Login.getInstance().clear();
        Channel.getInstance().getSdk().logout();
        AntiAddiction.getInstance().clear();
        this.updateUI(UIType.Login);
    }

    /**
     * 点击修复热更
     * @returns
     */
    protected onClickFix(): void {
        Game.getInstance().fix();
    }

    /**
     * 点击社交
     */
    protected onClickSocial(): void {
        const socials = Channel.getInstance().getConfig().getSocialType();
        if (!socials.length) {
            return;
        }
        UI.getInstance().open("PopupSocial");
    }

    /**
     * 打开客服界面
     */
    protected onClickService(): void {
        NsnHelp.getInstance().show(NsnHelpEntranceId.Login);
    }

    protected onClickDebug(): void {
        const comp = this.nodeLogo.getComponent(DebugEntry);
        if (comp) {
            comp.increaseSecretTouchCount1();
        }
    }
}
