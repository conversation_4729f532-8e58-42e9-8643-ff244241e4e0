/*
 * @Author: chenx
 * @Date: 2025-01-23 15:21:25
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-16 14:20:07
 */
import DropMenu, { IDrawMenuInfo } from "../../nsn/comp/ui/DropMenu";
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import i18n from "../config/i18n/I18n";
import { EnumAttributeNature } from "../data/base/BaseAttribute";
import DataAttribute from "../data/extend/DataAttribute";
import TBAttribute from "../data/parser/TBAttribute";
import Attribute, { AttrSourceType } from "../game/Attribute";
import CombatScore from "../game/CombatScore";
import CombatScoreUtils from "../utils/CombatScoreUtils";

/**
 * 下拉菜单index-筛选
 */
enum DropMenuIndex {
    Total = 0, // 总
    Power, // 王权
    Lead, // 角色
    Equip, // 装备
    Archer, // 弓箭手
    Magic, // 魔法
    Tank, // 战车
    Pet, // 宠物
    Weapon, // 神器
    Wing, // 背饰
    Talent, // 天赋
    Bless, // 祝福
    Forge, // 锻造台
    Collection, // 藏品
    Other, // 其他
}

const { ccclass, property } = cc._decorator;

/**
 * 属性展示-总
 */
@ccclass
export default class PopupAttrShowTotal extends I18nComponent {
    @property(cc.Node)
    nodeCombatScore: cc.Node = null; // 战斗力
    @property(DropMenu)
    dropMenuFilter: DropMenu = null; // 下拉菜单-筛选
    @property(cc.Node)
    nodeFilter: cc.Node = null; // 筛选按钮
    @property(cc.Node)
    nodeFinal: cc.Node = null; // 最终属性
    @property(cc.Node)
    nodeFinalContent: cc.Node = null; // 最终属性content
    @property(cc.Node)
    nodeSecond: cc.Node = null; // 二级属性
    @property(cc.Node)
    nodeSecondContent: cc.Node = null; // 二级属性content
    @property(cc.Node)
    nodeMain: cc.Node = null; // 主属性
    @property(cc.Node)
    nodeMainContent: cc.Node = null; // 主属性content
    @property(cc.Node)
    nodeAttrItem: cc.Node = null; // 属性item
    @property(cc.Node)
    nodeDesc: cc.Node = null; // 描述
    @property(cc.Node)
    nodeDescBg: cc.Node = null; // 描述bg
    @property(cc.Label)
    lbtDesc: cc.Label = null; // 描述

    private dropMenuIndex: number = -1; // 下拉菜单index-筛选
    private isFilter: boolean = false; // 是否筛选

    protected onLoad(): void {
        this.nodeAttrItem.parent = null;

        this.initInfo();
        this.updateAttrInfo();
    }

    protected onDestroy(): void {
        this.nodeAttrItem.destroy();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        CombatScoreUtils.update(this.nodeCombatScore, CombatScore.getInstance().getScore());

        const dropMenuData: IDrawMenuInfo = {
            info: [
                i18n.equip0024,
                i18n.power0012,
                i18n.system0001,
                i18n.system0002,
                i18n.partner0001,
                i18n.system0004,
                i18n.system0005,
                i18n.system0006,
                i18n.system0007,
                i18n.system0008,
                i18n.system0009,
                i18n.system0010,
                i18n.system0003,
                i18n.system0011,
                i18n.system0012,
            ],
        };
        this.dropMenuFilter.maxShowItemCount = dropMenuData.info.length;
        this.dropMenuFilter.initDropMenuInfo(dropMenuData);
        this.dropMenuIndex = DropMenuIndex.Total;
        this.dropMenuFilter.setDefaultIndex(this.dropMenuIndex);

        this.nodeFilter.child("spBox").child("spIcon").active = this.isFilter;
    }

    /**
     * 更新属性信息
     */
    private updateAttrInfo(): void {
        const baseAttr: { [attrId: number]: { value: number; info: DataAttribute } } = {};
        const allAttrInfo = TBAttribute.getInstance().getList();
        allAttrInfo.forEach((e) => {
            switch (e.nature) {
                case EnumAttributeNature.FinalAttribute:
                    this.dropMenuIndex === DropMenuIndex.Total && (baseAttr[e.id] = { value: 0, info: e });
                    break;
                case EnumAttributeNature.SecondAttribute:
                case EnumAttributeNature.MainAttribute:
                    baseAttr[e.id] = { value: 0, info: e };
                    break;
                default:
                    break;
            }
        });
        let attr: { [attrId: number]: number } = null;
        switch (this.dropMenuIndex) {
            case DropMenuIndex.Total:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Total);
                break;
            case DropMenuIndex.Power:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Power);
                break;
            case DropMenuIndex.Lead:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Lead);
                break;
            case DropMenuIndex.Equip:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Equip);
                break;
            case DropMenuIndex.Archer:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Archer);
                break;
            case DropMenuIndex.Magic:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Magic);
                break;
            case DropMenuIndex.Tank:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Tank);
                break;
            case DropMenuIndex.Pet:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Pet);
                break;
            case DropMenuIndex.Weapon:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Weapon);
                break;
            case DropMenuIndex.Wing:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Wing);
                break;
            case DropMenuIndex.Talent:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Talent);
                break;
            case DropMenuIndex.Bless:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Bless);
                break;
            case DropMenuIndex.Forge:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Forge);
                break;
            case DropMenuIndex.Collection:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Collection);
                break;
            case DropMenuIndex.Other:
                attr = Attribute.getInstance().getShowAttr(AttrSourceType.Other);
                break;
            default:
                break;
        }
        for (const key in attr) {
            if (baseAttr[key]) {
                baseAttr[key].value = attr[key];
            }
        }
        const finalData: [number, number, DataAttribute][] = [];
        const secondData: [number, number, DataAttribute][] = [];
        const mainData: [number, number, DataAttribute][] = [];
        for (const key in baseAttr) {
            const attrId = parseInt(key);
            const tempBaseAttr = baseAttr[key];

            if (tempBaseAttr.info.show === 0) {
                continue;
            }
            if (this.isFilter && tempBaseAttr.value === 0) {
                continue;
            }

            switch (tempBaseAttr.info.nature) {
                case EnumAttributeNature.FinalAttribute:
                    finalData.push([attrId, tempBaseAttr.value, tempBaseAttr.info]);
                    break;
                case EnumAttributeNature.SecondAttribute:
                    secondData.push([attrId, tempBaseAttr.value, tempBaseAttr.info]);
                    break;
                case EnumAttributeNature.MainAttribute:
                    mainData.push([attrId, tempBaseAttr.value, tempBaseAttr.info]);
                    break;
                default:
                    break;
            }
        }

        this.nodeFinal.active = finalData.length !== 0;
        this.nodeFinalContent.destroyAllChildren();
        finalData.sort(([, , attrInfoA], [, , attrInfoB]) => attrInfoA.sort - attrInfoB.sort);
        for (const [attrId, attrValue, attrInfo] of finalData) {
            const nodeItem = Loader.getInstance().instantiate(this.nodeAttrItem);
            this.nodeFinalContent.addChild(nodeItem);

            nodeItem.button(attrInfo.desc);
            const formatData = TBAttribute.getInstance().formatAttribute([attrId, attrValue]);
            nodeItem.child("lbtName").label(formatData.name);
            nodeItem.child("lbtValue").label(formatData.value);
        }

        this.nodeSecond.active = secondData.length !== 0;
        this.nodeSecondContent.destroyAllChildren();
        secondData.sort(([, , attrInfoA], [, , attrInfoB]) => attrInfoA.sort - attrInfoB.sort);
        for (const [attrId, attrValue, attrInfo] of secondData) {
            const nodeItem = Loader.getInstance().instantiate(this.nodeAttrItem);
            this.nodeSecondContent.addChild(nodeItem);

            nodeItem.button(attrInfo.desc);
            const formatData = TBAttribute.getInstance().formatAttribute([attrId, attrValue]);
            nodeItem.child("lbtName").label(formatData.name);
            nodeItem.child("lbtValue").label(formatData.value);
        }

        this.nodeMain.active = mainData.length !== 0;
        this.nodeMainContent.destroyAllChildren();
        mainData.sort(([, , attrInfoA], [, , attrInfoB]) => attrInfoA.sort - attrInfoB.sort);
        for (const [attrId, attrValue, attrInfo] of mainData) {
            const nodeItem = Loader.getInstance().instantiate(this.nodeAttrItem);
            this.nodeMainContent.addChild(nodeItem);

            nodeItem.button(attrInfo.desc);
            const formatData = TBAttribute.getInstance().formatAttribute([attrId, attrValue]);
            nodeItem.child("lbtName").label(formatData.name);
            nodeItem.child("lbtValue").label(formatData.value);
        }
    }

    /**
     * 显示描述
     * @param event
     * @param desc 描述
     */
    protected onClickShowDesc(event: cc.Event.EventTouch, desc: string): void {
        if (!desc) {
            return;
        }

        this.nodeDesc.active = true;
        const nodeItem = event.getCurrentTarget();
        let pos = nodeItem.convertToWorldSpaceAR(cc.v2(0, nodeItem.height / 4));
        pos = this.nodeDesc.convertToNodeSpaceAR(pos);
        this.nodeDescBg.setPosition(pos);
        this.lbtDesc.string = desc;
    }

    /**
     * 关闭描述
     */
    protected onClickCloseDesc(): void {
        this.nodeDesc.active = false;
    }

    /**
     * 选中下拉菜单项-筛选
     * @param dropMenuIndex 下拉菜单index
     */
    protected onClickSelectDropMenuItem(dropMenuIndex: number): void {
        if (this.dropMenuIndex === dropMenuIndex) {
            return;
        }

        this.dropMenuIndex = dropMenuIndex;
        this.updateAttrInfo();
    }

    /**
     * 筛选
     */
    protected onClickFilter(): void {
        this.isFilter = !this.isFilter;
        this.nodeFilter.child("spBox").child("spIcon").active = this.isFilter;
        this.updateAttrInfo();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
