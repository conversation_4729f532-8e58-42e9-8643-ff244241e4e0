/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-24 14:13:34
 */

import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { EnumUnionPara } from "../data/base/BaseUnion";
import TBUnion from "../data/parser/TBUnion";
import Player from "../game/Player";
import UnionSiege, { EnumChallengeDifficulty, UNION_DEFENSE_EXT_ADD_START } from "../game/UnionSiege";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatActivityUnionDefenseChallengeResult extends I18nComponent {
    @property(cc.Node)
    spTitle1: cc.Node = null;
    @property(cc.Node)
    spTitle2: cc.Node = null;

    @property(cc.Node)
    nodePlayer1: cc.Node = null; // 我方
    @property(cc.Node)
    nodePlayer2: cc.Node = null; // 敌方

    @property(cc.Node)
    layout: cc.Node = null;

    @property(cc.Node)
    reward: cc.Node = null;
    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private data: {
        type: EnumUnionPara;
        rivalUnionId: number;
        rivalRoleId: string;
        isWin?: boolean;
        challengeDifficulty?: EnumChallengeDifficulty;
    } = null;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle1,
                url: "texture/result/spResultTitle12",
            },
            {
                sprite: this.spTitle2,
                url: "texture/result/spResultTitle13",
            },
        ];
    }

    protected onLoad(): void {
        this.data = this.args;
        this.updateUI();
    }

    protected registerHandler(): void {}

    private updateUI(): void {
        const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
        const playerInfo = Player.getInstance().getInfo();
        const rivalUnionInfo = unionSiegeInfo.find((e) => e.unionId === this.data.rivalUnionId); // 对方公会
        const findRival = rivalUnionInfo.siegeDetailInfos.find((e) => e.playerInfo.playerId === this.data.rivalRoleId);

        if (this.data.type === EnumUnionPara.UnionDefenseChallengeFail) {
            this.spTitle1.active = false;
            this.spTitle2.active = true;
        } else {
            this.spTitle1.active = true;
            this.spTitle2.active = false;
        }

        PlayerInfoUtils.updateHead(this.nodePlayer1.child("nodeHead"), playerInfo);
        PlayerInfoUtils.updateHead(this.nodePlayer2.child("nodeHead"), findRival.playerInfo);
        this.nodePlayer1.child("lbtName").label(playerInfo.name);
        this.nodePlayer2.child("lbtName").label(findRival.playerInfo.name);

        const unionData = TBUnion.getInstance().getValueByPara(this.data.type); // 奖励数据
        ItemUtils.refreshView(this.reward, this.prefabItem, unionData);

        const star = this.layout.child("star");
        const count = this.layout.child("count");
        if (this.data.isWin && this.data.type === EnumUnionPara.UnionDefenseChallengeWin) {
            this.layout.active = true;
            const detailObj = UnionSiege.getInstance().getUnionSiegeDetailObjByUnionIdAndPlayerId(
                this.data.rivalUnionId,
                this.data.rivalRoleId
            );
            const num = UnionSiege.getInstance().getStarCount(detailObj.memberType, this.data.challengeDifficulty);
            ImageUtils.setUnionDefenseStar(star, true);
            count.label("+" + num);
        } else if (this.data.type === EnumUnionPara.UnionDefenseChallengeSweeps) {
            this.layout.active = true;
            ImageUtils.setUnionDefenseStar(star, true);
            count.label("+" + UNION_DEFENSE_EXT_ADD_START);
        } else {
            this.layout.active = false;
        }
    }

    /**
     * 点击关闭
     */
    protected onClickClose(): void {
        if (UI.getInstance().isExist("UIDungeonCombatUnionDefense")) {
            UI.getInstance().closeToWindow("UIDungeonCombatUnionDefense");
        }
        UI.getInstance().close();
    }
}
