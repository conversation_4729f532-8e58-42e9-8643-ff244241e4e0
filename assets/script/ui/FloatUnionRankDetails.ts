/*
 * @Author: zhangwj
 * @Date: 2023-11-7 10:22:13
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-15 14:01:30
 */

import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import Platform from "../../nsn/platform/Platform";
import UI from "../../nsn/ui/UI";
import { IUnionInfo, IUnionMember, UnionGetRet } from "../../protobuf/proto";
import TBUnionLevel from "../data/parser/TBUnionLevel";
import Union from "../game/Union";
import ImageUtils from "../utils/ImageUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatUnionRankDetails extends I18nComponent {
    @property(cc.Node)
    flag: cc.Node = null;
    @property(cc.Node)
    unionId: cc.Node = null;
    @property(cc.Node)
    unionLv: cc.Node = null;
    @property(cc.Node)
    unionName: cc.Node = null;
    @property(cc.Node)
    unionCount: cc.Node = null;
    @property(cc.Node)
    leadName: cc.Node = null;
    @property(cc.Node)
    unionDetails: cc.Node = null;
    @property(ListView)
    list: ListView = null;

    private unionInfoId: number = 0;

    protected onLoad(): void {
        this.unionInfoId = this.args;

        UI.getInstance().showSoftLoading();
        Union.getInstance().sendUnionGet(this.unionInfoId);
    }

    protected registerHandler(): void {
        Union.getInstance().on(
            UnionGetRet.prototype.clazzName,
            (unionInfo: IUnionInfo, members: IUnionMember[]) => {
                UI.getInstance().hideSoftLoading();
                this.updateUI(unionInfo, members);
                this.updateList(members);
            },
            this
        );
    }

    private updateUI(unionInfo: IUnionInfo, members: IUnionMember[]): void {
        const level = TBUnionLevel.getInstance().getLevelByExp(unionInfo.exp);
        const data = TBUnionLevel.getInstance().getDataById(level);

        ImageUtils.setUnionFlag(this.flag, unionInfo.flag);
        this.unionId.label("ID：" + unionInfo.id);
        this.unionLv.label("Lv." + level);
        this.unionName.label(unionInfo.name);
        this.unionCount.label(members.length + "/" + data.peopleLimit);
        this.leadName.label(unionInfo.ownerName);
        this.unionDetails.label(unionInfo.publicize);
    }

    private updateList(members: IUnionMember[]): void {
        members.sort((a, b) => {
            if (a.unionPositionId !== b.unionPositionId) {
                return a.unionPositionId - b.unionPositionId;
            }
            return b.player.combatScore - a.player.combatScore;
        });
        this.list.setListData(members);
    }

    protected onClickCopy(): void {
        Platform.getInstance().copyToClipboard(this.unionInfoId + "");
    }

    /**
     * 关闭UI
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
