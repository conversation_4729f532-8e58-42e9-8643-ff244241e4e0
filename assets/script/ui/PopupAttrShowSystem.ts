/*
 * @Author: chenx
 * @Date: 2025-01-23 15:21:31
 * @Last Modified by: chenx
 * @Last Modified time: 2025-01-24 15:21:48
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { EnumAttributeNature } from "../data/base/BaseAttribute";
import DataAttribute from "../data/extend/DataAttribute";
import TBAttribute from "../data/parser/TBAttribute";
import Attribute, { AttrSourceType } from "../game/Attribute";

const { ccclass, property } = cc._decorator;

/**
 * 属性展示-系统
 */
@ccclass
export default class PopupAttrShowSystem extends I18nComponent {
    @property(cc.Node)
    nodeFinal: cc.Node = null; // 最终属性
    @property(cc.Node)
    nodeFinalContent: cc.Node = null; // 最终属性content
    @property(cc.Node)
    nodeSecond: cc.Node = null; // 二级属性
    @property(cc.Node)
    nodeSecondContent: cc.Node = null; // 二级属性content
    @property(cc.Node)
    nodeMain: cc.Node = null; // 主属性
    @property(cc.Node)
    nodeMainContent: cc.Node = null; // 主属性content
    @property(cc.Node)
    nodeAttrItem: cc.Node = null; // 属性item
    @property(cc.Node)
    nodeNoInfo: cc.Node = null; // 无信息
    @property(cc.Node)
    nodeDesc: cc.Node = null; // 描述
    @property(cc.Node)
    nodeDescBg: cc.Node = null; // 描述bg
    @property(cc.Label)
    lbtDesc: cc.Label = null; // 描述

    private sourceType: AttrSourceType = null; // 来源类型

    protected onLoad(): void {
        this.nodeAttrItem.parent = null;

        this.sourceType = this.args;
        this.initInfo();
    }

    protected onDestroy(): void {
        this.nodeAttrItem.destroy();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        const attr = Attribute.getInstance().getShowAttr(this.sourceType);
        const finalData: [number, number, DataAttribute][] = [];
        const secondData: [number, number, DataAttribute][] = [];
        const mainData: [number, number, DataAttribute][] = [];
        for (const key in attr) {
            const attrId = parseInt(key);

            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
            if (attrInfo.show === 0) {
                continue;
            }

            switch (attrInfo.nature) {
                case EnumAttributeNature.FinalAttribute:
                    finalData.push([attrId, attr[key], attrInfo]);
                    break;
                case EnumAttributeNature.SecondAttribute:
                    secondData.push([attrId, attr[key], attrInfo]);
                    break;
                case EnumAttributeNature.MainAttribute:
                    mainData.push([attrId, attr[key], attrInfo]);
                    break;
                default:
                    break;
            }
        }

        this.nodeFinal.active = finalData.length !== 0;
        finalData.sort(([, , attrInfoA], [, , attrInfoB]) => attrInfoA.sort - attrInfoB.sort);
        for (const [attrId, attrValue, attrInfo] of finalData) {
            const nodeItem = Loader.getInstance().instantiate(this.nodeAttrItem);
            this.nodeFinalContent.addChild(nodeItem);

            nodeItem.button(attrInfo.desc);
            const formatData = TBAttribute.getInstance().formatAttribute([attrId, attrValue]);
            nodeItem.child("lbtName").label(formatData.name);
            nodeItem.child("lbtValue").label(formatData.value);
        }

        this.nodeSecond.active = secondData.length !== 0;
        secondData.sort(([, , attrInfoA], [, , attrInfoB]) => attrInfoA.sort - attrInfoB.sort);
        for (const [attrId, attrValue, attrInfo] of secondData) {
            const nodeItem = Loader.getInstance().instantiate(this.nodeAttrItem);
            this.nodeSecondContent.addChild(nodeItem);

            nodeItem.button(attrInfo.desc);
            const formatData = TBAttribute.getInstance().formatAttribute([attrId, attrValue]);
            nodeItem.child("lbtName").label(formatData.name);
            nodeItem.child("lbtValue").label(formatData.value);
        }

        this.nodeMain.active = mainData.length !== 0;
        mainData.sort(([, , attrInfoA], [, , attrInfoB]) => attrInfoA.sort - attrInfoB.sort);
        for (const [attrId, attrValue, attrInfo] of mainData) {
            const nodeItem = Loader.getInstance().instantiate(this.nodeAttrItem);
            this.nodeMainContent.addChild(nodeItem);

            nodeItem.button(attrInfo.desc);
            const formatData = TBAttribute.getInstance().formatAttribute([attrId, attrValue]);
            nodeItem.child("lbtName").label(formatData.name);
            nodeItem.child("lbtValue").label(formatData.value);
        }

        this.nodeNoInfo.active = finalData.length === 0 && secondData.length === 0 && mainData.length === 0;
    }

    /**
     * 显示描述
     * @param event
     * @param desc 描述
     */
    protected onClickShowDesc(event: cc.Event.EventTouch, desc: string): void {
        if (!desc) {
            return;
        }

        this.nodeDesc.active = true;
        const nodeItem = event.getCurrentTarget();
        let pos = nodeItem.convertToWorldSpaceAR(cc.v2(0, nodeItem.height / 4));
        pos = this.nodeDesc.convertToNodeSpaceAR(pos);
        this.nodeDescBg.setPosition(pos);
        this.lbtDesc.string = desc;
    }

    /**
     * 关闭描述
     */
    protected onClickCloseDesc(): void {
        this.nodeDesc.active = false;
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
