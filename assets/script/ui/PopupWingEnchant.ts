/*
 * @Author: JackyF<PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-17 14:27:50
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { WingsEnchantUpgradeRet } from "../../protobuf/proto";
import DissolveEffect, { EnumDissolveMode } from "../comp/DissolveEffect";
import i18n from "../config/i18n/I18n";
import TBAttribute from "../data/parser/TBAttribute";
import TBWingEnchant from "../data/parser/TBWingEnchant";
import Bag from "../game/Bag";
import Wing from "../game/Wing";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import TweenUtil from "../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupWingEnchant extends I18nComponent {
    @property(cc.Node)
    enchantNode: cc.Node = null;

    @property(cc.Label)
    title: cc.Label = null;
    @property(cc.Node)
    attr: cc.Node = null;

    @property(cc.Node)
    upgradeCost: cc.Node = null;
    @property(cc.Node)
    costIcon: cc.Node = null;
    @property(cc.Node)
    costCount: cc.Node = null;
    @property(cc.Node)
    btnUpgrade: cc.Node = null;
    @property(cc.Node)
    btnLight: cc.Node = null;
    @property(cc.Node)
    btnMax: cc.Node = null;
    @property(sp.Skeleton)
    upgradeLight: sp.Skeleton = null;

    private curType: number = 1;

    protected onLoad(): void {
        for (let i = 0; i < this.enchantNode.childrenCount; i++) {
            this.enchantNode.children[i].button(i + 1);
        }
        this.initUI();
        this.updateUI();
    }

    protected registerHandler(): void {
        Wing.getInstance().on(
            WingsEnchantUpgradeRet.prototype.clazzName,
            () => {
                this.updateUI();
                this.playAnimation();
            },
            this
        );
    }

    private initUI(): void {
        const uv = [
            [0, 0.5, 0, 0.5],
            [0.5, 1, 0, 0.5],
            [0, 0.5, 0.5, 1],
            [0.5, 1, 0.5, 1],
        ];
        for (let i = 0; i < this.enchantNode.childrenCount; i++) {
            const icon = this.enchantNode.children[i].child("icon");
            const comp1 = icon.getComponent(DissolveEffect);
            comp1.setValue(1);
            comp1.setTexUVOffset(uv[i]);
            this.playEnchantAnimation(icon, i === 0 ? EnumDissolveMode.Show : EnumDissolveMode.Hide);

            const select = this.enchantNode.children[i].child("select");
            const comp2 = select.getComponent(DissolveEffect);
            comp2.setValue(1);
            comp2.setTexUVOffset(uv[i]);
            this.playEnchantAnimation(select, i === 0 ? EnumDissolveMode.Show : EnumDissolveMode.Hide);
        }
    }

    private updateUI(): void {
        this.updateEnchant();
        this.updateAttr();
        this.updateBtns();
    }

    private updateEnchant(): void {
        for (let i = 0; i < this.enchantNode.childrenCount; i++) {
            const node = this.enchantNode.children[i];
            const textBg = node.child("textBg");
            const text = node.child("text");
            const red = node.child("red");

            const type = i + 1;
            const level = Wing.getInstance().getEnchantLevelByType(type);
            text.label("Lv." + level);
            textBg.color = type === this.curType ? "#3A1D17" : "#5C3820";
            text.color = type === this.curType ? "#ECBD94" : "#B28054";

            const nextLevel = level + 1;
            const nextData = TBWingEnchant.getInstance().getDataByTypeAndLevel(type, nextLevel);
            if (!nextData) {
                red.active = false;
            } else {
                const data = TBWingEnchant.getInstance().getDataByTypeAndLevel(type, level);
                const upgradeCost = data.upgradeCost[0];
                red.active = Bag.getInstance().isEnough(upgradeCost[0], upgradeCost[1]);
            }
        }
    }

    private updateAttr(): void {
        const level = Wing.getInstance().getEnchantLevelByType(this.curType);
        const data = TBWingEnchant.getInstance().getDataByTypeAndLevel(this.curType, level);
        const nextLevel = level + 1;
        const nextData = TBWingEnchant.getInstance().getDataByTypeAndLevel(this.curType, nextLevel);

        this.title.string = data.name;
        const title2 = this.attr.child("title2");
        const title3 = this.attr.child("title3");
        const values = this.attr.child("attr");
        const v1 = values.child("v1");
        const v2 = values.child("v2");
        const v3 = values.child("v3");
        const arrow = values.child("arrow");
        title2.label(TextUtils.format(i18n.common0029, level));
        const stepAttribute = data.stepAttribute[0];
        const { name, value } = TBAttribute.getInstance().formatAttribute([
            stepAttribute[0],
            stepAttribute[1] + stepAttribute[2] * (level - data.levelInterval[0]),
        ]);
        v1.label(name);
        v2.label(value);

        if (nextData) {
            title3.active = true;
            v3.active = true;
            arrow.active = true;
            title2.x = 0;
            v2.x = 0;

            title3.label(TextUtils.format(i18n.common0029, nextLevel));
            const nextStepAttribute = nextData.stepAttribute[0];
            const { value: nextValue } = TBAttribute.getInstance().formatAttribute([
                nextStepAttribute[0],
                nextStepAttribute[1] + nextStepAttribute[2] * (nextLevel - nextData.levelInterval[0]),
            ]);
            v3.label(nextValue);
        } else {
            title3.active = false;
            v3.active = false;
            arrow.active = false;
            title2.x = 290;
            v2.x = 290;
        }
    }

    private updateBtns(): void {
        const level = Wing.getInstance().getEnchantLevelByType(this.curType);
        const list = TBWingEnchant.getInstance().getDataByType(this.curType);
        const maxLevel = list[list.length - 1].levelInterval[1];
        if (level >= maxLevel) {
            this.btnUpgrade.active = false;
            this.upgradeCost.active = false;
            this.btnMax.active = true;
        } else {
            this.btnUpgrade.active = true;
            this.upgradeCost.active = true;
            this.btnMax.active = false;
            const data = TBWingEnchant.getInstance().getDataByTypeAndLevel(this.curType, level);
            const upgradeCost = data.upgradeCost[0];
            ImageUtils.setItemIcon(this.costIcon, upgradeCost[0]);
            ItemUtils.refreshCount(this.costCount, upgradeCost[0], upgradeCost[1]);
            this.btnLight.active = Bag.getInstance().isEnough(upgradeCost[0], upgradeCost[1]);
            if (this.btnLight.active) {
                this.btnLight.stopAllActions();
                TweenUtil.breath(this.btnLight);
            }
        }
    }

    private playAnimation(): void {
        this.upgradeLight.node.active = true;
        this.upgradeLight.setAnimation(0, "wait", false);
        this.upgradeLight.setCompleteListener(() => {
            this.upgradeLight.setCompleteListener(null);
            this.upgradeLight.node.active = false;
        });

        const node = this.enchantNode.children[this.curType - 1];
        const spine = node.child("spine").getComponent(sp.Skeleton);
        spine.node.active = true;
        spine.setAnimation(0, "wait", false);
        spine.setCompleteListener(() => {
            spine.setCompleteListener(null);
            spine.node.active = false;
        });
    }

    protected onClickEnchant(event: cc.Event.EventTouch, type: number): void {
        if (this.curType === type) {
            return;
        }
        const oldType = this.curType;
        this.curType = type;
        const oldNode = this.enchantNode.children[oldType - 1];
        const newNode = this.enchantNode.children[type - 1];
        this.playEnchantAnimation(oldNode.child("icon"), EnumDissolveMode.Hide);
        this.playEnchantAnimation(oldNode.child("select"), EnumDissolveMode.Hide);
        this.playEnchantAnimation(newNode.child("icon"), EnumDissolveMode.Show);
        this.playEnchantAnimation(newNode.child("select"), EnumDissolveMode.Show);
        this.updateUI();
    }

    private playEnchantAnimation(node: cc.Node, mode: EnumDissolveMode): void {
        const comp = node.getComponent(DissolveEffect);
        comp.setMode(mode);
    }

    protected onClickUpgrade(): void {
        const level = Wing.getInstance().getEnchantLevelByType(this.curType);
        const data = TBWingEnchant.getInstance().getDataByTypeAndLevel(this.curType, level);
        const upgradeCost = data.upgradeCost[0];
        if (!Bag.getInstance().isEnough(upgradeCost[0], upgradeCost[1])) {
            UI.getInstance().open("FloatItemSource", upgradeCost[0]);
            return;
        }
        Wing.getInstance().sendWingsEnchantUpgrade(this.curType);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
