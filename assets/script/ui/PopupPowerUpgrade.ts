/*
 * @Author: Jrrend
 * @Date: 2024-03-18 11:03:03
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-24 16:43:23
 */

import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import Tips from "../../nsn/util/Tips";
import { PowerBreakOutRet, PowerPromotionRet, PowerUpgradeRet, TaskUpdateRet } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import { EnumTaskSeqModule } from "../data/base/BaseTaskSeq";
import TBAttribute from "../data/parser/TBAttribute";
import { JumpType } from "../data/parser/TBJump";
import TBPower from "../data/parser/TBPower";
import TBPowerLevel from "../data/parser/TBPowerLevel";
import TBPowerPromotion from "../data/parser/TBPowerPromotion";
import TBTaskDetail from "../data/parser/TBTaskDetail";
import TBTaskSeq from "../data/parser/TBTaskSeq";
import Power from "../game/Power";
import Task from "../game/Task";
import ImageUtils from "../utils/ImageUtils";
import JumpUtils from "../utils/JumpUtils";
import TweenUtil from "../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupPowerUpgrade extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null;
    @property(cc.Node)
    nowLv: cc.Node = null;
    @property(cc.Node)
    nextLv: cc.Node = null;
    @property([cc.Node])
    bubbles: cc.Node[] = [];
    @property(cc.Node)
    nodeTask: cc.Node = null;
    @property(cc.Node)
    tip1: cc.Node = null;
    @property(cc.Node)
    tip2: cc.Node = null;
    @property(cc.Node)
    btnUpgrade: cc.Node = null;
    @property(cc.Node)
    btnUpgradeLight: cc.Node = null;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/syncUI/power/spGradePopTitle",
            },
        ];
    }

    protected registerHandler(): void {
        Task.getInstance().on(
            TaskUpdateRet.prototype.clazzName,
            () => {
                this.updateTaskUI();
                this.updateTipUI();
                this.updateBtnUpgradeUI();
            },
            this
        );
        Power.getInstance().on(
            [
                PowerUpgradeRet.prototype.clazzName,
                PowerBreakOutRet.prototype.clazzName,
                PowerPromotionRet.prototype.clazzName,
            ],
            () => {
                this.updateAttrUI();
                this.updateTaskUI();
                this.updateTipUI();
                this.updateBtnUpgradeUI();
            },
            this
        );
    }

    protected onLoad(): void {
        this.updateAttrUI();
        this.updateTaskUI();
        this.updateTipUI();
        this.updateBtnUpgradeUI();
        this.initNodeAni();
    }

    private initNodeAni(): void {
        for (const e of this.bubbles) {
            e.stopAllActions();
            TweenUtil.float(e); // 气泡浮动
        }

        for (let i = 0; i < this.nodeTask.childrenCount; i++) {
            const node = this.nodeTask.children[i];
            const btn = node.getComponent(cc.Button);
            btn.interactable = false;
            node.opacity = 0;
            node.scale = 0;
            cc.tween(node)
                .delay(i * 0.15)
                .to(0.1, {
                    opacity: 255,
                    scale: 1.1,
                })
                .to(0.05, {
                    scale: 1,
                })
                .call(() => {
                    btn.interactable = true;
                })
                .start();
            TweenUtil.breath(node.child("nodeMask")); // 可领取呼吸态
        }
        TweenUtil.breath(this.btnUpgradeLight); // 可突破光效
    }

    private updateAttrUI(): void {
        const powerInfo = Power.getInstance().getPowerInfo();
        const titleId = TBPowerLevel.getInstance().getTitleIdById(powerInfo.kingLevelId);
        const { name: name1 } = TBPower.getInstance().getDataById(titleId);
        const { name: name2 } = TBPower.getInstance().getDataById(titleId + 1); // 最高级按钮会隐藏 所以不用处理
        this.nowLv.child("text").label(name1);
        this.nextLv.child("text").label(name2);
        const data1 = TBPowerLevel.getInstance()
            .getList()
            .find((e) => e.titleId === titleId);
        const data2 = TBPowerLevel.getInstance()
            .getList()
            .find((e) => e.titleId === titleId + 1);
        ImageUtils.setPowerIcon2(this.nowLv.child("icon"), this.nowLv.child("stars"), data1.id);
        ImageUtils.setPowerIcon2(this.nextLv.child("icon"), this.nextLv.child("stars"), data2.id);

        const info1 = TBPowerPromotion.getInstance().getDataByTitle(titleId);
        const info2 = TBPowerPromotion.getInstance().getDataByTitle(titleId + 1);
        for (let i = 0; i < info1.attribute.length; i++) {
            const { name, value } = TBAttribute.getInstance().formatAttribute([
                info1.attribute[i][0],
                info2.attribute[i][1] - info1.attribute[i][1],
            ]);
            this.bubbles[i].child("text1").label(name);
            this.bubbles[i].child("text2").label("+" + value);
        }
    }

    private updateTaskUI(): void {
        const powerInfo = Power.getInstance().getPowerInfo();
        const titleId = TBPowerLevel.getInstance().getTitleIdById(powerInfo.kingLevelId);
        const taskGroup = TBPowerPromotion.getInstance().getTaskGroupById(titleId);
        for (let i = 0; i < this.nodeTask.childrenCount; i++) {
            const { reachValue, desc, reward, id } = TBTaskDetail.getInstance().getDataById(taskGroup[i]);
            const { progress, isAwarded } = Task.getInstance().getTaskInfo(taskGroup[i]);
            const nodeTask = this.nodeTask.children[i];

            const underWayText = nodeTask.child("underWayText"); // 进行中
            const canGetText = nodeTask.child("canGetText"); // 可领取
            const taskName = nodeTask.child("text");
            const taskProgress = nodeTask.child("progress");
            const progressText = taskProgress.child("text");
            const item = nodeTask.child("item");
            const icon = item.child("icon");
            const count = item.child("count");
            const nodeMask = nodeTask.child("nodeMask"); // 可领取表现
            const complete = nodeTask.child("complete"); // 已领取表现

            nodeTask.button(id);
            taskName.label(TextUtils.format(desc, reachValue));
            taskProgress.progress(progress / reachValue);
            progressText.label(`${progress}/${reachValue}`);
            ImageUtils.setItemIcon(icon, reward[0][0]);
            count.label(reward[0][1]);

            if (isAwarded) {
                underWayText.active = false;
                canGetText.active = false;
                nodeMask.active = false;
                complete.active = true;
            } else if (progress >= reachValue && !isAwarded) {
                underWayText.active = false;
                canGetText.active = true;
                nodeMask.active = true;
                complete.active = false;
            } else {
                underWayText.active = true;
                canGetText.active = false;
                nodeMask.active = false;
                complete.active = false;
            }
        }
    }

    private updateTipUI(): void {
        const powerInfo = Power.getInstance().getPowerInfo();
        const titleId = TBPowerLevel.getInstance().getTitleIdById(powerInfo.kingLevelId);
        const levelTask = TBPowerPromotion.getInstance().getConditionById(titleId);
        const { name } = TBPowerLevel.getInstance().getDataById(levelTask);
        this.tip1.active = true;
        this.tip2.active = true;
        this.tip1.richText(i18n.power0014);
        this.tip2.richText(TextUtils.format(i18n.power0004, name));

        // 最高等级文本处理
        const length = TBPowerLevel.getInstance().getCount();
        if (length === powerInfo.kingLevelId) {
            this.tip1.active = true;
            this.tip2.active = false;
            this.tip1.richText(TextUtils.format(i18n.power0005, name));
        }
    }

    private updateBtnUpgradeUI(): void {
        const powerInfo = Power.getInstance().getPowerInfo();
        const titleId = TBPowerLevel.getInstance().getTitleIdById(powerInfo.kingLevelId);
        const powerData = TBPowerLevel.getInstance().getDataById(powerInfo.kingLevelId);
        const taskGroup = TBPowerPromotion.getInstance().getTaskGroupById(titleId);
        let isUpgrade1 = true; // 条件1  任务进度完成
        let count = 0;
        for (const e of taskGroup) {
            const { reachValue } = TBTaskDetail.getInstance().getDataById(e);
            const { progress, isAwarded } = Task.getInstance().getTaskInfo(e);
            if (progress < reachValue) {
                isUpgrade1 = false;
            }
            if (!isAwarded) {
                isUpgrade1 = false;
            }
            if (isAwarded) {
                count++;
            }
        }

        // 所有任务已领取的时候
        if (count === taskGroup.length) {
            isUpgrade1 = true;
        }

        const levelTask = TBPowerPromotion.getInstance().getConditionById(titleId);
        const isUpgrade2 = levelTask === powerInfo.kingLevelId && powerInfo.upgradeTimes === powerData.num; // 条件2 已达到晋升等级
        const btn = this.btnUpgrade.getComponent(cc.Button);
        if (isUpgrade1 && isUpgrade2) {
            this.btnUpgrade.getComponent(GrayComp).gray = false;
            CocosExt.setButtonEnable(btn, true);
            this.btnUpgradeLight.active = true;
        } else {
            this.btnUpgrade.getComponent(GrayComp).gray = true;
            CocosExt.setButtonEnable(btn, false);
            this.btnUpgradeLight.active = false;
        }
    }

    protected onClickReward(sender: cc.Event.EventTouch, id: number): void {
        const { reachValue, jumpId } = TBTaskDetail.getInstance().getDataById(id);
        const { progress, isAwarded } = Task.getInstance().getTaskInfo(id);
        if (isAwarded) {
            Tips.getInstance().show(i18n.task0002);
            return;
        }

        if (progress >= reachValue) {
            const taskSeqData = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.PowerTask);
            Task.getInstance().sendTaskTakeAward(taskSeqData.id, id);
        } else {
            JumpUtils.jump(JumpType.System, jumpId);
        }
    }

    protected onClickUpgrade(): void {
        Power.getInstance().sendPowerPromotion();
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
