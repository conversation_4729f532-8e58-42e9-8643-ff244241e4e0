/*
 * @Author: chenx
 * @Date: 2024-10-12 11:04:44
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-29 17:30:50
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import Time from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    ChildDealType,
    ConfidantChildBatchRejectApplyRet,
    ConfidantChildDealApplyNotice,
    ConfidantChildDealApplyRet,
    ConfidantChildMarryApplyNotice,
    IChildInfo,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import Confidant, { ConfidantEvent } from "../game/Confidant";
import { IChildApplyData } from "../prefab/confidant/PrefabConfidantChildApplyItem";

const { ccclass, property } = cc._decorator;

/**
 * 知己-王储申请
 */
@ccclass
export default class PopupConfidantChildApply extends I18nComponent {
    @property(ListView)
    listPlayer: ListView = null; // 玩家列表
    @property(cc.Node)
    btnRefuse: cc.Node = null; // 拒绝按钮
    @property(cc.Node)
    nodeCloseTips: cc.Node = null; // 关闭提示

    childData: IChildInfo = null; // 王储数据
    applyData: IChildApplyData[] = null; // 申请数据

    protected onLoad(): void {
        this.childData = this.args;
        this.updateListInfo();

        cc.tween(this.nodeCloseTips)
            .repeatForever(
                cc
                    .tween()
                    .to(1, { opacity: 0 }, { easing: cc.easing.sineIn })
                    .to(1, { opacity: 255 }, { easing: cc.easing.sineOut })
            )
            .start();
    }

    protected registerHandler(): void {
        Confidant.getInstance().on(
            ConfidantChildMarryApplyNotice.prototype.clazzName,
            (data: ConfidantChildMarryApplyNotice) => {
                if (this.childData.childUuid === data.childUuid) {
                    this.childData = Confidant.getInstance().getChildData(this.childData.childUuid);
                    this.updateListInfo();
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildDealApplyRet.prototype.clazzName,
            (data: ConfidantChildDealApplyRet) => {
                if (this.childData.childUuid === data.childUuid) {
                    switch (data.dealType) {
                        case ChildDealType.Agree:
                            UI.getInstance().close();
                            this.childData = Confidant.getInstance().getChildData(this.childData.childUuid);
                            UI.getInstance().open("PopupChildMarriage", this.childData);
                            break;
                        case ChildDealType.Reject:
                            this.childData = Confidant.getInstance().getChildData(this.childData.childUuid);
                            this.updateListInfo();
                            break;
                        default:
                            break;
                    }
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildDealApplyNotice.prototype.clazzName,
            (data: ConfidantChildDealApplyNotice) => {
                if (this.childData.childUuid === data.childUuid && data.dealType === ChildDealType.Agree) {
                    UI.getInstance().closeToWindow("UIConfidantMarriage");
                    Tips.getInstance().info(i18n.confidant0032);
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildBatchRejectApplyRet.prototype.clazzName,
            (data: ConfidantChildBatchRejectApplyRet) => {
                if (this.childData.childUuid === data.childUuid) {
                    this.childData = Confidant.getInstance().getChildData(this.childData.childUuid);
                    this.updateListInfo();
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantEvent.UpdateListApply,
            () => {
                this.updateListInfo();
            },
            this
        );
    }

    /**
     * 更新列表信息
     */
    private updateListInfo(): void {
        this.applyData = [];
        const nowTime = Time.getInstance().now();
        this.childData.marryApplyInfos.forEach((e) => {
            if (e.applyExpireTime > nowTime) {
                this.applyData.push({ applyData: e, childUuid: this.childData.childUuid });
            }
        });

        this.applyData.sort((a, b) => b.applyData.applyTime - a.applyData.applyTime);

        this.listPlayer.scrollView.stopAutoScroll();
        this.listPlayer.setListData(this.applyData);

        CocosExt.setButtonEnable(this.btnRefuse, this.applyData.length !== 0);
    }

    /**
     * 拒绝
     */
    protected onClickRefuse(): void {
        if (!this.applyData || this.applyData.length === 0) {
            return;
        }

        Confidant.getInstance().sendChildBatchRefuse(this.childData.childUuid);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
