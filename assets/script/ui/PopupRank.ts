/*
 * @Author: Jrrend
 * @Date: 2024-03-18 11:35:03
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-15 16:40:39
 */

import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { NodeUtils } from "../../nsn/util/NodeUtils";
import TextUtils from "../../nsn/util/TextUtils";
import { RankGetRet, UnionBossRankRet, UnionSiegeRankRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import TBDungeonTower from "../data/parser/TBDungeonTower";
import { ITEM_ID } from "../data/parser/TBItem";
import TBMainBarrier from "../data/parser/TBMainBarrier";
import TBMainMap from "../data/parser/TBMainMap";
import TBPowerLevel from "../data/parser/TBPowerLevel";
import TBRank, { RANK_ID } from "../data/parser/TBRank";
import Rank, { IRankInfo } from "../game/Rank";
import Union from "../game/Union";
import UnionSiege from "../game/UnionSiege";
import NumberUtils from "../utils/NumberUtils";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";

const { ccclass, property } = cc._decorator;

interface IRankConfig {
    id: number;
    score?: (s: number) => string;
}

export const RANK_CONFIG: IRankConfig[] = [
    {
        id: RANK_ID.DUNGEON_TOWER,
        score: (s: number) => {
            const data = TBDungeonTower.getInstance().getDataById(s);
            if (data) {
                return TextUtils.format(i18n.dungeon0043, data.layer, data.grade);
            } else {
                return "";
            }
        },
    },
    {
        id: RANK_ID.DUNGEON_THIEF,
        score: (s: number) => {
            return NumberUtils.format(s, 1, 0);
        },
    },
    {
        id: RANK_ID.UNION_BOSS,
        score: (s: number) => {
            return NumberUtils.format(s, 1, 0);
        },
    },
    {
        id: RANK_ID.DUNGEON_MAIN,
        score: (s: number) => {
            const levelInfo = TBMainBarrier.getInstance().getDataById(s);
            const mapInfo = TBMainMap.getInstance().getDataById(levelInfo.map);
            return `${mapInfo.name}${mapInfo.reveal}-${levelInfo.reveal}`;
        },
    },
    {
        id: RANK_ID.POWER,
        score: (s: number) => {
            const levelInfo = TBPowerLevel.getInstance().getDataById(s);
            return levelInfo.name;
        },
    },
    {
        id: RANK_ID.UNION_DEFENSE,
        score: (s: number) => {
            return TextUtils.format(i18n.unionDefense0012, s);
        },
    },
];

@ccclass
export default class PopupRank extends I18nComponent {
    @property(cc.Node)
    title: cc.Node = null;

    @property([cc.Node])
    top3: cc.Node[] = [];
    @property(cc.Node)
    mine: cc.Node = null;

    @property(cc.Node)
    scoreTitle: cc.Node = null;
    @property(ListView)
    list: ListView = null;
    @property(cc.Node)
    none: cc.Node = null;

    private rankId: number = 0;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.title,
                url: "texture/syncUI/rank/spRankPopTitleTxt",
            },
        ];
    }

    protected onLoad(): void {
        this.rankId = this.args;
        switch (this.rankId) {
            case RANK_ID.UNION_BOSS:
                Union.getInstance().sendUnionBossRank();
                break;
            case RANK_ID.UNION_DEFENSE:
                const data = TBRank.getInstance().getDataById(this.rankId);
                UnionSiege.getInstance().sendUnionSiegeRank(data.activityId);
                break;
            default:
                Rank.getInstance().sendRankGet(this.rankId);
                break;
        }

        UI.getInstance().showSoftLoading(NodeUtils.getWorldPosOfCenter(this.list.node));
    }

    protected onDestroy(): void {
        UI.getInstance().hideSoftLoading();
    }

    protected registerHandler(): void {
        Rank.getInstance().on(
            RankGetRet.prototype.clazzName,
            (rankId: number) => {
                if (rankId !== this.rankId) {
                    return;
                }
                this.updateUI();
            },
            this
        );
        Union.getInstance().on(
            UnionBossRankRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
        UnionSiege.getInstance().on(
            UnionSiegeRankRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateTop3();
        this.updateListInfo();
        this.updateMyRank();
    }

    private updateTop3(): void {
        const data = this.getRankData();
        for (let i = 0; i < 3; i++) {
            const node = this.top3[i];
            const info = data.ranks[i];
            const role = node.child("role");
            const none = node.child("none");
            if (data.ranks[i]) {
                role.active = true;
                none.active = false;
                PlayerInfoUtils.updateRole(role.child("nodeRole"), info);
                const name = role.child("name");
                const btn = role.child("btn");
                const title = role.child("title");
                name.label(info.player.name);
                btn.button(info.player.playerId);
                if (info.player.title !== ITEM_ID.DEFAULT_TITLE) {
                    title.active = true;
                    PlayerInfoUtils.updateTitle(title, info.player);
                } else {
                    title.active = false;
                }
            } else {
                role.active = false;
                none.active = true;
            }
        }
    }

    private updateListInfo(): void {
        const data = this.getRankData();
        const rankData = TBRank.getInstance().getDataById(this.rankId);
        this.scoreTitle.label(rankData.title);
        this.list.setListData(
            data.ranks.map((v) => {
                return {
                    rankId: this.rankId,
                    rankItem: v,
                };
            })
        );
        this.none.active = !data.ranks.length;
    }

    private updateMyRank(): void {
        const { myRank } = this.getRankData();
        const rankData = TBRank.getInstance().getDataById(this.rankId);
        const rank = this.mine.child("rank");
        const icon = this.mine.child("icon");
        const name = this.mine.child("name");
        const scoreCount = this.mine.child("count");
        const power = this.mine.child("power");
        const title = this.mine.child("title");
        const maxRank = rankData.maxRank;
        rank.label(myRank.rankNo ? (myRank.rankNo <= maxRank ? myRank.rankNo + "" : maxRank + "+") : i18n.rank0003);
        PlayerInfoUtils.updateHead(icon, myRank.player);
        PlayerInfoUtils.updateTitleOrPower(title, power, myRank.player);
        name.label(myRank.player.name);
        scoreCount.label(this.getScoreText(myRank.score));
    }

    /**
     * 获取数据显示文本
     * @param score
     */
    private getScoreText(score: number): string {
        const config = RANK_CONFIG.find((v) => v.id === this.rankId);
        if (!config) {
            return "";
        }
        if (!config.score) {
            return score + "";
        }
        return config.score(score);
    }

    private getRankData(): IRankInfo {
        switch (this.rankId) {
            case RANK_ID.UNION_BOSS:
                const myRank1 = Union.getInstance().getBossRankMyItem();
                const ranks1 = Union.getInstance().getBossRankItems();
                const data1: IRankInfo = {
                    ranks: ranks1,
                    myRank: myRank1,
                    reqTime: 0,
                    limit: 0,
                };
                return data1;
            case RANK_ID.UNION_DEFENSE:
                const myRank2 = UnionSiege.getInstance().getMyRankItem();
                const ranks2 = UnionSiege.getInstance().getRankItems();
                const data2: IRankInfo = {
                    ranks: ranks2,
                    myRank: myRank2,
                    reqTime: 0,
                    limit: 0,
                };
                return data2;
            default:
                return Rank.getInstance().getDataById(this.rankId);
        }
    }

    protected onClickPlayer(sender: cc.Event.EventTouch, playerId: string): void {
        UI.getInstance().open("FloatOtherPlayerInfo", playerId);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
