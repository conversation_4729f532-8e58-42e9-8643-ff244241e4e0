/*
 * @Author: JackyFu
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-24 11:57:17
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import PopUps from "../game/PopUps";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupVipServicePush extends I18nComponent {
    @property(cc.Node)
    bg: cc.Node = null;
    @property(cc.Node)
    qrNode: cc.Node = null;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.bg,
                url: "texture/syncUI/vipCustomerService/spSuperVIPFrame",
            },
        ];
    }

    protected onLoad(): void {
        this.updateQR();
    }

    private updateQR(): void {
        const data = PopUps.getInstance().getPopUpsInfos();
        if (!data || !data[0]) {
            return;
        }
        const info = JSON.parse(data[0].content);
        if (!info && !info.url) {
            return;
        }
        this.qrNode.getComponent("CQRCode").string = info.url;
    }

    protected onClickClose(): void {
        PopUps.getInstance().setPopUpsEntryShow(true);
        UI.getInstance().close();
    }
}
