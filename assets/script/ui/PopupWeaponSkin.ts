/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-24 14:08:31
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import Tips from "../../nsn/util/Tips";
import { WeaponBattleRet, WeaponUpgradeStarRet } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import { EnumWeaponType } from "../data/base/BaseWeapon";
import TBAttribute from "../data/parser/TBAttribute";
import { JumpType } from "../data/parser/TBJump";
import TBSkill, { EnumSkillParamLevelType } from "../data/parser/TBSkill";
import TBWeapon from "../data/parser/TBWeapon";
import TBWeaponStar from "../data/parser/TBWeaponStar";
import Bag from "../game/Bag";
import Skill from "../game/Skill";
import Weapon, { WeaponEvent } from "../game/Weapon";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import JumpUtils from "../utils/JumpUtils";
import SpineUtils from "../utils/SpineUtils";
import TweenUtil from "../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupWeaponSkin extends I18nComponent {
    @property(cc.Node)
    fateNode: cc.Node = null;
    @property(cc.Node)
    lbtName: cc.Node = null;
    @property(cc.Node)
    stars: cc.Node = null;
    @property(cc.Node)
    weaponIcon: cc.Node = null;
    @property(cc.Node)
    quality: cc.Node = null;
    @property(cc.Node)
    btnFight: cc.Node = null;

    @property(cc.Node)
    skillIcon: cc.Node = null;
    @property(cc.Node)
    skillName: cc.Node = null;
    @property(cc.Node)
    skillDesc: cc.Node = null;

    @property(cc.Node)
    attrNode: cc.Node = null;

    @property(ListView)
    list: ListView = null;

    @property(cc.Node)
    upgradeCost: cc.Node = null;
    @property(cc.Node)
    costIcon: cc.Node = null;
    @property(cc.Node)
    costCount: cc.Node = null;
    @property(cc.Node)
    btnUpgrade: cc.Node = null;
    @property(cc.Node)
    btnUpgradeText: cc.Node = null;
    @property(cc.Node)
    btnLight: cc.Node = null;

    @property(cc.Node)
    btnGoto: cc.Node = null;

    private weaponId: number = 0;
    private weaponList: number[] = [];

    protected onLoad(): void {
        this.updateUI();
    }

    protected registerHandler(): void {
        Weapon.getInstance().on(
            WeaponEvent.SelectedWeapon,
            (weaponId: number) => {
                this.weaponId = weaponId;
                this.updateUI();
            },
            this
        );
        Weapon.getInstance().on(
            WeaponBattleRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
        Weapon.getInstance().on(
            WeaponUpgradeStarRet.prototype.clazzName,
            (data: WeaponUpgradeStarRet) => {
                this.updateUI();
                UI.getInstance().open("FloatWeaponUpStar", data.weaponId);
            },
            this
        );
    }

    private updateUI(): void {
        this.updateList();
        this.updateFateAttr();
        this.updateWeapon();
        this.updateSkill();
        this.updateAttr();
        this.updateBtns();
    }

    private updateList(): void {
        const weaponIds = TBWeapon.getInstance()
            .getDataByType(EnumWeaponType.AdvancedWeapon)
            .map((v) => v.id);
        const teamId = Weapon.getInstance().getTeamId();
        const curTeam = weaponIds.includes(teamId) ? [teamId] : [];
        const own = weaponIds
            .filter((v) => v !== teamId && !!Weapon.getInstance().getDataById(v))
            .sort((a, b) => {
                const aData = TBWeapon.getInstance().getDataById(a);
                const bData = TBWeapon.getInstance().getDataById(b);
                return bData.quality - aData.quality;
            });
        const notOwn = weaponIds
            .filter((v) => v !== teamId && !Weapon.getInstance().getDataById(v))
            .sort((a, b) => {
                const aData = TBWeapon.getInstance().getDataById(a);
                const bData = TBWeapon.getInstance().getDataById(b);
                return bData.quality - aData.quality;
            });
        this.weaponList = curTeam.concat(own).concat(notOwn);
        this.weaponId = this.weaponId || this.weaponList[0];
        this.list.setListData(
            this.weaponList.map((v) => {
                return {
                    weaponId: v,
                    selected: v === this.weaponId,
                };
            })
        );
    }

    private updateFateAttr(): void {
        const data = TBWeapon.getInstance().getDataById(this.weaponId);
        const info = Weapon.getInstance().getDataById(this.weaponId);
        const starData = TBWeaponStar.getInstance().getDataByQualityAndStar(data.quality, info ? info.star : 0);
        if (starData.attribute.length > 1) {
            const fateAttr = TBAttribute.getInstance().getFateAttrs(starData.attribute);
            if (fateAttr) {
                this.fateNode.active = true;
                const { name, value } = TBAttribute.getInstance().formatAttribute(fateAttr);
                this.fateNode.child("text").label(name + "+" + value);
            } else {
                this.fateNode.active = false;
            }
        } else {
            this.fateNode.active = false;
        }
    }

    private updateWeapon(): void {
        const data = TBWeapon.getInstance().getDataById(this.weaponId);
        const info = Weapon.getInstance().getDataById(this.weaponId);
        this.lbtName.label(data.name);
        ImageUtils.setQuality6(this.quality, data.quality);
        SpineUtils.setWeapon(this.weaponIcon, data.res);
        const teamId = Weapon.getInstance().getTeamId();
        const weaponIds = Weapon.getInstance().getWeaponIds();
        if (weaponIds.includes(this.weaponId)) {
            this.btnFight.active = true;
            this.btnFight.spriteAsync(
                "texture/syncUI/tank/" + (this.weaponId === teamId ? "btnChariotGo2" : "btnChariotGo1")
            );
        } else {
            this.btnFight.active = false;
        }
        const stars = info ? info.star : 0;
        ImageUtils.setStarsIcon(this.stars, stars);
    }

    private updateSkill(): void {
        const data = TBWeapon.getInstance().getDataById(this.weaponId);
        const info = Weapon.getInstance().getDataById(this.weaponId);
        const skillData = TBSkill.getInstance().getDataById(data.skillId[0]);
        ImageUtils.setSkillIcon(this.skillIcon, skillData.id);
        this.skillName.label(skillData.name);
        const skillValues = Skill.getInstance().getSkillValueById(skillData.id, {
            [EnumSkillParamLevelType.WeaponStar]: info ? info.star : 0,
        });
        this.skillDesc.richText(TextUtils.format(skillData.desc, ...skillValues));
    }

    private updateAttr(): void {
        const data = TBWeapon.getInstance().getDataById(this.weaponId);
        const info = Weapon.getInstance().getDataById(this.weaponId);
        const nextStar = info ? info.star + 1 : 1;
        const curData = TBWeaponStar.getInstance().getDataByQualityAndStar(data.quality, info ? info.star : 0);
        const nextData = TBWeaponStar.getInstance().getDataByQualityAndStar(data.quality, nextStar);
        const title2 = this.attrNode.child("title2");
        const title3 = this.attrNode.child("title3");
        const values = this.attrNode.child("attr");
        const v1 = values.child("v1");
        const v2 = values.child("v2");
        const v3 = values.child("v3");
        const arrow = values.child("arrow");
        const { name, value } = TBAttribute.getInstance().formatAttribute(curData.attribute[0]);
        v1.label(name);
        v2.label(value);

        if (nextData) {
            title3.active = true;
            v3.active = true;
            arrow.active = true;
            title2.x = 0;
            v2.x = 0;

            title3.label(TextUtils.format(i18n.weapon0001, nextStar));
            const { value: nextValue } = TBAttribute.getInstance().formatAttribute(nextData.attribute[0]);
            v3.label(nextValue);
        } else {
            title3.active = false;
            v3.active = false;
            arrow.active = false;
            title2.x = 290;
            v2.x = 290;
        }
    }

    private updateBtns(): void {
        const data = TBWeapon.getInstance().getDataById(this.weaponId);
        const info = Weapon.getInstance().getDataById(this.weaponId);
        if (info) {
            this.btnUpgrade.active = true;
            this.btnGoto.active = false;
            const nextData = TBWeaponStar.getInstance().getDataByQualityAndStar(data.quality, info.star + 1);
            if (!nextData) {
                this.upgradeCost.active = false;
                this.btnUpgrade.getComponent(GrayComp).gray = true;
                this.btnUpgrade.getComponent(cc.Button).interactable = false;
                this.btnUpgradeText.label(i18n.common0064);
                this.btnLight.active = false;
            } else {
                this.upgradeCost.active = true;
                this.btnUpgrade.getComponent(GrayComp).gray = false;
                this.btnUpgrade.getComponent(cc.Button).interactable = true;
                this.btnUpgradeText.label(i18n.common0069);
                ImageUtils.setItemIcon(this.costIcon, data.id);
                ItemUtils.refreshCount(this.costCount, data.id, nextData.upgradeCost);

                this.btnLight.active = Bag.getInstance().isEnough(data.id, nextData.upgradeCost);
                if (this.btnLight.active) {
                    this.btnLight.stopAllActions();
                    TweenUtil.breath(this.btnLight);
                }
            }
        } else {
            this.btnUpgrade.active = false;
            this.btnGoto.active = true;
        }
    }

    protected onClickUpgrade(): void {
        const data = TBWeapon.getInstance().getDataById(this.weaponId);
        const info = Weapon.getInstance().getDataById(this.weaponId);
        const nextData = TBWeaponStar.getInstance().getDataByQualityAndStar(data.quality, info.star + 1);
        if (!Bag.getInstance().isEnough(data.id, nextData.upgradeCost)) {
            UI.getInstance().open("FloatItemSource", data.id);
            return;
        }
        Weapon.getInstance().sendWeaponUpgradeStar(this.weaponId);
    }

    protected onClickFight(): void {
        const weaponIds = Weapon.getInstance().getWeaponIds();
        if (!weaponIds.includes(this.weaponId)) {
            return;
        }
        const teamId = Weapon.getInstance().getTeamId();
        if (this.weaponId === teamId) {
            return;
        }
        Weapon.getInstance().sendWeaponBattle(this.weaponId);
    }

    protected onClickPreview(): void {
        UI.getInstance().open("PopupWeaponStarPreview", this.weaponId);
    }

    protected onClickGet(): void {
        const data = TBWeapon.getInstance().getDataById(this.weaponId);
        if (!data.jumpId) {
            Tips.getInstance().show(i18n.activity0019);
            return;
        }
        JumpUtils.jump(JumpType.System, data.jumpId);
    }

    protected onClickFate(): void {
        UI.getInstance().open("FloatWeaponFatePreview", { weaponId: this.weaponId, isMax: false });
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
