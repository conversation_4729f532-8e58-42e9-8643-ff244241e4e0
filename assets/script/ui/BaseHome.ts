/*
 * @Author: chenx
 * @Date: 2024-02-23 16:41:30
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:28:33
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import CommonPopup from "../core/commonPopup/CommonPopup";
import Guide from "../core/Guide";
import { EnumPopupType } from "../data/base/BasePopup";
import { DungeonType } from "../game/Combat";
import CombatDungeonMain from "../game/combat/CombatDungeonMain";
import CombatScore from "../game/CombatScore";
import PopUps from "../game/PopUps";

const { ccclass, property } = cc._decorator;

/**
 * 主界面
 */
@ccclass
export default class BaseHome extends I18nComponent {
    @property(cc.Node)
    nodeModuleContent: cc.Node = null; // 模块content

    protected onLoad(): void {
        this.initEachModule();
    }

    protected start(): void {
        // 触发首次引导
        Guide.getInstance().checkAll();
        // 触发弹窗
        CommonPopup.getInstance().triggerByType(EnumPopupType.LoginType);
        // vip弹窗
        PopUps.getInstance().checkPopUps();
    }

    protected onDestroy(): void {}

    /**
     * 初始化各模块
     */
    private initEachModule(): void {
        // 主线副本
        CombatDungeonMain.getInstance().initCombat(DungeonType.Main);
        CombatScore.getInstance().updateScore();

        Loader.getInstance().loadPrefab("prefab/home/<USER>", (prefab) => {
            if (cc.isValid(this.nodeModuleContent)) {
                const node = Loader.getInstance().instantiate(prefab);
                this.nodeModuleContent.addChild(node);

                node.zIndex = 1;
            }
        });

        // 主菜单
        Loader.getInstance().loadPrefab("prefab/home/<USER>", (prefab) => {
            if (cc.isValid(this.nodeModuleContent)) {
                const node = Loader.getInstance().instantiate(prefab);
                this.nodeModuleContent.addChild(node);

                node.zIndex = 2;
            }
        });

        // 功能ui
        Loader.getInstance().loadPrefab("prefab/home/<USER>", (prefab) => {
            if (cc.isValid(this.nodeModuleContent)) {
                const node = Loader.getInstance().instantiate(prefab);
                this.nodeModuleContent.addChild(node);

                node.zIndex = 3;
            }
        });

        // 制作
        Loader.getInstance().loadPrefab("prefab/home/<USER>", (prefabModule) => {
            if (cc.isValid(this.nodeModuleContent)) {
                const nodeModule = Loader.getInstance().instantiate(prefabModule);
                this.nodeModuleContent.addChild(nodeModule);

                nodeModule.zIndex = 4;
            }
        });

        // 公会互助提示
        Loader.getInstance().loadPrefab("prefab/home/<USER>", (prefabModule) => {
            if (cc.isValid(this.nodeModuleContent)) {
                const nodeModule = Loader.getInstance().instantiate(prefabModule);
                this.nodeModuleContent.addChild(nodeModule);

                nodeModule.zIndex = 101;
            }
        });

        // 触摸
        Loader.getInstance().loadPrefab("prefab/home/<USER>", (prefab) => {
            if (cc.isValid(this.nodeModuleContent)) {
                const node = Loader.getInstance().instantiate(prefab);
                this.nodeModuleContent.addChild(node);

                node.zIndex = 10001;
            }
        });
    }
}
