/*
 * @Author: JackyF<PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-17 16:40:06
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TBTank from "../data/parser/TBTank";
import TBTankStar from "../data/parser/TBTankStar";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatTankFatePreview extends I18nComponent {
    @property(ListView)
    list: ListView = null;

    private tankId: number = -1;

    protected onLoad(): void {
        this.tankId = this.args;
        this.updateList();
    }

    private updateList(): void {
        const data = TBTank.getInstance().getDataById(this.tankId);
        const starDataArr = TBTankStar.getInstance().getDataByQuality(data.quality);
        this.list.setListData(starDataArr);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
