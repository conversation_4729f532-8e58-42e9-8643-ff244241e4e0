/*
 * @Author: JackyFu
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-25 15:05:53
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TBTank from "../data/parser/TBTank";
import TBTankStar from "../data/parser/TBTankStar";
import Tank from "../game/Tank";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatTankFatePreview extends I18nComponent {
    @property(ListView)
    list: ListView = null;

    private tankId: number = -1;
    private isMax: boolean = false;

    protected onLoad(): void {
        this.tankId = this.args.tankId;
        this.isMax = this.args.isMax;
        this.updateList();
    }

    private updateList(): void {
        const data = TBTank.getInstance().getDataById(this.tankId);
        const starDataArr = TBTankStar.getInstance().getDataByQuality(data.quality);
        this.list.setListData(starDataArr);
        if (this.isMax) {
            this.list.scrollTo(starDataArr.length - 1);
        } else {
            const info = Tank.getInstance().getInfoById(this.tankId);
            const star = info ? info.star : 0;
            const index = starDataArr.findIndex((e) => e.star === star);
            this.list.scrollTo(index);
        }
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
