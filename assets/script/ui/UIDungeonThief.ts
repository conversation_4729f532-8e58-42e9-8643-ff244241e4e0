/*
 * @Author: chenx
 * @Date: 2024-09-18 14:45:28
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:40:17
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { DungeonThiefRewardRet, DungeonThiefSweetRewardRet, RankGetRet, ShopPurchaseRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumDungeonType } from "../data/base/BaseDungeon";
import TBDungeon from "../data/parser/TBDungeon";
import TBDungeonThief from "../data/parser/TBDungeonThief";
import TBPrivilegeConfig, { PRIVILEGE_ID } from "../data/parser/TBPrivilegeConfig";
import { RANK_ID } from "../data/parser/TBRank";
import Bag from "../game/Bag";
import { DungeonType } from "../game/Combat";
import DungeonThief from "../game/DungeonThief";
import Privilege from "../game/Privilege";
import Rank from "../game/Rank";
import Shop from "../game/Shop";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class UIDungeonThief extends I18nComponent {
    @property(cc.Node)
    nodeTitle: cc.Node = null;
    @property(cc.Node)
    title: cc.Node = null;
    @property(cc.Node)
    monster: cc.Node = null;

    @property(cc.Node)
    rewards: cc.Node = null;

    @property(cc.Node)
    rank: cc.Node = null;
    @property(cc.Node)
    hurt: cc.Node = null;

    @property(cc.Node)
    btnCombat: cc.Node = null;
    @property(cc.Node)
    costIcon: cc.Node = null;
    @property(cc.Node)
    costCount: cc.Node = null;
    @property(cc.Node)
    tips: cc.Node = null;
    @property(cc.Node)
    red: cc.Node = null;
    @property(cc.Node)
    nodeSweep: cc.Node = null; // 扫荡

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.btnCombat,
                url: "texture/syncUI/dungeon/btnInstanceFight1",
            },
            {
                sprite: this.nodeTitle,
                url: "texture/syncUI/dungeon/spGDBBSystemTitle",
            },
            {
                sprite: this.nodeSweep,
                url: "texture/syncUI/dungeon/btnInstanceFight2",
            },
        ];
    }

    protected onLoad(): void {
        this.updateUI();
        this.requestRank();
    }

    protected registerHandler(): void {
        DungeonThief.getInstance().on(
            [DungeonThiefRewardRet.prototype.clazzName, DungeonThiefSweetRewardRet.prototype.clazzName],
            () => {
                this.updateUI();
                this.requestRank();
            },
            this
        );
        Shop.getInstance().on(
            ShopPurchaseRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
        Rank.getInstance().on(
            RankGetRet.prototype.clazzName,
            () => {
                const rankInfo = Rank.getInstance().getDataById(RANK_ID.DUNGEON_THIEF);
                if (rankInfo && rankInfo.myRank.rankNo) {
                    this.rank.label(TextUtils.format(i18n.rank0001, rankInfo.myRank.rankNo));
                } else {
                    this.rank.label(i18n.rank0014);
                }
            },
            this
        );
    }

    private updateUI(): void {
        const dungeonId = DungeonThief.getInstance().getCurDungeonId();
        const data = TBDungeonThief.getInstance().getDataById(dungeonId);
        SpineUtils.setMonster(this.monster, data.monster[data.monster.length - 1][0][0], "wait");
        const dungeonData = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonThief);
        this.title.label(dungeonData.name);
        const maxDamage = DungeonThief.getInstance().getHistoryMaxScore();
        this.hurt.label(NumberUtils.format(maxDamage));
        ItemUtils.refreshView(this.rewards, this.prefabItem, data.reward);

        const [costId, costCount] = dungeonData.cost[0];
        const maxCount = this.getMaxCount();
        ImageUtils.setItemIcon(this.costIcon, costId);
        const count = Bag.getInstance().getItemCountById(costId);
        this.costCount.richText(
            TextUtils.format(count >= costCount ? i18n.common0072 : i18n.common0073, count, maxCount)
        );
        this.tips.label(TextUtils.format(i18n.dungeon0047, maxCount));
        this.red.active = Bag.getInstance().isEnough(dungeonData.cost[0][0], dungeonData.cost[0][1]);

        this.nodeSweep.active = maxDamage > 0;
    }

    private getMaxCount(): number {
        const data = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonThief);
        let maxCount = 0;
        maxCount += data.recoveryTime[0];
        const privilegeIds = [PRIVILEGE_ID.DUNGEON_THIEF_MONTHLY, PRIVILEGE_ID.DUNGEON_THIEF_FOREVER];
        for (const e of privilegeIds) {
            if (Privilege.getInstance().hasPrivilege(e)) {
                const data = TBPrivilegeConfig.getInstance().getDataById(e);
                maxCount += data.para.value;
            }
        }
        return maxCount;
    }

    private requestRank(): void {
        Rank.getInstance().sendRankGet(RANK_ID.DUNGEON_THIEF);
    }

    protected onClickRank(): void {
        UI.getInstance().open("PopupRank", RANK_ID.DUNGEON_THIEF);
    }

    /**
     * 战斗
     */
    protected onClickCombat(): void {
        const data = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonThief);
        const [costId, costCount] = data.cost[0];
        if (!Bag.getInstance().isEnough(costId, costCount)) {
            UI.getInstance().open("FloatItemSource", costId);
            return;
        }

        UI.getInstance().open("UIDungeonCombatThief", {
            type: DungeonType.Thief,
            levelId: DungeonThief.getInstance().getCurDungeonId(),
        });
    }

    /**
     * 扫荡
     */
    protected onClickSweep(): void {
        const maxDamage = DungeonThief.getInstance().getHistoryMaxScore();
        if (maxDamage <= 0) {
            return;
        }
        const dungeonInfo = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonThief);
        const [costId, costNum] = dungeonInfo.cost[0];
        if (!Bag.getInstance().isEnough(costId, costNum)) {
            UI.getInstance().open("FloatItemSource", costId);
            return;
        }

        DungeonThief.getInstance().sendSweep();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
