/*
 * @Author: <PERSON>yF<PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-17 09:50:55
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import Time, { HOUR_TO_SECOND } from "../../nsn/util/Time";
import { EnumLimitedPlayTabType, EnumLimitedPlayType } from "../data/base/BaseLimitedPlay";
import { EnumUnionPara } from "../data/base/BaseUnion";
import DataLimitedPlay from "../data/extend/DataLimitedPlay";
import TBLimitedPlay from "../data/parser/TBLimitedPlay";
import TBUnion from "../data/parser/TBUnion";
import Activity from "../game/Activity";
import GameSwitch from "../game/GameSwitch";
import TweenUtil from "../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupActivityUnion extends I18nComponent {
    @property(cc.Node)
    nodeTitle: cc.Node = null;
    @property(ListView)
    listView: ListView = null;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.nodeTitle,
                url: "texture/syncUI/union/spUnionEvenTitle",
            },
        ];
    }

    protected onLoad(): void {
        this.updateList();
        TweenUtil.playListViewVerticalAni(this.listView);
    }

    protected onDestroy(): void {
        UI.getInstance().hideSoftLoading();
    }

    private updateList(): void {
        const data = TBLimitedPlay.getInstance().getListByTab(EnumLimitedPlayTabType.UnionActivity);
        const a: DataLimitedPlay[] = [];
        const b: DataLimitedPlay[] = [];
        const c: DataLimitedPlay[] = [];
        for (const e of data) {
            const { result } = GameSwitch.getInstance().check(e.switchID);
            if (!result) {
                c.push(e);
            } else {
                let isOpening = null;
                if (e.type === EnumLimitedPlayType.UnionChallenge) {
                    const thisToday = Time.getInstance().getTodayZero();
                    const endPointTime = TBUnion.getInstance().getValueByPara(EnumUnionPara.BossChallengeEndTime);
                    const endTime = thisToday + endPointTime * HOUR_TO_SECOND * 1000;
                    const timeGap = endTime - Time.getInstance().now();
                    isOpening = timeGap > 0;
                } else {
                    isOpening = Activity.getInstance().isOpeningById(e.activityID);
                }

                if (isOpening) {
                    a.push(e);
                } else {
                    b.push(e);
                }
            }
        }
        a.sort((a, b) => a.order - b.order);
        b.sort((a, b) => a.order - b.order);
        c.sort((a, b) => a.order - b.order);
        this.listView.setListData(a.concat(b).concat(c));
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
