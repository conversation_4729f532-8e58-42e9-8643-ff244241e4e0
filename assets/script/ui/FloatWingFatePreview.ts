/*
 * @Author: JackyFu
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-25 15:06:07
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TBWing from "../data/parser/TBWing";
import TBWingStar from "../data/parser/TBWingStar";
import Wing from "../game/Wing";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatWingFatePreview extends I18nComponent {
    @property(ListView)
    list: ListView = null;

    private wingId: number = -1;
    private isMax: boolean = false; // 是否满星

    protected onLoad(): void {
        this.wingId = this.args.wingId;
        this.isMax = this.args.isMax;
        this.updateList();
    }

    private updateList(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        const starDataArr = TBWingStar.getInstance().getDataByQuality(data.quality);
        this.list.setListData(starDataArr);
        if (this.isMax) {
            this.list.scrollTo(starDataArr.length - 1);
        } else {
            const info = Wing.getInstance().getDataById(this.wingId);
            const star = info ? info.star : 0;
            const index = starDataArr.findIndex((e) => e.star === star);
            this.list.scrollTo(index);
        }
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
