/*
 * @Author: chenx
 * @Date: 2024-09-02 13:48:33
 * @Last Modified by: <PERSON>yF<PERSON>
 * @Last Modified time: 2024-11-29 17:29:55
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import Time from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import { EquipDungeonSearchRet, EquipSearchType, IPlayerInfo } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumDungeonEquipmentTotalPara } from "../data/base/BaseDungeonEquipmentTotal";
import TBDungeonEquipmentTotal from "../data/parser/TBDungeonEquipmentTotal";
import DungeonEquip from "../game/DungeonEquip";
import { IDungeonEquipInviteData } from "../prefab/dungeon/PrefabDungeonEquipInviteItem";

/**
 * 界面参数
 */
export interface IPopupDungeonEquipInviteArgs {
    teamId: string; // 队伍id
    levelId: number; // 关卡id
}

/**
 * 页签类型
 */
enum TabType {
    Common = 1, // 推荐
    Friend = 2, // 好友
    Union = 3, // 公会
}

/**
 * 页签类型
 */
const TAB_TYPE = [TabType.Common, TabType.Friend, TabType.Union];

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 装备副本-邀请
 */
@ccclass
export default class PopupDungeonEquipInvite extends I18nComponent {
    @property(ListView)
    listInvite: ListView = null; // 邀请列表
    @property(cc.Node)
    nodeNoDataTips: cc.Node = null; // 无数据提示
    @property(cc.Button)
    btnShare: cc.Button = null; // 分享按钮
    @property(cc.Label)
    lbtShare: cc.Label = null; // 分享按钮
    @property(cc.Button)
    btnBatchInvite: cc.Button = null; // 批量邀请按钮
    @property(cc.Label)
    lbtBatchInvite: cc.Label = null; // 批量邀请按钮
    @property([cc.Node])
    nodeTab: cc.Node[] = []; // 页签

    initData: IPopupDungeonEquipInviteArgs = null; // 初始数据
    tabType: TabType = TabType.Common; // 页签类型
    tabData: { [type: number]: { isSend: boolean; inviteData: IPlayerInfo[] } } = {}; // 页签数据
    inviteData: IDungeonEquipInviteData[] = null; // 邀请数据
    refreshTimeByShare: number = 0; // 刷新时间-分享倒计时
    refreshTimeByBatchInvite: number = 0; // 刷新时间-批量邀请倒计时

    protected onLoad(): void {
        this.initData = this.args;
        this.nodeTab.forEach((v, i) => v.button(TAB_TYPE[i]));
        this.updateTabState();
        this.updateListInfo(true);
        this.updateTimeStateByShare();
        this.updateTimeStateByBatchInvite();
    }

    protected update(dt: number): void {
        if (this.refreshTimeByShare > 0) {
            this.refreshTimeByShare -= dt;
            if (this.refreshTimeByShare <= 0) {
                this.updateTimeStateByShare();
            }
        }

        if (this.refreshTimeByBatchInvite > 0) {
            this.refreshTimeByBatchInvite -= dt;
            if (this.refreshTimeByBatchInvite <= 0) {
                this.updateTimeStateByBatchInvite();
            }
        }
    }

    protected registerHandler(): void {
        DungeonEquip.getInstance().on(
            EquipDungeonSearchRet.prototype.clazzName,
            (data: EquipDungeonSearchRet) => {
                let type: TabType = null;
                switch (data.searchType) {
                    case EquipSearchType.EquipRecommend:
                        type = TabType.Common;
                        break;
                    case EquipSearchType.EquipFriend:
                        type = TabType.Friend;
                        break;
                    case EquipSearchType.EquipGuild:
                        type = TabType.Union;
                        break;
                    default:
                        break;
                }
                this.tabData[type].inviteData = data.playerInfos;
                if (type === this.tabType) {
                    this.updateListInfo(true);
                    this.updateTimeStateByBatchInvite();
                }
            },
            this
        );
    }

    /**
     * 更新页签状态
     */
    private updateTabState(): void {
        this.nodeTab.forEach((e) => {
            e.child("select").active = CocosExt.getButtonData(e) === this.tabType;
        });
    }

    /**
     * 更新列表信息-邀请列表
     * @param isInit 是否为初始化调用
     */
    private updateListInfo(isInit: boolean = false): void {
        if (!isInit) {
            this.listInvite.scrollView.stopAutoScroll();
            this.listInvite.setListData(this.inviteData);
            return;
        }

        const tabData = this.tabData[this.tabType];
        if (!tabData) {
            this.tabData[this.tabType] = { isSend: true, inviteData: null };
            switch (this.tabType) {
                case TabType.Common:
                    DungeonEquip.getInstance().sendSearch(EquipSearchType.EquipRecommend);
                    break;
                case TabType.Friend:
                    DungeonEquip.getInstance().sendSearch(EquipSearchType.EquipFriend);
                    break;
                case TabType.Union:
                    DungeonEquip.getInstance().sendSearch(EquipSearchType.EquipGuild);
                    break;
                default:
                    break;
            }
            return;
        }
        if (!tabData.inviteData) {
            return;
        }

        this.inviteData = [];
        tabData.inviteData.forEach(
            (e) =>
                e.loginTime > e.logoutTime &&
                this.inviteData.push({ playerData: e, teamId: this.initData.teamId, levelId: this.initData.levelId })
        );
        this.inviteData.sort((a, b) => {
            return a.playerData.combatScore >= b.playerData.combatScore ? -1 : 1;
        });
        this.listInvite.scrollView.stopAutoScroll();
        this.listInvite.cleanList();
        this.listInvite.setListData(this.inviteData);
        this.nodeNoDataTips.active = this.inviteData.length === 0;
    }

    /**
     * 更新时间状态-分享倒计时
     */
    private updateTimeStateByShare(): void {
        const time = DungeonEquip.getInstance().getInviteTime("share");
        let isShare = !time;
        if (!isShare) {
            const duration = Time.getInstance().now() - time;
            const duration2 =
                TBDungeonEquipmentTotal.getInstance().getValueByPara(EnumDungeonEquipmentTotalPara.InvitationCd) * 1000;
            isShare = duration >= duration2;
            if (!isShare) {
                this.lbtShare.string = TimeFormat.getInstance().getTextByDuration(
                    duration2 - duration,
                    TimeDurationFormatType.D_H_M_S_0
                );
            }
        }
        CocosExt.setButtonEnable(this.btnShare, isShare);
        if (isShare) {
            this.lbtShare.string = i18n.dungeon0015;
        }

        !isShare && (this.refreshTimeByShare = REFRESH_DURATION);
    }

    /**
     * 更新时间状态-批量邀请倒计时
     */
    private updateTimeStateByBatchInvite(): void {
        if (!this.inviteData || this.inviteData.length === 0) {
            CocosExt.setButtonEnable(this.btnBatchInvite, false);
            this.lbtBatchInvite.string = i18n.dungeon0016;
            return;
        }

        const time = DungeonEquip.getInstance().getInviteTime("batchInvite");
        let isBatchInvite = !time;
        if (!isBatchInvite) {
            const duration = Time.getInstance().now() - time;
            const duration2 =
                TBDungeonEquipmentTotal.getInstance().getValueByPara(EnumDungeonEquipmentTotalPara.InvitationCd) * 1000;
            isBatchInvite = duration >= duration2;
            if (!isBatchInvite) {
                this.lbtBatchInvite.string = TimeFormat.getInstance().getTextByDuration(
                    duration2 - duration,
                    TimeDurationFormatType.D_H_M_S_0
                );
            }
        }
        CocosExt.setButtonEnable(this.btnBatchInvite, isBatchInvite);
        if (isBatchInvite) {
            this.lbtBatchInvite.string = i18n.dungeon0016;
        }

        !isBatchInvite && (this.refreshTimeByBatchInvite = REFRESH_DURATION);
    }

    /**
     * 分享
     */
    protected onClickShare(): void {
        const time = DungeonEquip.getInstance().getInviteTime("share");
        if (time) {
            const isShare =
                Time.getInstance().now() - time >=
                TBDungeonEquipmentTotal.getInstance().getValueByPara(EnumDungeonEquipmentTotalPara.InvitationCd) * 1000;
            if (!isShare) {
                return;
            }
        }

        DungeonEquip.getInstance().sendShare(this.initData.teamId, this.initData.levelId);

        DungeonEquip.getInstance().setInviteTime("share", Time.getInstance().now());
        this.updateTimeStateByShare();
    }

    /**
     * 批量邀请
     */
    protected onClickBatchInvite(): void {
        if (!this.inviteData || this.inviteData.length === 0) {
            return;
        }
        const time = DungeonEquip.getInstance().getInviteTime("batchInvite");
        if (time) {
            const isBatchInvite =
                Time.getInstance().now() - time >=
                TBDungeonEquipmentTotal.getInstance().getValueByPara(EnumDungeonEquipmentTotalPara.InvitationCd) * 1000;
            if (!isBatchInvite) {
                return;
            }
        }

        const playerId: string[] = [];
        const nowTime = Time.getInstance().now();
        this.inviteData.forEach((e) => {
            const time = DungeonEquip.getInstance().getInviteTime(e.playerData.playerId);
            let isInvite = !time;
            if (!isInvite) {
                isInvite =
                    nowTime - time >=
                    TBDungeonEquipmentTotal.getInstance().getValueByPara(EnumDungeonEquipmentTotalPara.InvitationCd) *
                        1000;
            }
            if (isInvite) {
                playerId.push(e.playerData.playerId);
                DungeonEquip.getInstance().setInviteTime(e.playerData.playerId, nowTime);
            }
        });
        if (playerId.length !== 0) {
            DungeonEquip.getInstance().sendInvite(this.initData.teamId, this.initData.levelId, playerId);
            this.updateListInfo();
        }
        DungeonEquip.getInstance().setInviteTime("batchInvite", Time.getInstance().now());
        this.updateTimeStateByBatchInvite();
    }

    /**
     * 选择页签
     * @param event 事件
     * @param type 页签类型
     */
    protected onClickSelectTab(event: cc.Event.EventTouch, type: TabType): void {
        if (!type || this.tabType === type) {
            return;
        }

        this.tabType = type;
        this.updateTabState();
        this.updateListInfo(true);
        this.updateTimeStateByBatchInvite();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
