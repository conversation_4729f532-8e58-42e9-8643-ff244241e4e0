/*
 * @Author: chenx
 * @Date: 2024-09-26 17:38:54
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-02 15:08:38
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import DropMenu, { IDrawMenuInfo } from "../../nsn/comp/ui/DropMenu";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import Time from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    ChildDealType,
    ConfidantChildBatchRejectApplyRet,
    ConfidantChildCancelProposeRet,
    ConfidantChildDealApplyNotice,
    ConfidantChildDealApplyRet,
    ConfidantChildMarryApplyNotice,
    ConfidantChildMarryApplyRet,
    ConfidantChildPublishRet,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointId } from "../core/redPoint/RedPointId";
import { EnumPrincessChildSex } from "../data/base/BasePrincessChild";
import { EnumPrincessTotalPara } from "../data/base/BasePrincessTotal";
import TBAttribute from "../data/parser/TBAttribute";
import TBItem from "../data/parser/TBItem";
import TBPrincessChild from "../data/parser/TBPrincessChild";
import TBPrincessTotal from "../data/parser/TBPrincessTotal";
import Confidant, { ConfidantEvent, IConfidantChildData } from "../game/Confidant";
import ImageUtils from "../utils/ImageUtils";
import NumberUtils from "../utils/NumberUtils";

/**
 * 页签类型
 */
enum TabType {
    Unmarried = 1, // 未婚
    Married = 2, // 已婚
}

/**
 * 页签类型
 */
const TAB_TYPE = [TabType.Unmarried, TabType.Married];

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 知己-联姻
 */
@ccclass
export default class UIConfidantMarriage extends I18nComponent {
    @property(cc.Sprite)
    spIcon: cc.Sprite = null; // 王储icon
    @property(cc.Sprite)
    spQualityIcon: cc.Sprite = null; // 品质icon
    @property(cc.Node)
    nodeName: cc.Node = null; // 名称
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    nodeMenTag: cc.Node = null; // 男性tag
    @property(cc.Node)
    nodeWomenTag: cc.Node = null; // 女性tag
    @property(cc.Node)
    nodeLevel: cc.Node = null; // 等级
    @property(cc.Label)
    lbtLevel: cc.Label = null; // 等级
    @property(cc.Node)
    nodeScoreLine: cc.Node = null; // 评分线
    @property(cc.Label)
    lbtScore: cc.Label = null; // 评分
    @property(cc.Node)
    nodeAttr: cc.Node = null; // 属性按钮
    @property(cc.Node)
    nodeApply: cc.Node = null; // 申请按钮

    @property(ListView)
    listUnmarried: ListView = null; // 未婚王储列表
    @property(ListView)
    listMarried: ListView = null; // 已婚王储列表
    @property(DropMenu)
    dropMenuUnmarried: DropMenu = null; // 未婚王储下拉菜单

    @property([cc.Node])
    nodeTab: cc.Node[] = []; // 页签

    tabType: TabType = TabType.Unmarried; // 页签类型
    unmarriedData: IConfidantChildData[] = null; // 未婚王储数据
    marriedData: IConfidantChildData[] = null; // 已婚王储数据
    dropMenuIndex: number = -1; // 下拉菜单index-未婚王储
    selectUnmarriedUuid: string = ""; // 已选择未婚王储uuid
    selectMarriedUuid: string = ""; // 已选择已婚王储uuid
    refreshTime: number = 0; // 刷新时间-申请按钮

    protected onLoad(): void {
        this.updateTabState(true);
        this.initDropMenu();
        this.updateTabInfo();
        this.updateChildInfo();
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.updateApplyState();
            }
        }
    }

    protected registerHandler(): void {
        Confidant.getInstance().on(
            ConfidantEvent.SelectUnmarriedChild,
            (childUuid: string) => {
                if (this.tabType === TabType.Unmarried) {
                    this.selectUnmarriedUuid = childUuid;
                    this.updateTabInfo();
                    this.updateChildInfo();
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantEvent.SelectMarriedChild,
            (childUuid: string) => {
                if (this.tabType === TabType.Married) {
                    this.selectMarriedUuid = childUuid;
                    this.updateTabInfo();
                    this.updateChildInfo();
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantEvent.UpdateListUnmarried,
            (childUuid: string) => {
                if (this.tabType === TabType.Unmarried) {
                    if (this.selectUnmarriedUuid !== childUuid) {
                        this.updateTabInfo();
                    } else {
                        this.selectUnmarriedUuid = "";
                        this.updateTabInfo();
                        this.updateChildInfo();
                    }
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildPublishRet.prototype.clazzName,
            (data: ConfidantChildPublishRet) => {
                if (this.unmarriedData) {
                    const childData = this.unmarriedData.find((e) => e.data.childUuid === data.childUuid);
                    childData.data = Confidant.getInstance().getChildData(childData.data.childUuid);
                }
                if (this.tabType === TabType.Unmarried) {
                    if (this.selectUnmarriedUuid !== data.childUuid) {
                        this.updateTabInfo();
                    } else {
                        this.selectUnmarriedUuid = "";
                        this.updateTabInfo();
                        this.updateChildInfo();
                    }
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildCancelProposeRet.prototype.clazzName,
            (data: ConfidantChildCancelProposeRet) => {
                if (this.unmarriedData) {
                    const childData = this.unmarriedData.find((e) => e.data.childUuid === data.childUuid);
                    childData.data = Confidant.getInstance().getChildData(childData.data.childUuid);
                }
                if (this.tabType === TabType.Unmarried) {
                    if (this.selectUnmarriedUuid !== data.childUuid) {
                        this.updateTabInfo();
                    } else {
                        this.selectUnmarriedUuid = "";
                        this.updateTabInfo();
                        this.updateChildInfo();
                    }
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildMarryApplyRet.prototype.clazzName,
            (data: ConfidantChildMarryApplyRet) => {
                if (!data.companionInfo) {
                    if (this.unmarriedData) {
                        const childData = this.unmarriedData.find((e) => e.data.childUuid === data.myChildUuid);
                        childData.data = Confidant.getInstance().getChildData(childData.data.childUuid);
                    }
                    if (this.tabType === TabType.Unmarried) {
                        if (this.selectUnmarriedUuid !== data.myChildUuid) {
                            this.updateTabInfo();
                        } else {
                            this.selectUnmarriedUuid = "";
                            this.updateTabInfo();
                            this.updateChildInfo();
                        }
                    }
                } else {
                    if (this.tabType === TabType.Unmarried) {
                        if (this.selectUnmarriedUuid !== data.myChildUuid) {
                            this.updateTabInfo(true);
                        } else {
                            this.selectUnmarriedUuid = "";
                            this.updateTabInfo(true);
                            this.updateChildInfo();
                        }
                    } else {
                        this.updateTabInfo(true);
                    }
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildMarryApplyNotice.prototype.clazzName,
            (data: ConfidantChildMarryApplyNotice) => {
                if (this.unmarriedData) {
                    const childData = this.unmarriedData.find((e) => e.data.childUuid === data.childUuid);
                    childData.data = Confidant.getInstance().getChildData(childData.data.childUuid);
                }
                if (this.tabType === TabType.Unmarried) {
                    if (this.selectUnmarriedUuid === data.childUuid) {
                        this.updateApplyState();
                    }
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildDealApplyRet.prototype.clazzName,
            (data: ConfidantChildDealApplyRet) => {
                if (this.unmarriedData) {
                    const childData = this.unmarriedData.find((e) => e.data.childUuid === data.childUuid);
                    childData.data = Confidant.getInstance().getChildData(childData.data.childUuid);
                }
                switch (data.dealType) {
                    case ChildDealType.Agree:
                        if (this.tabType === TabType.Unmarried) {
                            if (this.selectUnmarriedUuid !== data.childUuid) {
                                this.updateTabInfo(true);
                            } else {
                                this.selectUnmarriedUuid = "";
                                this.updateTabInfo(true);
                                this.updateChildInfo();
                            }
                        } else {
                            this.updateTabInfo(true);
                        }
                        break;
                    case ChildDealType.Reject:
                        if (this.tabType === TabType.Unmarried && this.selectUnmarriedUuid === data.childUuid) {
                            this.updateApplyState();
                        }
                        break;
                    default:
                        break;
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildDealApplyNotice.prototype.clazzName,
            (data: ConfidantChildDealApplyNotice) => {
                if (this.unmarriedData) {
                    const childData = this.unmarriedData.find((e) => e.data.childUuid === data.childUuid);
                    childData.data = Confidant.getInstance().getChildData(childData.data.childUuid);
                }
                switch (data.dealType) {
                    case ChildDealType.Agree:
                        if (this.tabType === TabType.Unmarried) {
                            if (this.selectUnmarriedUuid !== data.childUuid) {
                                this.updateTabInfo(true);
                            } else {
                                this.selectUnmarriedUuid = "";
                                this.updateTabInfo(true);
                                this.updateChildInfo();
                            }
                        } else {
                            this.updateTabInfo(true);
                        }
                        break;
                    case ChildDealType.Reject:
                        if (this.tabType === TabType.Unmarried) {
                            if (this.selectUnmarriedUuid !== data.childUuid) {
                                this.updateTabInfo();
                            } else {
                                this.selectUnmarriedUuid = "";
                                this.updateTabInfo();
                                this.updateChildInfo();
                            }
                        }
                        break;
                    default:
                        break;
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildBatchRejectApplyRet.prototype.clazzName,
            (data: ConfidantChildBatchRejectApplyRet) => {
                if (this.unmarriedData) {
                    const childData = this.unmarriedData.find((e) => e.data.childUuid === data.childUuid);
                    childData.data = Confidant.getInstance().getChildData(childData.data.childUuid);
                }
                if (this.tabType === TabType.Unmarried) {
                    if (this.selectUnmarriedUuid === data.childUuid) {
                        this.updateApplyState();
                    }
                }
            },
            this
        );
    }

    /**
     * 更新页签状态
     * @param isInit 是否为初始化调用
     */
    private updateTabState(isInit: boolean = false): void {
        if (isInit) {
            this.nodeTab.forEach((e, i) => e.button(TAB_TYPE[i]));
        }

        this.nodeTab.forEach((e) => {
            const type: TabType = CocosExt.getButtonData(e);
            e.child("select").active = type === this.tabType;
            switch (type) {
                case TabType.Married:
                    const childData = Confidant.getInstance().getMarriedChildData();
                    e.child("spLockTag").active = childData.length === 0;
                    break;
                default:
                    break;
            }
        });
    }

    /**
     * 初始化下拉菜单
     */
    private initDropMenu(): void {
        const drawMenuInfo: IDrawMenuInfo = {
            info: [i18n.confidant0018, i18n.confidant0019, i18n.confidant0020, i18n.confidant0021, i18n.confidant0022],
        };
        this.dropMenuUnmarried.initDropMenuInfo(drawMenuInfo);
        this.dropMenuIndex = 0;
        this.dropMenuUnmarried.setDefaultIndex(this.dropMenuIndex);
    }

    /**
     * 更新页签信息
     * @param isInit 是否为初始化调用
     */
    private updateTabInfo(isInit: boolean = false): void {
        this.listUnmarried.node.opacity = this.tabType === TabType.Unmarried ? 255 : 0;
        this.listUnmarried.node.y = this.tabType === TabType.Unmarried ? 822 : 0;
        this.listMarried.node.opacity = this.tabType === TabType.Married ? 255 : 0;
        this.listMarried.node.y = this.tabType === TabType.Married ? 822 : 0;
        this.dropMenuUnmarried.node.active = this.tabType === TabType.Unmarried;
        switch (this.tabType) {
            case TabType.Unmarried:
                isInit = isInit || !this.unmarriedData;
                if (isInit) {
                    this.unmarriedData = [];
                    const childData = Confidant.getInstance().getUnmarriedChildData();
                    childData.forEach((e) => {
                        const itemInfo = TBItem.getInstance().getDataById(e.childId);
                        const childInfo = TBPrincessChild.getInstance().getDataById(e.childId);
                        let score = 0;
                        childInfo.attribute.forEach(([attrId, init, step]) => {
                            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
                            score = score + (init + step * (e.level - 1)) * attrInfo.combat;
                        });
                        this.unmarriedData.push({
                            id: e.childId,
                            quality: itemInfo.quality,
                            score,
                            info: childInfo,
                            data: e,
                            isSelected: false,
                        });
                    });
                }

                let childData = this.unmarriedData.concat();
                const nowTime = Time.getInstance().now();
                switch (this.dropMenuIndex) {
                    case 0:
                        childData.sort((a, b) => {
                            return a.score > b.score ? -1 : 1;
                        });
                        break;
                    case 1:
                        childData.sort((a, b) => {
                            return b.quality - a.quality;
                        });
                        break;
                    case 2:
                        childData = childData.filter((e) => e.data.proposeExpireTime <= nowTime);
                        childData.sort((a, b) => {
                            if (a.score !== b.score) {
                                return a.score > b.score ? -1 : 1;
                            }
                            if (a.quality !== b.quality) {
                                return b.quality - a.quality;
                            }
                            return a.id - b.id;
                        });
                        break;
                    case 3:
                        childData = childData.filter((e) => e.data.proposeExpireTime > nowTime);
                        childData.sort((a, b) => {
                            if (a.score !== b.score) {
                                return a.score > b.score ? -1 : 1;
                            }
                            if (a.quality !== b.quality) {
                                return b.quality - a.quality;
                            }
                            return a.id - b.id;
                        });
                        break;
                    case 4:
                        childData = childData.filter(
                            (e) => e.data.appointPlayerInfo && e.data.proposeExpireTime > nowTime
                        );
                        childData.sort((a, b) => {
                            if (a.score !== b.score) {
                                return a.score > b.score ? -1 : 1;
                            }
                            if (a.quality !== b.quality) {
                                return b.quality - a.quality;
                            }
                            return a.id - b.id;
                        });
                        break;
                    default:
                        break;
                }

                this.selectUnmarriedUuid === "" &&
                    childData.length > 0 &&
                    (this.selectUnmarriedUuid = childData[0].data.childUuid);
                this.unmarriedData.forEach((e) => {
                    e.isSelected = e.data.childUuid === this.selectUnmarriedUuid;
                });

                this.listUnmarried.scrollView.stopAutoScroll();
                this.listUnmarried.setListData(childData);
                break;
            case TabType.Married:
                isInit = isInit || !this.marriedData;
                if (isInit) {
                    this.marriedData = [];
                    const childData = Confidant.getInstance().getMarriedChildData();
                    childData.forEach((e) => {
                        const itemInfo = TBItem.getInstance().getDataById(e.childId);
                        const childInfo = TBPrincessChild.getInstance().getDataById(e.childId);
                        const childInfo2 = TBPrincessChild.getInstance().getDataById(e.companionInfo.childId);
                        let score = 0;
                        childInfo.attribute.forEach(([attrId, init, step]) => {
                            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
                            score = score + (init + step * (e.level - 1)) * attrInfo.combat;
                        });
                        childInfo2.attribute.forEach(([attrId, init, step]) => {
                            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
                            score = score + (init + step * (childInfo2.levelLimit - 1)) * attrInfo.combat;
                        });
                        this.marriedData.push({
                            id: e.childId,
                            quality: itemInfo.quality,
                            score,
                            info: childInfo,
                            data: e,
                            isSelected: false,
                        });
                    });
                }

                this.marriedData.sort((a, b) => {
                    if (a.score !== b.score) {
                        return a.score > b.score ? -1 : 1;
                    }
                    if (a.quality !== b.quality) {
                        return b.quality - a.quality;
                    }
                    return a.id - b.id;
                });

                this.selectMarriedUuid === "" &&
                    this.marriedData.length > 0 &&
                    (this.selectMarriedUuid = this.marriedData[0].data.childUuid);
                this.marriedData.forEach((e) => {
                    e.isSelected = e.data.childUuid === this.selectMarriedUuid;
                });

                this.listMarried.scrollView.stopAutoScroll();
                this.listMarried.setListData(this.marriedData);
                break;
            default:
                break;
        }
    }

    /**
     * 更新王储信息
     */
    private updateChildInfo(): void {
        let childData: IConfidantChildData = null;
        switch (this.tabType) {
            case TabType.Unmarried:
                if (this.selectUnmarriedUuid !== "") {
                    childData = this.unmarriedData.find((e) => e.data.childUuid === this.selectUnmarriedUuid);
                }
                break;
            case TabType.Married:
                if (this.selectMarriedUuid !== "") {
                    childData = this.marriedData.find((e) => e.data.childUuid === this.selectMarriedUuid);
                }
                break;
            default:
                break;
        }
        const isShow = !!childData;

        this.nodeName.active = isShow;
        this.nodeLevel.active = isShow;
        this.nodeScoreLine.active = isShow;
        this.nodeAttr.active = isShow;
        this.updateApplyState();
        if (!isShow) {
            this.spIcon.node.sprite(null);
            this.spQualityIcon.node.sprite(null);
            this.lbtScore.string = "";
        } else {
            const childInfo = TBPrincessChild.getInstance().getDataById(childData.data.childId);
            let tempRes = "";
            for (const [level, res] of childInfo.res) {
                if (childData.data.level >= level) {
                    tempRes = res + "";
                } else {
                    break;
                }
            }
            ImageUtils.setConfidantIcon2(this.spIcon, tempRes);
            ImageUtils.setConfidantQualityIcon(this.spQualityIcon, childData.quality);
            this.lbtName.string = childData.data.name;
            this.nodeMenTag.active = childInfo.sex === EnumPrincessChildSex.Boy;
            this.nodeWomenTag.active = childInfo.sex === EnumPrincessChildSex.Girl;
            this.lbtLevel.string = childData.data.level + "";
            this.lbtScore.string = TextUtils.format(i18n.confidant0015, NumberUtils.format(childData.score, 1, 0));
        }
    }

    /**
     * 更新申请按钮状态
     */
    private updateApplyState(): void {
        let childData: IConfidantChildData = null;
        switch (this.tabType) {
            case TabType.Unmarried:
                if (this.selectUnmarriedUuid !== "") {
                    childData = this.unmarriedData.find((e) => e.data.childUuid === this.selectUnmarriedUuid);
                }
                break;
            case TabType.Married:
                if (this.selectMarriedUuid !== "") {
                    childData = this.marriedData.find((e) => e.data.childUuid === this.selectMarriedUuid);
                }
                break;
            default:
                break;
        }
        const nowTime = Time.getInstance().now();
        const isShow =
            childData &&
            !childData.data.companionInfo &&
            childData.data.marryApplyInfos.findIndex((e) => e.applyExpireTime > nowTime) !== -1;
        this.nodeApply.active = isShow;

        isShow && (this.refreshTime = REFRESH_DURATION);
    }

    /**
     * 属性
     */
    protected onClickAttr(): void {
        const initData = {
            showAttrId: TBPrincessTotal.getInstance().getValueByPara(
                EnumPrincessTotalPara.CrownPrinceOverallAttributeDisplay
            ),
            attrData: [],
        };
        switch (this.tabType) {
            case TabType.Unmarried:
                if (this.selectUnmarriedUuid !== "") {
                    const childData = this.unmarriedData.find((e) => e.data.childUuid === this.selectUnmarriedUuid);
                    childData.info.attribute.forEach(([attrId, init, step]) => {
                        const index = initData.attrData.findIndex((e) => e[0] === attrId);
                        if (index !== -1) {
                            initData.attrData[index][1] += init + step * (childData.data.level - 1);
                        } else {
                            initData.attrData.push([attrId, init + step * (childData.data.level - 1)]);
                        }
                    });
                }
                break;
            case TabType.Married:
                if (this.selectMarriedUuid !== "") {
                    const childData = this.marriedData.find((e) => e.data.childUuid === this.selectMarriedUuid);
                    childData.info.attribute.forEach(([attrId, init, step]) => {
                        const index = initData.attrData.findIndex((e) => e[0] === attrId);
                        if (index !== -1) {
                            initData.attrData[index][1] += init + step * (childData.data.level - 1);
                        } else {
                            initData.attrData.push([attrId, init + step * (childData.data.level - 1)]);
                        }
                    });
                    const childInfo = TBPrincessChild.getInstance().getDataById(childData.data.companionInfo.childId);
                    childInfo.attribute.forEach(([attrId, init, step]) => {
                        const index = initData.attrData.findIndex((e) => e[0] === attrId);
                        if (index !== -1) {
                            initData.attrData[index][1] += init + step * (childInfo.levelLimit - 1);
                        } else {
                            initData.attrData.push([attrId, init + step * (childInfo.levelLimit - 1)]);
                        }
                    });
                }
                break;
            default:
                break;
        }
        UI.getInstance().open("FloatAttribute", initData);
    }

    /**
     * 申请
     */
    protected onClickApply(): void {
        let childData: IConfidantChildData = null;
        switch (this.tabType) {
            case TabType.Unmarried:
                if (this.selectUnmarriedUuid !== "") {
                    childData = this.unmarriedData.find((e) => e.data.childUuid === this.selectUnmarriedUuid);
                }
                break;
            case TabType.Married:
                if (this.selectMarriedUuid !== "") {
                    childData = this.marriedData.find((e) => e.data.childUuid === this.selectMarriedUuid);
                }
                break;
            default:
                break;
        }
        const nowTime = Time.getInstance().now();
        const isShow =
            childData &&
            !childData.data.companionInfo &&
            childData.data.marryApplyInfos.findIndex((e) => e.applyExpireTime > nowTime) !== -1;
        if (!isShow) {
            Tips.getInstance().info(i18n.confidant0031);
            return;
        }

        UI.getInstance().open("PopupConfidantChildApply", childData.data);
    }

    /**
     * 选择下拉菜单item-未婚王储
     * @param index 下拉菜单index
     */
    protected onClickSelectDropMenuItem(index: number): void {
        if (this.dropMenuIndex === index) {
            return;
        }

        this.dropMenuIndex = index;
        this.selectUnmarriedUuid = "";
        this.updateTabInfo();
        this.updateChildInfo();
    }

    /**
     * 选择页签
     * @param event 事件
     * @param type 页签类型
     */
    protected onClickSelectTab(event: cc.Event.EventTouch, type: TabType): void {
        if (!type || this.tabType === type) {
            return;
        }
        switch (type) {
            case TabType.Married:
                const childData = Confidant.getInstance().getMarriedChildData();
                if (childData.length === 0) {
                    Tips.getInstance().info(i18n.confidant0017);
                    return;
                }
                break;
            default:
                break;
        }

        if (Confidant.getInstance().getChildUnmarriedNewRed()) {
            RedPoint.getInstance().record(RedPointId.ChildMarriageUnmarriedNew);
            RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedNew);
        }

        this.tabType = type;
        this.updateTabState();
        this.updateTabInfo();
        this.updateChildInfo();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        if (Confidant.getInstance().getChildUnmarriedNewRed()) {
            RedPoint.getInstance().record(RedPointId.ChildMarriageUnmarriedNew);
            RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedNew);
        }

        UI.getInstance().close();
    }
}
