/*
 * @Author: zhangwj
 * @Date: 2023-8-12 15:03:23
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:47:55
 */

import Audio from "../../nsn/audio/Audio";
import ListView from "../../nsn/comp/3rd/List/ListView";
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import MathUtils from "../../nsn/util/MathUtils";
import { BagUpdateRet, ExpeditionEventComplete, ExpeditionEventCompleteRet, IEventObj } from "../../protobuf/proto";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import i18n from "../config/i18n/I18n";
import { EnumActivityUniversalPara } from "../data/base/BaseActivityUniversal";
import { EnumExpeditionEventLuck, EnumExpeditionEventType } from "../data/base/BaseExpeditionEvent";
import { EnumMonsterBaseType } from "../data/base/BaseMonsterBase";
import DataExpeditionEvent from "../data/extend/DataExpeditionEvent";
import TBActivityUniversal from "../data/parser/TBActivityUniversal";
import TBExpedition from "../data/parser/TBExpedition";
import TBExpeditionEvent from "../data/parser/TBExpeditionEvent";
import { ITEM_ID } from "../data/parser/TBItem";
import TBMonsterBase from "../data/parser/TBMonsterBase";
import ActivityExpedition, { EnumActivityExpeditionEvent } from "../game/activity/ActivityExpedition";
import Bag from "../game/Bag";
import { DungeonType } from "../game/Combat";
import AudioUtils from "../utils/AudioUtils";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";

const { ccclass, property } = cc._decorator;

const PROGRESS_GAP = 116;

enum EnumEventPhrase {
    Front,
    Middle,
    Back,
    New,
    End,
}

interface IEventItem {
    desc: string;
    lucky: number;
    firstDesc: boolean;
    day?: number;
    reward?: number[][];
    playAni?: boolean;
}

const LEAD_POS_X = -280;
const MONSTER_POS_X = 280;
const LEAD_SPEED = 500;
const MONSTER_SPEED = 60;
const EFFECT_SPEED = 500;
const BG_SCROLL_SPEED = 200;

const LEAD_RUN_ANI_NAME = "run";
const LEAD_WALK_ANI_NAME = "trot";
const LEAD_IDLE_ANI_NAME = "wait";
const LEAD_ATTACK_ANI_NAME = "attack";
const LEAD_HURT_ANI_NAME = "hit";
const MONSTER_MOVE_ANI_NAME = "move";
const MONSTER_DIE_ANI_NAME = "die";
const MONSTER_ATTACK_ANI_NAME = "attack";

enum EnumLeadStatus {
    Enter,
    Walk,
    Idle,
    Fight,
}

enum EnumBattleResult {
    Success = 1,
    Failed = 2,
}

const ANI_EVENT_NAME = "trigger";

const LUCK_EVENT_CLOSE_WINDOWS = [
    "FloatActivityExpeditionTurntableGame",
    "FloatActivityExpeditionTreasureGame",
    "PopupActivityExpeditionSettings",
];

@ccclass
export default class UIActivityExpeditionGame extends I18nComponent {
    @property(cc.Node)
    contentBg1: cc.Node = null;
    @property(cc.Node)
    contentBg2: cc.Node = null;
    @property(cc.Node)
    lead: cc.Node = null;
    @property(cc.Node)
    monster: cc.Node = null;
    @property(cc.Node)
    effect: cc.Node = null;
    @property(cc.Node)
    skillEffect: cc.Node = null;

    @property(cc.Node)
    resIcon: cc.Node = null;
    @property(cc.Node)
    resCount: cc.Node = null;

    @property(cc.Node)
    dayProgress: cc.Node = null;
    @property(cc.Node)
    nodeProgress: cc.Node = null;
    @property(cc.Node)
    nodeLastIcon: cc.Node = null;
    @property(cc.Node)
    contentProgress: cc.Node = null;
    @property(cc.Node)
    nodeDot: cc.Node = null;
    @property(cc.Node)
    nodeDotWhite: cc.Node = null;

    @property(ListView)
    listView: ListView = null;

    @property([cc.Node])
    btnLuckyLight: cc.Node[] = [];
    @property(cc.Node)
    btnNextDay: cc.Node = null;
    @property(cc.Node)
    btnNextDayProgress: cc.Node = null;
    @property(cc.Node)
    btnFight: cc.Node = null;
    @property(cc.Node)
    btnFightDot: cc.Node = null;
    @property(cc.Node)
    btnPlay: cc.Node = null;
    @property(cc.Node)
    btnPlayText1: cc.Node = null;
    @property(cc.Node)
    btnPlayText2: cc.Node = null;
    @property(cc.Node)
    btnChoose: cc.Node = null;
    @property([cc.Node])
    btnChooseBtns: cc.Node[] = [];
    @property([cc.Node])
    btnChooseTexts: cc.Node[] = [];
    @property(cc.Node)
    btnLucky: cc.Node = null;
    @property(cc.Node)
    btnLittleLucky: cc.Node = null;
    @property(cc.Node)
    btnLittleLuckyIcon: cc.Node = null;
    @property(cc.Node)
    btnLittleLuckyText: cc.Node = null;
    @property(cc.Node)
    btnBigLucky: cc.Node = null;
    @property(cc.Node)
    btnBigLuckyText: cc.Node = null;
    @property(cc.Node)
    btnBigLuckyIcon: cc.Node = null;
    @property(cc.Node)
    blockInput: cc.Node = null;

    @property(cc.Node)
    luckyTips: cc.Node = null;
    @property(cc.Node)
    battleTips: cc.Node = null;

    private activityId: number = 0;
    private progressData: { id: number; index: number }[] = [];
    private eventParas: number[] = [];
    private leadStatus: EnumLeadStatus = EnumLeadStatus.Enter;
    private dt: number = 0;
    private isTodayEventShowCompleted: boolean = false;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.luckyTips,
                url: "texture/syncUI/activity/expedition/spTravelTxt",
            },
            {
                sprite: this.btnNextDay,
                url: "texture/syncUI/activity/expedition/spTravelButton1",
            },
            {
                sprite: this.btnFight,
                url: "texture/syncUI/activity/expedition/spTravelButton2",
            },
            {
                sprite: this.skillEffect,
                url: "texture/skillEffect/spSkillPic401101",
            },
            {
                sprite: this.btnLittleLuckyIcon,
                url: "texture/activity/expedition/lucky/spTravelTxt3",
            },
            {
                sprite: this.btnBigLuckyIcon,
                url: "texture/activity/expedition/lucky/spTravelTxt4",
            },
        ];
    }

    protected onLoad(): void {
        this.activityId = this.args;
        this.nodeProgress.parent = null;
        this.skillEffect.parent = null;

        this.initProgress();
        this.initRes();

        this.startGame();
    }

    protected onDestroy(): void {
        this.nodeProgress.destroy();
        this.skillEffect.destroy();
        UI.getInstance().hideBlockInput();
        Audio.getInstance().stopEffectByName(AUDIO_EFFECT_TYPE.EXPEDITION_RUN);
    }

    protected registerHandler(): void {
        ActivityExpedition.getInstance().on(
            ExpeditionEventCompleteRet.prototype.clazzName,
            (eventUuid: number) => {
                const allEvents = ActivityExpedition.getInstance().getAllEvents();
                if (!allEvents.length) {
                    return;
                }
                const event = ActivityExpedition.getInstance().getEventByUuid(eventUuid);
                const eventData = TBExpeditionEvent.getInstance().getDataById(event.eventId);
                switch (eventData.type) {
                    // 大小吉
                    case EnumExpeditionEventType.TurntableLuck3:
                    case EnumExpeditionEventType.DigTreasureLuck4:
                        this.updateRes();
                        this.updateLuckyCount();
                        this.checkGameCompleted();
                        break;
                    default:
                        this.eventParas = [];
                        this.startDay(true);
                        break;
                }
            },
            this
        );

        ActivityExpedition.getInstance().on(
            EnumActivityExpeditionEvent.GameEventEnd,
            (event: IEventObj, para: number) => {
                this.enableBlockInput(false);
                this.updateLeadStatus(EnumLeadStatus.Walk);
                const eventData = TBExpeditionEvent.getInstance().getDataById(event.eventId);
                switch (eventData.type) {
                    // 大小吉
                    case EnumExpeditionEventType.TurntableLuck3:
                    case EnumExpeditionEventType.DigTreasureLuck4:
                        ActivityExpedition.getInstance().sendExpeditionEventComplete(this.activityId, event.eventUuid, [
                            para,
                        ]);
                        break;
                    default:
                        this.eventParas = [para];
                        this.showEvent(event, eventData, EnumEventPhrase.Back, para);
                        this.updateBtns(eventData, EnumEventPhrase.Back);
                        break;
                }
            },
            this
        );

        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                if (data.srcReq !== ExpeditionEventComplete.prototype.clazzName) {
                    return;
                }
                UI.getInstance().open("FloatActivityExpeditionSuccess", data.gotItem);
            },
            this
        );
    }

    protected update(dt: number): void {
        const leadSpine = this.lead.getComponent(sp.Skeleton);
        const track = leadSpine.getCurrent(0);
        const currentAnimationName = track?.animation?.name;
        switch (this.leadStatus) {
            case EnumLeadStatus.Idle:
                if (currentAnimationName !== LEAD_IDLE_ANI_NAME) {
                    leadSpine.clearTracks();
                    leadSpine.setAnimation(0, LEAD_IDLE_ANI_NAME, true);
                    Audio.getInstance().stopEffectByName(AUDIO_EFFECT_TYPE.EXPEDITION_RUN);
                }
                break;
            case EnumLeadStatus.Enter:
                if (currentAnimationName !== LEAD_RUN_ANI_NAME) {
                    leadSpine.clearTracks();
                    leadSpine.setAnimation(0, LEAD_RUN_ANI_NAME, true);
                    Audio.getInstance().playEffect(
                        AUDIO_EFFECT_TYPE.EXPEDITION_RUN,
                        AUDIO_EFFECT_PATH.EXPEDITION,
                        true
                    );
                }
                const gap = LEAD_POS_X - this.lead.x;
                if (Math.abs(gap) > 10) {
                    this.lead.x += LEAD_SPEED * dt;
                } else {
                    this.leadStatus = EnumLeadStatus.Walk;
                }
                break;
            case EnumLeadStatus.Walk:
                if (currentAnimationName !== LEAD_WALK_ANI_NAME) {
                    leadSpine.clearTracks();
                    leadSpine.setAnimation(0, LEAD_WALK_ANI_NAME, true);
                    Audio.getInstance().playEffect(
                        AUDIO_EFFECT_TYPE.EXPEDITION_RUN,
                        AUDIO_EFFECT_PATH.EXPEDITION,
                        true
                    );
                }

                const count = this.contentBg1.childrenCount;
                const edge = (cc.winSize.width + this.contentBg1.children[0].width) / -2;
                for (let i = 0; i < count; i++) {
                    const item1 = this.contentBg1.children[i];
                    const item2 = this.contentBg2.children[i];
                    item1.x -= BG_SCROLL_SPEED * dt;
                    item2.x -= BG_SCROLL_SPEED * dt;
                    if (item1.x <= edge) {
                        const preIdx = (i - 1 + count) % count;
                        item1.x = this.contentBg1.children[preIdx].x - BG_SCROLL_SPEED * dt + item1.width;
                        item2.x = this.contentBg2.children[preIdx].x - BG_SCROLL_SPEED * dt + item2.width;
                    }
                }
                break;
            case EnumLeadStatus.Fight:
                this.dt += dt;
                if (this.dt > 0.3) {
                    this.dt -= 0.3;
                    let isActiveSome = false;
                    for (const e of this.btnFightDot.children) {
                        if (!e.active) {
                            e.active = true;
                            isActiveSome = true;
                            break;
                        }
                    }
                    if (!isActiveSome) {
                        this.btnFightDot.children.forEach((e) => {
                            e.active = false;
                        });
                    }
                }
                break;
            default:
                break;
        }
    }

    private async startGame(): Promise<void> {
        this.updateBtns(null, EnumEventPhrase.New);
        const ongoing = ActivityExpedition.getInstance().isOngoing();
        if (ongoing) {
            await this.addHistory();
        }
        await this.justWait(0.5);
        this.startDay(false);
    }

    private initProgress(): void {
        const normalEvent = ActivityExpedition.getInstance().getNormalEvents();
        for (let i = 0; i < normalEvent.length - 1; i++) {
            const id = normalEvent[i].expeditionId;
            const expeditionData = TBExpedition.getInstance().getDataById(id);
            if (!expeditionData.iconRes) {
                continue;
            }
            this.progressData.push({ id, index: i });
        }

        const lastEvent = normalEvent[normalEvent.length - 1];
        const lastExpeditionData = TBExpedition.getInstance().getDataById(lastEvent.expeditionId);
        this.nodeLastIcon.spriteAsync("texture/activity/expedition/event/spTravelEvent" + lastExpeditionData.iconRes);
    }

    private updateProgress(): void {
        this.dayProgress.active = true;
        const normalEvents = ActivityExpedition.getInstance().getNormalEvents();
        const count = this.contentProgress.childrenCount;
        const expeditionIdx = ActivityExpedition.getInstance().getCurrentEventIndex();
        const edge = this.progressData.length - 3;
        let curIdx = -1;

        for (let i = 0; i < edge; i++) {
            const data = this.progressData[i];
            if (data.index === expeditionIdx) {
                curIdx = i;
                break;
            }
            const nextData = this.progressData[i + 1];
            if (expeditionIdx > data.index && expeditionIdx < nextData.index) {
                curIdx = i;
                break;
            }
        }
        const idx = curIdx !== -1 ? curIdx - (curIdx % 2) : edge;
        const items = this.contentProgress.children;

        const move = count && idx && !(idx % 2) && this.progressData[idx].index === expeditionIdx;
        const starIdx = move ? idx - 2 : idx;
        const length = move ? starIdx + 5 : starIdx + 3;
        for (let i = starIdx; i < length; i++) {
            let item = items[i - starIdx];
            const data = this.progressData[i];
            if (!item) {
                item = Loader.getInstance().instantiate(this.nodeProgress);
                this.contentProgress.addChild(item);
                item.x = (i - starIdx) * PROGRESS_GAP;

                const expeditionData = TBExpedition.getInstance().getDataById(data.id);
                item.child("icon").spriteAsync(
                    "texture/activity/expedition/event/spTravelEvent" + expeditionData.iconRes
                );

                item.child("day").label(data.index + 1 + "");
            }

            const nextData = this.progressData[i + 1];
            let next = 0;
            if (nextData) {
                next = nextData.index;
            } else {
                next = normalEvents.length - 1;
            }

            const fill = (expeditionIdx - data.index) / (next - data.index);
            const icon = item.child("icon");
            if (expeditionIdx !== data.index) {
                icon.scale = 0.95;
            } else {
                cc.tween(icon).to(0.1, { scale: 1.3 }).start();
            }

            const bar = item.child("bar");
            bar.getComponent(cc.Sprite).fillRange = fill;

            if (expeditionIdx === data.index) {
                const worldPos = icon.convertToWorldSpaceAR(cc.v2());
                const localPos = this.nodeDot.parent.convertToNodeSpaceAR(worldPos);
                this.nodeDot.x = localPos.x;
                this.nodeDot.y = localPos.y + (icon.height * 1.2) / 2;
            }

            if (expeditionIdx > data.index && expeditionIdx < next) {
                const worldPos = bar.convertToWorldSpaceAR(cc.v2());
                const localPos = this.nodeDot.parent.convertToNodeSpaceAR(worldPos);
                this.nodeDot.x = localPos.x - bar.width / 2 + bar.width * fill;
                this.nodeDot.y = localPos.y + (bar.height + this.nodeDot.height) / 2;
            }
        }

        if (expeditionIdx === normalEvents.length - 1) {
            this.nodeLastIcon.scale = 1.2;
            this.nodeDot.x = this.nodeLastIcon.x;
            this.nodeDot.y = this.nodeLastIcon.y + (this.nodeLastIcon.height * 1.2) / 2;
        }

        this.nodeDotWhite.active = curIdx !== -1;
        this.contentProgress.parent.width = curIdx === -1 ? 400 : 313;
        if (move) {
            const moveNum = this.contentProgress.childrenCount - count;
            for (const e of this.contentProgress.children) {
                const x = e.x - PROGRESS_GAP * moveNum;
                cc.tween(e)
                    .to(1, { x })
                    .call(() => {
                        if (x < 0) {
                            e.destroy();
                        }
                    })
                    .start();
            }

            cc.tween(this.nodeDot)
                .to(1, { x: this.nodeDot.x - PROGRESS_GAP * moveNum })
                .start();
        }
    }

    /**
     * 更新按钮
     * @param data
     * @param phrase
     */
    private updateBtns(data: DataExpeditionEvent, phrase: EnumEventPhrase): void {
        switch (phrase) {
            case EnumEventPhrase.Front:
                this.btnNextDay.active = !data.desc && !data.backDesc;
                this.btnLucky.active = true;
                this.btnFight.active = false;
                this.btnChoose.active = false;
                this.btnPlay.active = false;
                break;
            case EnumEventPhrase.Middle:
                switch (data.type) {
                    case EnumExpeditionEventType.ConventionEvent:
                    case EnumExpeditionEventType.PlotEvent:
                        this.btnNextDay.active = !data.backDesc;
                        this.btnLucky.active = true;
                        this.btnFight.active = false;
                        this.btnChoose.active = false;
                        this.btnPlay.active = false;
                        break;
                    case EnumExpeditionEventType.SelectEvent:
                        this.btnNextDay.active = false;
                        this.btnLucky.active = false;
                        this.btnFight.active = false;
                        this.btnChoose.active = true;
                        this.btnPlay.active = false;
                        for (let i = 0; i < data.para.length; i++) {
                            this.btnChooseBtns[i].button(data.para[i][0]);
                            this.btnChooseTexts[i].label(data.para[i][1]);
                        }
                        this.updateLeadStatus(EnumLeadStatus.Idle);
                        break;
                    case EnumExpeditionEventType.FightEvent:
                        this.btnNextDay.active = false;
                        this.btnLucky.active = true;
                        this.btnFight.active = true;
                        this.btnChoose.active = false;
                        this.btnPlay.active = false;
                        this.updateLeadStatus(EnumLeadStatus.Idle);
                        break;
                    case EnumExpeditionEventType.DigTreasure:
                        this.btnNextDay.active = false;
                        this.btnLucky.active = true;
                        this.btnFight.active = false;
                        this.btnChoose.active = false;
                        this.btnPlay.active = true;
                        this.btnPlayText1.label(i18n.expedition0006);
                        this.btnPlayText2.label(i18n.expedition0006);
                        this.updateLeadStatus(EnumLeadStatus.Idle);
                        break;
                    case EnumExpeditionEventType.Turntable:
                        this.btnNextDay.active = false;
                        this.btnLucky.active = true;
                        this.btnFight.active = false;
                        this.btnChoose.active = false;
                        this.btnPlay.active = true;
                        this.btnPlayText1.label(i18n.expedition0007);
                        this.btnPlayText2.label(i18n.expedition0007);
                        this.updateLeadStatus(EnumLeadStatus.Idle);
                        break;
                    case EnumExpeditionEventType.MagicHat:
                        this.btnNextDay.active = false;
                        this.btnLucky.active = true;
                        this.btnFight.active = false;
                        this.btnChoose.active = false;
                        this.btnPlay.active = true;
                        this.btnPlayText1.label(i18n.expedition0008);
                        this.btnPlayText2.label(i18n.expedition0008);
                        this.updateLeadStatus(EnumLeadStatus.Idle);
                        break;
                    default:
                        break;
                }
                break;
            case EnumEventPhrase.Back:
                this.btnNextDay.active = true;
                this.btnLucky.active = true;
                this.btnFight.active = false;
                this.btnChoose.active = false;
                this.btnPlay.active = false;
                break;
            case EnumEventPhrase.New:
                this.btnNextDay.active = false;
                this.btnLucky.active = true;
                this.btnFight.active = false;
                this.btnChoose.active = false;
                this.btnPlay.active = false;
                break;
            case EnumEventPhrase.End:
                this.btnNextDay.active = false;
                this.btnLucky.active = true;
                this.btnFight.active = false;
                this.btnChoose.active = false;
                this.btnPlay.active = false;
                break;
            default:
                break;
        }

        this.updateLuckyCount();

        if (this.btnNextDay.active) {
            this.checkGameCompleted();
        }
    }

    /**
     * 更新大小吉
     */
    private updateLuckyCount(): void {
        const [littleLuckyId, littleLuckyLimitCount] = TBActivityUniversal.getInstance().getValueByPara(
            EnumActivityUniversalPara.LimitLuck3
        );
        const [bigLuckyId, bigLuckyLimitCount] = TBActivityUniversal.getInstance().getValueByPara(
            EnumActivityUniversalPara.LimitLuck4
        );
        let littleLuckyCount = 0;
        let bigLuckyCount = 0;
        const events = ActivityExpedition.getInstance().getAllEvents();
        const curEvent = ActivityExpedition.getInstance().getCurrentEvent();

        const luckEvents = ActivityExpedition.getInstance().getLuckyEvents();
        const maxCompletedLittleLuckyExpeditionId = Math.max(
            ...luckEvents.filter((v) => v.isEnd && v.eventId === littleLuckyId).map((v) => v.expeditionId)
        );
        const maxCompletedBigLuckyExpeditionId = Math.max(
            ...luckEvents.filter((v) => v.isEnd && v.eventId === bigLuckyId).map((v) => v.expeditionId)
        );
        for (const e of events) {
            if (e.expeditionId <= curEvent.expeditionId) {
                const eventData = TBExpeditionEvent.getInstance().getDataById(e.eventId);
                switch (eventData.type) {
                    case EnumExpeditionEventType.TurntableLuck3:
                    case EnumExpeditionEventType.DigTreasureLuck4:
                        break;
                    default:
                        switch (e.luckNum) {
                            case EnumExpeditionEventLuck.Luck3:
                                if (e.expeditionId > maxCompletedLittleLuckyExpeditionId) {
                                    littleLuckyCount++;
                                }
                                break;
                            case EnumExpeditionEventLuck.Luck4:
                                if (e.expeditionId > maxCompletedBigLuckyExpeditionId) {
                                    bigLuckyCount++;
                                }
                                break;
                            default:
                                break;
                        }
                        break;
                }
            }
        }
        this.btnLittleLuckyText.label(littleLuckyCount + "/" + littleLuckyLimitCount);
        this.btnBigLuckyText.label(bigLuckyCount + "/" + bigLuckyLimitCount);
    }

    /**
     * 添加历史
     */
    private async addHistory(): Promise<void> {
        const normalEvents = ActivityExpedition.getInstance().getNormalEvents();
        const curIndex = ActivityExpedition.getInstance().getCurrentEventIndex();
        const data: IEventItem[] = [];
        for (let i = 0; i < curIndex; i++) {
            const event = normalEvents[i];
            const eventData = TBExpeditionEvent.getInstance().getDataById(event.eventId);
            const expeditionData = TBExpedition.getInstance().getDataById(event.expeditionId);
            if (eventData.frontDesc) {
                data.push({
                    desc: eventData.frontDesc,
                    lucky: event.luckNum,
                    day: expeditionData.day,
                    playAni: false,
                    firstDesc: true,
                });
            }
            if (eventData.desc) {
                data.push({
                    desc: eventData.desc,
                    lucky: event.luckNum,
                    day: eventData.frontDesc ? 0 : expeditionData.day,
                    playAni: false,
                    firstDesc: !eventData.frontDesc,
                });
            }
            if (eventData.backDesc) {
                const para = event.eventParas[0];
                let desc = "";
                if (isNaN(para)) {
                    desc = eventData.backDesc;
                } else {
                    switch (eventData.type) {
                        case EnumExpeditionEventType.SelectEvent:
                        case EnumExpeditionEventType.FightEvent:
                            const paraIndex = eventData.para.findIndex((v) => v[0] === para);
                            desc = eventData.backDesc.split("#")[paraIndex];
                            break;
                        default:
                            desc = eventData.backDesc;
                            break;
                    }
                }
                data.push({
                    desc,
                    lucky: event.luckNum,
                    day: eventData.frontDesc || eventData.desc ? 0 : expeditionData.day,
                    reward: this.getReward(eventData, para),
                    playAni: false,
                    firstDesc: !eventData.frontDesc && !eventData.desc,
                });
            }
            this.listView.setListData(data);
            this.listView.scrollTo(data.length);
        }
        await this.justWait(0.5);
    }

    /**
     * 开始一天
     */
    private async startDay(showNextBtnAni: boolean): Promise<void> {
        this.isTodayEventShowCompleted = false;
        const event = ActivityExpedition.getInstance().getCurrentEvent();
        const eventData = TBExpeditionEvent.getInstance().getDataById(event.eventId);

        // 播放按钮光效
        showNextBtnAni && (await this.playBtnTextAni(event));

        // 更新进度
        this.updateProgress();

        // 更新资源
        this.updateRes();

        // 第一阶段
        if (eventData.frontDesc) {
            this.showEvent(event, eventData, EnumEventPhrase.Front);
            await this.justWait(this.isFirstBigLuckyEvent(event, eventData, EnumEventPhrase.Front) ? 1.5 : 0.5);
        }

        // 第二阶段
        if (eventData.desc) {
            this.showEvent(event, eventData, EnumEventPhrase.Middle);
            this.updateBtns(eventData, EnumEventPhrase.Middle);
            await this.justWait(this.isFirstBigLuckyEvent(event, eventData, EnumEventPhrase.Middle) ? 1.5 : 0.5);
        }

        // 第三阶段
        switch (eventData.type) {
            case EnumExpeditionEventType.ConventionEvent:
            case EnumExpeditionEventType.PlotEvent:
                if (eventData.backDesc) {
                    this.showEvent(event, eventData, EnumEventPhrase.Back);
                    this.updateBtns(eventData, EnumEventPhrase.Back);
                    await this.justWait(this.isFirstBigLuckyEvent(event, eventData, EnumEventPhrase.Back) ? 1.5 : 0.5);
                } else {
                    this.checkLuckyEvent();
                }
                break;
            default:
                break;
        }

        this.isTodayEventShowCompleted = true;
    }

    /**
     * 显示事件日志
     * @param event
     * @param eventData
     * @param phrase
     * @param para
     */
    private showEvent(event: IEventObj, eventData: DataExpeditionEvent, phrase: EnumEventPhrase, para?: number): void {
        switch (phrase) {
            case EnumEventPhrase.Front:
                {
                    const listData = this.listView.getListData<IEventItem>();
                    const expeditionData = TBExpedition.getInstance().getDataById(event.expeditionId);
                    const eventItem: IEventItem = {
                        desc: eventData.frontDesc,
                        lucky: event.luckNum,
                        day: expeditionData.day,
                        playAni: true,
                        firstDesc: true,
                    };
                    listData.push(eventItem);
                    this.listView.setListData(listData);
                    this.scheduleOnce(() => {
                        this.listView.scrollTo(listData.length, 0.1);
                    });
                }
                break;
            case EnumEventPhrase.Middle:
                {
                    const listData = this.listView.getListData<IEventItem>();
                    const expeditionData = TBExpedition.getInstance().getDataById(event.expeditionId);
                    const eventItem: IEventItem = {
                        desc: eventData.desc,
                        lucky: event.luckNum,
                        day: eventData.frontDesc ? 0 : expeditionData.day,
                        playAni: true,
                        firstDesc: !eventData.frontDesc,
                    };
                    listData.push(eventItem);
                    this.listView.setListData(listData);
                    this.scheduleOnce(() => {
                        this.listView.scrollTo(listData.length, 0.1);
                    });
                }
                break;
            case EnumEventPhrase.Back:
                {
                    const listData = this.listView.getListData<IEventItem>();
                    const expeditionData = TBExpedition.getInstance().getDataById(event.expeditionId);
                    let desc = "";
                    if (isNaN(para)) {
                        desc = eventData.backDesc;
                    } else {
                        switch (eventData.type) {
                            case EnumExpeditionEventType.SelectEvent:
                            case EnumExpeditionEventType.FightEvent:
                                const paraIndex = eventData.para.findIndex((v) => v[0] === para);
                                desc = eventData.backDesc.split("#")[paraIndex];
                                break;
                            default:
                                desc = eventData.backDesc;
                                break;
                        }
                    }
                    const eventItem: IEventItem = {
                        desc,
                        lucky: event.luckNum,
                        day: eventData.frontDesc || eventData.desc ? 0 : expeditionData.day,
                        reward: this.getReward(eventData, para),
                        playAni: true,
                        firstDesc: !eventData.frontDesc && !eventData.desc,
                    };
                    listData.push(eventItem);
                    this.listView.setListData(listData);
                    this.scheduleOnce(() => {
                        this.listView.scrollTo(listData.length, 0.1);
                    });

                    this.checkLuckyEvent();
                }
                break;
            default:
                break;
        }
    }

    /**
     * 检测大小吉事件
     */
    private async checkLuckyEvent(): Promise<void> {
        const curEvent = ActivityExpedition.getInstance().getCurrentEvent();
        const luckyEvents = ActivityExpedition.getInstance().getLuckyEvents();
        const luckEvent = luckyEvents.find((v) => v.expeditionId === curEvent.expeditionId);
        if (luckEvent && !luckEvent.isEnd) {
            const luckEventData = TBExpeditionEvent.getInstance().getDataById(luckEvent.eventId);
            switch (luckEventData.type) {
                case EnumExpeditionEventType.TurntableLuck3:
                    {
                        const some = LUCK_EVENT_CLOSE_WINDOWS.some((v) => UI.getInstance().isExist(v));
                        if (some) {
                            UI.getInstance().closeToWindow(this.node.name);
                        }
                        this.enableBlockInput(true);
                        this.updateLeadStatus(EnumLeadStatus.Idle);
                        await this.showLuckyTips();
                        UI.getInstance().open("FloatActivityExpeditionTurntableGame", luckEvent);
                    }
                    break;
                case EnumExpeditionEventType.DigTreasureLuck4:
                    {
                        const some = LUCK_EVENT_CLOSE_WINDOWS.some((v) => UI.getInstance().isExist(v));
                        if (some) {
                            UI.getInstance().closeToWindow(this.node.name);
                        }
                        this.enableBlockInput(true);
                        this.updateLeadStatus(EnumLeadStatus.Idle);
                        await this.showLuckyTips();
                        UI.getInstance().open("FloatActivityExpeditionTreasureGame", luckEvent);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 检测游戏是否结束
     */
    private checkGameCompleted(): void {
        const allEvents = ActivityExpedition.getInstance().getAllEvents();
        const unCompletedEvents = allEvents.filter((v) => !v.isEnd);
        if (unCompletedEvents) {
            if (unCompletedEvents.length === 1) {
                this.updateBtns(null, EnumEventPhrase.End);
                ActivityExpedition.getInstance().sendExpeditionEventComplete(
                    this.activityId,
                    unCompletedEvents[0].eventUuid,
                    this.eventParas
                );
            }
        }
    }

    /**
     * 空等
     * @param second
     * @returns
     */
    private justWait(second: number): Promise<void> {
        return new Promise<void>((resolve) => {
            this.scheduleOnce(() => {
                resolve();
            }, second);
        });
    }

    /**
     * 显示战斗
     */
    private showBattle(): void {
        const event = ActivityExpedition.getInstance().getCurrentEvent();
        const eventData = TBExpeditionEvent.getInstance().getDataById(event.eventId);
        const rwdIdx = eventData.para.findIndex((v) => v[0] === event.preEventParas[0]);
        const [para, , monsterId, scale] = eventData.para[rwdIdx];

        const leadSpine = this.lead.getComponent(sp.Skeleton);
        leadSpine.setAnimation(0, LEAD_IDLE_ANI_NAME, false);

        const leadFire = (): void => {
            const srcPos = cc.v2(this.lead.x + 100, this.lead.y + 85);
            const dstPos = cc.v2(this.monster.x - 30, this.monster.y + (this.monster.height / 2) * scale);
            const effect = Loader.getInstance().instantiate(this.skillEffect);
            effect.parent = this.effect;
            effect.x = srcPos.x;
            effect.y = srcPos.y;
            effect.angle = (-Math.atan2(dstPos.y - srcPos.y, dstPos.x - srcPos.x) * 180) / Math.PI;
            cc.tween(effect)
                .to(cc.Vec2.distance(srcPos, dstPos) / EFFECT_SPEED, { x: dstPos.x, y: dstPos.y })
                .call(() => {
                    effect.destroy();
                    AudioUtils.playCombatEffect(`skillWeapon401001`, AUDIO_EFFECT_PATH.COMBAT, DungeonType.Boss);
                    cc.tween(this.monster)
                        .call(() => {
                            this.monster.color = cc.color(255, 130, 130);
                        })
                        .delay(0.3)
                        .call(() => {
                            this.monster.color = cc.color(255, 255, 255);
                        })
                        .start();
                })
                .start();
        };

        const leadRepeatAttack = (): void => {
            leadSpine.clearTracks();
            leadSpine.setAnimation(0, LEAD_ATTACK_ANI_NAME, false);
            leadSpine.setEventListener((trackEntry: sp.spine.TrackEntry, spineEvent: sp.spine.Event) => {
                if (spineEvent.data.name === ANI_EVENT_NAME) {
                    leadSpine.setEventListener(null);
                    leadFire();
                }
            });
            leadSpine.setCompleteListener(() => {
                leadSpine.setCompleteListener(null);
                leadSpine.setAnimation(0, LEAD_IDLE_ANI_NAME, false);
            });
        };
        this.schedule(leadRepeatAttack, 1.2, cc.macro.REPEAT_FOREVER, 0.5);

        const monsterSpine = this.monster.getComponent(sp.Skeleton);
        this.monster.opacity = 0;
        this.monster.scale = scale;
        this.monster.x = MONSTER_POS_X;

        const monsterData = TBMonsterBase.getInstance().getDataById(monsterId);
        this.monster.skeletonAsync("spine/monster/efMonster" + monsterData.res, () => {
            if (monsterData.type === EnumMonsterBaseType.BossMonster) {
                Audio.getInstance().playEffect(
                    AUDIO_EFFECT_TYPE.DUNGEON_MAIN_MONSTER_COMING,
                    AUDIO_EFFECT_PATH.DUNGEON
                ); // 怪兽来袭
            }
            let monsterTargetPosX = 0;
            switch (para) {
                case EnumBattleResult.Success:
                    monsterTargetPosX =
                        MONSTER_POS_X - (MONSTER_POS_X - LEAD_POS_X) * MathUtils.getRandomValue(0.5, 0.6);
                    break;
                case EnumBattleResult.Failed:
                    monsterTargetPosX = LEAD_POS_X + this.monster.width * scale * 0.5 + 20;
                    break;
                default:
                    break;
            }
            cc.tween(this.monster)
                .to(0.5, { opacity: 255 })
                .to((MONSTER_POS_X - monsterTargetPosX) / MONSTER_SPEED, { x: monsterTargetPosX })
                .call(() => {
                    this.unschedule(leadRepeatAttack);
                    leadSpine.setCompleteListener(null);
                    switch (para) {
                        case EnumBattleResult.Success:
                            monsterSpine.clearTracks();
                            monsterSpine.setAnimation(0, MONSTER_DIE_ANI_NAME, false);
                            monsterSpine.setCompleteListener(() => {
                                monsterSpine.setCompleteListener(null);
                                cc.tween(this.monster)
                                    .to(0.3, { opacity: 0 })
                                    .call(() => {
                                        this.enableBtnFight(true);
                                        this.showBattleTips(para);
                                        ActivityExpedition.getInstance().emit(
                                            EnumActivityExpeditionEvent.GameEventEnd,
                                            event,
                                            para
                                        );
                                    })
                                    .start();
                            });
                            break;
                        case EnumBattleResult.Failed:
                            monsterSpine.clearTracks();
                            monsterSpine.setAnimation(0, MONSTER_ATTACK_ANI_NAME, false);
                            monsterSpine.setEventListener(
                                (trackEntry: sp.spine.TrackEntry, spineEvent: sp.spine.Event) => {
                                    if (spineEvent.data.name === ANI_EVENT_NAME) {
                                        monsterSpine.setEventListener(null);
                                        leadSpine.setAnimation(0, LEAD_HURT_ANI_NAME, false);
                                        leadSpine.setCompleteListener(() => {
                                            leadSpine.setCompleteListener(null);
                                            cc.tween(this.monster)
                                                .to(0.3, { opacity: 0 })
                                                .call(() => {
                                                    this.enableBtnFight(true);
                                                    this.showBattleTips(para);
                                                    ActivityExpedition.getInstance().emit(
                                                        EnumActivityExpeditionEvent.GameEventEnd,
                                                        event,
                                                        para
                                                    );
                                                })
                                                .start();
                                        });
                                    }
                                }
                            );
                            break;
                        default:
                            break;
                    }
                })
                .start();
            monsterSpine.clearTracks();
            monsterSpine.setAnimation(0, MONSTER_MOVE_ANI_NAME, true);
        });
    }

    private getReward(data: DataExpeditionEvent, para?: number): number[][] {
        let reward: number[][] = [];
        switch (data.type) {
            case EnumExpeditionEventType.ConventionEvent:
            case EnumExpeditionEventType.PlotEvent:
                reward = data.result;
                break;
            case EnumExpeditionEventType.SelectEvent:
            case EnumExpeditionEventType.FightEvent:
            case EnumExpeditionEventType.Turntable:
            case EnumExpeditionEventType.MagicHat:
                {
                    const result = data.result.find((v) => v[0] === para);
                    if (result) {
                        reward = [[result[1], result[2]]];
                    }
                }
                break;
            case EnumExpeditionEventType.DigTreasure:
                {
                    const eventPara = data.para.find((v) => v[0] === para);
                    const result = data.result[0];
                    if (result) {
                        reward = [[result[0], result[1] * eventPara[2]]];
                    }
                }
                break;
            default:
                break;
        }
        return reward;
    }

    /**
     * 播放按钮动画
     * @param event
     */
    private async playBtnTextAni(event: IEventObj): Promise<void> {
        const showLuckyBtnAni = (index: number, delay: number): void => {
            cc.tween(this.btnLuckyLight[index])
                .delay(delay)
                .call(() => {
                    this.btnLuckyLight[index].active = true;
                    const light = this.btnLuckyLight[index].getComponent(sp.Skeleton);
                    light.setAnimation(0, "wait", false);
                    light.setCompleteListener(() => {
                        light1.setCompleteListener(null);
                    });
                    const text = this.btnLuckyLight[index].child("text");
                    text.y = 130;
                    text.opacity = 0;
                    text.scale = 0.7;
                    cc.tween(text)
                        .to(0.1, { opacity: 255 })
                        .to(0.12, { y: 150, scale: 1.3 })
                        .to(0.06, { scale: 1 })
                        .to(0.08, { opacity: 0 })
                        .call(() => {
                            this.btnLuckyLight[index].active = false;
                        })
                        .start();
                })
                .start();
        };

        this.btnLuckyLight[0].active = true;
        const light1 = this.btnLuckyLight[0].getComponent(sp.Skeleton);
        light1.setAnimation(0, "wait", false);
        light1.setCompleteListener(() => {
            light1.setCompleteListener(null);
            this.btnLuckyLight[0].active = false;
        });

        this.btnNextDayProgress.active = true;
        const progressBar = this.btnNextDayProgress.getComponent(cc.ProgressBar);
        progressBar.progress = 0;
        cc.tween(progressBar)
            .to(0.3, { progress: 1 })
            .call(() => {
                this.btnNextDayProgress.active = false;
            })
            .start();

        return new Promise<void>((resolve, reject) => {
            switch (event.luckNum) {
                case EnumExpeditionEventLuck.Luck5:
                    showLuckyBtnAni(1, 0.4);
                    this.enableBtnNextDay(false);
                    Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.EXPEDITION_LUCK5, AUDIO_EFFECT_PATH.EXPEDITION);
                    cc.tween(this.btnNextDay)
                        .delay(0.8)
                        .call(() => {
                            this.enableBtnNextDay(true);
                            resolve();
                        })
                        .start();
                    break;
                case EnumExpeditionEventLuck.Luck3:
                    showLuckyBtnAni(2, 0.4);
                    this.enableBtnNextDay(false);
                    Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.EXPEDITION_LUCK3, AUDIO_EFFECT_PATH.EXPEDITION);
                    cc.tween(this.btnNextDay)
                        .delay(0.8)
                        .call(() => {
                            this.enableBtnNextDay(true);
                            resolve();
                        })
                        .start();
                    break;
                case EnumExpeditionEventLuck.Luck4:
                    showLuckyBtnAni(2, 0.4);
                    showLuckyBtnAni(3, 0.8);
                    this.enableBtnNextDay(false);
                    Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.EXPEDITION_LUCK4, AUDIO_EFFECT_PATH.EXPEDITION);
                    cc.tween(this.btnNextDay)
                        .delay(1.2)
                        .call(() => {
                            this.enableBtnNextDay(true);
                            resolve();
                        })
                        .start();
                    break;
                case EnumExpeditionEventLuck.Luck1:
                    this.enableBtnNextDay(false);
                    cc.tween(this.btnNextDay)
                        .delay(0.4)
                        .call(() => {
                            this.enableBtnNextDay(true);
                            resolve();
                        })
                        .start();
                    break;
                default:
                    break;
            }
        });
    }

    private initRes(): void {
        ImageUtils.setItemIcon(this.resIcon, ITEM_ID.LIMIT_GAME_PLAY_COIN);
        this.updateRes();
    }

    private updateRes(): void {
        const reward = ActivityExpedition.getInstance().getReward();
        const find = reward.find((v) => v.itemInfoId === ITEM_ID.LIMIT_GAME_PLAY_COIN);
        const count = find?.num || 0;
        this.resCount.label(count + "");
    }

    private enableBtnNextDay(enable: boolean): void {
        this.btnNextDay.getComponent(cc.Button).interactable = enable;
        this.btnNextDay.spriteAsync("texture/syncUI/activity/expedition/spTravelButton" + (enable ? 1 : 4));
    }

    private enableBtnFight(enable: boolean): void {
        this.btnFight.getComponent(cc.Button).interactable = enable;
        this.btnFight.spriteAsync("texture/syncUI/activity/expedition/spTravelButton" + (enable ? 2 : 5));
        this.btnFightDot.active = !enable;
        this.btnFightDot.children.forEach((v) => {
            v.active = false;
        });
    }

    private enableBlockInput(enable: boolean): void {
        this.blockInput.active = enable;
    }

    private updateLeadStatus(status: EnumLeadStatus): void {
        this.leadStatus = status;
    }

    private showLuckyTips(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.luckyTips.active = true;
            cc.tween(this.luckyTips)
                .to(0.2, { opacity: 255, scale: 1.4 })
                .to(0.1, { scale: 1.2 })
                .delay(0.5)
                .to(0.2, { opacity: 0, scale: 0 })
                .call(() => {
                    this.luckyTips.active = false;
                    resolve();
                })
                .start();
        });
    }

    private showBattleTips(result: EnumBattleResult): void {
        this.battleTips.spriteAsync("texture/syncUI/activity/expedition/spTravelResult" + result);
        this.battleTips.active = true;
        cc.tween(this.battleTips)
            .to(0.2, { opacity: 255, scale: 1.4 })
            .to(0.1, { scale: 1.2 })
            .delay(0.5)
            .to(0.2, { opacity: 0, scale: 0 })
            .call(() => {
                this.battleTips.active = false;
                this.battleTips.sprite(null);
            })
            .start();
    }

    /**
     * 是否是首条大吉
     * @param event
     * @param eventData
     * @param phrase
     * @returns
     */
    private isFirstBigLuckyEvent(event: IEventObj, eventData: DataExpeditionEvent, phrase: EnumEventPhrase): boolean {
        if (event.luckNum !== EnumExpeditionEventLuck.Luck4) {
            return false;
        }

        switch (phrase) {
            case EnumEventPhrase.Front:
                return true;
            case EnumEventPhrase.Middle:
                return !eventData.frontDesc;
            case EnumEventPhrase.Back:
                return !eventData.frontDesc && !eventData.desc;
            default:
                return false;
        }
    }

    protected onClickSettings(): void {
        UI.getInstance().open("PopupActivityExpeditionSettings");
    }

    protected onClickNextDay(): void {
        if (!this.isTodayEventShowCompleted) {
            return;
        }
        const event = ActivityExpedition.getInstance().getCurrentEvent();
        ActivityExpedition.getInstance().sendExpeditionEventComplete(this.activityId, event.eventUuid, this.eventParas);
    }

    protected onClickChoose(sender: cc.Event.EventTouch, para: number): void {
        const event = ActivityExpedition.getInstance().getCurrentEvent();
        ActivityExpedition.getInstance().emit(EnumActivityExpeditionEvent.GameEventEnd, event, para);
    }

    protected onClickFight(): void {
        this.enableBtnFight(false);
        this.updateLeadStatus(EnumLeadStatus.Fight);
        this.showBattle();
    }

    protected onClickLittleLucky(): void {
        UI.getInstance().open("FloatActivityExpeditionTurntableGame");
    }

    protected onClickBigLucky(): void {
        UI.getInstance().open("FloatActivityExpeditionTreasureGame");
    }

    protected onClickPlay(): void {
        const event = ActivityExpedition.getInstance().getCurrentEvent();
        const eventData = TBExpeditionEvent.getInstance().getDataById(event.eventId);
        switch (eventData.type) {
            case EnumExpeditionEventType.DigTreasure:
                this.enableBlockInput(true);
                UI.getInstance().open("FloatActivityExpeditionTreasureGame", event);
                break;
            case EnumExpeditionEventType.Turntable:
                this.enableBlockInput(true);
                UI.getInstance().open("FloatActivityExpeditionTurntableGame", event);
                break;
            case EnumExpeditionEventType.MagicHat:
                this.enableBlockInput(true);
                UI.getInstance().open("FloatActivityExpeditionHatGame", event);
                break;
            default:
                break;
        }
    }

    protected onClickItemInfo(): void {
        ItemUtils.showInfo(ITEM_ID.LIMIT_GAME_PLAY_COIN);
    }

    /**
     * 关闭UI
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
