/*
 * @Author: chenx
 * @Date: 2025-07-14 14:54:38
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-14 16:11:06
 */
import UI from "../../nsn/ui/UI";
import { UnionSiegeEndChallengeRet } from "../../protobuf/proto";
import { EnumUnionPara } from "../data/base/BaseUnion";
import UnionSiege from "../game/UnionSiege";
import PrefabCombatDungeonPvp from "../prefab/combat/PrefabCombatDungeonPvp";

const { ccclass } = cc._decorator;

/**
 * 公会攻防战副本
 */
@ccclass
export default class UIDungeonCombatUnionDefense extends PrefabCombatDungeonPvp {
    protected registerHandler(): void {
        super.registerHandler();

        // 公会攻防战-结束战斗
        UnionSiege.getInstance().on(
            UnionSiegeEndChallengeRet.prototype.clazzName,
            (data: UnionSiegeEndChallengeRet) => {
                const baseData = this.dungeonPvpCtl.getBaseData();
                baseData.resetTime = 0;

                UI.getInstance().open("FloatActivityUnionDefenseChallengeResult", {
                    type: data.isWin ? EnumUnionPara.UnionDefenseChallengeWin : EnumUnionPara.UnionDefenseChallengeFail,
                    rivalUnionId: data.rivalUnionId,
                    rivalRoleId: data.rivalRoleId,
                    isWin: data.isWin,
                    challengeDifficulty: data.challengeDifficulty,
                });
            },
            this
        );
    }
}
