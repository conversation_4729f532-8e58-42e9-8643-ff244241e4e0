/* 采矿系统 => 科技研究
 * @Author: wangym
 * @Date: 2024-04-08 09:29:58
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-17 14:12:07
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import { MINUTE_TO_SECOND } from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import Tips from "../../nsn/util/Tips";
import {
    AdsWatchRet,
    BagUpdateRet,
    ItemInfo,
    TalentTreeAdQuickenRet,
    TalentTreeBeHelpedRet,
    TalentTreeCompleteRet,
    TalentTreePropQuickenRet,
    TalentTreeType,
    TalentTreeUpgradeRet,
} from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import { EnumHelpType } from "../data/base/BaseHelp";
import { EnumTalentLeafNature } from "../data/base/BaseTalentLeaf";
import { EnumTalentTreeType } from "../data/base/BaseTalentTree";
import TBAdvertisement, { AdConfigId } from "../data/parser/TBAdvertisement";
import TBAttribute, { IAttrInfo } from "../data/parser/TBAttribute";
import TBEconomyAttribute from "../data/parser/TBEconomyAttribute";
import TBTalentLeaf from "../data/parser/TBTalentLeaf";
import TBTalentLeafGroup from "../data/parser/TBTalentLeafGroup";
import TBTalentTree from "../data/parser/TBTalentTree";
import Ad from "../game/Ad";
import Bag from "../game/Bag";
import EconomyAttribute, { EconomyAttributeEvent } from "../game/EconomyAttribute";
import Mining from "../game/Mining";
import Talent from "../game/Talent";
import Union from "../game/Union";
import UnionHelp from "../game/UnionHelp";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import TweenUtil from "../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

const TIME_INTERVAL = 1;

const NODE_INTERVAL = 311.5; // 两个垂直技能节点间的间距（px）

@ccclass
export default class PopupMiningResearch extends I18nComponent {
    // => 属性介绍面板
    @property(cc.Node)
    nodeProperty: cc.Node = null; // 属性介绍面板
    @property(cc.Node)
    lbtResearchName: cc.Node = null; // 属性介绍面板中技能的名称

    @property(cc.Node)
    nodeEffect: cc.Node = null; // 初始加成
    @property(cc.Node)
    lbtEffect: cc.Node = null; // 初始加成文本
    @property(cc.Node)
    nodeSign: cc.Node = null; // 标记
    @property(cc.Node)
    nodeEffectNext: cc.Node = null; // 次级加成
    @property(cc.Node)
    lbtEffectNext: cc.Node = null; // 次级加成文本
    @property(cc.Node)
    nodeMax: cc.Node = null; // 属性介绍面板中技能研究等级达到Max显示的节点
    @property(cc.Node)
    lbtLock: cc.Node = null; // 未解锁提示文本节点
    @property(cc.Node)
    btnSearch: cc.Node = null; // 开始研究按钮
    @property(cc.Node)
    searchTime: cc.Node = null;
    @property(cc.Node)
    desc: cc.Node = null;
    @property(cc.Node)
    maxLbtName: cc.Node = null;

    // => 正在研究面板
    @property(cc.Node)
    nodeResearchingPanel: cc.Node = null; // 正在研究的遮罩面板
    @property(cc.Node)
    lbtResearching: cc.Node = null; // 正在研究的技能显示文本（正在研究 XXXX）
    @property(cc.Node)
    lbtResearchingTime: cc.Node = null; // 正在研究的技能剩余时间倒计时
    @property(cc.Node)
    btnResearchingAd: cc.Node = null; // 看广告加速研究按钮
    @property(cc.Node)
    lbtResearchingAd: cc.Node = null; // 看广告加速研究按钮剩余次数
    @property(cc.Node)
    btnResearchingSpeedUp: cc.Node = null; // 一键加速
    @property(cc.Node)
    nodeCost: cc.Node = null;
    @property(cc.Node)
    maxOrUnlockEffectNode: cc.Node = null;
    @property(cc.Node)
    nodeUnionHelp: cc.Node = null; // 公会互助按钮

    // => 树
    @property(cc.Node)
    nodeTree: cc.Node = null; // 树节点
    @property(cc.Node)
    nodeContent: cc.Node = null; // 内容节点

    private curResearchId: number = null; // 当前已选中的研究点ID

    private curTime: number = 1;

    private isRequest: boolean = false; // 是否正在请求完成研究

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.nodeMax,
                url: "texture/syncUI/mining/spMiningTreeTag",
            },
        ];
    }

    protected onLoad(): void {
        this.initSelect();
        this.updateContent();
    }

    protected update(dt: number): void {
        if (this.curTime >= TIME_INTERVAL) {
            this.curTime -= TIME_INTERVAL;
            const leftTime = Mining.getInstance().getResearchLeftTime();
            this.lbtResearchingTime.label(
                TimeFormat.getInstance().getTextByDuration(Math.max(0, leftTime), TimeDurationFormatType.HH_MM_SS)
            );
            this.onResearchComplete();
        } else {
            this.curTime += dt;
        }
    }

    protected registerHandler(): void {
        Talent.getInstance().on(
            [
                TalentTreeUpgradeRet.prototype.clazzName,
                TalentTreeAdQuickenRet.prototype.clazzName,
                TalentTreePropQuickenRet.prototype.clazzName,
            ],
            () => {
                this.updateContent();
            },
            this
        );
        Talent.getInstance().on(
            TalentTreeCompleteRet.prototype.clazzName,
            (data: TalentTreeCompleteRet) => {
                this.updateContent();
                this.isRequest = false;
                UI.getInstance().open("FloatMiningResearchComplete", { researchId: data.treeInfo.leafId });
            },
            this
        );

        Ad.getInstance().on(
            AdsWatchRet.prototype.clazzName,
            (msg: AdsWatchRet) => {
                if (msg.adInfo.id === AdConfigId.MiningResearch) {
                    Talent.getInstance().sendSpeedUpUpgradeByAd(TalentTreeType.MiningTree);
                }
            },
            this
        );

        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            () => {
                this.updateContent();
            },
            this
        );
        // 公会互助-求助-天赋
        UnionHelp.getInstance().on(
            TalentTreeBeHelpedRet.prototype.clazzName,
            () => {
                this.updateContent();
            },
            this
        );
        // 经济属性-更新
        EconomyAttribute.getInstance().on(
            EconomyAttributeEvent.Update,
            () => {
                this.updateContent();
            },
            this
        );
    }

    private initSelect(): void {
        let tempId: number;
        for (const node of this.nodeTree.children) {
            if (node.getComponent(cc.Button)) {
                const researchId = Number.parseInt(CocosExt.getButtonData(node));
                const researchConfig = TBTalentLeaf.getInstance().getDataById(researchId);
                const level = Talent.getInstance().getLevel(TalentTreeType.MiningTree, researchId);
                tempId = researchId;
                if (level < TBTalentLeafGroup.getInstance().getDataByGroup(researchConfig.group).length) {
                    break;
                } else {
                    continue;
                }
            }
        }
        this.curResearchId = tempId;

        const id = Talent.getInstance().getTreeData(TalentTreeType.MiningTree)?.leafId;
        const leftTime = Mining.getInstance().getResearchLeftTime();
        const node = this.nodeTree.children.find((item) => {
            const component = item.getComponent(cc.Button);
            if (!component) {
                return false;
            }
            const researchId = Number.parseInt(CocosExt.getButtonData(item));
            if (id && leftTime > 0) {
                // 当前正在研究
                return researchId === id;
            } else {
                const researchConfig = TBTalentLeaf.getInstance().getDataById(researchId);
                const level = Talent.getInstance().getLevel(TalentTreeType.MiningTree, researchId);
                const maxLevel = TBTalentLeafGroup.getInstance().getDataByGroup(researchConfig.group).length;
                const isUnlock = Mining.getInstance().isResearchUnlock(researchId);
                return isUnlock && level < maxLevel;
            }
        });
        if (node) {
            this.scheduleOnce(() => {
                this.nodeContent.y = Math.max(-node.y - NODE_INTERVAL - node.height / 2, 0);
            });
        }
    }

    private updateContent(): void {
        for (const node of this.nodeTree.children) {
            if (node.getComponent(cc.Button)) {
                // 技能节点
                this.updateResearch(node);
            } else {
                // 连接线节点
                this.updateLine(node);
            }
        }
        this.updatePanel();
    }

    /**
     * 更新技能面板
     * @param node 节点
     */
    private updateResearch(node: cc.Node): void {
        const data = CocosExt.getButtonData(node);
        const researchId = Number.parseInt(data);
        const researchConfig = TBTalentLeaf.getInstance().getDataById(researchId);
        const level = Talent.getInstance().getLevel(TalentTreeType.MiningTree, researchId);
        const maxLevel = TBTalentLeafGroup.getInstance().getDataByGroup(researchConfig.group).length;
        const isUnlock = Mining.getInstance().isResearchUnlock(researchId);
        node.child("nodeGray").getComponent(GrayComp).gray = !isUnlock;
        node.child("nodeGray")
            .child("spSkillIcon")
            .spriteAsync(`texture/mining/research/icon/iconMiningTreePic${researchConfig.res}`);
        node.child("spSelect").active = this.curResearchId === researchId;
        node.child("nodeProgress").getComponent(cc.ProgressBar).progress = Math.min(1, level / maxLevel);
        node.child("lbtProgress").label(`${level}/${maxLevel}`);
        node.child("spRedDot").active =
            isUnlock && Mining.getInstance().getResearchLeftTime() <= 0 && this.isConsumeEnough(researchConfig.id);
    }

    /**
     * 更新技能面板之间的连接线
     * @param node 连接线节点
     */
    private updateLine(node: cc.Node): void {
        const [, sufSearchId] = node.name.split("l");
        if (!sufSearchId) {
            return;
        }
        node.active = Mining.getInstance().isResearchUnlock(Number.parseInt(sufSearchId));
    }

    /**
     * 更新属性面板
     */
    private updatePanel(): void {
        const researchConfig = TBTalentLeaf.getInstance().getDataById(this.curResearchId);
        const researchData = Talent.getInstance().getTreeData(TalentTreeType.MiningTree);
        const level = Talent.getInstance().getLevel(TalentTreeType.MiningTree, this.curResearchId);
        const isUnlock = Mining.getInstance().isResearchUnlock(this.curResearchId);
        const leftTime = Mining.getInstance().getResearchLeftTime();

        const maxLevel = TBTalentLeafGroup.getInstance().getDataByGroup(
            researchConfig ? researchConfig.group : null
        ).length;
        // 当前等级加成
        const curAttrConfig = TBTalentLeafGroup.getInstance().getDataByGroupAndLevel(
            researchConfig ? researchConfig.group : null,
            level
        );
        // 下一个等级加成
        const nextAttrConfig = TBTalentLeafGroup.getInstance().getDataByGroupAndLevel(
            researchConfig ? researchConfig.group : null,
            level + 1
        );
        this.lbtResearchName.label(researchConfig ? researchConfig.name : "");
        this.lbtResearchName.active = leftTime > 0 && level !== maxLevel;

        // 加成效果
        this.nodeEffect.active = true;
        this.nodeEffectNext.active = level !== 0 && level !== maxLevel;
        this.nodeSign.active = level !== 0 && level !== maxLevel;
        // 当前加成文本
        const curData: IAttrInfo = curAttrConfig && {
            type: curAttrConfig.attribute[0][0],
            value: curAttrConfig.attribute[0][1],
        };
        const nextData: IAttrInfo = nextAttrConfig && {
            type: nextAttrConfig.attribute[0][0],
            value: nextAttrConfig.attribute[0][1],
        };
        switch (researchConfig.nature) {
            case EnumTalentLeafNature.Attribute:
                this.lbtEffect.getComponent(cc.RichText).string = TextUtils.format(
                    level === 0 ? nextAttrConfig?.text : curAttrConfig?.text,
                    `${
                        (level === 0 ? nextData : curData)
                            ? TBAttribute.getInstance().formatAttribute(level === 0 ? nextData : curData).value
                            : ""
                    }`
                );
                if (this.nodeEffectNext.active) {
                    this.lbtEffectNext.getComponent(cc.RichText).string = TextUtils.format(
                        curAttrConfig?.text,
                        `<color=#93ffa5>${
                            nextData
                                ? TBAttribute.getInstance().formatAttribute([nextData.type, nextData.value]).value
                                : ""
                        }</color>`
                    );
                }

                break;
            case EnumTalentLeafNature.EconomyAttribute:
                this.lbtEffect.getComponent(cc.RichText).string = TextUtils.format(
                    level === 0 ? nextAttrConfig?.text : curAttrConfig?.text,
                    `${
                        (level === 0 ? nextData : curData)
                            ? TBEconomyAttribute.getInstance().formatAttribute(level === 0 ? nextData : curData).value
                            : ""
                    }`
                );
                if (this.nodeEffectNext.active) {
                    this.lbtEffectNext.getComponent(cc.RichText).string = TextUtils.format(
                        curAttrConfig?.text,
                        `<color=#93ffa5>${
                            nextData
                                ? TBEconomyAttribute.getInstance().formatAttribute([nextData.type, nextData.value])
                                      .value
                                : ""
                        }</color>`
                    );
                }
                break;
            default:
                break;
        }

        // 研究按钮
        this.btnSearch.active = isUnlock && !!nextAttrConfig;
        this.btnSearch.button(this.curResearchId);
        this.searchTime.label(
            nextAttrConfig
                ? TimeFormat.getInstance().getTextByDuration(
                      nextAttrConfig.time * MINUTE_TO_SECOND * 1000,
                      TimeDurationFormatType.HH_MM_SS
                  )
                : ""
        );
        this.nodeCost.active = isUnlock && !!nextAttrConfig;
        if (this.nodeCost.active) {
            ImageUtils.setItemIcon(this.nodeCost.child("icon"), nextAttrConfig?.cost[0][0]);
            ItemUtils.refreshCount(this.nodeCost.child("cost"), nextAttrConfig?.cost[0][0], nextAttrConfig?.cost[0][1]);
        }

        // max
        this.nodeMax.active = level > 0 && level === maxLevel;
        this.maxLbtName.active = level > 0 && level === maxLevel;
        this.maxLbtName.label(researchConfig ? researchConfig.name : "");
        this.desc.active = leftTime <= 0 && level !== maxLevel;
        this.desc.label(researchConfig ? researchConfig.name : "");
        this.maxOrUnlockEffectNode.active = level === maxLevel || !isUnlock;
        if (level === maxLevel) {
            switch (researchConfig.nature) {
                case EnumTalentLeafNature.Attribute:
                    this.maxOrUnlockEffectNode
                        .child("lbtDesc")
                        .richText(
                            TextUtils.format(
                                level === 0 ? nextAttrConfig?.text : curAttrConfig?.text,
                                `<color=#93ffa5>${
                                    (level === 0 ? nextData : curData)
                                        ? TBAttribute.getInstance().formatAttribute(level === 0 ? nextData : curData)
                                              .value
                                        : ""
                                }</color>`
                            )
                        );
                    break;
                case EnumTalentLeafNature.EconomyAttribute:
                    this.maxOrUnlockEffectNode
                        .child("lbtDesc")
                        .richText(
                            TextUtils.format(
                                level === 0 ? nextAttrConfig?.text : curAttrConfig?.text,
                                `<color=#93ffa5>${
                                    (level === 0 ? nextData : curData)
                                        ? TBEconomyAttribute.getInstance().formatAttribute(
                                              level === 0 ? nextData : curData
                                          ).value
                                        : ""
                                }</color>`
                            )
                        );
                    break;
                default:
                    break;
            }
        } else if (!isUnlock) {
            switch (researchConfig.nature) {
                case EnumTalentLeafNature.Attribute:
                    this.maxOrUnlockEffectNode
                        .child("lbtDesc")
                        .richText(
                            TextUtils.format(
                                level === 0 ? nextAttrConfig?.text : curAttrConfig?.text,
                                `${
                                    (level === 0 ? nextData : curData)
                                        ? TBAttribute.getInstance().formatAttribute(level === 0 ? nextData : curData)
                                              .value
                                        : ""
                                }`
                            )
                        );
                    break;
                case EnumTalentLeafNature.EconomyAttribute:
                    this.maxOrUnlockEffectNode
                        .child("lbtDesc")
                        .richText(
                            TextUtils.format(
                                level === 0 ? nextAttrConfig?.text : curAttrConfig?.text,
                                `${
                                    (level === 0 ? nextData : curData)
                                        ? TBEconomyAttribute.getInstance().formatAttribute(
                                              level === 0 ? nextData : curData
                                          ).value
                                        : ""
                                }`
                            )
                        );
                    break;
                default:
                    break;
            }
        }

        // 未解锁提示文本
        this.lbtLock.active = !isUnlock;
        this.lbtLock.getComponent(cc.RichText).string =
            (researchConfig?.previous || [])
                .map((item) => {
                    const [preResearchId, preLevel] = item;
                    const name = TBTalentLeaf.getInstance().getDataById(preResearchId).name;
                    return (
                        `<color=#615A5B>` +
                        TextUtils.format(i18n.mining0001, name, `<color=#688A28>${preLevel}</color>`) +
                        `</color>`
                    );
                })
                .join("\n") +
            `\n<color=#948485><size=32>` +
            i18n.mining0033 +
            `</color></s>`;

        // 遮罩
        this.nodeResearchingPanel.active = researchData && researchData.leafId && !researchData.isComplete;
        this.lbtResearching.getComponent(cc.RichText).string =
            `<color=#443933>` +
            TextUtils.format(
                i18n.mining0002,
                `<outline color=#121212 width=2><color=#00FF18><color=#EEEEEE>${
                    TBTalentLeaf.getInstance().getDataById(researchData.leafId)?.name || ""
                }</color></o>`
            ) +
            `</c>`;
        this.lbtResearchingTime.label(
            TimeFormat.getInstance().getTextByDuration(Math.max(0, leftTime), TimeDurationFormatType.HH_MM_SS)
        );

        this.btnResearchingAd.active = leftTime > 0;
        const adConfig = TBAdvertisement.getInstance().getDataById(AdConfigId.MiningResearch);
        this.lbtResearchingAd.label(
            TextUtils.format(
                i18n.mining0009,
                adConfig?.count - Ad.getInstance().getWatchTimesById(AdConfigId.MiningResearch), // 已观看次数
                adConfig?.count || 0 // 总次数
            )
        );
        this.btnResearchingAd.active = leftTime > 0;
        this.btnResearchingAd.button(researchData.leafId);
        this.btnResearchingSpeedUp.active = leftTime > 0;
        this.btnResearchingSpeedUp.button(researchData.leafId);
        const isUnionHelp = leftTime > 0 && !!Union.getInstance().getInfo();
        this.nodeUnionHelp.active = isUnionHelp;
        if (isUnionHelp) {
            const isHelp = UnionHelp.getInstance().getHelpState(EnumHelpType.TalentHelp);
            this.nodeUnionHelp.child("lbtText").label(isHelp ? i18n.union0083 : i18n.union0084);
            TweenUtil.swing(this.nodeUnionHelp, isHelp);
        }
    }

    /**
     * 是否有足够的道具进行研究
     * @param researchId 研究ID
     * @returns {boolean}
     */
    private isConsumeEnough(researchId: number): boolean {
        const researchConfig = TBTalentLeaf.getInstance().getDataById(researchId);
        const level = Talent.getInstance().getLevel(TalentTreeType.MiningTree, researchId);
        const nextAttrConfig = TBTalentLeafGroup.getInstance().getDataByGroupAndLevel(researchConfig.group, level + 1);
        if (!nextAttrConfig) {
            // 已达最大等级
            return false;
        }
        let result = true;
        for (const consume of nextAttrConfig.cost) {
            const data = ItemInfo.create({ itemInfoId: consume[0], num: consume[1] });
            if (!Bag.getInstance().isEnough(data.itemInfoId, data.num)) {
                result = false;
                break;
            }
        }
        return result;
    }

    /**
     * 研发完成，发送完成请求
     * @returns
     */
    protected onResearchComplete(): void {
        const researchData = Talent.getInstance().getTreeData(TalentTreeType.MiningTree);
        if (!researchData || !researchData.leafId || researchData.isComplete) {
            return;
        }
        if (Mining.getInstance().getResearchLeftTime() >= 0) {
            return;
        }
        if (this.isRequest) {
            return;
        }
        this.isRequest = true;
        Talent.getInstance().sendFinishUpgrade(TalentTreeType.MiningTree);
    }

    /**
     * 点击观看广告
     * @param sender
     * @param researchId
     * @returns
     */
    protected onClickWatchAdvertisement(sender: cc.Event.EventTouch, researchId: number): void {
        const researchData = Talent.getInstance().getTreeData(TalentTreeType.MiningTree);
        if (!researchData || researchId !== researchData.leafId) {
            // 没有正在进行的研究，或正在研究的非当前技能
            return;
        }
        const leftTime = Mining.getInstance().getResearchLeftTime();
        if (leftTime <= 0) {
            Tips.getInstance().info(i18n.mining0003);
            return;
        }
        // 广告可观看次数不足
        if (!Ad.getInstance().hasWatchTimesById(AdConfigId.MiningResearch)) {
            Tips.getInstance().info(i18n.ads0003);
            return;
        }
        if (Ad.getInstance().checkAdPrivilege(AdConfigId.MiningResearch)) {
            // 免广告特权
            Ad.getInstance().sendAdsWatch(AdConfigId.MiningResearch);
        } else {
            // 正常观看广告
            UI.getInstance().open("FloatMiningWatchAdvertisement", {
                adId: AdConfigId.MiningResearch,
                adText: i18n.mining0027,
                tips: i18n.mining0028,
                res: "texture/mining/ad/spScienceParkPicture",
                adTips: i18n.mining0031,
            });
        }
    }

    /**
     * 点击消耗道具加速研究
     * @param sender
     * @param researchId
     * @returns
     */
    protected onClickConsumeItem(sender: cc.Event.EventTouch, researchId: number): void {
        const researchData = Talent.getInstance().getTreeData(TalentTreeType.MiningTree);
        if (!researchData || researchId !== researchData.leafId) {
            return;
        }
        const leftTime = Mining.getInstance().getResearchLeftTime();
        if (leftTime <= 0) {
            Tips.getInstance().info(i18n.mining0003);
            return;
        }
        const talentTreeInfo = TBTalentTree.getInstance().getDataByType(EnumTalentTreeType.MiningTalent);
        if (!Bag.getInstance().isEnough(talentTreeInfo.speedItem, 1)) {
            UI.getInstance().open("FloatItemSource", talentTreeInfo.speedItem);
            return;
        }
        UI.getInstance().open("PopupItemUse", {
            itemInfoId: talentTreeInfo.speedItem,
            getEffectText: (count: number) => {
                return TextUtils.format(
                    i18n.mining0008,
                    TimeFormat.getInstance().getTextByDuration(
                        5 * MINUTE_TO_SECOND * 1000 * count,
                        TimeDurationFormatType.HH_MM_SS
                    )
                );
            },
            getMaxCount: () => {
                return Math.ceil(leftTime / (5 * MINUTE_TO_SECOND * 1000));
            },
            useItem: (count: number) => {
                const tempCount = Math.min(
                    Math.ceil(Mining.getInstance().getResearchLeftTime() / (5 * MINUTE_TO_SECOND * 1000)),
                    count
                );
                Talent.getInstance().sendSpeedUpUpgradeByItem(TalentTreeType.MiningTree, tempCount);
            },
        });
    }

    /**
     * 点击选中某个研究
     * @param sender
     * @param researchId
     * @returns {void}
     */
    protected onClickSelectResearch(sender: cc.Event.EventTouch, data: string): void {
        const researchId = Number.parseInt(data);
        if (!researchId || this.curResearchId === researchId) {
            return;
        }
        this.curResearchId = researchId;
        this.updateContent();
    }

    /**
     * 点击开始研究
     * @param sender
     * @param researchId
     * @returns
     */
    protected onClickResearch(sender: cc.Event.EventTouch, researchId: number): void {
        const leftTime = Mining.getInstance().getResearchLeftTime();
        if (leftTime > 0) {
            Tips.getInstance().info(i18n.mining0004);
            return;
        }
        if (!Mining.getInstance().isResearchUnlock(researchId)) {
            Tips.getInstance().info(i18n.mining0005);
            return;
        }
        const researchConfig = TBTalentLeaf.getInstance().getDataById(this.curResearchId);
        const level = Talent.getInstance().getLevel(TalentTreeType.MiningTree, researchId);
        if (level >= TBTalentLeafGroup.getInstance().getDataByGroup(researchConfig.group).length) {
            Tips.getInstance().info(i18n.mining0006);
            return;
        }
        const nextAttrConfig = TBTalentLeafGroup.getInstance().getDataByGroupAndLevel(researchConfig.group, level + 1);
        if (!Bag.getInstance().isEnough(nextAttrConfig.cost[0][0], nextAttrConfig.cost[0][1])) {
            Tips.getInstance().info(i18n.common0025);
            return;
        }
        Talent.getInstance().sendUpgrade(TalentTreeType.MiningTree, researchId);
    }

    /**
     * 公会互助
     */
    protected onClickUnionHelp(): void {
        if (!UnionHelp.getInstance().getHelpState(EnumHelpType.TalentHelp)) {
            return;
        }

        UnionHelp.getInstance().sendHelpByTalent(TalentTreeType.MiningTree);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
