/*
 * @Author: JackyF<PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-12 14:18:27
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import Platform from "../../nsn/platform/Platform";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { IUnionInfo, IUnionMember, PlayerInfoQueryRet, UnionGetRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import TBUnionLevel from "../data/parser/TBUnionLevel";
import Player from "../game/Player";
import Union from "../game/Union";
import UnionSiege from "../game/UnionSiege";
import CombatScoreUtils from "../utils/CombatScoreUtils";
import ImageUtils from "../utils/ImageUtils";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupActivityUnionDefenseUnionDetails extends I18nComponent {
    @property(cc.Node)
    flag: cc.Node = null;
    @property(cc.Node)
    unionId: cc.Node = null;
    @property(cc.Node)
    unionLv: cc.Node = null;
    @property(cc.Node)
    unionName: cc.Node = null;
    @property(cc.Node)
    unionCount: cc.Node = null;
    @property(cc.Node)
    unionServe: cc.Node = null;
    @property(cc.Node)
    unionCombat: cc.Node = null;
    @property(cc.Node)
    leadHead: cc.Node = null;
    @property(cc.Node)
    leadName: cc.Node = null;
    @property(cc.Node)
    leadCombat: cc.Node = null;
    @property(cc.Node)
    leadPower: cc.Node = null;
    @property(cc.Node)
    leadTag: cc.Node = null;

    private unionInfoId: number = 0;

    protected onLoad(): void {
        this.unionInfoId = this.args;
        UI.getInstance().showSoftLoading();
        Union.getInstance().sendUnionGet(this.unionInfoId);
        this.updateUI(true);
    }

    protected registerHandler(): void {
        Player.getInstance().on(
            PlayerInfoQueryRet.prototype.clazzName,
            (data: PlayerInfoQueryRet) => {
                this.unionServe.label(data.serverName);
            },
            this
        );

        Union.getInstance().on(
            UnionGetRet.prototype.clazzName,
            (unionInfo: IUnionInfo, members: IUnionMember[]) => {
                const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
                const info = unionSiegeInfo.find((e) => e.unionId === this.unionInfoId);
                const level = TBUnionLevel.getInstance().getLevelByExp(info.unionInfo.exp);
                const unionLevelData = TBUnionLevel.getInstance().getDataById(level);
                this.unionCount.label(members.length + "/" + unionLevelData.peopleLimit);
            },
            this
        );
    }

    private updateUI(initServeName: boolean = false): void {
        const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
        const info = unionSiegeInfo.find((e) => e.unionId === this.unionInfoId);
        const level = TBUnionLevel.getInstance().getLevelByExp(info.unionInfo.exp);
        initServeName && Player.getInstance().sendPlayerInfoQuery(info.leaderInfo.playerId);
        ImageUtils.setUnionFlag(this.flag, info.unionInfo.flag);
        this.unionId.label(TextUtils.format(i18n.unionDefense0001, info.unionId));
        this.unionLv.label("Lv." + level);
        this.unionName.label(info.unionInfo.name);
        CombatScoreUtils.update(this.unionCombat, info.sumGVGCombat);
        PlayerInfoUtils.updateHead(this.leadHead, info.leaderInfo);
        this.leadName.label(info.leaderInfo.name);
        CombatScoreUtils.update(this.leadCombat, info.leaderInfo.combatScore);
        PlayerInfoUtils.updatePower(this.leadPower, info.leaderInfo);
        this.leadTag.spriteAsync("texture/union/position/iconUnionTag1"); // 会长标识
    }

    protected onClickCopyID(): void {
        Platform.getInstance().copyToClipboard(this.unionInfoId + "");
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
