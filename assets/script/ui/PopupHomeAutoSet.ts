/*
 * @Author: chenx
 * @Date: 2025-04-16 17:20:43
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:41:39
 */
import DropMenu, { IDrawMenuInfo } from "../../nsn/comp/ui/DropMenu";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import Tips from "../../nsn/util/Tips";
import { MapBarrierSyncRet, PrivilegeUpdateNoticeRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointId } from "../core/redPoint/RedPointId";
import { EnumPrivilegeConfigType } from "../data/base/BasePrivilegeConfig";
import { EnumUniversalPara } from "../data/base/BaseUniversal";
import { GAME_SWITCH_ID } from "../data/parser/TBGameSwitch";
import TBPrivilegeConfig, { PRIVILEGE_ID } from "../data/parser/TBPrivilegeConfig";
import TBUniversal from "../data/parser/TBUniversal";
import Combat, { CombatEvent, DungeonType } from "../game/Combat";
import DungeonMain from "../game/DungeonMain";
import GameSwitch from "../game/GameSwitch";
import MakeArrow, { MakeArrowEvent } from "../game/MakeArrow";
import Privilege, { PrivilegeEvent } from "../game/Privilege";
import Setting, { SettingId, SettingScene } from "../game/Setting";

/**
 * 下拉菜单index-制作消耗
 */
enum DropMenuIndex {
    Cost = 0, // 消耗-x1
    Cost2, // 消耗-x2
    Cost3, // 消耗-x4
}

/**
 * 下拉菜单index-游戏速度
 */
enum DropMenuIndex2 {
    Speed = 0, // 速度-x1
    Speed2, // 速度-x1.5
    Speed3, // 速度-x2
}

const { ccclass, property } = cc._decorator;

/**
 * 主界面-自动设置
 */
@ccclass
export default class PopupHomeAutoSet extends I18nComponent {
    @property(cc.Sprite)
    spTitle: cc.Sprite = null; // 标题
    @property(DropMenu)
    dropMenu: DropMenu = null; // 制作消耗
    @property(DropMenu)
    dropMenu2: DropMenu = null; // 游戏速度
    @property(cc.Node)
    nodeCheckbox: cc.Node = null; // 勾选框
    @property(cc.Node)
    nodeCheckbox2: cc.Node = null; // 勾选框
    @property(cc.Node)
    nodeSwitch: cc.Node = null; // 开关-开启自动制作
    @property(cc.Node)
    nodeSwitch2: cc.Node = null; // 开关-关闭自动制作

    private dropMenuIndex: number = -1; // 下拉菜单index-制作消耗
    private dropMenuData: IDrawMenuInfo = null; // 下拉菜单数据-制作消耗
    private dropMenuIndex2: number = -1; // 下拉菜单index-游戏速度
    private dropMenuData2: IDrawMenuInfo = null; // 下拉菜单数据-游戏速度

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/syncUI/dungeon/spMIAutoTitle",
            },
        ];
    }

    protected onLoad(): void {
        this.initInfo();
        this.initDropMenu();
        this.updateCheckboxState();
        this.updateCheckboxState2();
    }

    protected registerHandler(): void {
        // 特权-更新通知/更新生效状态
        Privilege.getInstance().on(
            [PrivilegeUpdateNoticeRet.prototype.clazzName, PrivilegeEvent.UpdateEffectState],
            () => {
                this.initDropMenu();
            },
            this
        );
        // 主线副本-同步数据
        DungeonMain.getInstance().on(
            MapBarrierSyncRet.prototype.clazzName,
            () => {
                this.updateCheckboxState2();
            },
            this
        );
    }

    /**
     * 初始化信息
     */
    protected initInfo(): void {
        const isOpen = Setting.getInstance().getSwitchState(SettingId.MakeArrowAutoDispose);
        this.nodeSwitch.active = !isOpen;
        this.nodeSwitch2.active = isOpen;
    }

    /**
     * 初始化下拉菜单
     */
    private initDropMenu(): void {
        this.dropMenuData = { info: [], status: [] };
        [DropMenuIndex.Cost, DropMenuIndex.Cost2, DropMenuIndex.Cost3].forEach((e, i) => {
            switch (e) {
                case DropMenuIndex.Cost:
                    this.dropMenuIndex = i;
                    this.dropMenuData.info.push(1 + "");
                    this.dropMenuData.status.push(true);
                    break;
                case DropMenuIndex.Cost2:
                    {
                        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.MAKE_COST);
                        const isEffect = Privilege.getInstance().hasPrivilege(privilegeInfo.id);
                        if (Setting.getInstance().getSwitchState(SettingId.MakeArrowCost)) {
                            if (isEffect) {
                                this.dropMenuIndex = i;
                            } else {
                                Setting.getInstance().setSwitchState(SettingId.MakeArrowCost);
                            }
                        }
                        this.dropMenuData.info.push(privilegeInfo.para.value + "");
                        this.dropMenuData.status.push(isEffect);
                    }
                    break;
                case DropMenuIndex.Cost3:
                    {
                        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.MAKE_COST_2);
                        const isEffect = Privilege.getInstance().hasPrivilege(privilegeInfo.id);
                        if (Setting.getInstance().getSwitchState(SettingId.MakeArrowCost2)) {
                            if (isEffect) {
                                this.dropMenuIndex = i;
                            } else {
                                Setting.getInstance().setSwitchState(SettingId.MakeArrowCost2);
                            }
                        }
                        this.dropMenuData.info.push(privilegeInfo.para.value + "");
                        this.dropMenuData.status.push(isEffect);
                    }
                    break;
                default:
                    break;
            }
        });
        this.dropMenu.setDefaultIndex(this.dropMenuIndex);
        this.dropMenu.initDropMenuInfo(this.dropMenuData);

        this.dropMenuData2 = { info: [], status: [] };
        [DropMenuIndex2.Speed, DropMenuIndex2.Speed2, DropMenuIndex2.Speed3].forEach((e, i) => {
            switch (e) {
                case DropMenuIndex2.Speed:
                    this.dropMenuIndex2 = i;
                    this.dropMenuData2.info.push(1 + "");
                    this.dropMenuData2.status.push(true);
                    break;
                case DropMenuIndex2.Speed2:
                    {
                        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.GAME_SPEED);
                        const isEffect = Privilege.getInstance().hasPrivilege(privilegeInfo.id);
                        if (Setting.getInstance().getSwitchState(SettingId.DungeonMainGameSpeed)) {
                            if (isEffect) {
                                this.dropMenuIndex2 = i;
                            } else {
                                Setting.getInstance().setSwitchState(SettingId.DungeonMainGameSpeed);
                            }
                        }
                        this.dropMenuData2.info.push(privilegeInfo.para.value + "");
                        this.dropMenuData2.status.push(isEffect);
                    }
                    break;
                case DropMenuIndex2.Speed3:
                    {
                        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.GAME_SPEED_2);
                        const isEffect = Privilege.getInstance().hasPrivilege(privilegeInfo.id);
                        if (Setting.getInstance().getSwitchState(SettingId.DungeonMainGameSpeed2)) {
                            if (isEffect) {
                                this.dropMenuIndex2 = i;
                            } else {
                                Setting.getInstance().setSwitchState(SettingId.DungeonMainGameSpeed2);
                            }
                        }
                        this.dropMenuData2.info.push(privilegeInfo.para.value + "");
                        this.dropMenuData2.status.push(isEffect);
                    }
                    break;
                default:
                    break;
            }
        });
        this.dropMenu2.setDefaultIndex(this.dropMenuIndex2);
        this.dropMenu2.initDropMenuInfo(this.dropMenuData2);
    }

    /**
     * 更新勾选框状态
     */
    private updateCheckboxState(): void {
        const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.EQUIP_AUTO_DISPOSE);
        this.nodeCheckbox.child("nodeLock").active = !result;
        const nodeBg = this.nodeCheckbox.child("spBg");
        nodeBg.active = result;
        const isCheck = result && Setting.getInstance().getSwitchState(SettingId.EquipAutoDispose);
        result && (nodeBg.child("spIcon").active = isCheck);
    }

    /**
     * 更新勾选框状态
     */
    private updateCheckboxState2(): void {
        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataListByType(EnumPrivilegeConfigType.MainJump);
        const isEffect = privilegeInfo.findIndex((e) => Privilege.getInstance().hasPrivilege(e.id)) !== -1;
        const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.DUNGEON_MAIN_SKIP_LEVEL);
        this.nodeCheckbox2.child("nodeLock").active = !isEffect && !result;
        const nodeBg = this.nodeCheckbox2.child("spBg");
        nodeBg.active = isEffect || result;
        const isCheck = (isEffect || result) && Setting.getInstance().getSwitchState(SettingId.DungeonMainSkipLevel);
        (isEffect || result) && (nodeBg.child("spIcon").active = isCheck);
        const levelData = DungeonMain.getInstance().getLevelData();
        if (isCheck) {
            const para: number[][] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.PassesXLevels);
            let maxTime = 0;
            para.forEach(([, time]) => {
                maxTime = Math.max(maxTime, time);
            });
            let passLevelNum = 0;
            for (const e of levelData.passTimes) {
                if (e > maxTime) {
                    break;
                }

                passLevelNum++;
            }
            if (passLevelNum !== 0) {
                this.nodeCheckbox2.child("lbtText").label(TextUtils.format(i18n.dungeon0050, passLevelNum));
            } else {
                this.nodeCheckbox2.child("lbtText").label(i18n.dungeon0049);
            }
        } else {
            this.nodeCheckbox2.child("lbtText").label(i18n.dungeon0049);
        }
    }

    /**
     * 检测下拉菜单项-制作消耗
     * @param index
     */
    protected checkDropMenuItem(index: number): boolean {
        if (RedPoint.getInstance().isRecord(RedPointId.MakeCostUnlock)) {
            RedPoint.getInstance().cancelRecord(RedPointId.MakeCostUnlock);
            RedPoint.getInstance().check(RedPointId.MakeCostUnlock);
        }

        if (!this.dropMenuData.status[index]) {
            switch (index) {
                case DropMenuIndex.Cost:
                    break;
                case DropMenuIndex.Cost2:
                    {
                        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.MAKE_COST);
                        Tips.getInstance().info(privilegeInfo.show);
                    }
                    break;
                case DropMenuIndex.Cost3:
                    {
                        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.MAKE_COST_2);
                        Tips.getInstance().info(privilegeInfo.show);
                    }
                    break;
                default:
                    break;
            }
        }
        return this.dropMenuData.status[index];
    }

    /**
     * 检测下拉菜单项-游戏速度
     * @param index
     */
    protected checkDropMenuItem2(index: number): boolean {
        if (RedPoint.getInstance().isRecord(RedPointId.GameSpeedUnlock)) {
            RedPoint.getInstance().cancelRecord(RedPointId.GameSpeedUnlock);
            RedPoint.getInstance().check(RedPointId.GameSpeedUnlock);
        }

        if (!this.dropMenuData2.status[index]) {
            switch (index) {
                case DropMenuIndex2.Speed:
                    break;
                case DropMenuIndex2.Speed2:
                    {
                        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.GAME_SPEED);
                        Tips.getInstance().info(privilegeInfo.show);
                    }
                    break;
                case DropMenuIndex2.Speed3:
                    {
                        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.GAME_SPEED_2);
                        Tips.getInstance().info(privilegeInfo.show);
                    }
                    break;
                default:
                    break;
            }
        }
        return this.dropMenuData2.status[index];
    }

    /**
     * 选择下拉菜单项-制作消耗
     * @param index
     */
    protected onClickSelectDropMenuItem(index: number): void {
        if (this.dropMenuIndex === index) {
            return;
        }

        this.dropMenuIndex = index;
        switch (this.dropMenuIndex) {
            case DropMenuIndex.Cost:
                Setting.getInstance().getSwitchState(SettingId.MakeArrowCost) &&
                    Setting.getInstance().setSwitchState(SettingId.MakeArrowCost, false);
                Setting.getInstance().getSwitchState(SettingId.MakeArrowCost2) &&
                    Setting.getInstance().setSwitchState(SettingId.MakeArrowCost2, false);
                break;
            case DropMenuIndex.Cost2:
                Setting.getInstance().setSwitchState(SettingId.MakeArrowCost, false);
                Setting.getInstance().getSwitchState(SettingId.MakeArrowCost2) &&
                    Setting.getInstance().setSwitchState(SettingId.MakeArrowCost2, false);
                break;
            case DropMenuIndex.Cost3:
                Setting.getInstance().getSwitchState(SettingId.MakeArrowCost) &&
                    Setting.getInstance().setSwitchState(SettingId.MakeArrowCost, false);
                Setting.getInstance().setSwitchState(SettingId.MakeArrowCost2, false);
                break;
            default:
                break;
        }
        Setting.getInstance().saveData(SettingScene.Switch);

        MakeArrow.getInstance().emit(MakeArrowEvent.UpdateMakeCost);
    }

    /**
     * 选择下拉菜单项-游戏速度
     * @param index
     */
    protected onClickSelectDropMenuItem2(index: number): void {
        if (this.dropMenuIndex2 === index) {
            return;
        }

        this.dropMenuIndex2 = index;
        switch (this.dropMenuIndex2) {
            case DropMenuIndex2.Speed:
                Setting.getInstance().getSwitchState(SettingId.DungeonMainGameSpeed) &&
                    Setting.getInstance().setSwitchState(SettingId.DungeonMainGameSpeed, false);
                Setting.getInstance().getSwitchState(SettingId.DungeonMainGameSpeed2) &&
                    Setting.getInstance().setSwitchState(SettingId.DungeonMainGameSpeed2, false);
                break;
            case DropMenuIndex2.Speed2:
                Setting.getInstance().setSwitchState(SettingId.DungeonMainGameSpeed, false);
                Setting.getInstance().getSwitchState(SettingId.DungeonMainGameSpeed2) &&
                    Setting.getInstance().setSwitchState(SettingId.DungeonMainGameSpeed2, false);
                break;
            case DropMenuIndex2.Speed3:
                Setting.getInstance().getSwitchState(SettingId.DungeonMainGameSpeed) &&
                    Setting.getInstance().setSwitchState(SettingId.DungeonMainGameSpeed, false);
                Setting.getInstance().setSwitchState(SettingId.DungeonMainGameSpeed2, false);
                break;
            default:
                break;
        }
        Setting.getInstance().saveData(SettingScene.Switch);

        Combat.getInstance().emit(CombatEvent.UpdateGameSpeed, DungeonType.Main);
    }

    /**
     * 设置勾选框状态
     */
    protected onClickSetCheckboxState(): void {
        const { result, msg } = GameSwitch.getInstance().check(GAME_SWITCH_ID.EQUIP_AUTO_DISPOSE);
        if (!result) {
            Tips.getInstance().info(msg);
            return;
        }

        Setting.getInstance().setSwitchState(SettingId.EquipAutoDispose);
        this.updateCheckboxState();
    }

    /**
     * 设置勾选框状态
     */
    protected onClickSetCheckboxState2(): void {
        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataListByType(EnumPrivilegeConfigType.MainJump);
        const isEffect = privilegeInfo.findIndex((e) => Privilege.getInstance().hasPrivilege(e.id)) !== -1;
        if (!isEffect) {
            const { result, msg } = GameSwitch.getInstance().check(GAME_SWITCH_ID.DUNGEON_MAIN_SKIP_LEVEL);
            if (!result) {
                Tips.getInstance().info(msg);
                return;
            }
        }

        Setting.getInstance().setSwitchState(SettingId.DungeonMainSkipLevel);
        this.updateCheckboxState2();
    }

    /**
     * 设置开关状态-自动制作
     */
    protected onClickSetSwitchState(): void {
        Setting.getInstance().setSwitchState(SettingId.MakeArrowAutoDispose);
        MakeArrow.getInstance().emit(MakeArrowEvent.AutoMakeSwitch);
        UI.getInstance().close();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
