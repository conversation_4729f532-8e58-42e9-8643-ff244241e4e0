/*
 * @Author: chenx
 * @Date: 2024-10-12 11:46:31
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-02 15:13:35
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { IPlayerInfo } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumPrincessChildSex } from "../data/base/BasePrincessChild";
import TBAttribute from "../data/parser/TBAttribute";
import TBItem from "../data/parser/TBItem";
import TBPrincessChild from "../data/parser/TBPrincessChild";
import ImageUtils from "../utils/ImageUtils";
import NumberUtils from "../utils/NumberUtils";

/**
 * 界面参数
 */
export interface IFloatChildPreviewArgs {
    childId: number; // 王储id
    playerData: IPlayerInfo; // 玩家数据
}

const { ccclass, property } = cc._decorator;

/**
 * 知己-王储预览
 */
@ccclass
export default class FloatChildPreview extends I18nComponent {
    @property(cc.Sprite)
    spIcon: cc.Sprite = null; // 王储icon
    @property(cc.Sprite)
    spQualityIcon: cc.Sprite = null; // 品质icon
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    nodeMenTag: cc.Node = null; // 男性tag
    @property(cc.Node)
    nodeWomenTag: cc.Node = null; // 女性tag
    @property(cc.Label)
    lbtLevel: cc.Label = null; // 等级
    @property(cc.Label)
    lbtScore: cc.Label = null; // 评分
    @property(cc.Label)
    lbtPlayerName: cc.Label = null; // 玩家名称
    @property(cc.Node)
    nodeContent: cc.Node = null; // 属性content
    @property(cc.Node)
    nodeItem: cc.Node = null; // 属性item

    @property(cc.Node)
    nodeCloseTips: cc.Node = null; // 关闭提示

    initData: IFloatChildPreviewArgs = null; // 初始数据

    protected onLoad(): void {
        this.nodeItem.parent = null;

        this.initData = this.args;
        this.initInfo();
    }

    protected onDestroy(): void {
        this.nodeItem.destroy();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        const childInfo = TBPrincessChild.getInstance().getDataById(this.initData.childId);
        let tempRes = "";
        for (const [level, res] of childInfo.res) {
            if (childInfo.levelLimit >= level) {
                tempRes = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantIcon2(this.spIcon, tempRes);
        const itemInfo = TBItem.getInstance().getDataById(this.initData.childId);
        ImageUtils.setConfidantQualityIcon(this.spQualityIcon, itemInfo.quality);
        this.lbtName.string = childInfo.name;
        this.nodeMenTag.active = childInfo.sex === EnumPrincessChildSex.Boy;
        this.nodeWomenTag.active = childInfo.sex === EnumPrincessChildSex.Girl;
        this.lbtLevel.string = childInfo.levelLimit + "";
        let score = 0;
        childInfo.attribute.forEach(([attrId, init, step]) => {
            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
            score = score + (init + step * (childInfo.levelLimit - 1)) * attrInfo.combat;
        });
        this.lbtScore.string = TextUtils.format(i18n.confidant0015, NumberUtils.format(score, 1, 0));
        this.lbtPlayerName.string = TextUtils.format(i18n.confidant0028, this.initData.playerData.name);
        let nodeItem: cc.Node = null;
        const attr = childInfo.attribute.concat();
        attr.sort(([attrId], [attrId2]) => attrId - attrId2);
        attr.forEach(([attrId, init, step], i) => {
            const attrData = TBAttribute.getInstance().formatAttribute([
                attrId,
                init + step * (childInfo.levelLimit - 1),
            ]);
            if (i % 3 === 0) {
                nodeItem = Loader.getInstance().instantiate(this.nodeItem);
                this.nodeContent.addChild(nodeItem);

                nodeItem.child("lbtAttr").label(`${attrData.name}+${attrData.value}`);
            } else if (i % 3 === 1) {
                nodeItem.child("lbtAttr2").label(`${attrData.name}+${attrData.value}`);
            } else {
                nodeItem.child("lbtAttr3").label(`${attrData.name}+${attrData.value}`);
            }
        });

        cc.tween(this.nodeCloseTips)
            .repeatForever(
                cc
                    .tween()
                    .to(1, { opacity: 0 }, { easing: cc.easing.sineIn })
                    .to(1, { opacity: 255 }, { easing: cc.easing.sineOut })
            )
            .start();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
