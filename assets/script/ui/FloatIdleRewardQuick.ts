/*
 * @Author: <PERSON>y<PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-17 12:02:37
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { HOUR_TO_SECOND } from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import { IdleEarningsTakeHoursRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumEconomyAttributeType } from "../data/base/BaseEconomyAttribute";
import { EnumIdleEarningsTotalPara } from "../data/base/BaseIdleEarningsTotal";
import { EnumPrivilegeConfigType } from "../data/base/BasePrivilegeConfig";
import TBIdleEarningsTotal from "../data/parser/TBIdleEarningsTotal";
import TBMainBarrier from "../data/parser/TBMainBarrier";
import TBMainOtherReward from "../data/parser/TBMainOtherReward";
import TBPrivilegeConfig from "../data/parser/TBPrivilegeConfig";
import Bag from "../game/Bag";
import DungeonMain from "../game/DungeonMain";
import EconomyAttribute from "../game/EconomyAttribute";
import IdleReward from "../game/IdleReward";
import Privilege from "../game/Privilege";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatIdleRewardQuick extends I18nComponent {
    @property(ListView)
    rewardList: ListView = null;
    @property(cc.Node)
    rewardLayout: cc.Node = null;

    @property(cc.Node)
    cost: cc.Node = null;
    @property(cc.Node)
    costIcon: cc.Node = null;
    @property(cc.Node)
    costCount: cc.Node = null;

    @property(cc.Node)
    btnText: cc.Node = null;
    @property(cc.Node)
    count: cc.Node = null;

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    protected onLoad(): void {
        this.updateUI();
    }

    protected registerHandler(): void {
        IdleReward.getInstance().on(
            IdleEarningsTakeHoursRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        const barrierId = DungeonMain.getInstance().getLevelId();
        const { idlePrestigeEarnings, idleOtherEarningsId } = TBMainBarrier.getInstance().getDataById(barrierId);
        const { idleOtherEarnings } = TBMainOtherReward.getInstance().getDataById(idleOtherEarningsId);
        const idleTime = parseFloat(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.ItemSetTime)
        );
        const idleEarnTime = parseFloat(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.ProfitTime)
        );
        const otherAttr = EconomyAttribute.getInstance().getOtherAttr();
        const rewards = idlePrestigeEarnings
            .map((v) => {
                return [
                    v[0],
                    (v[1] / idleEarnTime) *
                        HOUR_TO_SECOND *
                        (1 + otherAttr[EnumEconomyAttributeType.ReputationBonus].value),
                ];
            })
            .concat(
                idleOtherEarnings.map((v) => {
                    return [v[0], (v[1] / idleTime) * HOUR_TO_SECOND];
                })
            );
        if (rewards.length <= 4) {
            this.rewardList.node.active = false;
            this.rewardLayout.active = true;
            ItemUtils.refreshView(this.rewardLayout, this.prefabItem, rewards);
        } else {
            this.rewardList.node.active = true;
            this.rewardLayout.active = false;
            this.rewardList.setListData(rewards);
        }

        let freeCount = parseInt(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.FreeProfitNum)
        );
        freeCount += this.getPrivilegeFreeCount();
        const costCount = parseInt(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.CostProfitNum)
        );
        const freeTimes = IdleReward.getInstance().getFreeTimes();
        const costTimes = IdleReward.getInstance().getCostTimes();
        if (freeTimes < freeCount) {
            this.cost.active = false;
            this.btnText.label(i18n.idle0006);
        } else if (costTimes < costCount) {
            this.cost.active = true;
            this.btnText.label(i18n.idle0007);

            const cost = TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.CostProfitItem);
            ImageUtils.setItemIcon(this.costIcon, cost[0]);
            ItemUtils.refreshCount(this.costCount, cost[0], Math.min(cost[1] + costTimes * cost[2], cost[3]));
        } else {
            this.cost.active = false;
            this.btnText.label(i18n.idle0007);
        }

        this.count.label(TextUtils.format(i18n.idle0008, freeCount + costCount - freeTimes - costTimes));
    }

    private getPrivilegeFreeCount(): number {
        let count = 0;
        const privileges = TBPrivilegeConfig.getInstance().getDataListByType(EnumPrivilegeConfigType.QuickAFKTimes);
        for (const e of privileges) {
            if (Privilege.getInstance().hasPrivilege(e.id)) {
                count += e.para.value;
            }
        }
        return count;
    }

    protected onClickGet(): void {
        let freeCount = parseInt(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.FreeProfitNum)
        );
        freeCount += this.getPrivilegeFreeCount();
        const costCount = parseInt(
            TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.CostProfitNum)
        );
        const freeTimes = IdleReward.getInstance().getFreeTimes();
        const costTimes = IdleReward.getInstance().getCostTimes();

        if (freeTimes + costTimes >= freeCount + costCount) {
            Tips.getInstance().show(i18n.idle0004);
            return;
        }

        if (freeTimes >= freeCount && costTimes < costCount) {
            const cost = TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.CostProfitItem);
            if (!Bag.getInstance().isEnough(cost[0], Math.min(cost[1] + costTimes * cost[2], cost[3]))) {
                UI.getInstance().open("FloatItemSource", cost[0]);
                return;
            }
        }

        IdleReward.getInstance().sendIdleEarningsTakeHours();
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
