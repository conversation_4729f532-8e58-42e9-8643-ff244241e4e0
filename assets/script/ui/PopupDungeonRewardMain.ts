/*
 * @Author: chenx
 * @Date: 2025-03-25 17:15:13
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:54:42
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import MathUtils from "../../nsn/util/MathUtils";
import TextUtils from "../../nsn/util/TextUtils";
import Time from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import Tips from "../../nsn/util/Tips";
import {
    EquipFatigueNoticeRet,
    EquipInitRet,
    IItemInfo,
    MapBarrierInitRet,
    MapBarrierPropDropRet,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointId } from "../core/redPoint/RedPointId";
import { EnumUniversalPara } from "../data/base/BaseUniversal";
import TBMainBarrier from "../data/parser/TBMainBarrier";
import TBMainMap from "../data/parser/TBMainMap";
import TBMainRandomDrop from "../data/parser/TBMainRandomDrop";
import TBMainRewardGroup from "../data/parser/TBMainRewardGroup";
import TBMonsterBase from "../data/parser/TBMonsterBase";
import TBMonsterGroup from "../data/parser/TBMonsterGroup";
import { RANK_ID } from "../data/parser/TBRank";
import TBUniversal from "../data/parser/TBUniversal";
import Bag from "../game/Bag";
import Combat, { CombatEvent, DungeonType } from "../game/Combat";
import DungeonMain, { DungeonMainEvent } from "../game/DungeonMain";
import Equip, { EquipEvent } from "../game/Equip";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";
import SpineUtils from "../utils/SpineUtils";

/**
 * 道具掉落数据
 */
interface IItemDropData {
    id: number; // 道具id
    dropTimes: number; // 掉落次数
    limitDropTimes: number; // 掉落次数限制
    unlockTips: string; // 解锁提示
    isLock: boolean; // 是否未解锁
}

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 主线副本-奖励
 */
@ccclass
export default class PopupDungeonRewardMain extends I18nComponent {
    @property(cc.Label)
    lbtTitle: cc.Label = null; // 标题
    @property(cc.Label)
    lbtMonsterName: cc.Label = null; // 怪兽名称
    @property(sp.Skeleton)
    spineMonster: sp.Skeleton = null; // 怪兽
    @property(cc.Node)
    nodeReward: cc.Node = null; // 奖励
    @property(cc.ProgressBar)
    prgFatigue: cc.ProgressBar = null; // 疲劳
    @property(cc.Label)
    lbtFatigue: cc.Label = null; // 疲劳
    @property(cc.Node)
    nodeRecoverTips: cc.Node = null; // 恢复提示
    @property(cc.Label)
    lbtRecoverTips: cc.Label = null; // 恢复提示
    @property(cc.RichText)
    rtDropTips: cc.RichText = null; // 掉落提示

    @property(cc.Node)
    nodeInfo2: cc.Node = null; // 信息

    @property(cc.Node)
    nodeReward2: cc.Node = null; // 奖励
    @property(cc.Node)
    nodeRewardItem2: cc.Node = null; // 奖励item

    @property(cc.Prefab)
    prefabRewardItem: cc.Prefab = null; // 奖励item

    private levelId: number = -1; // 关卡id
    private refreshTime: number = 0; // 刷新时间
    private itemDropData: IItemDropData[] = []; // 道具掉落数据
    private isJumpBoss: boolean = false; // 是否跳转boss

    protected onLoad(): void {
        this.nodeRewardItem2.parent = null;

        const { levelId, isJumpBoss } = this.args;
        this.levelId = levelId;
        this.isJumpBoss = isJumpBoss;
        this.nodeInfo2.active = this.isJumpBoss;
        this.updateLevelInfo();
        this.updateFatigueState();
        this.updateItemDropState(true);
    }

    protected onDestroy(): void {
        this.nodeRewardItem2.destroy();
    }

    protected start(): void {
        if (!RedPoint.getInstance().isRecord(RedPointId.EquipRecoverFatigueByAd)) {
            RedPoint.getInstance().record(RedPointId.EquipRecoverFatigueByAd);
        }
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.updateFatigueState(true);
            }
        }
    }

    protected registerHandler(): void {
        // 主线副本-更新关卡
        DungeonMain.getInstance().on(
            DungeonMainEvent.UpdateLevel,
            (levelId: number) => {
                this.levelId = levelId;
                this.updateLevelInfo();
                this.updateFatigueState();
            },
            this
        );
        // 装备-初始化
        Equip.getInstance().on(
            EquipInitRet.prototype.clazzName,
            () => {
                this.updateFatigueState();
            },
            this
        );
        // 装备-恢复疲劳
        Equip.getInstance().on(
            EquipEvent.RecoverFatigue,
            () => {
                this.updateFatigueState();
            },
            this
        );
        // 装备-更新疲劳通知
        Equip.getInstance().on(
            EquipFatigueNoticeRet.prototype.clazzName,
            () => {
                this.updateFatigueState();
            },
            this
        );
        // 主线副本-初始化数据/掉落道具
        DungeonMain.getInstance().on(
            [MapBarrierInitRet.prototype.clazzName, MapBarrierPropDropRet.prototype.clazzName],
            () => {
                this.updateItemDropState();
            },
            this
        );
    }

    /**
     * 更新关卡信息
     */
    private updateLevelInfo(): void {
        const levelInfo = TBMainBarrier.getInstance().getDataById(this.levelId);
        const mapInfo = TBMainMap.getInstance().getDataById(levelInfo.map);
        this.lbtTitle.string = `${mapInfo.name}${mapInfo.reveal}-${levelInfo.reveal}`;
        const groupInfo = TBMonsterGroup.getInstance().getDataById(levelInfo.monsterGrouopId);
        const monsterId = groupInfo.monster[groupInfo.monster.length - 1][0][0];
        const monsterInfo = TBMonsterBase.getInstance().getDataById(monsterId);
        this.lbtMonsterName.string = monsterInfo.name;
        SpineUtils.setMonster(this.spineMonster, monsterId, "wait");
        const rewardData: IItemInfo[] = [];
        const rewardInfo = TBMainRewardGroup.getInstance().getDataById(levelInfo.rewardGroupId);
        rewardInfo.reward.forEach(([itemId, num]) => {
            const tempRewardData = rewardData.find((e) => e.itemInfoId === itemId);
            if (tempRewardData) {
                tempRewardData.num += num;
            } else {
                rewardData.push({ itemInfoId: itemId, num });
            }
        });
        ItemUtils.refreshView(this.nodeReward, this.prefabRewardItem, rewardData);
        let minLevel = -1;
        let maxLevel = -1;
        mapInfo.monstersdropLevel.forEach(([level]) => {
            minLevel = minLevel !== -1 ? Math.min(minLevel, level) : level;
            maxLevel = maxLevel !== -1 ? Math.max(maxLevel, level) : level;
        });
        mapInfo.bossdropLevel.forEach(([level]) => {
            minLevel = minLevel !== -1 ? Math.min(minLevel, level) : level;
            maxLevel = maxLevel !== -1 ? Math.max(maxLevel, level) : level;
        });
        this.rtDropTips.string = TextUtils.format(i18n.equip0074, minLevel, maxLevel);
    }

    /**
     * 更新疲劳状态
     * @param isUpdate 是否为update调用
     */
    private updateFatigueState(isUpdate: boolean = false): void {
        const fatigueData = Equip.getInstance().getFatigueData();
        const levelInfo = TBMainBarrier.getInstance().getDataById(this.levelId);
        if (!isUpdate) {
            const fatigue = levelInfo.equipLimit - fatigueData.times;
            const progressFatigue = MathUtils.floor(Math.min(fatigue, levelInfo.equipLimit) / levelInfo.equipLimit, 3);
            this.prgFatigue.progress = progressFatigue;
            this.prgFatigue.barSprite.node.opacity = progressFatigue !== 0 ? 255 : 0;
            this.lbtFatigue.string = `${fatigue}/${levelInfo.equipLimit}`;

            this.refreshTime = 0;
            this.nodeRecoverTips.active = fatigue < levelInfo.equipLimit;
            fatigue < levelInfo.equipLimit && this.updateFatigueState(true);
        } else {
            const [recoverDuration, recoverNum] = TBUniversal.getInstance().getValueByPara(
                EnumUniversalPara.EquipmentDropsRestore
            );
            const surplusDuration = Math.max(
                recoverDuration * 1000 - (Time.getInstance().now() - fatigueData.recoverTime),
                0
            );
            this.lbtRecoverTips.string = TextUtils.format(
                i18n.equip0073,
                TimeFormat.getInstance().getTextByDuration(surplusDuration, TimeDurationFormatType.MM_SS),
                recoverNum
            );

            if (surplusDuration > 0) {
                this.refreshTime = REFRESH_DURATION;
            } else {
                Equip.getInstance().updateFatigueState();
            }
        }
    }

    /**
     * 更新道具掉落状态
     * @param isInit 是否为初始化调用
     */
    private updateItemDropState(isInit: boolean = false): void {
        if (isInit) {
            const dropInfo = TBMainRandomDrop.getInstance().getLast();
            dropInfo.reward.forEach(([itemId]) => {
                const tempDropInfo = TBMainRandomDrop.getInstance().getDataByItemId(itemId);
                const levelInfo = TBMainBarrier.getInstance().getDataById(tempDropInfo.minLevel);
                const mapInfo = TBMainMap.getInstance().getDataById(levelInfo.map);
                this.itemDropData.push({
                    id: itemId,
                    dropTimes: 0,
                    limitDropTimes: 0,
                    unlockTips: TextUtils.format(
                        i18n.common0067,
                        `${mapInfo.name}${mapInfo.reveal}-${levelInfo.reveal}`
                    ),
                    isLock: false,
                });

                const nodeItem = Loader.getInstance().instantiate(this.nodeRewardItem2);
                this.nodeReward2.addChild(nodeItem);

                nodeItem.button(itemId);
                ImageUtils.setItemQuality(nodeItem.child("spQuality"), itemId);
                ImageUtils.setItemIcon(nodeItem.child("icon"), itemId);
                SpineUtils.setItemQuality(nodeItem.child("spineQuality"), itemId);
            });
        }

        const levelData = DungeonMain.getInstance().getLevelData();
        const dropInfo = TBMainRandomDrop.getInstance().getDataByLevelId(levelData.mapBarrierId);
        this.nodeReward2.children.forEach((e) => {
            const itemId: number = CocosExt.getButtonData(e);
            const itemDropData = this.itemDropData.find((e2) => e2.id === itemId);
            const index = dropInfo.reward.findIndex(([tempItemId]) => tempItemId === itemId);
            itemDropData.isLock = index === -1;
            itemDropData.dropTimes = levelData.dropMap[itemId] || 0;
            itemDropData.limitDropTimes = index !== -1 ? dropInfo.reward[index][3] : 0;

            if (!itemDropData.isLock) {
                e.child("count").label(
                    NumberUtils.format(dropInfo.reward[index][1] * itemDropData.dropTimes) +
                        "/" +
                        NumberUtils.format(dropInfo.reward[index][1] * itemDropData.limitDropTimes)
                );
            } else {
                e.child("count").label("");
            }
            e.child("nodeLock").active = itemDropData.isLock;
        });
    }

    /**
     * 排行榜
     */
    protected onClickRank(): void {
        UI.getInstance().open("PopupRank", RANK_ID.DUNGEON_MAIN);
    }

    /**
     * 恢复疲劳
     */
    protected onClickRecoverFatigue(): void {
        const [itemId, recoverNum] = TBUniversal.getInstance().getValueByPara(
            EnumUniversalPara.ItemEquipmentDropsRestore
        );
        if (!Bag.getInstance().isEnough(itemId, 1)) {
            UI.getInstance().open("FloatItemSource", itemId);
            return;
        }

        UI.getInstance().open("PopupItemUse", {
            itemInfoId: itemId,
            getEffectText: (count: number) => {
                return TextUtils.format(i18n.equip0075, recoverNum * count);
            },
            useItem: (count: number) => {
                Equip.getInstance().sendRecoverFatigueByItem(count);
            },
        });
    }

    /**
     * 是否跳转boss
     */
    protected onClickJumpBoss(): void {
        if (!this.isJumpBoss) {
            return;
        }

        UI.getInstance().close();
        Combat.getInstance().emit(CombatEvent.JumpBoss, DungeonType.Main);
    }

    /**
     * 道具掉落信息
     * @param event
     * @param itemId 道具id
     */
    protected onClickItemDropInfo(event: cc.Event.EventTouch, itemId: number): void {
        if (!itemId) {
            return;
        }
        const itemDropData = this.itemDropData.find((e) => e.id === itemId);
        if (!itemDropData) {
            return;
        }
        if (itemDropData.isLock) {
            Tips.getInstance().info(itemDropData.unlockTips);
            return;
        }

        ItemUtils.showInfo(itemDropData.id);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
