/*
 * @Author: chenx
 * @Date: 2024-10-12 11:49:15
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-14 14:41:25
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TimeFormat, { TimeFormatType } from "../../nsn/util/TimeFormat";
import { IChildInfo } from "../../protobuf/proto";
import TBAttribute from "../data/parser/TBAttribute";
import TBPrincessChild from "../data/parser/TBPrincessChild";
import ImageUtils from "../utils/ImageUtils";

const { ccclass, property } = cc._decorator;

/**
 * 知己-王储联姻
 */
@ccclass
export default class PopupChildMarriage extends I18nComponent {
    @property(cc.Sprite)
    spTitle: cc.Sprite = null; // 标题
    @property(cc.Label)
    lbtPlayerName: cc.Label = null; // 玩家name
    @property(cc.Sprite)
    spHead: cc.Sprite = null; // 王储head
    @property(cc.Sprite)
    spHead2: cc.Sprite = null; // 王储head
    @property(cc.Label)
    lbtName: cc.Label = null; // 王储name
    @property(cc.Label)
    lbtName2: cc.Label = null; // 王储name
    @property(cc.Label)
    lbtMarriageTime: cc.Label = null; // 联姻time
    @property(cc.Node)
    nodeContent: cc.Node = null; // 属性content
    @property(cc.Node)
    nodeItem: cc.Node = null; // 属性item

    childData: IChildInfo = null; // 王储数据

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/syncUI/confidant/spConfidantTitle2",
            },
        ];
    }

    protected onLoad(): void {
        this.nodeItem.parent = null;

        this.childData = this.args;
        this.initInfo();
    }

    protected onDestroy(): void {
        this.nodeItem.destroy();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        this.lbtPlayerName.string = this.childData.companionInfo.playerInfo.name;
        const childInfo = TBPrincessChild.getInstance().getDataById(this.childData.childId);
        let tempRes = "";
        for (const [level, res] of childInfo.res) {
            if (this.childData.level >= level) {
                tempRes = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantHead(this.spHead, tempRes);
        this.lbtName.string = this.childData.name;
        const childInfo2 = TBPrincessChild.getInstance().getDataById(this.childData.companionInfo.childId);
        let tempRes2 = "";
        for (const [level, res] of childInfo2.res) {
            if (childInfo2.levelLimit >= level) {
                tempRes2 = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantHead(this.spHead2, tempRes2);
        this.lbtName2.string = childInfo2.name;
        this.lbtMarriageTime.string = TimeFormat.getInstance().getTextByTime(
            TimeFormatType.YYYY_MM_DD,
            this.childData.companionInfo.marryTime
        );
        const attr: number[][] = [];
        childInfo.attribute.forEach(([attrId, init, step], i) => {
            const index = attr.findIndex(([attrId2]) => attrId2 === attrId);
            if (index === -1) {
                attr.push([attrId, init + step * (this.childData.level - 1)]);
            } else {
                attr[index][1] += init + step * (this.childData.level - 1);
            }
        });
        childInfo2.attribute.forEach(([attrId, init, step], i) => {
            const index = attr.findIndex(([attrId2]) => attrId2 === attrId);
            if (index === -1) {
                attr.push([attrId, init + step * (childInfo2.levelLimit - 1)]);
            } else {
                attr[index][1] += init + step * (childInfo2.levelLimit - 1);
            }
        });
        let nodeItem: cc.Node = null;
        attr.sort(([attrId], [attrId2]) => attrId - attrId2);
        attr.forEach(([attrId, value], i) => {
            const attrData = TBAttribute.getInstance().formatAttribute([attrId, value]);
            if (i % 2 === 0) {
                nodeItem = Loader.getInstance().instantiate(this.nodeItem);
                this.nodeContent.addChild(nodeItem);

                nodeItem.child("lbtAttr").label(`${attrData.name}+${attrData.value}`);
            } else {
                nodeItem.child("lbtAttr2").label(`${attrData.name}+${attrData.value}`);
            }
        });
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
