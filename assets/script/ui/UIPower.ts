/*
 * @Author: tangtq
 * @Date: 2024-07-26 14:56:16
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-24 10:04:50
 */

import Audio from "../../nsn/audio/Audio";
import LongPressButton from "../../nsn/comp/ui/LongPressButton";
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import MathUtils from "../../nsn/util/MathUtils";
import Tips from "../../nsn/util/Tips";
import { BagUpdateRet, PowerBreakOutRet, PowerPromotionRet, PowerUpgradeRet } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import i18n from "../config/i18n/I18n";
import Guide from "../core/Guide";
import { EnumPowerLevelUpType } from "../data/base/BasePowerLevel";
import { EnumUniversalPara } from "../data/base/BaseUniversal";
import TBAttribute from "../data/parser/TBAttribute";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import TBPower from "../data/parser/TBPower";
import TBPowerLevel from "../data/parser/TBPowerLevel";
import TBUniversal from "../data/parser/TBUniversal";
import { AttrSourceType } from "../game/Attribute";
import Bag from "../game/Bag";
import LeadSkin from "../game/LeadSkin";
import Power from "../game/Power";
import Setting, { SettingId } from "../game/Setting";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";
import SpineUtils from "../utils/SpineUtils";

/**
 * 提示节点位置
 */
const TIPS_POS = [
    { x: -331, y: 128 },
    { x: 233, y: 464 },
    { x: -278, y: 298 },
    { x: 248, y: 286 },
    { x: -239, y: 466 },
    { x: 281, y: 134 },
];

const NODE_COUNT = 10; // 每个阶段都有十层

const ANIMATIONS = ["wait1", "wait2", "wait3"];

const { ccclass, property } = cc._decorator;

@ccclass
export default class UIPower extends I18nComponent {
    @property(cc.Node)
    topBg: cc.Node = null; // 顶部背景
    @property(cc.Node)
    nodeTitle: cc.Node = null;
    @property(cc.Node)
    lead: cc.Node = null; // 主角立绘
    @property(cc.Node)
    leadBreachSpine: cc.Node = null;
    @property(cc.Node)
    tips: cc.Node = null; // 气泡提示
    @property(cc.Node)
    powerName: cc.Node = null; // 王权名称
    @property(cc.Node)
    powerIcon: cc.Node = null;
    @property(cc.Node)
    powerStage: cc.Node = null;
    @property(cc.Node)
    powerCount: cc.Node = null;
    @property(cc.Node)
    powerLv: cc.Node = null;
    @property(cc.Node)
    details: cc.Node = null; // 属性详情
    @property(cc.Node)
    nodeConsume: cc.Node = null; // 消耗
    @property(cc.Node)
    nodeProgress: cc.Node = null; // 进度条
    @property(cc.Node)
    btnPromote: cc.Node = null;
    @property(cc.Node)
    btnBreach: cc.Node = null;
    @property(cc.Node)
    btnUpgrade: cc.Node = null;
    @property(cc.Node)
    btnText: cc.Node = null;
    @property(cc.Node)
    upgradeText: cc.Node = null;
    @property(cc.Node)
    breachText: cc.Node = null;
    @property(cc.Node)
    promoteText: cc.Node = null;
    @property(cc.Node)
    text1: cc.Node = null;
    @property(cc.Node)
    text2: cc.Node = null;
    @property(cc.Node)
    tipNode: cc.Node = null; // 提示节点
    @property(cc.Node)
    light: cc.Node = null; // 进度条动画
    @property(cc.Node)
    toggle: cc.Node = null;
    @property(cc.Node)
    efShipSpine: cc.Node = null; // 粒子飞入动效

    private iisAllUpgrade: boolean = false;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.nodeTitle,
                url: "texture/syncUI/power/spGradeSystemTitle",
            },
            {
                sprite: this.upgradeText,
                url: "texture/syncUI/power/btnGradeUp1",
            },
            {
                sprite: this.breachText,
                url: "texture/syncUI/power/btnGradeUp2",
            },
            {
                sprite: this.text1,
                url: "texture/syncUI/power/btnGradeUp3",
            },
            {
                sprite: this.text2,
                url: "texture/syncUI/power/btnGradeUp4",
            },
            {
                sprite: this.promoteText,
                url: "texture/syncUI/power/btnGradeUp5",
            },
        ];
    }

    protected registerHandler(): void {
        Power.getInstance().on(
            PowerUpgradeRet.prototype.clazzName,
            () => {
                this.createTipNode();
                this.updateBtnUI();
                this.playUpgradeAni(true);
                this.iisAllUpgrade = false;
            },
            this
        );
        Power.getInstance().on(
            PowerBreakOutRet.prototype.clazzName,
            () => {
                this.btnBreach.getComponent(cc.Button).interactable = false; // 只能突破一下
                this.playUpgradeAni();
                const spine = this.leadBreachSpine;
                const spineComp = spine.getComponent(sp.Skeleton);
                spine.active = true;
                spineComp.setAnimation(0, "wait", false);
                spineComp.setCompleteListener(() => {
                    spine.active = false;
                    spineComp.setCompleteListener(null);
                    this.updateBtnUI();
                    UI.getInstance().open("FloatPowerUpgrade");
                });
            },
            this
        );
        Power.getInstance().on(
            PowerPromotionRet.prototype.clazzName,
            () => {
                this.updateBtnUI();
                this.updateProgressUI();
                this.updateAttrAndPowerUI();
                UI.getInstance().close();
                UI.getInstance().open("FloatPowerPromote");
            },
            this
        );
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                const powerInfo = Power.getInstance().getPowerInfo();
                const powerLevelInfo = TBPowerLevel.getInstance().getDataById(powerInfo.kingLevelId);
                const isBreach = TBPowerLevel.getInstance().isBreachThrough(powerInfo.kingLevelId);
                const itemCount = Bag.getInstance().getItemCountById(powerLevelInfo.upgradeCost[0][0]);
                const isUpgrade =
                    powerLevelInfo.upgradeCost.length > 0 && itemCount >= powerLevelInfo.upgradeCost[0][1]; // 是否可提升
                const isPromote = TBPowerLevel.getInstance().isNextPromoteThrough(
                    powerInfo.kingLevelId,
                    powerInfo.kingLevelId + 1,
                    powerInfo.upgradeTimes
                ); // 是否晋升
                if (!isBreach) {
                    if (
                        isUpgrade &&
                        !isPromote &&
                        !isBreach &&
                        this.toggle.active &&
                        this.toggle.getComponent(cc.Toggle).isChecked
                    ) {
                        const num = Power.getInstance().getUpgradeLevels(powerInfo.kingLevelId, powerInfo.upgradeTimes);
                        const count =
                            num === 0 ? powerLevelInfo.upgradeCost[0][1] : powerLevelInfo.upgradeCost[0][1] * num;
                        ItemUtils.refreshCount(
                            this.nodeConsume.child("text"),
                            powerLevelInfo.upgradeCost[0][0],
                            count,
                            false,
                            i18n.power0019,
                            i18n.power0020
                        );
                    } else {
                        ItemUtils.refreshCount(
                            this.nodeConsume.child("text"),
                            powerLevelInfo.upgradeCost[0][0],
                            powerLevelInfo.upgradeCost[0][1],
                            false,
                            i18n.power0019,
                            i18n.power0020
                        );
                    }
                }
            },
            this
        );
    }

    protected onLoad(): void {
        this.tipNode.parent = null;
        this.light.parent = null;
        this.efShipSpine.parent = null;

        this.toggle.getComponent(cc.Toggle).isChecked = Setting.getInstance().getSwitchState(
            SettingId.PowerBatchUpgrade
        );
        this.init();
    }

    protected onDestroy(): void {
        this.tipNode.destroy();
        this.light.destroy();
        this.efShipSpine.destroy();
    }

    private init(): void {
        const skinId = LeadSkin.getInstance().getId();
        const data = TBLeadSkin.getInstance().getDataById(skinId);
        const spineLead = this.lead.child("icon").getComponent(sp.Skeleton);
        spineLead.setCompleteListener(null);
        SpineUtils.setLeadWithoutAniName(spineLead, data.res, () => {
            spineLead.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                if (trackEntry.animation.name === "exclusivewait") {
                    spineLead.setAnimation(0, "wait", false);
                    spineLead.addAnimation(0, "wait", false);
                    spineLead.addAnimation(0, "wait", false);
                    spineLead.addAnimation(0, "wait", false);
                    spineLead.addAnimation(0, "wait", false);
                    spineLead.addAnimation(0, "exclusivewait", false);
                }
            });
            spineLead.setAnimation(0, "exclusivewait", false);
        });
        this.updateProgressUI();
        this.updateBtnUI();
        this.updateAttrAndPowerUI();
    }

    private updateProgressUI(): void {
        const powerInfo = Power.getInstance().getPowerInfo();
        const powerLevelInfo = TBPowerLevel.getInstance().getDataById(powerInfo.kingLevelId);
        const powerData = TBPower.getInstance().getDataById(powerLevelInfo.titleId);
        const isMax = powerData.id === TBPower.getInstance().getCount(); // 当前阶段是否满级
        if (isMax) {
            this.nodeProgress.progress(1);
            this.nodeProgress.child("text").label("MAX");
            return;
        }

        const allCost = TBPowerLevel.getInstance().getSortTotalById(powerInfo.kingLevelId);
        const nowCost = TBPowerLevel.getInstance().getSortNowTotalById(powerInfo.kingLevelId, powerInfo.upgradeTimes);
        this.nodeProgress.progress(nowCost / allCost); // 初始化进度条
        this.nodeProgress
            .child("text")
            .label(NumberUtils.format(nowCost, 1, 0) + "/" + NumberUtils.format(allCost, 1, 0));
    }

    /**
     * 更新属性UI和爵位UI
     */
    private updateAttrAndPowerUI(): void {
        const powerInfo = Power.getInstance().getPowerInfo();
        const powerLevelInfo = TBPowerLevel.getInstance().getDataById(powerInfo.kingLevelId);
        const powerData = TBPower.getInstance().getDataById(powerLevelInfo.titleId);
        const isMax = powerData.id === TBPower.getInstance().getCount(); // 当前阶段是否满级
        const isBreach = TBPowerLevel.getInstance().isBreachThrough(powerInfo.kingLevelId);
        const powerLevelData = TBPowerLevel.getInstance().getDataById(powerInfo.kingLevelId);
        const itemCount = Bag.getInstance().getItemCountById(powerLevelData.upgradeCost[0][0]);
        const sort = TBPowerLevel.getInstance().getSortById(powerInfo.kingLevelId);
        const isUpgrade = powerLevelData.upgradeCost.length > 0 && itemCount >= powerLevelData.upgradeCost[0][1]; // 是否可提升
        const isPromote = TBPowerLevel.getInstance().isNextPromoteThrough(
            powerInfo.kingLevelId,
            powerInfo.kingLevelId + 1,
            powerInfo.upgradeTimes
        ); // 是否晋升

        if (isBreach) {
            this.nodeConsume.active = false;
        } else {
            this.nodeConsume.active = true;
            if (
                isUpgrade &&
                !isPromote &&
                !isBreach &&
                this.toggle.active &&
                this.toggle.getComponent(cc.Toggle).isChecked
            ) {
                const num = Power.getInstance().getUpgradeLevels(powerInfo.kingLevelId, powerInfo.upgradeTimes);
                const count = num === 0 ? powerLevelData.upgradeCost[0][1] : powerLevelData.upgradeCost[0][1] * num;
                ItemUtils.refreshCount(
                    this.nodeConsume.child("text"),
                    powerLevelData.upgradeCost[0][0],
                    count,
                    false,
                    i18n.power0019,
                    i18n.power0020
                );
            } else {
                ItemUtils.refreshCount(
                    this.nodeConsume.child("text"),
                    powerLevelData.upgradeCost[0][0],
                    powerLevelData.upgradeCost[0][1],
                    false,
                    i18n.power0019,
                    i18n.power0020
                );
            }

            ImageUtils.setItemIcon(this.nodeConsume.child("icon"), powerLevelData.upgradeCost[0][0]);
        }

        ImageUtils.setPowerIcon1(this.powerIcon.child("icon"), this.powerIcon.child("stars"), powerInfo.kingLevelId);
        this.powerLv.label("Lv. " + powerLevelData.level + "");
        this.powerName.label(powerLevelData.name.split("·")[0] ? powerLevelData.name.split("·")[0] : "");
        this.powerStage.label(powerLevelData.name.split("·")[1] ? powerLevelData.name.split("·")[1] : "");
        this.powerCount.label(isMax ? "1/1" : sort + "/" + NODE_COUNT); // 层数进度

        const addValueArr = TBPowerLevel.getInstance().getAttrNowTotalById(
            powerInfo.kingLevelId,
            powerInfo.upgradeTimes
        );

        const baseAttr = TBPowerLevel.getInstance().getBaseAttribute(powerInfo.kingLevelId);
        for (let i = 0; i < this.details.childrenCount; i++) {
            const node = this.details.children[i];
            const { name, value: curValue } = TBAttribute.getInstance().formatAttribute([
                baseAttr[i][0],
                baseAttr[i][1] + baseAttr[i][2] * powerInfo.upgradeTimes,
            ]);
            const { value: addValue } = TBAttribute.getInstance().formatAttribute([baseAttr[i][0], addValueArr[i]]);
            const text = node.child("attr").child("text");
            const allCount = node.child("allCount");
            const addCount = node.child("addCount");
            text.label(name);
            allCount.label(curValue);
            addCount.label("+" + addValue);
        }
    }

    /**
     * 更新按钮UI
     */
    private updateBtnUI(): void {
        const powerInfo = Power.getInstance().getPowerInfo();
        const powerLevelInfo = TBPowerLevel.getInstance().getDataById(powerInfo.kingLevelId);
        const itemCount = Bag.getInstance().getItemCountById(powerLevelInfo.upgradeCost[0][0]);
        const isUpgrade = powerLevelInfo.upgradeCost.length > 0 && itemCount >= powerLevelInfo.upgradeCost[0][1]; // 是否可提升

        const isBreach = TBPowerLevel.getInstance().isBreachThrough(powerInfo.kingLevelId); // 是否突破状态
        const isPromote = TBPowerLevel.getInstance().isNextPromoteThrough(
            powerInfo.kingLevelId,
            powerInfo.kingLevelId + 1,
            powerInfo.upgradeTimes
        ); // 是否晋升
        const powerData = TBPower.getInstance().getDataById(powerLevelInfo.titleId);
        const isMax = powerData.id === TBPower.getInstance().getCount(); // 当前阶段是否满级
        const uniData = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.PowerBatchLevel); // 一键升级

        if (isUpgrade && !isPromote && !isBreach && !isMax) {
            this.btnUpgrade.active = true;
            this.btnUpgrade.getComponent(LongPressButton).longPressTime = Guide.getInstance().isGuideShowing()
                ? 200
                : 0.4; // 引导时按钮长按时间增加
            this.btnUpgrade.child("btnIcon").getComponent(sp.Skeleton).clearTracks();
            this.btnUpgrade.child("btnIcon").getComponent(sp.Skeleton).setAnimation(0, "wait2", true);
            this.nodeConsume.active = true;
            this.btnBreach.active = false;
            this.btnText.active = false;
            this.btnPromote.active = true;
            this.toggle.active = powerLevelInfo.titleId >= uniData;
        } else if (isBreach) {
            this.btnUpgrade.active = false;
            this.nodeConsume.active = false;
            this.btnBreach.active = true;
            this.btnBreach.getComponent(sp.Skeleton).clearTracks();
            this.btnBreach.getComponent(sp.Skeleton).setAnimation(0, "wait2", true);
            this.btnBreach.getComponent(cc.Button).interactable = true; // 突破时打开按钮限制
            this.btnText.active = false;
            this.btnPromote.active = true;
            this.toggle.active = false;
        } else if (isPromote) {
            this.btnUpgrade.active = false;
            this.nodeConsume.active = false;
            this.btnBreach.active = false;
            this.btnText.active = true;
            this.text1.active = true;
            this.text2.active = false;
            this.btnText.getComponent(GrayComp).gray = true;
            this.btnPromote.active = true;
            this.toggle.active = false;
        } else if (isMax) {
            this.btnUpgrade.active = false;
            this.nodeConsume.active = false;
            this.btnBreach.active = false;
            this.btnText.active = true;
            this.text1.active = false;
            this.text2.active = true;
            this.btnText.getComponent(GrayComp).gray = true;
            this.btnPromote.active = false; // 隐藏去晋升的弹窗按钮
            this.toggle.active = false;
        } else {
            // 平常状态
            this.btnUpgrade.active = true;
            this.btnUpgrade.getComponent(LongPressButton).longPressTime = Guide.getInstance().isGuideShowing()
                ? 200
                : 0.4; // 引导时按钮长按时间增加
            this.btnUpgrade.child("btnIcon").getComponent(sp.Skeleton).clearTracks();
            this.btnUpgrade.child("btnIcon").getComponent(sp.Skeleton).setAnimation(0, "wait", true);
            this.nodeConsume.active = true;
            this.btnBreach.active = false;
            this.btnText.active = false;
            this.btnPromote.active = true;
            this.toggle.active = powerLevelInfo.titleId >= uniData;
        }
    }

    /**
     * 创建提示节点并播放动画
     */
    private createTipNode(): void {
        if (this.iisAllUpgrade) {
            return; // 一键提升跳过
        }

        const powerInfo = Power.getInstance().getPowerInfo();
        const length = TBPowerLevel.getInstance().getCount(); // 最高级
        if (powerInfo.kingLevelId === length) {
            return;
        }
        const node = Loader.getInstance().instantiate(this.tipNode);
        node.active = true;
        node.parent = this.tips;
        const randomIndex = MathUtils.getRandomInt(0, 6);
        const attr = TBPowerLevel.getInstance().getBaseAttributeByLevel(powerInfo.kingLevelId, powerInfo.upgradeTimes);

        if (attr.length > 0) {
            const { name } = TBAttribute.getInstance().getDataById(attr[0]);
            node.child("text").label(name + "  +" + attr[2]);
            cc.tween(node)
                .to(0.33, {
                    opacity: 255,
                    position: cc.v2(TIPS_POS[randomIndex].x, TIPS_POS[randomIndex].y),
                })
                .delay(0.7)
                .to(0.3, {
                    opacity: 0,
                    y: TIPS_POS[randomIndex].y + 30,
                })
                .call(() => {
                    node.destroy();
                })
                .start();
        }
    }

    protected onClickUpgradePower(): void {
        Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.POWER_LEVEL, AUDIO_EFFECT_PATH.SYSTEM);
        const power = Power.getInstance().getPowerInfo();
        const powerLevelInfo = TBPowerLevel.getInstance().getDataById(power.kingLevelId);

        if (this.toggle.getComponent(cc.Toggle).isChecked) {
            const num = Power.getInstance().getUpgradeLevels(power.kingLevelId, power.upgradeTimes);
            if (num === 0) {
                Tips.getInstance().show(i18n.power0018);
                return;
            }

            Power.getInstance().sendPowerUpgrade(num); // 一键提升
            this.iisAllUpgrade = true;
            return;
        }

        if (!Bag.getInstance().isEnough(powerLevelInfo.upgradeCost[0][0], powerLevelInfo.upgradeCost[0][1])) {
            Tips.getInstance().show(i18n.power0018);
            return;
        }
        Power.getInstance().sendPowerUpgrade(1); // 发送提升请求
    }

    private playUpgradeAni(showFlyLight: boolean = false): void {
        const powerInfo = Power.getInstance().getPowerInfo();
        const allCost = TBPowerLevel.getInstance().getSortTotalById(powerInfo.kingLevelId);
        const nowCost = TBPowerLevel.getInstance().getSortNowTotalById(powerInfo.kingLevelId, powerInfo.upgradeTimes);
        const powerLevelInfo = TBPowerLevel.getInstance().getDataById(powerInfo.kingLevelId);
        const powerData = TBPower.getInstance().getDataById(powerLevelInfo.titleId);
        const isMax = powerData.id === TBPower.getInstance().getCount(); // 当前阶段是否满级

        const light3 = Loader.getInstance().instantiate(this.light);
        const lightSp3 = light3.getComponent(sp.Skeleton);
        light3.parent = this.nodeProgress;

        const efShipSpine = Loader.getInstance().instantiate(this.efShipSpine);
        const efShipSpineSp = efShipSpine.getComponent(sp.Skeleton);
        efShipSpine.parent = this.node;
        const randomIndex = MathUtils.getRandomInt(0, 3);

        if (isMax) {
            this.nodeProgress.progress(1);
            this.nodeProgress.child("text").label("MAX");
        } else {
            cc.tween(this.nodeProgress.getComponent(cc.ProgressBar))
                .delay(0.3)
                .to(
                    0.1,
                    {
                        progress: nowCost / allCost,
                    },
                    { easing: cc.easing.quadOut }
                )
                .call(() => {
                    this.nodeProgress
                        .child("text")
                        .label(NumberUtils.format(nowCost, 1, 0) + "/" + NumberUtils.format(allCost, 1, 0));
                })
                .start();
        }

        if (showFlyLight) {
            efShipSpine.active = true;
            efShipSpineSp.setAnimation(0, ANIMATIONS[randomIndex], false);
            efShipSpineSp.setCompleteListener(() => {
                efShipSpine.destroy();
            });

            this.scheduleOnce(() => {
                light3.active = true;
                lightSp3.setAnimation(0, "wait", false);
                this.updateAttrAndPowerUI();
                lightSp3.setCompleteListener(() => {
                    light3.destroy();
                });
            }, 0.3);
        } else {
            this.updateAttrAndPowerUI();
        }
    }

    /**
     * 突破
     */
    protected onClickBreach(): void {
        const power = Power.getInstance().getPowerInfo();
        const data = TBPowerLevel.getInstance().getDataById(power.kingLevelId);
        if (!data || data.upType !== EnumPowerLevelUpType.BreakThrough) {
            return;
        }
        Power.getInstance().sendPowerBreakOut();
    }

    /**
     * 晋升
     */
    protected onClickPromote(): void {
        UI.getInstance().open("PopupPowerUpgrade");
    }

    /**
     * 属性展示
     */
    protected onClickAttrShow(): void {
        UI.getInstance().open("PopupAttrShowSystem", AttrSourceType.Power);
    }

    /**
     * 装备收益
     */
    protected onClickEquipIncome(): void {
        UI.getInstance().open("PopupEquipIncome", { isPlayUpgradeAni: false, isOpenPowerUI: false });
    }

    protected onClickToggle(): void {
        Setting.getInstance().setSwitchState(SettingId.PowerBatchUpgrade);
        this.updateAttrAndPowerUI();
    }

    /**
     * 王权之巅
     */
    protected onClickPeak(): void {
        UI.getInstance().open("UIPowerPeak");
    }

    /**
     * 王权之路
     */
    protected onClickRoad(): void {
        UI.getInstance().open("PopupPowerRoad");
    }

    /**
     * 关闭界面
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
