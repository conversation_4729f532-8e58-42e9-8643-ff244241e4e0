/*
 * @Author: <PERSON>y<PERSON><PERSON>
 * @Date: 2024-03-18 11:35:03
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-06-27 16:05:15
 */

import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { ArrowGroupSyncRet, RechargeNoticeRet, RechargeReceivePackRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { Sku } from "../core/Sku";
import { EnumFundMarkupType } from "../data/base/BaseFundMarkup";
import TBFundMarkup from "../data/parser/TBFundMarkup";
import { ITEM_ID } from "../data/parser/TBItem";
import TBRecharge, { RECHARGE_ID } from "../data/parser/TBRecharge";
import <PERSON>A<PERSON> from "../game/MakeArrow";
import Recharge from "../game/Recharge";
import ImageUtils from "../utils/ImageUtils";
import NumberUtils from "../utils/NumberUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupPowerFund extends I18nComponent {
    @property(cc.Node)
    title: cc.Node = null;
    @property(ListView)
    list: ListView = null;
    @property(cc.Node)
    cost: cc.Node = null;
    @property(cc.Node)
    btnBuy: cc.Node = null;
    @property(cc.Node)
    btnBuyText: cc.Node = null;
    @property(cc.Node)
    got: cc.Node = null;

    private forgeIronCostCount: number = 0;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.title,
                url: "texture/syncUI/powerFund/spReputationBPTitle",
            },
        ];
    }

    protected onLoad(): void {
        this.forgeIronCostCount = MakeArrow.getInstance().getForgeIronCostCounts();
        this.updateUI();
    }

    protected registerHandler(): void {
        MakeArrow.getInstance().on(
            ArrowGroupSyncRet.prototype.clazzName,
            () => {
                this.updateCost();

                const isRecharge = Recharge.getInstance().isRecharged(RECHARGE_ID.POWER_FUND);
                if (!isRecharge) {
                    return;
                }
                const newForgeIronCostCount = MakeArrow.getInstance().getForgeIronCostCounts();
                const costs = TBFundMarkup.getInstance()
                    .getDataByType(EnumFundMarkupType.ReputationPackage)
                    .map((v) => v.cost);
                let isCrossLevel = false;
                for (const e of costs) {
                    if (e > this.forgeIronCostCount && e <= newForgeIronCostCount) {
                        isCrossLevel = true;
                        break;
                    }
                }
                if (!isCrossLevel) {
                    return;
                }
                this.updateList();
                this.forgeIronCostCount = newForgeIronCostCount;
            },
            this
        );
        Recharge.getInstance().on(
            [RechargeNoticeRet.prototype.clazzName, RechargeReceivePackRet.prototype.clazzName],
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateList();
        this.updateCost();
        this.updateBtns();
    }

    private updateList(): void {
        const data = TBFundMarkup.getInstance().getDataByType(EnumFundMarkupType.ReputationPackage);
        this.list.setListData(data);

        const isRecharge = Recharge.getInstance().isRecharged(RECHARGE_ID.POWER_FUND);
        if (!isRecharge) {
            return;
        }

        let index = -1;
        const count = MakeArrow.getInstance().getForgeIronCostCounts();
        const { pack } = TBRecharge.getInstance().getDataById(RECHARGE_ID.POWER_FUND);
        for (let i = 0; i < data.length; i++) {
            const isEnough = count >= data[i].cost;
            const isGot = Recharge.getInstance().getBuyPackReceived(pack[i]);
            if (isEnough && !isGot) {
                index = i;
                break;
            }
        }
        if (index === -1) {
            index = data.findIndex((v) => v.cost > count);
        }
        if (index !== -1) {
            this.list.scrollTo(--index);
        } else {
            this.list.scrollTo(index !== -1 ? index : data.length - 1);
        }
    }

    private updateCost(): void {
        ImageUtils.setRichTextItemIcons(this.cost, ["prop" + ITEM_ID.MAKE_ARROW_STONE], () => {
            this.cost.richText(
                TextUtils.format(
                    i18n.powerFund0003,
                    "prop" + ITEM_ID.MAKE_ARROW_STONE,
                    NumberUtils.format(MakeArrow.getInstance().getForgeIronCostCounts())
                )
            );
        });
    }

    private updateBtns(): void {
        const isRecharged = Recharge.getInstance().isRecharged(RECHARGE_ID.POWER_FUND);
        if (isRecharged) {
            this.btnBuy.active = false;
            this.got.active = true;
        } else {
            this.btnBuy.active = true;
            this.got.active = false;
            this.btnBuyText.label(Sku.getInstance().getPriceText(RECHARGE_ID.POWER_FUND));
        }
    }

    protected onClickBuy(): void {
        Recharge.getInstance().prePay(RECHARGE_ID.POWER_FUND);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
