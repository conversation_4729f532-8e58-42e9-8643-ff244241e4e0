/*
 * @Author: <PERSON>y<PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-06-30 15:25:00
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import Platform from "../../nsn/platform/Platform";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import Tips from "../../nsn/util/Tips";
import { FriendApplyRet, FriendSetBlacklistRet, PlayerInfoQueryRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumExpandType } from "../data/base/BaseExpand";
import TBArcher from "../data/parser/TBArcher";
import { CHAT_CHANNEL } from "../data/parser/TBChat";
import TBExpand from "../data/parser/TBExpand";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import TBMagicSkill from "../data/parser/TBMagicSkill";
import TBPet from "../data/parser/TBPet";
import TBSkill from "../data/parser/TBSkill";
import TBTank from "../data/parser/TBTank";
import TBWeapon from "../data/parser/TBWeapon";
import TBWing from "../data/parser/TBWing";
import Friend from "../game/Friend";
import Player from "../game/Player";
import CombatScoreUtils from "../utils/CombatScoreUtils";
import ImageUtils from "../utils/ImageUtils";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

enum UIType {
    UserInfo,
    Setting,
}

const TAB_ENUM = [UIType.UserInfo, UIType.Setting];

@ccclass
export default class FloatOtherPlayerInfo extends I18nComponent {
    @property([cc.Node])
    btns: cc.Node[] = [];
    @property(cc.Node)
    content1: cc.Node = null; // 他人信息
    @property(cc.Node)
    tank: cc.Node = null;
    @property(cc.Node)
    nodeLead: cc.Node = null; // 主角
    @property(sp.Skeleton)
    spineLead: sp.Skeleton = null; // 主角
    @property(sp.Skeleton)
    spineWeapon: sp.Skeleton = null; // 武器
    @property(sp.Skeleton)
    spineWing: sp.Skeleton = null; // 背饰
    @property(cc.Node)
    pet: cc.Node = null;
    @property(cc.Node)
    nodeHead: cc.Node = null;
    @property(cc.Node)
    playerName: cc.Node = null;
    @property(cc.Node)
    playerId: cc.Node = null;
    @property(cc.Node)
    titleIcon: cc.Node = null;
    @property(cc.Node)
    serverName: cc.Node = null;
    @property(cc.Node)
    powerIcon: cc.Node = null;
    @property(cc.Node)
    unionName: cc.Node = null;
    @property(cc.Node)
    combat: cc.Node = null; // 战斗力
    @property(cc.Node)
    btnAdd: cc.Node = null;
    @property(cc.Node)
    btnBlack: cc.Node = null;
    @property(cc.Node)
    btnChat: cc.Node = null;
    @property(cc.Node)
    btnReport: cc.Node = null;

    @property(cc.Node)
    content2: cc.Node = null; // 他人搭配
    @property(cc.Node)
    topNode: cc.Node = null;

    @property(cc.Node)
    initiativeLayout: cc.Node = null;
    @property(cc.Node)
    none1: cc.Node = null;
    @property(cc.Node)
    passiveLayout: cc.Node = null;
    @property(cc.Node)
    none2: cc.Node = null;
    @property(cc.Node)
    text1: cc.Node = null;
    @property(cc.Node)
    text2: cc.Node = null;

    private data: PlayerInfoQueryRet = null; // 玩家信息
    private curTab: UIType = UIType.UserInfo;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.text1,
                url: "texture/syncUI/magical/spMagicTxt1",
            },
            {
                sprite: this.text2,
                url: "texture/syncUI/magical/spMagicTxt2",
            },
        ];
    }

    protected onLoad(): void {
        UI.getInstance().showSoftLoading();
        Player.getInstance().sendPlayerInfoQuery(this.args);
        for (let i = 0; i < this.btns.length; i++) {
            this.btns[i].button(TAB_ENUM[i]);
        }
    }

    protected onDestroy(): void {
        UI.getInstance().hideSoftLoading();
    }

    protected registerHandler(): void {
        Player.getInstance().on(
            PlayerInfoQueryRet.prototype.clazzName,
            (data: PlayerInfoQueryRet) => {
                this.data = data;
                this.updateUI();
            },
            this
        );
        Friend.getInstance().on(
            FriendApplyRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
        Friend.getInstance().on(
            FriendSetBlacklistRet.prototype.clazzName,
            () => {
                Tips.getInstance().show(i18n.friend0022);
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        switch (this.curTab) {
            case UIType.UserInfo:
                this.content1.active = true;
                this.content2.active = false;
                this.updatePlayerInfo();
                break;
            case UIType.Setting:
                this.content1.active = false;
                this.content2.active = true;
                this.updatePlayerPartnerCollocation();
                this.updatePlayerMagicalCollocation();
                break;
            default:
                break;
        }
    }

    /**
     * 玩家信息
     */
    private updatePlayerInfo(): void {
        const info = this.data.playerInfo;
        // 个人信息
        PlayerInfoUtils.updateHead(this.nodeHead, info);
        this.playerName.label(info.name);
        this.playerId.label("ID：" + info.gameId);
        PlayerInfoUtils.updateTitle(this.titleIcon, info);
        this.serverName.label(this.data.serverName); // 服务器名称
        PlayerInfoUtils.updatePower(this.powerIcon, info);
        this.unionName.label(this.data.unionName ? this.data.unionName : i18n.union0062);

        CombatScoreUtils.update(this.combat, this.data.playerInfo.combatScore);

        const isMine = Player.getInstance().getId() === this.args; // 是否本人
        const isMyFriend = Friend.getInstance().isFriend(this.args);
        const isApply = Friend.getInstance().isFriendApplying(this.args);
        if (isMine) {
            this.btnAdd.active = false;
            this.btnBlack.active = false;
            this.btnChat.active = false;
        } else {
            this.btnAdd.active = !isMyFriend && !isApply;
            this.btnBlack.active = isMyFriend;
            this.btnChat.active = isMyFriend;
        }
        this.btnReport.active = Player.getInstance().getId() !== info.playerId;

        if (this.data.leadSkinMagicalId) {
            // 个性化-圣装
            const skinData = TBLeadSkin.getInstance().getDataById(this.data.leadSkinMagicalId);
            SpineUtils.setLeadWithoutAniName(this.spineLead, skinData.res, () => {
                // @ts-ignore
                const attachUtil = this.spineLead.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                this.spineLead.setAnimation(0, "attackWait", true);
            });

            if (this.data.weaponMagicalId) {
                const weaponData = TBWeapon.getInstance().getDataById(this.data.weaponMagicalId);
                SpineUtils.setWeaponWithoutAniName(this.spineWeapon, weaponData.res, () => {
                    // @ts-ignore
                    const attachUtil = this.spineWeapon.attachUtil;
                    attachUtil.destroyAllAttachedNodes();
                    attachUtil.generateAllAttachedNodes();

                    const boneHand = this.spineWeapon.findBone("hand");
                    this.spineWeapon.node.setPosition(
                        cc.v2(-boneHand.x / boneHand.scaleX, -boneHand.y / boneHand.scaleY)
                    );

                    this.spineWeapon.setAnimation(0, "attackWait", true);
                });
            }

            if (this.data.wingsMagicalId) {
                const wingData = TBWing.getInstance().getDataById(this.data.wingsMagicalId);
                SpineUtils.setWingWithoutAniName(this.spineWing, wingData.res, () => {
                    this.spineWing.setAnimation(0, "attackWait", true);
                });
            }
        }

        // 个性化-战车
        if (this.data.tankMagicalId) {
            const tankData = TBTank.getInstance().getDataById(this.data.tankMagicalId);
            SpineUtils.setTank(this.tank, tankData.res);
            this.nodeLead.x = -202;
        } else {
            this.nodeLead.x = 0;
        }

        // 个性化-宠物
        if (this.data.petMagicalId) {
            const petData = TBPet.getInstance().getDataById(this.data.petMagicalId);
            SpineUtils.setPet(this.pet, petData.res);
        }
    }

    /**
     * 玩家伙伴搭配
     */
    private updatePlayerPartnerCollocation(): void {
        for (let i = 0; i < this.topNode.childrenCount; i++) {
            const node = this.topNode.children[i];
            const info = this.data.archerInfos[i];
            const partner = node.child("partner");
            const lv = node.child("text");
            const stars = node.child("stars");
            const none = node.child("none");
            if (info) {
                const data = TBArcher.getInstance().getDataById(info.archerId);
                partner.active = true;
                lv.active = true;
                stars.active = true;
                none.active = false;
                SpineUtils.setArcher(partner, parseInt(data.res));
                lv.label(TextUtils.format(i18n.common0078, info.level, data.name));
                ImageUtils.setStarsIcon(stars, info ? info.star : 0);
            } else {
                partner.active = false;
                lv.active = false;
                stars.active = false;
                none.active = true;
            }
        }
    }

    /**
     * 玩家魔法搭配
     */
    private updatePlayerMagicalCollocation(): void {
        const res1 = "texture/magical/spMagicQualityA"; // 主动魔法路径
        const res2 = "texture/magical/spMagicQualityB"; // 被动魔法路径
        const initiativeMagicInfo = this.data.magicalGroupInfo.magicalBattleInfos.filter(
            (v) => TBExpand.getInstance().getDataById(v.expandId).type === EnumExpandType.InitiativeMagicUnlock
        );
        const passiveMagicInfo = this.data.magicalGroupInfo.magicalBattleInfos.filter(
            (v) => TBExpand.getInstance().getDataById(v.expandId).type === EnumExpandType.PassiveMagicUnlock
        );
        this.initiativeLayout.active = initiativeMagicInfo.length > 0;
        this.none1.active = !this.initiativeLayout.active;
        if (this.initiativeLayout.active) {
            for (let i = 0; i < this.initiativeLayout.childrenCount; i++) {
                const node = this.initiativeLayout.children[i];
                if (initiativeMagicInfo[i]) {
                    const magicData = TBMagicSkill.getInstance().getDataById(initiativeMagicInfo[i].magicalId);
                    node.active = true;
                    const data = this.data.magicalGroupInfo.magicalInfos.find(
                        (k) => k.magicalId === initiativeMagicInfo[i].magicalId
                    );
                    const skillData = TBSkill.getInstance().getDataById(magicData.skillId);
                    const bg = node.child("bg");
                    const icon = node.child("icon");
                    const level = node.child("level");
                    bg.spriteAsync(res1 + magicData.quality);
                    ImageUtils.setLeadSkillIcon(icon, skillData.res);
                    level.label("Lv." + data.level);
                } else {
                    node.active = false;
                }
            }
        }

        this.passiveLayout.active = passiveMagicInfo.length > 0;
        this.none2.active = !this.passiveLayout.active;
        if (this.passiveLayout.active) {
            for (let i = 0; i < this.passiveLayout.childrenCount; i++) {
                const node = this.passiveLayout.children[i];
                if (passiveMagicInfo[i]) {
                    const magicData = TBMagicSkill.getInstance().getDataById(passiveMagicInfo[i].magicalId);
                    if (magicData) {
                        node.active = true;
                        const data = this.data.magicalGroupInfo.magicalInfos.find(
                            (k) => k.magicalId === passiveMagicInfo[i].magicalId
                        );
                        const skillData = TBSkill.getInstance().getDataById(magicData.skillId);
                        const bg = node.child("bg");
                        const icon = node.child("icon");
                        const level = node.child("level");
                        bg.spriteAsync(res2 + magicData.quality);
                        ImageUtils.setLeadSkillIcon(icon, skillData.res);
                        level.label("Lv." + data.level);
                    }
                } else {
                    node.active = false;
                }
            }
        }
    }

    /**
     * 黑名单
     */
    protected onClickBlack(): void {
        if (!this.data) {
            return;
        }
        const isMine = Player.getInstance().getId() === this.args;
        const isMyFriend = Friend.getInstance().isFriend(this.args);
        if (!isMine && isMyFriend) {
            UI.getInstance().open("FloatInfo", {
                text: i18n.friend0025,
                confirmCb: () => {
                    Friend.getInstance().sendFriendSetBlacklist(this.args, false);
                },
            });
        }
    }

    /**
     * 私聊
     */
    protected onClickChat(): void {
        if (!this.data) {
            return;
        }
        const isMine = Player.getInstance().getId() === this.args;
        const isMyFriend = Friend.getInstance().isFriend(this.args);
        if (!isMine && isMyFriend) {
            if (UI.getInstance().isExist("PopupChat")) {
                UI.getInstance().closeAll(); // 从聊天界面进入时需关闭
            } else {
                UI.getInstance().close();
            }
            UI.getInstance().open("PopupChat", { channel: CHAT_CHANNEL.FRIEND });
        }
    }

    /**
     * 添加
     */
    protected onClickAdd(): void {
        if (!this.data) {
            return;
        }
        const isMine = Player.getInstance().getId() === this.args;
        const isMyFriend = Friend.getInstance().isFriend(this.args);
        if (!isMine && !isMyFriend) {
            Friend.getInstance().sendFriendApply(this.args);
        }
    }

    /**
     * 举报
     */
    protected onClickReport(): void {
        UI.getInstance().open("FloatPlayerReport", this.data);
    }

    /**
     * 属性对比
     */
    protected onClickAttrPK(): void {}

    protected onClickCopyID(): void {
        if (!this.data) {
            return;
        }

        Platform.getInstance().copyToClipboard(this.data.playerInfo.gameId + "");
    }

    protected onClickTab(sender: cc.Event.EventTouch, tab: UIType): void {
        if (!this.data) {
            return;
        }
        if (this.curTab === tab) {
            return;
        }
        this.curTab = tab;
        this.updateUI();
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
