/*
 * @Author: chenx
 * @Date: 2024-03-20 10:25:27
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:48:40
 */
import Audio from "../../nsn/audio/Audio";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import { IItemInfo } from "../../protobuf/proto";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import i18n from "../config/i18n/I18n";
import { EnumActivityUniversalPara } from "../data/base/BaseActivityUniversal";
import { EnumDungeonType } from "../data/base/BaseDungeon";
import TBActivityUniversal from "../data/parser/TBActivityUniversal";
import TBDungeon from "../data/parser/TBDungeon";
import TBDungeonBoss from "../data/parser/TBDungeonBoss";
import TBDungeonCloud from "../data/parser/TBDungeonCloud";
import TBDungeonTower from "../data/parser/TBDungeonTower";
import TBTrialBoss from "../data/parser/TBTrialBoss";
import Bag from "../game/Bag";
import Combat, { CombatEvent, DungeonType } from "../game/Combat";
import CombatSetting, { CombatSettingId } from "../game/CombatSetting";
import DungeonTower from "../game/DungeonTower";
import ItemUtils from "../utils/ItemUtils";

/**
 * 界面参数
 */
interface IFloatDungeonSuccessArgs {
    type: DungeonType; // 副本类型
    levelId: number; // 关卡id
    passTime: number; // 通关时间
    rewardData: IItemInfo[]; // 奖励数据
}

/**
 * 自动下一关时间段
 */
const AUTO_DURATION = 3;

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 副本-成功
 */
@ccclass
export default class FloatDungeonSuccess extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null;
    @property(cc.Node)
    time: cc.Node = null;
    @property(cc.Node)
    rewards: cc.Node = null;
    @property(cc.Node)
    nodeNextLevel: cc.Node = null; // 下一关按钮
    @property(cc.Label)
    lbtNextLevel: cc.Label = null; // 下一关按钮
    @property(cc.Node)
    nodeCheckbox: cc.Node = null; // 勾选框
    @property(cc.Node)
    nodeCheckboxIcon: cc.Node = null; // 勾选框icon

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private initData: IFloatDungeonSuccessArgs = null; // 初始化数据
    private nextLevelId: number = -1; // 下一关卡id
    private autoTime: number = AUTO_DURATION; // 自动下一关时间
    private refreshTime: number = 0; // 刷新时间
    private isAutoNextLevel: boolean = false; // 是否自动下一关
    private isEnoughCost: boolean = false; // 是否足够消耗

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/result/spResultTitle12",
            },
        ];
    }

    protected getGuideParam(): string {
        return this.initData?.type + "";
    }

    protected onLoad(): void {
        this.initData = this.args;
        this.initInfo();
        Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.DUNGEON_PVE_SUCCESS, AUDIO_EFFECT_PATH.DUNGEON);
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.autoTime -= 1;
                this.updateAutoTimeState();
            }
        }
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        this.time.label(
            TimeFormat.getInstance().getTextByDuration(
                Math.ceil(this.initData.passTime) * 1000,
                TimeDurationFormatType.MM_SS
            )
        );
        ItemUtils.refreshView(this.rewards, this.prefabItem, this.initData.rewardData);
        let costId = -1;
        let costNum = 0;
        switch (this.initData.type) {
            case DungeonType.Boss:
                {
                    const levelInfo = TBDungeonBoss.getInstance().getDataById(this.initData.levelId);
                    levelInfo.id !== levelInfo.nextGrade && (this.nextLevelId = levelInfo.nextGrade);
                    const dungeonInfo = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonBoss);
                    costId = dungeonInfo.cost[0][0];
                    costNum = dungeonInfo.cost[0][1];
                }
                break;
            case DungeonType.Cloud:
                {
                    const levelInfo = TBDungeonCloud.getInstance().getDataById(this.initData.levelId);
                    levelInfo.id !== levelInfo.nextGrade && (this.nextLevelId = levelInfo.nextGrade);
                    const dungeonInfo = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonCloud);
                    costId = dungeonInfo.cost[0][0];
                    costNum = dungeonInfo.cost[0][1];
                }
                break;
            case DungeonType.Tower:
                {
                    const isGetReward = DungeonTower.getInstance().hasReward();
                    if (!isGetReward) {
                        const levelInfo = TBDungeonTower.getInstance().getDataById(this.initData.levelId);
                        levelInfo.id !== levelInfo.nextId && (this.nextLevelId = levelInfo.nextId);
                    }
                    this.isAutoNextLevel = true;
                    const dungeonInfo = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonTower);
                    costId = dungeonInfo.cost[0][0];
                    costNum = dungeonInfo.cost[0][1];
                }
                break;
            case DungeonType.Trial:
                {
                    const levelInfo = TBTrialBoss.getInstance().getDataById(this.initData.levelId);
                    levelInfo.id !== levelInfo.nextId && (this.nextLevelId = levelInfo.nextId);
                    const para: number[][] = TBActivityUniversal.getInstance().getValueByPara(
                        EnumActivityUniversalPara.TrialCostItem
                    );
                    costId = para[0][0];
                    costNum = para[0][1];
                }
                break;
            default:
                break;
        }
        this.isEnoughCost = Bag.getInstance().isEnough(costId, costNum);
        const isNextLevel = this.nextLevelId !== -1 && this.isEnoughCost;
        this.nodeNextLevel.active = isNextLevel;
        this.isAutoNextLevel &&= isNextLevel;
        this.nodeCheckbox.active = this.isAutoNextLevel;
        this.isAutoNextLevel && this.updateCheckboxState();
    }

    /**
     * 更新勾选框状态
     */
    private updateCheckboxState(): void {
        let isCheck = false;
        switch (this.initData.type) {
            case DungeonType.Tower:
                isCheck = CombatSetting.getInstance().getSettingState(CombatSettingId.DungeonTowerAutoNextLevel);
                break;
            default:
                break;
        }
        this.nodeCheckboxIcon.active = isCheck;
        if (isCheck) {
            this.updateAutoTimeState(true);
        } else {
            this.lbtNextLevel.string = i18n.common0083;
        }
    }

    /**
     * 更新自动下一关时间状态
     * @param isInit 是否为初始化调用
     */
    private updateAutoTimeState(isInit: boolean = false): void {
        isInit && (this.autoTime = AUTO_DURATION);
        if (this.autoTime === 0) {
            UI.getInstance().closeFloatWindow(this.node.name);
            Combat.getInstance().emit(CombatEvent.NextLevel, this.nextLevelId);
        } else {
            this.lbtNextLevel.string = TextUtils.format(i18n.common0080, this.autoTime);

            this.refreshTime = REFRESH_DURATION;
        }
    }

    protected onClickQuit(): void {
        if (this.autoTime === 0) {
            return;
        }

        this.refreshTime = 0;

        switch (this.initData.type) {
            case DungeonType.Boss:
                UI.getInstance().closeToWindow("UIDungeonCombatBoss");
                break;
            case DungeonType.Cloud:
                UI.getInstance().closeToWindow("UIDungeonCombatCloud");
                break;
            case DungeonType.Tower:
                UI.getInstance().closeToWindow("UIDungeonCombatTower");
                break;
            case DungeonType.Trial:
                UI.getInstance().closeToWindow("UIDungeonCombatTrial");
                break;
            default:
                break;
        }
        UI.getInstance().close();
    }

    protected onClickAgain(): void {}

    protected onClickNext(): void {
        if (this.autoTime === 0) {
            return;
        }

        this.refreshTime = 0;

        if (this.nextLevelId === -1 || !this.isEnoughCost) {
            return;
        }

        UI.getInstance().closeFloatWindow(this.node.name);
        Combat.getInstance().emit(CombatEvent.NextLevel, this.nextLevelId);
    }

    /**
     * 设置勾选框状态
     */
    protected onClickSetCheckboxState(): void {
        if (this.autoTime === 0) {
            return;
        }

        this.refreshTime = 0;

        if (!this.isAutoNextLevel) {
            return;
        }

        switch (this.initData.type) {
            case DungeonType.Tower:
                CombatSetting.getInstance().setSettingState(CombatSettingId.DungeonTowerAutoNextLevel);
                break;
            default:
                break;
        }
        this.updateCheckboxState();
    }
}
