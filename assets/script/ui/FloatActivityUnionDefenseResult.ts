/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-13 12:51:58
 */

import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { PlayerInfoQueryRet } from "../../protobuf/proto";
import { EnumUnionPara } from "../data/base/BaseUnion";
import TBUnion from "../data/parser/TBUnion";
import Player from "../game/Player";
import Union from "../game/Union";
import UnionSiege from "../game/UnionSiege";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatActivityUnionDefenseResult extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null;
    @property([cc.Node])
    unions: cc.Node[] = [];
    @property(cc.Node)
    myServeName: cc.Node = null;
    @property(cc.Node)
    enemyServeName: cc.Node = null;

    @property(cc.Node)
    reward: cc.Node = null;
    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: `texture/result/spResultTitle26`,
            },
        ];
    }

    protected onLoad(): void {
        this.updateUI();
    }

    protected registerHandler(): void {
        Player.getInstance().on(
            PlayerInfoQueryRet.prototype.clazzName,
            (data: PlayerInfoQueryRet) => {
                const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
                const unionInfo = Union.getInstance().getInfo();
                const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
                const rivalUnionInfo = unionSiegeInfo.find((e) => e.rivalUnionId === unionInfo.id); // 对方
                if (data.playerInfo.playerId === myInfo?.leaderInfo?.playerId) {
                    this.myServeName.label(data.serverName);
                } else if (data.playerInfo.playerId === rivalUnionInfo?.leaderInfo?.playerId) {
                    this.enemyServeName.label(data.serverName);
                }
            },
            this
        );
    }

    private updateUI(): void {
        const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
        const unionInfo = Union.getInstance().getInfo();
        const isRound = UnionSiege.getInstance().isUnionRoundEmpty();
        const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
        const rivalUnionInfo = unionSiegeInfo.find((e) => e.rivalUnionId === unionInfo.id); // 对方

        let type = null;
        if (isRound) {
            type = EnumUnionPara.UnionDefenseBye;
        } else {
            if (
                myInfo.totalStarCount > rivalUnionInfo.totalStarCount ||
                (myInfo.totalStarCount === rivalUnionInfo.totalStarCount && myInfo.starTime < rivalUnionInfo.starTime)
            ) {
                type = EnumUnionPara.UnionDefenseWin;
            } else {
                type = EnumUnionPara.UnionDefenseFail;
            }
        }
        const unionData = TBUnion.getInstance().getValueByPara(type);
        ItemUtils.refreshView(this.reward, this.prefabItem, unionData);

        if (isRound) {
            for (let i = 0; i < this.unions.length; i++) {
                const e = this.unions[i];
                const unionFlag = e.child("flag");
                const unionName = e.child("nameText");
                const unionCount = e.child("layout").child("count");
                const unionStar = e.child("layout").child("star");
                if (i === 0) {
                    e.active = true;
                    e.x = 0;
                    ImageUtils.setUnionFlag(unionFlag, myInfo.unionInfo.flag);
                    unionName.label(myInfo.unionInfo.name);
                    Player.getInstance().sendPlayerInfoQuery(myInfo.leaderInfo.playerId);
                    unionStar.spriteAsync("texture/activity/union/defense/spUBStar2");
                    unionCount.label(myInfo.totalStarCount + "");
                } else if (i === 1) {
                    e.active = false;
                }
            }
            return;
        }

        for (let i = 0; i < this.unions.length; i++) {
            const e = this.unions[i];
            const unionFlag = e.child("flag");
            const unionName = e.child("nameText");
            const unionCount = e.child("layout").child("count");
            const unionStar = e.child("layout").child("star");
            if (i === 0) {
                e.x = -240;
                ImageUtils.setUnionFlag(unionFlag, myInfo.unionInfo.flag);
                unionName.label(myInfo.unionInfo.name);
                Player.getInstance().sendPlayerInfoQuery(myInfo.leaderInfo.playerId);
                unionStar.spriteAsync("texture/activity/union/defense/spUBStar2");
                unionCount.label(myInfo.totalStarCount + "");
            } else if (i === 1) {
                e.x = 240;
                ImageUtils.setUnionFlag(unionFlag, rivalUnionInfo.unionInfo.flag);
                unionName.label(rivalUnionInfo.unionInfo.name);
                Player.getInstance().sendPlayerInfoQuery(rivalUnionInfo.leaderInfo.playerId);
                unionStar.spriteAsync("texture/activity/union/defense/spUBStar2");
                unionCount.label(rivalUnionInfo.totalStarCount + "");
            }
        }
    }

    /**
     * 点击关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
