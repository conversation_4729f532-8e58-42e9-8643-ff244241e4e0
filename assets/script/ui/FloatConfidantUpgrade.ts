/*
 * @Author: chenx
 * @Date: 2024-10-09 14:33:49
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-10 16:58:48
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import i18n from "../config/i18n/I18n";
import TBAttribute from "../data/parser/TBAttribute";
import TBPrincess from "../data/parser/TBPrincess";
import TBPrincessChild from "../data/parser/TBPrincessChild";
import ImageUtils from "../utils/ImageUtils";

/**
 * 界面参数
 */
export interface IFloatConfidantUpgradeArgs {
    confidantId: number; // 知己id
    childId: number; // 王储id
    level: number; // 等级
}

const { ccclass, property } = cc._decorator;

/**
 * 知己-知己升级
 */
@ccclass
export default class FloatConfidantUpgrade extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null; // 标题
    @property(sp.Skeleton)
    spineTitleBg: sp.Skeleton = null; // 标题bg
    @property(cc.Node)
    nodeCloseTips: cc.Node = null; // 关闭提示

    @property(cc.Sprite)
    spHead: cc.Sprite = null; // 知己head
    @property(cc.Label)
    lbtLevel: cc.Label = null; // 等级
    @property(cc.Label)
    lbtLevel2: cc.Label = null; // 等级
    @property(cc.Node)
    nodeContent: cc.Node = null; // 属性content
    @property(cc.Node)
    nodeItem: cc.Node = null; // 属性item

    initData: IFloatConfidantUpgradeArgs = null; // 初始数据

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/syncUI/confidant/spResultTitle4",
            },
        ];
    }

    protected onLoad(): void {
        this.nodeItem.parent = null;

        this.initData = this.args;
        this.initInfo();
    }

    protected onDestroy(): void {
        this.nodeItem.destroy();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        let attr: number[][] = null;
        if (this.initData.confidantId !== -1) {
            const confidantInfo = TBPrincess.getInstance().getDataById(this.initData.confidantId);
            ImageUtils.setConfidantHead(this.spHead, confidantInfo.res);
            attr = confidantInfo.attribute;
        }
        if (this.initData.childId !== -1) {
            const childInfo = TBPrincessChild.getInstance().getDataById(this.initData.childId);
            let tempRes = "";
            for (const [level, res] of childInfo.res) {
                if (this.initData.level >= level) {
                    tempRes = res + "";
                } else {
                    break;
                }
            }
            ImageUtils.setConfidantHead(this.spHead, tempRes);
            attr = childInfo.attribute;
        }
        this.lbtLevel.string = TextUtils.format(i18n.dungeon0001, this.initData.level - 1);
        this.lbtLevel2.string = TextUtils.format(i18n.dungeon0001, this.initData.level);
        attr.forEach(([attrId, init, step]) => {
            const nodeItem = Loader.getInstance().instantiate(this.nodeItem);
            this.nodeContent.addChild(nodeItem);

            const attrData = TBAttribute.getInstance().formatAttribute([
                attrId,
                init + step * (this.initData.level - 1 - 1),
            ]);
            nodeItem.child("lbtAttr").label(`${attrData.name}+${attrData.value}`);
            const attrData2 = TBAttribute.getInstance().formatAttribute([
                attrId,
                init + step * (this.initData.level - 1),
            ]);
            nodeItem.child("lbtAttr2").label(`+${attrData2.value}`);
        });

        this.spineTitleBg.setAnimation(0, "wait", false);
        this.spineTitleBg.addAnimation(0, "wait2", true);

        cc.tween(this.nodeCloseTips)
            .repeatForever(
                cc
                    .tween()
                    .to(1, { opacity: 0 }, { easing: cc.easing.sineIn })
                    .to(1, { opacity: 255 }, { easing: cc.easing.sineOut })
            )
            .start();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
