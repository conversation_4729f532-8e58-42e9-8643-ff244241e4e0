/*
 * @Author: <PERSON>y<PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-16 15:07:20
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import Time from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    IUnionSiegeChallengeObj,
    IUnionSiegeShowObj,
    UnionSiegeEndChallengeNoticeRet,
    UnionSiegeEndChallengeRet,
    UnionSiegeStartChallengeNoticeRet,
    UnionSiegeStartChallengeRet,
} from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import { EnumUnionPara } from "../data/base/BaseUnion";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import TBUnion from "../data/parser/TBUnion";
import TBWeapon from "../data/parser/TBWeapon";
import TBWing from "../data/parser/TBWing";
import UnionSiege, { EnumChallengeDifficulty, EnumUnionSiegeMemberType } from "../game/UnionSiege";
import { IUnionDefenseLeadItemData } from "../prefab/activity/union/unionDefense/PrefabActivityUnionDefenseLeadItem";
import CombatScoreUtils from "../utils/CombatScoreUtils";
import ImageUtils from "../utils/ImageUtils";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";
import SpineUtils from "../utils/SpineUtils";

const DIFFICULTY_ARR = [
    EnumChallengeDifficulty.Ordinary,
    EnumChallengeDifficulty.Difficulty,
    EnumChallengeDifficulty.Nightmare,
];

interface IChallengeData {
    data: IUnionSiegeChallengeObj;
    difficulty: EnumChallengeDifficulty; // 挑战难度
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupActivityUnionDefenseChallenge extends I18nComponent {
    @property(cc.Node)
    rivalHead: cc.Node = null;
    @property(cc.Node)
    rivalName: cc.Node = null;
    @property(cc.Node)
    rivalCombat: cc.Node = null;
    @property(cc.Node)
    rivalNodeLead: cc.Node = null;

    @property(cc.Node)
    btn1ChallengeIcon: cc.Node = null;
    @property(cc.Node)
    btn2ChallengeIcon: cc.Node = null;
    @property(cc.Node)
    btn3ChallengeIcon: cc.Node = null;
    @property(cc.Node)
    isChallengeIcon1: cc.Node = null; // 被挑战中
    @property(cc.Node)
    isChallengeIcon2: cc.Node = null; // 被挑战中
    @property(cc.Node)
    isChallengeIcon3: cc.Node = null; // 被挑战中

    @property(cc.Node)
    winCount: cc.Node = null;
    @property(cc.Node)
    stars: cc.Node = null;
    @property(cc.Node)
    starNode: cc.Node = null;

    @property([cc.Node])
    btns: cc.Node[] = [];
    @property([cc.Node])
    isChallengeIcons: cc.Node[] = [];

    private data: IUnionDefenseLeadItemData = null;
    private dt: number = 1;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.btn1ChallengeIcon,
                url: `texture/syncUI/activity/union/defense/btnUBBattle`,
            },
            {
                sprite: this.btn2ChallengeIcon,
                url: `texture/syncUI/activity/union/defense/btnUBBattle`,
            },
            {
                sprite: this.btn3ChallengeIcon,
                url: `texture/syncUI/activity/union/defense/btnUBBattle`,
            },
            {
                sprite: this.isChallengeIcon1,
                url: `texture/syncUI/activity/union/defense/spUBStatus`,
            },
            {
                sprite: this.isChallengeIcon2,
                url: `texture/syncUI/activity/union/defense/spUBStatus`,
            },
            {
                sprite: this.isChallengeIcon3,
                url: `texture/syncUI/activity/union/defense/spUBStatus`,
            },
        ];
    }

    protected onLoad(): void {
        this.starNode.parent = null;
        this.data = this.args;

        this.initCenterUI();
        this.updateUI();
    }

    protected onDestroy(): void {
        this.starNode.destroy();

        UI.getInstance().hideSoftLoading();
    }

    protected update(dt: number): void {
        this.dt += dt;
        if (this.dt >= 1) {
            this.dt -= 1;
            this.updateBtnUI();
            this.updateCenterUI();
        }
    }

    protected registerHandler(): void {
        UnionSiege.getInstance().on(
            [
                UnionSiegeStartChallengeRet.prototype.clazzName,
                UnionSiegeStartChallengeNoticeRet.prototype.clazzName,
                UnionSiegeEndChallengeRet.prototype.clazzName,
                UnionSiegeEndChallengeNoticeRet.prototype.clazzName,
            ],
            () => {
                this.updateCenterUI();
                this.updateBtnUI();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateTopUI();
        this.updateCenterUI();
        this.updateBtnUI();
    }

    /**
     * 敌方头像及信息
     */
    private updateTopUI(): void {
        PlayerInfoUtils.updateHead(this.rivalHead, this.data.data.playerInfo);
        this.rivalName.label(this.data.data.playerInfo.name);
        CombatScoreUtils.update(this.rivalCombat, this.data.data.playerInfo.combatScore);
        this.setLeadUI(this.rivalNodeLead, this.data.data.showInfo);
    }

    private initCenterUI(): void {
        const info = UnionSiege.getInstance().getUnionSiegeDetailObjByUnionIdAndPlayerId(
            this.data.unionId,
            this.data.data.playerInfo.playerId
        );
        const allCount = UnionSiege.getInstance().getTotalStarCountByMemberType(info.memberType); // 总星数

        this.winCount.label(TextUtils.format(i18n.unionDefense0017, info.defendSuccessCount));
        for (let i = 0; i < allCount; i++) {
            const node = Loader.getInstance().instantiate(this.starNode);
            node.parent = this.stars;
            node.active = true;
        }
    }

    /**
     * 敌方星星和防守胜利次数
     */
    private updateCenterUI(): void {
        const info = UnionSiege.getInstance().getUnionSiegeDetailObjByUnionIdAndPlayerId(
            this.data.unionId,
            this.data.data.playerInfo.playerId
        );
        const allCount = UnionSiege.getInstance().getTotalStarCountByMemberType(info.memberType); // 总星数

        this.winCount.label(TextUtils.format(i18n.unionDefense0017, info.defendSuccessCount));
        const starCount = this.getStarCount(info.memberType);
        for (let i = 0; i < allCount; i++) {
            const node = this.stars.children[i];
            ImageUtils.setUnionDefenseStar(node, i < starCount);
        }
    }

    private updateBtnUI(): void {
        const unionData1 = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseDifficulty);
        const unionData2 = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefensePVPTime);
        const info = UnionSiege.getInstance().getUnionSiegeDetailObjByUnionIdAndPlayerId(
            this.data.unionId,
            this.data.data.playerInfo.playerId
        );

        for (let i = 0; i < this.btns.length; i++) {
            const node = this.btns[i];
            const now = Time.getInstance().now();
            const beChallenged =
                info.challengeInfos[i].challengerRoleId !== "" &&
                info.challengeInfos[i].challengeTime + unionData2 * 1000 > now;
            this.isChallengeIcons[i].active = beChallenged; // 被挑战中

            node.getComponent(GrayComp).gray = info.challengeInfos[i].isChallengeSuccess;
            CocosExt.setButtonEnable(node, !info.challengeInfos[i].isChallengeSuccess);
            const lv = node.child("lv");
            const layout = node.child("layout");
            const star = layout.child("star");
            const count = layout.child("count");

            const difficultiesCount = UnionSiege.getInstance().getStarCount(
                info.memberType,
                info.challengeInfos[i].challengeDifficulty
            ); // 每个难度对应的星数
            lv.label(TextUtils.format(i18n.unionDefense0013, unionData1[i] * 100));
            ImageUtils.setUnionDefenseStar(star, true);
            count.label("×" + difficultiesCount);

            const obj: IChallengeData = {
                data: info.challengeInfos[i],
                difficulty: DIFFICULTY_ARR[i],
            };
            node.button(obj);
        }
    }

    /**
     * 获取目前拥有的星数和总星数
     * @param type
     * @param difficulty
     * @returns
     */
    private getStarCount(type: EnumUnionSiegeMemberType): number {
        const info = UnionSiege.getInstance().getUnionSiegeDetailObjByUnionIdAndPlayerId(
            this.data.unionId,
            this.data.data.playerInfo.playerId
        );
        const allCount = UnionSiege.getInstance().getTotalStarCountByMemberType(type); // 总星数
        let count = allCount;
        for (const e of info.challengeInfos) {
            if (e.isChallengeSuccess) {
                const difficultiesCount = UnionSiege.getInstance().getStarCount(type, e.challengeDifficulty); // 每个难度对应的星数
                count -= difficultiesCount;
            }
        }
        return count;
    }

    private setLeadUI(node: cc.Node, data: IUnionSiegeShowObj): void {
        const spineLead = node.child("spineLead").getComponent(sp.Skeleton);
        const spineWeapon = node.child("nodeWeapon").child("spineWeapon").getComponent(sp.Skeleton);
        const spineWing = node.child("nodeWing").child("spineWing").getComponent(sp.Skeleton);

        // 主角
        const skinData = TBLeadSkin.getInstance().getDataById(data.leadSkinId);
        SpineUtils.setLeadWithoutAniName(spineLead, skinData.res, () => {
            // @ts-ignore
            const attachUtil = spineLead.attachUtil;
            attachUtil.destroyAllAttachedNodes();
            attachUtil.generateAllAttachedNodes();

            spineLead.setAnimation(0, "attackWait", true);
        });

        // 神器
        if (data.weaponId !== 0) {
            node.child("nodeWeapon").active = true;
            const weaponData = TBWeapon.getInstance().getDataById(data.weaponId);
            SpineUtils.setWeaponWithoutAniName(spineWeapon, weaponData.res, () => {
                // @ts-ignore
                const attachUtil = spineWeapon.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                const boneHand = spineWeapon.findBone("hand");
                spineWeapon.node.setPosition(cc.v2(-boneHand.x / boneHand.scaleX, -boneHand.y / boneHand.scaleY));
                spineWeapon.setAnimation(0, "attackWait", true);
            });
        } else {
            node.child("nodeWeapon").active = false;
        }

        // 背饰
        if (data.wingsId !== 0) {
            node.child("nodeWing").active = true;
            const wingData = TBWing.getInstance().getDataById(data.wingsId);
            SpineUtils.setWingWithoutAniName(spineWing, wingData.res, () => {
                spineWing.setAnimation(0, "attackWait", true);
            });
        } else {
            node.child("nodeWing").active = false;
        }
    }

    protected onClickBtn(sender: cc.Event.EventTouch, data: IChallengeData): void {
        if (data.data.isChallengeSuccess) {
            return;
        }

        const myDetailObjs = UnionSiege.getInstance().getMyUnionSingeDetailObj();
        const times = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseBattleNumber);
        if (times - myDetailObjs.todayChallengeCount <= 0) {
            Tips.getInstance().show(i18n.unionDefense0023);
            return;
        }

        const now = Time.getInstance().now();
        const unionData = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefensePVPTime);
        if (data.data.challengerRoleId !== "" && data.data.challengeTime + unionData * 1000 > now) {
            Tips.getInstance().show(i18n.unionDefense0018);
            return;
        }

        UnionSiege.getInstance().sendUnionSiegeStartChallenge(
            this.data.unionId,
            data.data.challengeDifficulty,
            this.data.data.playerInfo.playerId,
            this.data.activityId
        );
        UI.getInstance().showSoftLoading();
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
