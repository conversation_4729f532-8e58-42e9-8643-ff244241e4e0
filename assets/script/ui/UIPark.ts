/*
 * @Author: chenx
 * @Date: 2024-07-26 14:56:16
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:20:15
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import Tips from "../../nsn/util/Tips";
import {
    CollectType,
    DressEnum,
    IPlayerInfo,
    IPlotInfo,
    PlayerInfoQueryRet,
    PlotCollectRet,
    PlotGetRet,
    PlotIncrExpNoticeRet,
    PlotModifyDressRet,
    PlotModifyManageRet,
    PlotModifyNameRet,
    PlotModifyShowRet,
    PlotStartSnatchRet,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumParkingLotPara } from "../data/base/BaseParkingLot";
import { EnumParkingLotSkinType } from "../data/base/BaseParkingLotSkin";
import { EnumShopType } from "../data/base/BaseShop";
import { ITEM_ID } from "../data/parser/TBItem";
import TBParkingLot from "../data/parser/TBParkingLot";
import TBParkingLotSkin from "../data/parser/TBParkingLotSkin";
import TBTank from "../data/parser/TBTank";
import { DungeonType } from "../game/Combat";
import Park, { ParkEvent } from "../game/Park";
import Player from "../game/Player";
import PrefabParkSpaceItem, { IParkSpaceData, ParkSpaceState } from "../prefab/park/PrefabParkSpaceItem";
import { IParkTankData } from "../prefab/park/PrefabParkTankItem";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";
import PlayerResUtils from "../utils/PlayerResUtils";
import SpineUtils from "../utils/SpineUtils";

/**
 * 界面参数
 */
export interface IUIParkArgs {
    playerId: string; // 玩家id
    spaceId: number; // 车位id
}

/**
 * 菜单项数据
 */
const MENU_ITEM_DATA: string[] = ["PopupParkRecord", "PopupParkSkin", "PopupParkOrder", "PopupParkSearch"];

const { ccclass, property } = cc._decorator;

/**
 * 停车场
 */
@ccclass
export default class UIPark extends I18nComponent {
    @property(cc.Node)
    nodeHead: cc.Node = null; // 头像
    @property(cc.Label)
    lbtLevel: cc.Label = null; // 等级
    @property(cc.Label)
    lbtPlayerName: cc.Label = null; // 玩家名称
    @property(cc.ProgressBar)
    prgLevelExp: cc.ProgressBar = null; // 等级经验
    @property(cc.Label)
    lbtLevelExp: cc.Label = null; // 等级经验
    @property(cc.Node)
    nodeCollect: cc.Node = null; // 收藏
    @property(cc.Node)
    nodeCollectedIcon: cc.Node = null; // 已收藏icon

    @property(cc.Node)
    nodeResContent: cc.Node = null; // 资源栏content

    @property(cc.Node)
    nodeTankMenu: cc.Node = null; // 战车菜单
    @property(ListView)
    listTank: ListView = null; // 战车列表
    @property(cc.Node)
    nodeControlIcon: cc.Node = null; // 控制按钮icon

    @property([cc.Node])
    nodeMenuItem: cc.Node[] = []; // 菜单项
    @property(cc.Node)
    nodeClose: cc.Node = null; // 关闭
    @property(cc.Node)
    nodeJumpMyPark: cc.Node = null; // 跳转-我的停车场
    @property(cc.Node)
    btnRule: cc.Node = null; // 规则按钮

    @property(cc.Sprite)
    spDoor: cc.Sprite = null; // 大门
    @property(cc.Sprite)
    spLeftWall: cc.Sprite = null; // 左侧围墙
    @property(cc.Sprite)
    spRightWall: cc.Sprite = null; // 右侧围墙
    @property(cc.Sprite)
    spBottomWall: cc.Sprite = null; // 底部围墙
    @property([cc.Sprite])
    spLamp: cc.Sprite[] = []; // 路灯

    @property(cc.ScrollView)
    scvBg: cc.ScrollView = null; // 停车场bg
    @property(cc.Label)
    lbtParkName: cc.Label = null; // 停车场名称
    @property([cc.Node])
    nodeSpace: cc.Node[] = []; // 车位
    @property(cc.RichText)
    rtSetTips: cc.RichText = null; // 设置提示
    @property(sp.Skeleton)
    spineTank: sp.Skeleton = null; // 展示战车

    @property(cc.Prefab)
    prefabSpaceItem: cc.Prefab = null; // 车位item

    initData: IUIParkArgs = null; // 初始数据
    playerData: IPlayerInfo = null; // 玩家数据
    parkData: IPlotInfo = null; // 停车场数据
    spaceData: IParkSpaceData[] = []; // 车位数据
    tankData: IParkTankData[] = []; // 战车数据
    jumpPlayerId: string = ""; // 跳转-玩家id
    jumpPlayerData: IPlayerInfo = null; // 跳转-玩家数据
    jumpParkData: IPlotInfo = null; // 跳转-停车场数据
    isOther: boolean = false; // 是否为他人停车场
    isUnfold: boolean = false; // 是否展开-战车菜单
    isPlayingAni: boolean = false; // 是否正在播放动画-战车菜单

    protected onLoad(): void {
        this.nodeMenuItem.forEach((v, i) => v.button(i));

        this.initData = this.args;
        this.init();
    }

    protected start(): void {
        if (!this.initData || this.initData.playerId === Player.getInstance().getId()) {
            this.checkUpdate();
        }
        if (this.initData && this.initData.spaceId !== -1 && this.initData.playerId === Player.getInstance().getId()) {
            Park.getInstance().sendStartSnatchSpace(this.initData.playerId, this.initData.spaceId);
            this.initData.spaceId = -1;
        }
    }

    protected registerHandler(): void {
        Park.getInstance().on(
            PlotIncrExpNoticeRet.prototype.clazzName,
            () => {
                if (this.isOther) {
                    return;
                }

                this.checkUpdate();
            },
            this
        );
        Park.getInstance().on(
            PlotModifyNameRet.prototype.clazzName,
            () => {
                if (this.isOther) {
                    return;
                }

                this.parkData = Park.getInstance().getData();
                this.lbtParkName.string = this.parkData.name;
            },
            this
        );
        Park.getInstance().on(
            PlotModifyShowRet.prototype.clazzName,
            () => {
                if (this.isOther) {
                    return;
                }

                this.parkData = Park.getInstance().getData();
                this.updateShowTankInfo();
            },
            this
        );
        Park.getInstance().on(
            PlotCollectRet.prototype.clazzName,
            () => {
                if (!this.isOther) {
                    return;
                }

                const isCollected = Park.getInstance().isCollected(this.parkData.ownerRoleId);
                isCollected ? Tips.getInstance().info(i18n.park0016) : Tips.getInstance().info(i18n.park0017);
                this.nodeCollectedIcon.active = isCollected;
            },
            this
        );
        Park.getInstance().on(
            ParkEvent.SpaceParked,
            () => {
                if (this.isOther) {
                    Park.getInstance().sendGetOtherData(this.playerData.playerId);
                    UI.getInstance().closeToWindow(this.node.name);
                }
            },
            this
        );
        Park.getInstance().on(
            ParkEvent.UpdateSpaceState,
            (playerId: string, spaceId: number, isCheckUpgrade: boolean) => {
                if (this.parkData.ownerRoleId !== playerId) {
                    return;
                }

                if (!this.isOther) {
                    this.parkData = Park.getInstance().getData();
                } else {
                    this.parkData = Park.getInstance().getOtherData();
                }
                this.updateSpaceInfo(spaceId);

                this.updateListInfo();

                if (isCheckUpgrade) {
                    this.checkUpdate();
                }
            },
            this
        );
        Park.getInstance().on(
            ParkEvent.UpdateTankState,
            (tankId: number) => {
                this.updateListInfo(tankId);
            },
            this
        );
        Park.getInstance().on(
            ParkEvent.Jump,
            (playerId: string) => {
                if (playerId === this.playerData.playerId) {
                    UI.getInstance().closeToWindow(this.node.name);
                    return;
                }
                if (playerId === Player.getInstance().getId()) {
                    this.isOther = false;
                    this.parkData = Park.getInstance().getData();
                    this.playerData = Player.getInstance().getInfo();
                    this.updatePark(true);
                    this.checkUpdate();

                    UI.getInstance().closeToWindow(this.node.name);
                    return;
                }

                this.jumpPlayerData = null;
                this.jumpParkData = null;
                this.jumpPlayerId = playerId;
                Player.getInstance().sendPlayerInfoQuery(this.jumpPlayerId);
                Park.getInstance().sendGetOtherData(this.jumpPlayerId);
            },
            this
        );
        Player.getInstance().on(
            PlayerInfoQueryRet.prototype.clazzName,
            (data: PlayerInfoQueryRet) => {
                if (this.jumpPlayerId === "" || data.playerInfo.playerId !== this.jumpPlayerId) {
                    return;
                }

                this.jumpPlayerData = data.playerInfo;
                if (
                    this.jumpPlayerData &&
                    this.jumpParkData &&
                    this.jumpPlayerData.playerId === this.jumpParkData.ownerRoleId
                ) {
                    this.isOther = true;
                    this.playerData = this.jumpPlayerData;
                    this.parkData = this.jumpParkData;
                    this.jumpPlayerId = "";
                    this.jumpPlayerData = null;
                    this.jumpParkData = null;
                    this.updatePark(true);

                    UI.getInstance().closeToWindow(this.node.name);

                    if (this.initData && this.initData.spaceId !== -1) {
                        Park.getInstance().sendStartSnatchSpace(this.initData.playerId, this.initData.spaceId);
                        this.initData.spaceId = -1;
                    }
                }
            },
            this
        );
        Park.getInstance().on(
            PlotGetRet.prototype.clazzName,
            () => {
                const parkData = Park.getInstance().getOtherData();
                if (this.jumpPlayerId === "") {
                    if (this.parkData.ownerRoleId === parkData.ownerRoleId) {
                        this.parkData = parkData;
                        this.updatePark();
                    }
                    return;
                }
                if (parkData.ownerRoleId !== this.jumpPlayerId) {
                    return;
                }

                this.jumpParkData = parkData;
                if (
                    this.jumpPlayerData &&
                    this.jumpParkData &&
                    this.jumpPlayerData.playerId === this.jumpParkData.ownerRoleId
                ) {
                    this.isOther = true;
                    this.playerData = this.jumpPlayerData;
                    this.parkData = this.jumpParkData;
                    this.jumpPlayerId = "";
                    this.jumpPlayerData = null;
                    this.jumpParkData = null;
                    this.updatePark(true);

                    UI.getInstance().closeToWindow(this.node.name);

                    if (this.initData && this.initData.spaceId !== -1) {
                        Park.getInstance().sendStartSnatchSpace(this.initData.playerId, this.initData.spaceId);
                        this.initData.spaceId = -1;
                    }
                }
            },
            this
        );
        Park.getInstance().on(
            PlotStartSnatchRet.prototype.clazzName,
            (data: PlotStartSnatchRet) => {
                Park.getInstance().setPlayerCombatData(data.spaceRoleInfo);
                UI.getInstance().open("UIDungeonCombatPark", {
                    type: DungeonType.Park,
                    resultCb: (isWin: boolean) => {
                        Park.getInstance().sendEndSnatchSpace(data.ownerRoleId, data.spaceId, isWin);
                    },
                });
            },
            this
        );
        Park.getInstance().on(
            PlotModifyDressRet.prototype.clazzName,
            (type: DressEnum, skinId: number) => {
                if (this.isOther) {
                    return;
                }

                this.parkData = Park.getInstance().getData();
                this.updateSkinState(type);
            },
            this
        );
        Park.getInstance().on(
            PlotModifyManageRet.prototype.clazzName,
            () => {
                this.updateSetInfo();
            },
            this
        );
    }

    /**
     * 初始化
     */
    private init(): void {
        this.initSpace();
        if (this.initData && this.initData.playerId !== Player.getInstance().getId()) {
            this.jumpPlayerId = this.initData.playerId;
            Player.getInstance().sendPlayerInfoQuery(this.jumpPlayerId);
            Park.getInstance().sendGetOtherData(this.jumpPlayerId);
        } else {
            this.parkData = Park.getInstance().getData();
            this.playerData = Player.getInstance().getInfo();
            this.updatePark();
        }
        this.updateListInfo();
        PlayerResUtils.create(ITEM_ID.HOME_COIN, this.nodeResContent, { spacing: -14 });
    }

    /**
     * 初始化车位
     */
    private initSpace(): void {
        const spaceCount: number = TBParkingLot.getInstance().getValueByPara(EnumParkingLotPara.ParkingQuantity);
        for (let i = 0; i < spaceCount; i++) {
            const nodeItem = Loader.getInstance().instantiate(this.prefabSpaceItem);
            this.nodeSpace[i].addChild(nodeItem);
            const compItem = nodeItem.getComponent(PrefabParkSpaceItem);
            const data: IParkSpaceData = {
                id: i + 1,
                state: null,
                stateData: null,
                playerData: null,
                setData: null,
                isOther: false,

                nodeItem,
                compItem,
            };
            data.compItem.changeState(ParkSpaceState.Init, data);
            this.spaceData.push(data);
        }
    }

    /**
     * 更新停车场
     * @param isJump 是否跳转
     */
    private updatePark(isJump: boolean = false): void {
        this.updateSkinState();
        this.updateParkInfo();
        this.updatePlayerInfo();
        this.updateSpaceInfo();
        this.updateSetInfo();
        this.updateShowTankInfo();
        this.nodeJumpMyPark.active = this.isOther;
        this.btnRule.active = !this.isOther;
        if (isJump) {
            this.scvBg.stopAutoScroll();
            const offset = cc.v2();
            offset.y = (this.scvBg.content.height - cc.winSize.height) / 2;
            this.scvBg.scrollToOffset(offset, 0.6);
        }
    }

    /**
     * 更新皮肤状态
     * @param type 类型
     */
    private updateSkinState(type?: DressEnum): void {
        for (const e of [
            EnumParkingLotSkinType.Stall,
            EnumParkingLotSkinType.Gate,
            EnumParkingLotSkinType.Fence,
            EnumParkingLotSkinType.StreetLamp,
        ]) {
            if (type && Park.getInstance().getServerSkinType(e) !== type) {
                continue;
            }

            const usingSkinId = Park.getInstance().getUsingSkinId(e, this.parkData.dressInfos);
            const skinInfo = TBParkingLotSkin.getInstance().getDataById(usingSkinId);
            switch (e) {
                case EnumParkingLotSkinType.Stall:
                    this.spaceData.forEach((v) =>
                        CocosExt.setSpriteFrameAsync(
                            v.nodeItem.child("spBase"),
                            `texture/park/spParkDec${skinInfo.res}`
                        )
                    );
                    break;
                case EnumParkingLotSkinType.Gate:
                    CocosExt.setSpriteFrameAsync(this.spDoor, `texture/park/spParkDec${skinInfo.res}`);
                    break;
                case EnumParkingLotSkinType.Fence:
                    CocosExt.setSpriteFrameAsync(this.spLeftWall, `texture/park/spParkDecLeft${skinInfo.res}`);
                    CocosExt.setSpriteFrameAsync(this.spRightWall, `texture/park/spParkDecRight${skinInfo.res}`);
                    CocosExt.setSpriteFrameAsync(this.spBottomWall, `texture/park/spParkDecBottom${skinInfo.res}`);
                    break;
                case EnumParkingLotSkinType.StreetLamp:
                    this.spLamp.forEach((v) =>
                        CocosExt.setSpriteFrameAsync(v, `texture/park/spParkDec${skinInfo.res}`)
                    );
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 更新停车场信息
     */
    private updateParkInfo(): void {
        this.lbtParkName.string = this.parkData.name;
        const levelData = Park.getInstance().getLevelData(this.parkData.exp);
        this.lbtLevel.string = levelData.level + "";
        this.prgLevelExp.progress = levelData.isMax
            ? 1
            : Math.floor((levelData.exp / levelData.totalExp) * 1000) / 1000;
        this.lbtLevelExp.string = levelData.isMax ? i18n.bless0001 : `${levelData.exp}/${levelData.totalExp}`;
        const isShowCollect = this.isOther;
        this.nodeCollect.active = isShowCollect;
        if (isShowCollect) {
            this.nodeCollectedIcon.active = Park.getInstance().isCollected(this.parkData.ownerRoleId);
        }
    }

    /**
     * 更新玩家信息
     */
    private updatePlayerInfo(): void {
        PlayerInfoUtils.updateHead(this.nodeHead, this.playerData);
        this.lbtPlayerName.string = this.playerData.name;
    }

    /**
     * 更新车位信息
     * @param spaceId 车位id
     */
    private updateSpaceInfo(spaceId?: number): void {
        for (const data of this.spaceData) {
            if (spaceId && data.id !== spaceId) {
                continue;
            }

            data.stateData = this.parkData.plotSpaceInfos.find((v) => v.spaceId === data.id);
            data.playerData = this.playerData;
            data.setData = this.parkData.plotManageInfo;
            data.isOther = this.isOther;
            data.compItem.changeState(data.stateData ? ParkSpaceState.Parking : ParkSpaceState.Free);
        }
    }

    /**
     * 更新设置信息
     */
    private updateSetInfo(): void {
        if (this.parkData.plotManageInfo.isOpen) {
            this.rtSetTips.string = TextUtils.format(i18n.park0015, this.parkData.plotManageInfo.managePercent + "");
        } else {
            this.rtSetTips.string = !this.isOther ? i18n.park0013 : i18n.park0014;
        }
    }

    /**
     * 更新展示战车信息
     */
    private updateShowTankInfo(): void {
        this.spineTank.node.opacity = this.parkData.showTankIds.length === 0 ? 0 : 255;
        if (this.parkData.showTankIds.length === 0) {
            this.spineTank.skeletonData = null;
        } else {
            const tankInfo = TBTank.getInstance().getDataById(this.parkData.showTankIds[0]);
            const tankRes = tankInfo.res.split("#")[tankInfo.res.split("#").length - 1];
            SpineUtils.setTank(this.spineTank, tankRes);
        }
    }

    /**
     * 更新列表信息-战车列表
     * @param tankId 战车id
     */
    private updateListInfo(tankId: number = -1): void {
        this.tankData = [];
        const parkData = Park.getInstance().getData();
        parkData.ownerTankSpaceInfos.forEach((v) => {
            this.tankData.push({
                id: v.tankId,
                stateData: v,
            });
        });
        this.tankData.sort((a, b) => {
            if (a.stateData.startTime !== b.stateData.startTime) {
                return a.stateData.startTime - b.stateData.startTime;
            }
            return a.id - b.id;
        });
        this.listTank.scrollView.stopAutoScroll();
        this.listTank.cleanList();
        this.listTank.setListData(this.tankData);
        if (tankId !== -1) {
            const jumpIndex = this.tankData.findIndex((v) => v.id === tankId);
            if (jumpIndex !== -1) {
                this.listTank.scrollTo(jumpIndex, 0);
                !this.isPlayingAni && !this.isUnfold && this.playTankMenuAni();
            }
        }
    }

    /**
     * 检测升级
     */
    private checkUpdate(): void {
        const { level, oldLevel, isIncreaseExp } = Park.getInstance().checkUpgrade();
        if (level === oldLevel && !isIncreaseExp) {
            return;
        }

        if (!this.isOther) {
            this.parkData = Park.getInstance().getData();
            const levelData = Park.getInstance().getLevelData(this.parkData.exp);
            this.lbtLevel.string = levelData.level + "";
            this.prgLevelExp.progress = levelData.isMax
                ? 1
                : Math.floor((levelData.exp / levelData.totalExp) * 1000) / 1000;
            this.lbtLevelExp.string = levelData.isMax ? i18n.bless0001 : `${levelData.exp}/${levelData.totalExp}`;
        }

        if (level !== oldLevel) {
            Park.getInstance().emit(ParkEvent.Upgrade);
            UI.getInstance().open("FloatParkUpgrade", { level, oldLevel });
        }
    }

    /**
     * 播放战车菜单动画
     */
    private playTankMenuAni(): void {
        cc.Tween.stopAllByTarget(this.nodeTankMenu);

        this.isPlayingAni = true;
        if (!this.isUnfold) {
            cc.tween(this.nodeTankMenu)
                .to(0.4, { x: -this.nodeTankMenu.parent.width / 2 }, { easing: cc.easing.sineOut })
                .call(() => {
                    this.nodeControlIcon.scaleX = -1;
                    this.isUnfold = !this.isUnfold;
                    this.isPlayingAni = false;
                })
                .start();
        } else {
            cc.tween(this.nodeTankMenu)
                .to(
                    0.4,
                    { x: -this.nodeTankMenu.parent.width / 2 - this.nodeTankMenu.width },
                    { easing: cc.easing.sineIn }
                )
                .call(() => {
                    this.nodeControlIcon.scaleX = 1;
                    this.isUnfold = !this.isUnfold;
                    this.isPlayingAni = false;
                })
                .start();
        }
    }

    /**
     * 打开个人信息
     */
    protected onClickOpenPlayerInfo(): void {
        if (!this.playerData) {
            return;
        }

        UI.getInstance().open("FloatOtherPlayerInfo", this.playerData.playerId);
    }

    /**
     * 收藏
     */
    protected onClickCollect(): void {
        if (!this.isOther) {
            return;
        }

        Park.getInstance().sendCollect(
            this.parkData.ownerRoleId,
            Park.getInstance().isCollected(this.parkData.ownerRoleId) ? CollectType.CancelCollect : CollectType.Collect
        );
    }

    /**
     * 控制战车菜单
     */
    protected onClickControlTankMenu(): void {
        if (this.isPlayingAni) {
            return;
        }

        this.playTankMenuAni();
    }

    /**
     * 打开停车场信息
     */
    protected onClickOpenParkInfo(): void {
        if (this.isOther) {
            return;
        }

        UI.getInstance().open("PopupParkInfo");
    }

    /**
     * 打开设置
     */
    protected onClickOpenSet(): void {
        if (this.isOther) {
            return;
        }

        UI.getInstance().open("PopupParkSet");
    }

    /**
     * 打开展示战车
     */
    protected onClickOpenShowTank(): void {
        if (this.isOther) {
            return;
        }

        UI.getInstance().open("PopupParkShowTank");
    }

    /**
     * 选中菜单项
     * @param event 事件
     * @param index 菜单项index
     */
    protected onClickSelectMenuItem(event: cc.Event.EventTouch, index: number): void {
        UI.getInstance().closeToWindow(this.node.name);
        UI.getInstance().open(MENU_ITEM_DATA[index]);
    }

    /**
     * 商店
     */
    protected onClickShop(): void {
        UI.getInstance().open("PopupShop", {
            defaultType: EnumShopType.ParkShop,
            types: [
                {
                    type: EnumShopType.PVPShop,
                },
                {
                    type: EnumShopType.ParkShop,
                },
                {
                    type: EnumShopType.UnionShop,
                },
            ],
        });
    }

    /**
     * 跳转-我的停车场
     */
    protected onClickJumpMyPark(): void {
        if (!this.isOther) {
            return;
        }

        this.isOther = false;
        this.parkData = Park.getInstance().getData();
        this.playerData = Player.getInstance().getInfo();
        this.updatePark(true);
        this.checkUpdate();
    }

    /**
     * 关闭界面
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
