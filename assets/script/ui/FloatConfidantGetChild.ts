/*
 * @Author: chenx
 * @Date: 2024-10-09 16:41:45
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-09 17:31:22
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TBAttribute from "../data/parser/TBAttribute";
import TBPrincessChild from "../data/parser/TBPrincessChild";
import ImageUtils from "../utils/ImageUtils";

const { ccclass, property } = cc._decorator;

/**
 * 知己-获得王储
 */
@ccclass
export default class FloatConfidantGetChild extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null; // 标题
    @property(sp.Skeleton)
    spineTitleBg: sp.Skeleton = null; // 标题bg
    @property(cc.Node)
    nodeCloseTips: cc.Node = null; // 关闭提示

    @property(cc.Sprite)
    spQualityBg: cc.Sprite = null; // 品质bg
    @property(cc.Sprite)
    spIcon: cc.Sprite = null; // 王储icon
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    nodeContent: cc.Node = null; // 属性content
    @property(cc.Node)
    nodeItem: cc.Node = null; // 属性item

    childId: number = -1; // 王储id

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/result/spResultTitle1",
            },
        ];
    }

    protected onLoad(): void {
        this.nodeItem.parent = null;

        this.childId = this.args;
        this.initInfo();
    }

    protected onDestroy(): void {
        this.nodeItem.destroy();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        ImageUtils.setItemQuality(this.spQualityBg, this.childId);
        ImageUtils.setItemIcon(this.spIcon, this.childId);
        const childInfo = TBPrincessChild.getInstance().getDataById(this.childId);
        this.lbtName.string = childInfo.name;
        childInfo.attribute.forEach(([attrId, init, step]) => {
            const nodeItem = Loader.getInstance().instantiate(this.nodeItem);
            this.nodeContent.addChild(nodeItem);

            const attrData = TBAttribute.getInstance().formatAttribute([attrId, init + step * (1 - 1)]);
            nodeItem.child("lbtAttr").label(`${attrData.name}+${attrData.value}`);
        });

        this.spineTitleBg.setAnimation(0, "wait", false);
        this.spineTitleBg.addAnimation(0, "wait2", true);

        cc.tween(this.nodeCloseTips)
            .repeatForever(
                cc
                    .tween()
                    .to(1, { opacity: 0 }, { easing: cc.easing.sineIn })
                    .to(1, { opacity: 255 }, { easing: cc.easing.sineOut })
            )
            .start();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
