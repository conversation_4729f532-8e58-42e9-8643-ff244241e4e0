/*
 * @Author: zhangwj
 * @Date: 2023-8-12 15:03:23
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-15 16:39:33
 */

import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { EnumShopType } from "../data/base/BaseShop";
import { GAME_SWITCH_ID } from "../data/parser/TBGameSwitch";
import TBUnionLevel from "../data/parser/TBUnionLevel";
import GameSwitch from "../game/GameSwitch";
import Union from "../game/Union";
import GameSwitchUtils from "../utils/GameSwitchUtils";
import ImageUtils from "../utils/ImageUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class UIUnion extends I18nComponent {
    @property(cc.Node)
    flag: cc.Node = null;
    @property(cc.Node)
    lbtName: cc.Node = null;
    @property(cc.Node)
    level: cc.Node = null;
    @property(cc.Node)
    progress: cc.Node = null;
    @property(cc.Node)
    unionNode: cc.Node = null; // 公会
    @property(cc.Node)
    shopNode: cc.Node = null; // 商店
    @property(cc.Node)
    challengeNode: cc.Node = null; // 挑战
    @property(cc.Node)
    giftNode: cc.Node = null; // 团购
    @property(cc.Node)
    taskNode: cc.Node = null; // 任务

    protected onLoad(): void {
        this.updateUI();

        this.unionNode.child("icon").spriteAsync("texture/systemTitle/iconSystemNameDT");
        this.shopNode.child("icon").spriteAsync("texture/systemTitle/iconSystemNameSD");
        this.challengeNode.child("icon").spriteAsync("texture/systemTitle/iconSystemNameGHHD");
        this.giftNode.child("icon").spriteAsync("texture/systemTitle/iconSystemNameYJ");
        this.taskNode.child("icon").spriteAsync("texture/systemTitle/iconSystemNameRW");
    }

    private updateUI(): void {
        const unionInfo = Union.getInstance().getInfo();
        ImageUtils.setUnionFlag(this.flag, unionInfo.flag);
        this.lbtName.label(unionInfo.name);
        const unionLevel = TBUnionLevel.getInstance().getLevelByExp(unionInfo.exp);
        this.level.label("Lv." + unionLevel);
        const curLevelData = TBUnionLevel.getInstance().getDataById(unionLevel);
        const preLevelData = TBUnionLevel.getInstance().getDataById(unionLevel - 1);
        const preExp = preLevelData ? preLevelData.exp : 0;
        this.progress.progress((unionInfo.exp - preExp) / (curLevelData.exp - preExp));
        const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION_BOSS);
        this.challengeNode.color = !result ? "#BEBEBE" : "FFFFFF";
    }

    protected onClickHome(): void {
        const spine = this.unionNode.getComponent(sp.Skeleton);
        spine.clearTracks();
        spine.setAnimation(0, "click", false);
        spine.setCompleteListener(() => {
            spine.setCompleteListener(null);
            spine.setAnimation(0, "wait", true);
            UI.getInstance().open("UIUnionHall");
        });
    }

    protected onClickTask(): void {
        const spine = this.taskNode.getComponent(sp.Skeleton);
        spine.clearTracks();
        spine.setAnimation(0, "click", false);
        spine.setCompleteListener(() => {
            spine.setCompleteListener(null);
            spine.setAnimation(0, "wait", true);
            UI.getInstance().open("PopupUnionTask");
        });
    }

    protected onClickBoss(): void {
        const isUnlock = GameSwitchUtils.check(this.challengeNode);
        if (!isUnlock) {
            return;
        }

        const spine = this.challengeNode.getComponent(sp.Skeleton);
        spine.clearTracks();
        spine.setAnimation(0, "click", false);
        spine.setCompleteListener(() => {
            spine.setCompleteListener(null);
            spine.setAnimation(0, "wait", true);
            UI.getInstance().open("PopupActivityUnion");
        });
    }

    protected onClickShop(): void {
        const spine = this.shopNode.getComponent(sp.Skeleton);
        spine.clearTracks();
        spine.setAnimation(0, "click", false);
        spine.setCompleteListener(() => {
            spine.setCompleteListener(null);
            spine.setAnimation(0, "wait", true);
            UI.getInstance().open("PopupShop", {
                defaultType: EnumShopType.UnionShop,
                types: [
                    {
                        type: EnumShopType.PVPShop,
                    },
                    {
                        type: EnumShopType.ParkShop,
                    },
                    {
                        type: EnumShopType.UnionShop,
                    },
                ],
            });
        });
    }

    protected onClickTreasure(): void {
        const spine = this.giftNode.getComponent(sp.Skeleton);
        spine.clearTracks();
        spine.setAnimation(0, "click", false);
        spine.setCompleteListener(() => {
            spine.setCompleteListener(null);
            spine.setAnimation(0, "wait", true);
            UI.getInstance().open("PopupUnionTreasureShop");
        });
    }

    /**
     * 排行榜
     */
    protected onClickUnionRank(): void {
        UI.getInstance().open("PopupUnionRank");
    }

    /**
     * 互助
     */
    protected onClickHelp(): void {
        UI.getInstance().open("PopupUnionHelp");
    }

    /**
     * 关闭UI
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
