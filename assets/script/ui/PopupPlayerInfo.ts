/*
 * @Author: JackyF<PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-16 10:47:19
 */
import Audio from "../../nsn/audio/Audio";
import Channel from "../../nsn/config/Channel";
import Version from "../../nsn/config/Version";
import Language from "../../nsn/core/Language";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import Platform from "../../nsn/platform/Platform";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import Tips from "../../nsn/util/Tips";
import {
    LeadSkinMagicalRet,
    PetMagicalRet,
    PlayerInfoReplaceRet,
    PlayerRenameRet,
    TankMagicalRet,
    WeaponMagicalRet,
    WingsMagicalRet,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { LanguageType } from "../config/LanguageConfig";
import { GameProtocol } from "../core/GameProtocol";
import Login from "../core/Login";
import { EnumTaskDetailType } from "../data/base/BaseTaskDetail";
import { GAME_SWITCH_ID } from "../data/parser/TBGameSwitch";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import TBPet from "../data/parser/TBPet";
import TBPopup from "../data/parser/TBPopup";
import TBTank from "../data/parser/TBTank";
import TBWeapon from "../data/parser/TBWeapon";
import TBWing from "../data/parser/TBWing";
import DebugEntry from "../debug/DebugEntry";
import Bulletin from "../game/Bulletin";
import CombatScore from "../game/CombatScore";
import GameSwitch from "../game/GameSwitch";
import LeadSkin from "../game/LeadSkin";
import Pet from "../game/Pet";
import Player from "../game/Player";
import Recharge from "../game/Recharge";
import Tank from "../game/Tank";
import Task from "../game/Task";
import Union from "../game/Union";
import Weapon from "../game/Weapon";
import Wing from "../game/Wing";
import NsnHelp, { NsnHelpEntranceId } from "../sdk/NsnHelp";
import CombatScoreUtils from "../utils/CombatScoreUtils";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";
import SceneUtils from "../utils/SceneUtils";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

enum UIType {
    UserInfo,
    Setting,
}

const TAB_ENUM = [UIType.UserInfo, UIType.Setting];
const LANGUAGE = [LanguageType.Zhs, LanguageType.Zht, LanguageType.En];

@ccclass
export default class PopupPlayerInfo extends I18nComponent {
    @property([cc.Node])
    btns: cc.Node[] = [];
    @property(cc.Node)
    content1: cc.Node = null;
    @property(cc.Node)
    tank: cc.Node = null;
    @property(cc.Node)
    nodeLead: cc.Node = null; // 主角
    @property(sp.Skeleton)
    spineLead: sp.Skeleton = null; // 主角
    @property(sp.Skeleton)
    spineWeapon: sp.Skeleton = null; // 武器
    @property(sp.Skeleton)
    spineWing: sp.Skeleton = null; // 背饰
    @property(cc.Node)
    pet: cc.Node = null;
    @property(cc.Node)
    btnMail: cc.Node = null;
    @property(cc.Node)
    btnBulletin: cc.Node = null;
    @property(cc.Node)
    btnVipCustomerService: cc.Node = null;
    @property(cc.Node)
    nodeHead: cc.Node = null;
    @property(cc.Node)
    playerName: cc.Node = null;
    @property(cc.Node)
    playerId: cc.Node = null;
    @property(cc.Node)
    titleIcon: cc.Node = null;
    @property(cc.Node)
    serverName: cc.Node = null;
    @property(cc.Node)
    powerIcon: cc.Node = null;
    @property(cc.Node)
    unionName: cc.Node = null;
    @property(cc.Node)
    combat: cc.Node = null; // 战斗力

    @property(cc.Node)
    content2: cc.Node = null;
    @property(cc.Slider)
    musicSlider: cc.Slider = null;
    @property(cc.ProgressBar)
    musicProgressBar: cc.ProgressBar = null;
    @property(cc.Slider)
    effectSlider: cc.Slider = null;
    @property(cc.ProgressBar)
    effectProgressBar: cc.ProgressBar = null;
    @property([cc.Node])
    languages: cc.Node[] = [];
    @property(cc.Node)
    btnService: cc.Node = null;
    @property(cc.Node)
    btnAccountBind: cc.Node = null;
    @property(cc.Node)
    btnSocial: cc.Node = null;
    @property(cc.Node)
    btnWxAdd: cc.Node = null;
    @property(cc.Node)
    version: cc.Node = null;

    private curTab: UIType = UIType.UserInfo;
    private tabType: number = -1; // 切换语言页签类型

    protected onLoad(): void {
        for (let i = 0; i < this.btns.length; i++) {
            this.btns[i].button(TAB_ENUM[i]);
        }
        this.updateUI();
        this.updateSetting();
    }

    protected onDestroy(): void {
        Audio.getInstance().setMusicVolume(this.musicSlider.progress);
        Audio.getInstance().setEffectVolume(this.effectSlider.progress);
    }

    protected registerHandler(): void {
        Player.getInstance().on(
            PlayerRenameRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        LeadSkin.getInstance().on(
            LeadSkinMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        // 玩家-信息替换
        Player.getInstance().on(
            PlayerInfoReplaceRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        Tank.getInstance().on(
            TankMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        Weapon.getInstance().on(
            WeaponMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        Wing.getInstance().on(
            WingsMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        Pet.getInstance().on(
            PetMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
    }

    private updateUI(): void {
        switch (this.curTab) {
            case UIType.UserInfo:
                this.content1.active = true;
                this.content2.active = false;
                this.updatePlayerInfo();
                break;
            case UIType.Setting:
                this.content1.active = false;
                this.content2.active = true;
                break;
            default:
                break;
        }
    }

    private updatePlayerInfo(): void {
        const info = Player.getInstance().getInfo();
        const severInfo = Player.getInstance().getServerInfo();
        // 个人信息
        PlayerInfoUtils.updateHead(this.nodeHead, info);
        this.playerName.label(info.name);
        this.playerId.label("ID：" + info.gameId);
        PlayerInfoUtils.updateTitle(this.titleIcon, info);
        this.serverName.label(severInfo.name); // 服务器名称
        PlayerInfoUtils.updatePower(this.powerIcon, info);
        const unionInfo = Union.getInstance().getInfo();
        this.unionName.label(unionInfo ? unionInfo.name : i18n.union0062);
        CombatScoreUtils.update(this.combat, CombatScore.getInstance().getScore());
        this.btnMail.spriteAsync("texture/systemEntry/iconEntranceYJ");
        this.btnBulletin.spriteAsync("texture/systemEntry/iconEntranceGG");
        const supportVipCustomerService = Channel.getInstance().getConfig().supportVipCustomerService();
        const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.VIP_CUSTOMER_SERVICE);
        const isReachMinRecharge =
            Recharge.getInstance().getSumPrice() >=
            Math.min(
                ...TBPopup.getInstance()
                    .getDataListByUIName("PopupVipCustomerService")
                    .map((v) => v.value[0])
            );
        this.btnVipCustomerService.active = supportVipCustomerService && result && isReachMinRecharge;

        // 个性化
        const skinId = LeadSkin.getInstance().getMagicalId();
        const skinData = TBLeadSkin.getInstance().getDataById(skinId);
        SpineUtils.setLeadWithoutAniName(this.spineLead, skinData.res, () => {
            // @ts-ignore
            const attachUtil = this.spineLead.attachUtil;
            attachUtil.destroyAllAttachedNodes();
            attachUtil.generateAllAttachedNodes();

            this.spineLead.setAnimation(0, "attackWait", true);
        });

        // 穿戴神器
        if (GameSwitch.getInstance().check(GAME_SWITCH_ID.WEAPON).result) {
            const weaponId = Weapon.getInstance().getMagicalId();
            const weaponData = TBWeapon.getInstance().getDataById(weaponId);
            SpineUtils.setWeaponWithoutAniName(this.spineWeapon, weaponData.res, () => {
                // @ts-ignore
                const attachUtil = this.spineWeapon.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                const boneHand = this.spineWeapon.findBone("hand");
                this.spineWeapon.node.setPosition(cc.v2(-boneHand.x / boneHand.scaleX, -boneHand.y / boneHand.scaleY));

                this.spineWeapon.setAnimation(0, "attackWait", true);
            });
        } else {
            const weaponData = TBWeapon.getInstance().getDataById(skinData.initialWeapon);
            SpineUtils.setWeaponWithoutAniName(this.spineWeapon, weaponData.res, () => {
                // @ts-ignore
                const attachUtil = this.spineWeapon.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                const boneHand = this.spineWeapon.findBone("hand");
                this.spineWeapon.node.setPosition(cc.v2(-boneHand.x / boneHand.scaleX, -boneHand.y / boneHand.scaleY));

                this.spineWeapon.setAnimation(0, "attackWait", true);
            });
        }

        // 穿戴翅膀
        if (GameSwitch.getInstance().check(GAME_SWITCH_ID.WING).result) {
            const wingId = Wing.getInstance().getMagicalId();
            if (wingId !== 0) {
                const wingData = TBWing.getInstance().getDataById(wingId);
                SpineUtils.setWingWithoutAniName(this.spineWing, wingData.res, () => {
                    this.spineWing.setAnimation(0, "attackWait", true);
                });
            }
        }

        if (GameSwitch.getInstance().check(GAME_SWITCH_ID.TANK).result) {
            const tankId = Tank.getInstance().getMagicalId();
            const tankData = TBTank.getInstance().getDataById(tankId);
            SpineUtils.setTank(this.tank, tankData.res);
            this.nodeLead.x = -202;
        } else {
            this.nodeLead.x = 0;
        }

        if (GameSwitch.getInstance().check(GAME_SWITCH_ID.PET).result && Pet.getInstance().getHistoryPetIds().length) {
            const petId = Pet.getInstance().getMagicalId();
            const petData = TBPet.getInstance().getDataById(petId);
            SpineUtils.setPet(this.pet, petData.res);
        }
    }

    /**
     * 获取挂点-武器层
     * @param name 名称
     * @returns
     */
    public getAttached(name: string): cc.Node {
        // @ts-ignore
        const attachUtil = this.leadByWeapon.attachUtil;
        const attachedNode: cc.Node[] = attachUtil.getAttachedNodes(name);

        return attachedNode[0];
    }

    /**
     * 初始化设置
     */
    public updateSetting(): void {
        this.version.label(i18n.setting0008 + ": " + Version.getInstance().getGameVersion());
        this.musicSlider.progress = Audio.getInstance().getMusicVolume();
        this.musicProgressBar.progress = Audio.getInstance().getMusicVolume();
        this.effectSlider.progress = Audio.getInstance().getEffectVolume();
        this.effectProgressBar.progress = Audio.getInstance().getEffectVolume();
        const language = Language.getInstance().readLanguage() || Language.getInstance().getLanguage();
        const index = LANGUAGE.indexOf(language);
        this.languages.forEach((v, i) => v.button(i));
        this.tabType = index;
        this.updateTabState();
        this.btnService.active = Channel.getInstance().getConfig().supportNsnHelp();
        this.btnAccountBind.active = !!Channel.getInstance().getConfig().getBindType().length;
        this.btnSocial.active = !!Channel.getInstance().getConfig().getSocialType().length;
        this.btnWxAdd.active = Platform.getInstance().isWechatMinigame();
    }

    /**
     * 属性显示
     */
    protected onClickAttrShow(): void {
        UI.getInstance().open("PopupAttrShowTotal");
    }

    /**
     * 点击个性化
     */
    protected onClickPersonal(): void {
        const { result, msg } = GameSwitch.getInstance().check(GAME_SWITCH_ID.LEAD_PERSONALIZED);
        if (!result) {
            Tips.getInstance().show(msg);
            return;
        }
        UI.getInstance().open("PopupPersonalized");
    }

    protected onClickVipCustomerService(): void {
        UI.getInstance().open("PopupVipCustomerService");
    }

    protected onClickPlayerRename(): void {
        Task.getInstance().setTaskProgress(EnumTaskDetailType.ClickRenameTask, 1);
        UI.getInstance().open("FloatPlayerRename");
    }

    protected onClickCopyID(): void {
        const id = Player.getInstance().getGameId();
        Platform.getInstance().copyToClipboard(id + "");
    }

    /**
     *震动
     */
    protected onClickShake(): void {}

    /**
     * 精简模式
     */
    protected onClickSimpleMode(): void {}

    /**
     * 邮件
     */
    protected onClickMail(): void {
        UI.getInstance().open("PopupMail");
    }

    /**
     * 公告
     */
    public onClickBulletin(): void {
        UI.getInstance().showLoading(i18n.bulletin0003);
        Bulletin.getInstance().request(() => {
            UI.getInstance().hideLoading();
            const data = Bulletin.getInstance().getBulletinInfo();
            if (!data || !data.length) {
                Tips.getInstance().show(i18n.bulletin0001);
                return;
            }
            UI.getInstance().open("PopupBulletin");
        });
    }

    /**
     * 更新页签状态
     */
    private updateTabState(): void {
        this.languages.forEach((v) => {
            v.child("select").active = CocosExt.getButtonData(v) === this.tabType;
        });
    }

    protected onMusicSliderChanged(s: cc.Slider): void {
        this.musicProgressBar.progress = s.progress;
        Audio.getInstance().setMusicVolume(s.progress, false);
    }

    protected onEffectSliderChanged(s: cc.Slider): void {
        this.effectProgressBar.progress = s.progress;
        Audio.getInstance().setEffectVolume(s.progress, false);
    }

    protected onLanguageToggleChanged(event: cc.Event.EventTouch, type: number): void {
        if (this.tabType === type) {
            return;
        }
        this.tabType = type;
        this.updateTabState();
        const language = LANGUAGE[type];
        const curLanguage = Language.getInstance().readLanguage() || Language.getInstance().getLanguage();
        if (language !== curLanguage) {
            Language.getInstance().saveLanguage(language);
            Tips.getInstance().show(i18n.setting0009);
        }
    }

    protected onClickExit(): void {
        Login.getInstance().setAutoLogin(false);
        SceneUtils.exitInGameScene();
    }

    protected onClickDelete(): void {
        UI.getInstance().open("PopupDeleteAccount");
    }

    protected onClickCDK(): void {
        UI.getInstance().open("PopupExchangeCode");
    }

    protected onClickService(): void {
        NsnHelp.getInstance().show(NsnHelpEntranceId.Settings);
    }

    protected onClickAccountBind(): void {
        UI.getInstance().open("FloatAccountBind");
    }

    protected onClickSocial(): void {
        UI.getInstance().open("PopupSocial");
    }

    protected onClickWxAdd(): void {
        UI.getInstance().open("PopupWxFavorite");
    }

    protected onClickUserProtocol(): void {
        GameProtocol.openUserProtocol();
    }

    protected onClickPrivateProtocol(): void {
        GameProtocol.openSensitiveProtocol();
    }

    protected onClickDebug(): void {
        this.version.getComponent(DebugEntry).increaseSecretTouchCount1();
    }

    protected onClickTab(sender: cc.Event.EventTouch, tab: UIType): void {
        if (this.curTab === tab) {
            return;
        }
        this.curTab = tab;
        this.updateUI();
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
