/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-18 15:59:36
 */

import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import i18n from "../config/i18n/I18n";
import TBAttribute from "../data/parser/TBAttribute";
import TBItem from "../data/parser/TBItem";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import TBLeadSkinLevel from "../data/parser/TBLeadSkinLevel";
import TBLeadSkinStar from "../data/parser/TBLeadSkinStar";
import TBSkill, { EnumSkillParamLevelType } from "../data/parser/TBSkill";
import Skill from "../game/Skill";

import ColorUtils from "../utils/ColorUtils";
import ImageUtils from "../utils/ImageUtils";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatSkinPreviewDetail extends I18nComponent {
    @property(cc.Node)
    quality: cc.Node = null;
    @property(cc.Node)
    nameText: cc.Node = null;
    @property(cc.Node)
    attrText: cc.Node = null; // 拥有/上阵加成
    @property(cc.Node)
    fateNode: cc.Node = null; // 命运加成
    @property(cc.Node)
    spine: cc.Node = null;
    @property(cc.Node)
    starts: cc.Node = null;
    @property(cc.Node)
    desc: cc.Node = null;
    @property([cc.Node])
    skills: cc.Node[] = [];

    @property(cc.Node)
    previewNode: cc.Node = null; // 预览
    @property(cc.Node)
    blockInput: cc.Node = null;
    @property(ListView)
    list: ListView = null;

    private skinId: number = -1;

    protected onLoad(): void {
        this.skinId = this.args;
        this.updateFateAttr();
        this.updateUI();
    }

    private updateFateAttr(): void {
        const last = TBLeadSkinStar.getInstance().getLast();
        if (last.attribute.length > 1) {
            const fateAttr = TBAttribute.getInstance().getFateAttrs(last.attribute);
            if (fateAttr) {
                this.fateNode.active = true;
                const { name, value } = TBAttribute.getInstance().formatAttribute(fateAttr);
                this.fateNode.child("text").label(name + "+" + value);
            } else {
                this.fateNode.active = false;
            }
        } else {
            this.fateNode.active = false;
        }
    }

    private updateUI(): void {
        const data = TBLeadSkin.getInstance().getDataById(this.skinId);
        const itemData = TBItem.getInstance().getDataById(this.skinId);
        ImageUtils.setQuality6(this.quality, data.quality);
        const maxLevel = TBLeadSkinLevel.getInstance().getDataByQuality(data.quality).length;
        const starData = TBLeadSkinStar.getInstance().getDataByQuality(data.quality);
        this.nameText.label(TextUtils.format(i18n.partnerDrawCard0004, maxLevel, data.name));
        const attribute = starData[0].attribute.find((e) => e[1] !== 0);
        const { name, value } = TBAttribute.getInstance().formatAttribute(attribute);
        this.attrText.label(TextUtils.format(i18n.common0087, name + value));
        ImageUtils.setStarsIcon(this.starts, starData[starData.length - 1].star);
        this.desc.label(itemData.desc);

        const spine = this.spine.getComponent(sp.Skeleton);
        SpineUtils.setLeadWithoutAniName(spine, data.res, () => {
            spine.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                if (trackEntry.animation.name === "exclusivewait") {
                    spine.setAnimation(0, "wait", false);
                    spine.addAnimation(0, "wait", false);
                    spine.addAnimation(0, "wait", false);
                    spine.addAnimation(0, "wait", false);
                    spine.addAnimation(0, "wait", false);
                    spine.addAnimation(0, "exclusivewait", false);
                }
            });
            spine.setAnimation(0, "exclusivewait", false);
        });

        for (let i = 0; i < this.skills.length; i++) {
            const skillId = data.skillId[i];
            const node = this.skills[i];
            if (skillId) {
                node.active = true;
                const skillIcon = node.child("skillIcon");
                const skillDesc = node.child("skillDesc");
                const skillName = node.child("skillName");
                const skillTime = node.child("skillTime");
                const curValue = Skill.getInstance().getSkillValueById(skillId, {
                    [EnumSkillParamLevelType.SkinStar]: starData[starData.length - 1].star,
                });
                const skillData = TBSkill.getInstance().getDataById(skillId);
                ImageUtils.setLeadSkillIcon(skillIcon, skillData.res);
                skillDesc.richText(
                    TextUtils.format(
                        ColorUtils.replaceTextColors(skillData.desc, [
                            "#615A5B",
                            "#688A28",
                            "#615A5B",
                            "#688A28",
                            "#615A5B",
                            "#688A28",
                            "#615A5B",
                            "#688A28",
                        ]),
                        curValue
                    )
                );
                skillName.label(skillData.name);
                skillTime.label(TextUtils.format(i18n.common0062, skillData.cd));
            } else {
                node.active = false;
            }
        }
    }

    protected updateStartList(): void {
        const first = TBLeadSkinStar.getInstance().getFirst();
        const last = TBLeadSkinStar.getInstance().getLast();
        const data: { skinId: number; star: number }[] = [];
        for (let i = first.star; i <= last.star; i++) {
            data.push({ skinId: this.skinId, star: i });
        }
        this.list.setListData(data);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }

    protected onClickFate(): void {
        UI.getInstance().open("FloatSkinFatePreview", this.skinId);
    }

    protected onClickPreview(): void {
        this.previewNode.active = !this.previewNode.active;
        this.blockInput.active = !this.blockInput.active;
        if (this.previewNode.active) {
            this.updateStartList();
        }
    }
}
