/*
 * @Author: chenx
 * @Date: 2025-01-22 16:38:57
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:51:31
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { IItemInfo } from "../../protobuf/proto";
import { DungeonType } from "../game/Combat";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";

/**
 * 界面参数
 */
interface IFloatDungeonEndUnionArgs {
    type: DungeonType; // 类型
    passTime: number; // 通关时间
    damage: number; // 伤害
    rewardCount: number; // 奖励数量
    rewardCount2: number; // 奖励数量
    rewardData: IItemInfo[]; // 奖励数据
}

const { ccclass, property } = cc._decorator;

/**
 * 公会副本-结束
 */
@ccclass
export default class FloatDungeonEndUnion extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null; // 标题
    @property(cc.Label)
    lbtDamage: cc.Label = null; // 伤害
    @property(cc.Label)
    lbtRewardCount: cc.Label = null; // 奖励数量
    @property(cc.Label)
    lbtRewardCount2: cc.Label = null; // 奖励数量
    @property(cc.Node)
    nodeReward: cc.Node = null; // 奖励

    @property(cc.Prefab)
    prefabRewardItem: cc.Prefab = null; // 奖励item

    private initData: IFloatDungeonEndUnionArgs = null; // 初始化数据

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/result/spResultTitle15",
            },
        ];
    }

    protected onLoad(): void {
        this.initData = this.args;
        this.initInfo();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        this.lbtDamage.string = `${NumberUtils.format(this.initData.damage)}（${NumberUtils.format(
            this.initData.damage / this.initData.passTime
        )}/S）`;
        this.lbtRewardCount.string = `x${this.initData.rewardCount}`;
        this.lbtRewardCount2.string = `x${this.initData.rewardCount2}`;
        ItemUtils.refreshView(this.nodeReward, this.prefabRewardItem, this.initData.rewardData);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        switch (this.initData.type) {
            case DungeonType.Union:
                UI.getInstance().closeToWindow("UIDungeonCombatUnion");
                UI.getInstance().close();
                UI.getInstance().open("PopupUnionBoss");
                break;
            default:
                break;
        }
    }
}
