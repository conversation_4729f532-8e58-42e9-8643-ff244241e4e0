/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-17 20:09:52
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import Tips from "../../nsn/util/Tips";
import { WingsCreateRet, WingsLineupRet, WingsUpgradeStarRet } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import { EnumWingType } from "../data/base/BaseWing";
import TBAttribute from "../data/parser/TBAttribute";
import { JumpType } from "../data/parser/TBJump";
import TBSkill, { EnumSkillParamLevelType } from "../data/parser/TBSkill";
import TBWing from "../data/parser/TBWing";
import TBWingStar from "../data/parser/TBWingStar";
import Bag from "../game/Bag";
import Skill from "../game/Skill";
import Wing, { WingEvent } from "../game/Wing";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import JumpUtils from "../utils/JumpUtils";
import SpineUtils from "../utils/SpineUtils";
import TweenUtil from "../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupWingSkin extends I18nComponent {
    @property(cc.Node)
    fateNode: cc.Node = null;
    @property(cc.Node)
    lbtName: cc.Node = null;
    @property(cc.Node)
    stars: cc.Node = null;
    @property(cc.Node)
    wingIcon: cc.Node = null;
    @property(cc.Node)
    quality: cc.Node = null;
    @property(cc.Node)
    btnFight: cc.Node = null;

    @property(cc.Node)
    skillIcon: cc.Node = null;
    @property(cc.Node)
    skillName: cc.Node = null;
    @property(cc.Node)
    skillDesc: cc.Node = null;

    @property(cc.Node)
    attrNode: cc.Node = null;

    @property(ListView)
    list: ListView = null;

    @property(cc.Node)
    upgradeCost: cc.Node = null;
    @property(cc.Node)
    costIcon: cc.Node = null;
    @property(cc.Node)
    costCount: cc.Node = null;
    @property(cc.Node)
    btnUpgrade: cc.Node = null;
    @property(cc.Node)
    btnUpgradeText: cc.Node = null;
    @property(cc.Node)
    btnLight: cc.Node = null;

    @property(cc.Node)
    btnGoto: cc.Node = null;

    private wingId: number = 0;
    private wingList: number[] = [];

    protected onLoad(): void {
        this.updateUI();
    }

    protected registerHandler(): void {
        Wing.getInstance().on(
            WingEvent.SelectedWing,
            (wingId: number) => {
                this.wingId = wingId;
                this.updateUI();
            },
            this
        );
        Wing.getInstance().on(
            [WingsCreateRet.prototype.clazzName, WingsLineupRet.prototype.clazzName],
            () => {
                this.updateUI();
            },
            this
        );
        Wing.getInstance().on(
            WingsUpgradeStarRet.prototype.clazzName,
            (data: WingsUpgradeStarRet) => {
                this.updateUI();
                UI.getInstance().open("FloatWingUpStar", data.wingInfo.wingId);
            },
            this
        );
    }

    private updateUI(): void {
        this.updateList();
        this.updateFateAttr();
        this.updateWing();
        this.updateSkill();
        this.updateAttr();
        this.updateBtns();
    }

    private updateList(): void {
        const wingIds = TBWing.getInstance()
            .getDataByType(EnumWingType.AdvancedWing)
            .map((v) => v.id);
        const teamId = Wing.getInstance().getTeamId();
        const curTeam = wingIds.includes(teamId) ? [teamId] : [];
        const own = wingIds
            .filter((v) => v !== teamId && !!Wing.getInstance().getDataById(v))
            .sort((a, b) => {
                const aData = TBWing.getInstance().getDataById(a);
                const bData = TBWing.getInstance().getDataById(b);
                return bData.quality - aData.quality;
            });
        const notOwn = wingIds
            .filter((v) => v !== teamId && !Wing.getInstance().getDataById(v))
            .sort((a, b) => {
                const aData = TBWing.getInstance().getDataById(a);
                const bData = TBWing.getInstance().getDataById(b);
                return bData.quality - aData.quality;
            });
        this.wingList = curTeam.concat(own).concat(notOwn);
        this.wingId = this.wingId || this.wingList[0];
        this.list.setListData(
            this.wingList.map((v) => {
                return {
                    wingId: v,
                    selected: v === this.wingId,
                };
            })
        );
    }

    private updateFateAttr(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        const info = Wing.getInstance().getDataById(this.wingId);
        const starData = TBWingStar.getInstance().getDataByQualityAndStar(data.quality, info ? info.star : 0);
        if (starData.attribute.length > 1) {
            const fateAttr = TBAttribute.getInstance().getFateAttrs(starData.attribute);
            if (fateAttr) {
                this.fateNode.active = true;
                const { name, value } = TBAttribute.getInstance().formatAttribute(fateAttr);
                this.fateNode.child("text").label(name + "+" + value);
            } else {
                this.fateNode.active = false;
            }
        } else {
            this.fateNode.active = false;
        }
    }

    private updateWing(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        const info = Wing.getInstance().getDataById(this.wingId);
        this.lbtName.label(data.name);
        ImageUtils.setQuality6(this.quality, data.quality);
        SpineUtils.setWing(this.wingIcon, data.res);
        const teamId = Wing.getInstance().getTeamId();
        const wingIds = Wing.getInstance().getWingIds();
        if (wingIds.includes(this.wingId)) {
            this.btnFight.active = true;
            this.btnFight.spriteAsync(
                "texture/syncUI/tank/" + (this.wingId === teamId ? "btnChariotGo2" : "btnChariotGo1")
            );
        } else {
            this.btnFight.active = false;
        }
        const stars = info ? info.star : 0;
        ImageUtils.setStarsIcon(this.stars, stars);
    }

    private updateSkill(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        const info = Wing.getInstance().getDataById(this.wingId);
        const skillData = TBSkill.getInstance().getDataById(data.skillId[0]);
        ImageUtils.setSkillIcon(this.skillIcon, skillData.id);
        this.skillName.label(skillData.name);
        const skillValues = Skill.getInstance().getSkillValueById(skillData.id, {
            [EnumSkillParamLevelType.WingStar]: info ? info.star : 0,
        });
        this.skillDesc.richText(TextUtils.format(skillData.desc, ...skillValues));
    }

    private updateAttr(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        const info = Wing.getInstance().getDataById(this.wingId);
        const nextStar = info ? info.star + 1 : 1;
        const curData = TBWingStar.getInstance().getDataByQualityAndStar(data.quality, info ? info.star : 0);
        const nextData = TBWingStar.getInstance().getDataByQualityAndStar(data.quality, nextStar);
        const title2 = this.attrNode.child("title2");
        const title3 = this.attrNode.child("title3");
        const values = this.attrNode.child("attr");
        const v1 = values.child("v1");
        const v2 = values.child("v2");
        const v3 = values.child("v3");
        const arrow = values.child("arrow");
        const { name, value } = TBAttribute.getInstance().formatAttribute(curData.attribute[0]);
        v1.label(name);
        v2.label(value);

        if (nextData) {
            title3.active = true;
            v3.active = true;
            arrow.active = true;
            title2.x = 0;
            v2.x = 0;

            title3.label(TextUtils.format(i18n.wing0007, nextStar));
            const { value: nextValue } = TBAttribute.getInstance().formatAttribute(nextData.attribute[0]);
            v3.label(nextValue);
        } else {
            title3.active = false;
            v3.active = false;
            arrow.active = false;
            title2.x = 290;
            v2.x = 290;
        }
    }

    private updateBtns(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        const info = Wing.getInstance().getDataById(this.wingId);
        if (info) {
            this.btnUpgrade.active = true;
            this.btnGoto.active = false;
            const nextData = TBWingStar.getInstance().getDataByQualityAndStar(data.quality, info.star + 1);
            if (!nextData) {
                this.upgradeCost.active = false;
                this.btnUpgrade.getComponent(GrayComp).gray = true;
                this.btnUpgrade.getComponent(cc.Button).interactable = false;
                this.btnUpgradeText.label(i18n.common0064);
                this.btnLight.active = false;
            } else {
                this.upgradeCost.active = true;
                this.btnUpgrade.getComponent(GrayComp).gray = false;
                this.btnUpgrade.getComponent(cc.Button).interactable = true;
                this.btnUpgradeText.label(i18n.common0069);
                ImageUtils.setItemIcon(this.costIcon, data.id);
                ItemUtils.refreshCount(this.costCount, data.id, nextData.upgradeCost);

                this.btnLight.active = Bag.getInstance().isEnough(data.id, nextData.upgradeCost);
                if (this.btnLight.active) {
                    this.btnLight.stopAllActions();
                    TweenUtil.breath(this.btnLight);
                }
            }
        } else {
            this.btnUpgrade.active = false;
            this.btnGoto.active = true;
        }
    }

    protected onClickUpgrade(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        const info = Wing.getInstance().getDataById(this.wingId);
        const nextData = TBWingStar.getInstance().getDataByQualityAndStar(data.quality, info.star + 1);
        if (!Bag.getInstance().isEnough(data.id, nextData.upgradeCost)) {
            UI.getInstance().open("FloatItemSource", data.id);
            return;
        }
        Wing.getInstance().sendWingsUpgradeStar(this.wingId);
    }

    protected onClickFight(): void {
        const wingIds = Wing.getInstance().getWingIds();
        if (!wingIds.includes(this.wingId)) {
            return;
        }
        const teamId = Wing.getInstance().getTeamId();
        if (this.wingId === teamId) {
            return;
        }
        Wing.getInstance().sendWingsLineup(this.wingId);
    }

    protected onClickPreview(): void {
        UI.getInstance().open("PopupWingStarPreview", this.wingId);
    }

    protected onClickGet(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        if (!data.jumpId) {
            Tips.getInstance().show(i18n.activity0019);
            return;
        }
        JumpUtils.jump(JumpType.System, data.jumpId);
    }

    protected onClickFate(): void {
        UI.getInstance().open("FloatWingFatePreview", this.wingId);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
