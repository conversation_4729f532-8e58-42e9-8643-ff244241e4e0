/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-03-13 15:26:36
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { LeadSkinIncrStarRet } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import TBAttribute from "../data/parser/TBAttribute";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import TBLeadSkinStar from "../data/parser/TBLeadSkinStar";
import TBSkill, { EnumSkillParamLevelType } from "../data/parser/TBSkill";
import Bag from "../game/Bag";
import LeadSkin from "../game/LeadSkin";
import Skill from "../game/Skill";
import AudioUtils from "../utils/AudioUtils";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import SpineUtils from "../utils/SpineUtils";
import TweenUtil from "../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupSkinInfo extends I18nComponent {
    @property(cc.Node)
    lbtName: cc.Node = null;
    @property(cc.Node)
    stars: cc.Node = null;
    @property(cc.Node)
    role: cc.Node = null;
    @property(cc.Node)
    quality: cc.Node = null;
    @property(cc.Node)
    fateNode: cc.Node = null;

    @property([cc.Node])
    skills: cc.Node[] = [];

    @property(ListView)
    list: ListView = null;

    @property(cc.Node)
    upgradeCost: cc.Node = null;
    @property(cc.Node)
    costIcon: cc.Node = null;
    @property(cc.Node)
    costCount: cc.Node = null;
    @property(cc.Node)
    btnUpgrade: cc.Node = null;
    @property(cc.Node)
    btnText: cc.Node = null;
    @property(cc.Node)
    btnLight: cc.Node = null;

    private skinId: number = 0;

    protected onLoad(): void {
        this.skinId = this.args;
        this.updateUI(true);
    }

    protected registerHandler(): void {
        LeadSkin.getInstance().on(
            LeadSkinIncrStarRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(playAudioEffect: boolean = false): void {
        this.updateFateAttr();
        this.updateRole(playAudioEffect);
        this.updateSkill();
        this.updateAttr();
        this.updateBtns();
    }

    private updateFateAttr(): void {
        const data = TBLeadSkin.getInstance().getDataById(this.skinId);
        const info = LeadSkin.getInstance().getDataById(this.skinId);
        const starData = TBLeadSkinStar.getInstance().getDataByQualityAndStar(data.quality, info ? info.star : 0);
        if (starData.attribute.length > 1) {
            const fateAttr = TBAttribute.getInstance().getFateAttrs(starData.attribute);
            if (fateAttr) {
                this.fateNode.active = true;
                const { name, value } = TBAttribute.getInstance().formatAttribute(fateAttr);
                this.fateNode.child("text").label(name + "+" + value);
            } else {
                this.fateNode.active = false;
            }
        } else {
            this.fateNode.active = false;
        }
    }

    private updateRole(playAudioEffect: boolean = false): void {
        const data = TBLeadSkin.getInstance().getDataById(this.skinId);
        SpineUtils.setLead(this.role, data.res);

        const info = LeadSkin.getInstance().getDataById(this.skinId);
        this.lbtName.label("Lv." + (info ? info.level : 1) + " " + data.name);
        ImageUtils.setStarsIcon(this.stars, info ? info.star : 0);
        ImageUtils.setQuality6(this.quality, data.quality);
        playAudioEffect && AudioUtils.playLeadEffect(this.skinId);
    }

    private updateSkill(): void {
        const data = TBLeadSkin.getInstance().getDataById(this.skinId);
        if (data.skillId.length === 1) {
            this.skills[1].active = false;
            const skillData = TBSkill.getInstance().getDataById(data.skillId[0]);
            ImageUtils.setSkillIcon(this.skills[0], skillData.id);
            this.skills[0].button(data.skillId[0]);
        } else if (data.skillId.length === 2) {
            this.skills[1].active = true;
            for (let i = 0; i < this.skills.length; i++) {
                const skillData = TBSkill.getInstance().getDataById(data.skillId[i]);
                ImageUtils.setSkillIcon(this.skills[i], skillData.id);
                this.skills[i].button(data.skillId[i]);
            }
        }
    }

    private updateAttr(): void {
        const first = TBLeadSkinStar.getInstance().getFirst();
        const last = TBLeadSkinStar.getInstance().getLast();
        const data: { skinId: number; star: number }[] = [];
        for (let i = first.star; i <= last.star; i++) {
            data.push({ skinId: this.skinId, star: i });
        }
        this.list.setListData(data);
        const info = LeadSkin.getInstance().getDataById(this.skinId);
        this.list.scrollTo(info.star - 1, 0.1);
    }

    private updateBtns(): void {
        const info = LeadSkin.getInstance().getDataById(this.skinId);
        const nextData = TBLeadSkinStar.getInstance().getDataByQualityAndStar(info.quality, info.star + 1);
        if (!nextData) {
            this.upgradeCost.active = false;
            this.btnUpgrade.getComponent(GrayComp).gray = true;
            this.btnUpgrade.getComponent(cc.Button).interactable = false;
            this.btnText.label(i18n.common0064);
            this.btnLight.active = false;
        } else {
            ImageUtils.setItemIcon(this.costIcon, this.skinId);
            ItemUtils.refreshCount(this.costCount, this.skinId, nextData.cost);

            this.btnLight.active = Bag.getInstance().isEnough(this.skinId, nextData.cost);
            if (this.btnLight.active) {
                this.btnLight.stopAllActions();
                TweenUtil.breath(this.btnLight);
            }
        }
    }

    protected onClickSkillDetail(sender: cc.Event.EventTouch, skillId: number): void {
        const pos = sender.target.convertToWorldSpaceAR(cc.v2());
        const skillData = TBSkill.getInstance().getDataById(skillId);
        const skin = LeadSkin.getInstance().getDataById(this.skinId);
        const skillValues = Skill.getInstance().getSkillValueById(skillData.id, {
            [EnumSkillParamLevelType.SkinLevel]: skin.level,
            [EnumSkillParamLevelType.SkinStar]: skin.star,
        });
        UI.getInstance().open("FloatSkillTips", {
            pos,
            name: skillData.name,
            desc: TextUtils.format(skillData.desc, ...skillValues),
            cd: skillData.cd,
        });
    }

    protected onClickUpgrade(): void {
        const info = LeadSkin.getInstance().getDataById(this.skinId);
        const nextData = TBLeadSkinStar.getInstance().getDataByQualityAndStar(info.quality, info.star + 1);
        if (!Bag.getInstance().isEnough(this.skinId, nextData.cost)) {
            UI.getInstance().open("FloatItemSource", this.skinId);
            return;
        }
        LeadSkin.getInstance().sendLeadSkinIncrStar(this.skinId);
    }

    protected onClickFate(): void {
        UI.getInstance().open("FloatSkinFatePreview", this.skinId);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
