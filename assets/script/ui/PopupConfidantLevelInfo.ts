/*
 * @Author: chenx
 * @Date: 2024-10-09 09:36:46
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-10 16:17:05
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import i18n from "../config/i18n/I18n";
import TBAttribute from "../data/parser/TBAttribute";
import TBPrincess from "../data/parser/TBPrincess";
import TBPrincessChild from "../data/parser/TBPrincessChild";
import Confidant from "../game/Confidant";

/**
 * 界面参数
 */
export interface IPopupConfidantLevelInfoArgs {
    confidantId: number; // 知己id
    childUuid: string; // 王储uuid
}

/**
 * 等级数据
 */
interface ILevelData {
    level: number; // 等级
    curLevel: number; // 当前等级
    attr: number[][]; // 属性
    isShowTopLine: boolean; // 是否显示顶部线
    isShowBottomLine: boolean; // 是否显示底部线
}

const { ccclass, property } = cc._decorator;

/**
 * 知己-知己等级信息
 */
@ccclass
export default class PopupConfidantLevelInfo extends I18nComponent {
    @property(ListView)
    listLevel: ListView = null; // 等级列表
    @property(cc.Node)
    nodeCloseTips: cc.Node = null; // 关闭提示

    initData: IPopupConfidantLevelInfoArgs = null; // 初始数据
    levelData: ILevelData[] = []; // 等级数据

    protected onLoad(): void {
        this.initData = this.args;
        this.initInfo();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        if (this.initData.confidantId !== -1) {
            const confidantInfo = TBPrincess.getInstance().getDataById(this.initData.confidantId);
            const confidantData = Confidant.getInstance().getConfidantData(this.initData.confidantId);
            for (let i = 1; i <= confidantInfo.levelLimit; i++) {
                this.levelData.push({
                    level: i,
                    curLevel: confidantData.level,
                    attr: confidantInfo.attribute,
                    isShowTopLine: i !== 1,
                    isShowBottomLine: i !== confidantInfo.levelLimit,
                });
            }
        }
        if (this.initData.childUuid !== "") {
            const childData = Confidant.getInstance().getChildData(this.initData.childUuid);
            const childInfo = TBPrincessChild.getInstance().getDataById(childData.childId);
            for (let i = 1; i <= childInfo.levelLimit; i++) {
                this.levelData.push({
                    level: i,
                    curLevel: childData.level,
                    attr: childInfo.attribute,
                    isShowTopLine: i !== 1,
                    isShowBottomLine: i !== childInfo.levelLimit,
                });
            }
        }
        this.listLevel.scrollView.stopAutoScroll();
        this.listLevel.setListData(this.levelData);
        const jumpIndex = this.levelData.findIndex((e) => e.level === e.curLevel);
        this.listLevel.scrollTo(jumpIndex, 0);

        cc.tween(this.nodeCloseTips)
            .repeatForever(
                cc
                    .tween()
                    .to(1, { opacity: 0 }, { easing: cc.easing.sineIn })
                    .to(1, { opacity: 255 }, { easing: cc.easing.sineOut })
            )
            .start();
    }

    /**
     * 监听渲染事件-等级列表
     * @param nodeItem 列表item
     * @param index 列表index
     */
    protected onRenderEvent(nodeItem: cc.Node, index: number): void {
        index = Math.abs(index);
        const levelData = this.levelData[index];
        nodeItem.child("spLine").active = levelData.isShowTopLine;
        nodeItem.child("spLine2").active = levelData.isShowBottomLine;
        nodeItem.child("spLine3").active = levelData.isShowTopLine && levelData.level <= levelData.curLevel;
        nodeItem.child("spLine4").active = levelData.isShowBottomLine && levelData.level < levelData.curLevel;
        nodeItem.child("spLevelBg2").active = levelData.level <= levelData.curLevel;
        nodeItem.child("lbtLevel").label(levelData.level + "");
        nodeItem.child("contentTitle").child("lbtLevel").label(TextUtils.format(i18n.dungeon0001, levelData.level));
        nodeItem.child("contentTitle").child("lbtEffectTag").active = levelData.level <= levelData.curLevel;
        let attrText = "";
        let attrText2 = "";
        levelData.attr.forEach(([attrId, init, step], i) => {
            const attrData = TBAttribute.getInstance().formatAttribute([attrId, init + step * (levelData.level - 1)]);
            if (i % 2 === 0) {
                attrText !== "" && (attrText += "\n");
                attrText += `${attrData.name}+${attrData.value}`;
            } else {
                attrText2 !== "" && (attrText2 += "\n");
                attrText2 += `${attrData.name}+${attrData.value}`;
            }
        });
        nodeItem.child("lbtAttr").label(attrText);
        nodeItem.child("lbtAttr2").label(attrText2);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
