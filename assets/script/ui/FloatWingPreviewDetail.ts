/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-17 20:00:38
 */

import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import i18n from "../config/i18n/I18n";
import TBAttribute from "../data/parser/TBAttribute";
import TBItem from "../data/parser/TBItem";
import TBSkill, { EnumSkillParamLevelType } from "../data/parser/TBSkill";
import TBWing from "../data/parser/TBWing";
import TBWingStar from "../data/parser/TBWingStar";

import Skill from "../game/Skill";
import ColorUtils from "../utils/ColorUtils";
import ImageUtils from "../utils/ImageUtils";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatWingPreviewDetail extends I18nComponent {
    @property(cc.Node)
    quality: cc.Node = null;
    @property(cc.Node)
    nameText: cc.Node = null;
    @property(cc.Node)
    attrText: cc.Node = null; // 拥有/上阵加成
    @property(cc.Node)
    fateNode: cc.Node = null;
    @property(cc.Node)
    spine: cc.Node = null;
    @property(cc.Node)
    starts: cc.Node = null;
    @property(cc.Node)
    desc: cc.Node = null;
    @property(cc.Node)
    skillIcon: cc.Node = null;
    @property(cc.Node)
    skillName: cc.Node = null;
    @property(cc.Node)
    skillDesc: cc.Node = null;

    @property(cc.Node)
    previewNode: cc.Node = null; // 预览
    @property(cc.Node)
    blockInput: cc.Node = null;
    @property(ListView)
    list: ListView = null;

    private wingId: number = -1;

    protected onLoad(): void {
        this.wingId = this.args;
        this.updateFateAttr();
        this.updateUI();
    }

    private updateFateAttr(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        const starData = TBWingStar.getInstance().getDataByQuality(data.quality);
        const lastStarData = starData[starData.length - 1];
        if (lastStarData.attribute.length > 1) {
            const fateAttr = TBAttribute.getInstance().getFateAttrs(lastStarData.attribute);
            if (fateAttr) {
                this.fateNode.active = true;
                const { name, value } = TBAttribute.getInstance().formatAttribute(fateAttr);
                this.fateNode.child("text").label(name + "+" + value);
            } else {
                this.fateNode.active = false;
            }
        } else {
            this.fateNode.active = false;
        }
    }

    private updateUI(): void {
        const data = TBWing.getInstance().getDataById(this.wingId);
        const itemData = TBItem.getInstance().getDataById(this.wingId);
        const starData = TBWingStar.getInstance().getDataByQuality(data.quality);
        const attribute = starData[0].attribute.find((e) => e[1] !== 0);
        const { name, value } = TBAttribute.getInstance().formatAttribute(attribute);
        ImageUtils.setQuality6(this.quality, data.quality);
        this.nameText.label(data.name);
        this.attrText.label(TextUtils.format(i18n.common0087, name + value));
        ImageUtils.setStarsIcon(this.starts, starData[starData.length - 1].star);
        this.desc.label(itemData.desc);
        SpineUtils.setWing(this.spine, data.res);
        const curValue = Skill.getInstance().getSkillValueById(data.skillId[0], {
            [EnumSkillParamLevelType.WingStar]: starData[starData.length - 1].star,
        });
        const skillData = TBSkill.getInstance().getDataById(data.skillId[0]);
        this.skillDesc.richText(
            TextUtils.format(
                ColorUtils.replaceTextColors(skillData.desc, [
                    "#615A5B",
                    "#688A28",
                    "#615A5B",
                    "#688A28",
                    "#615A5B",
                    "#688A28",
                    "#615A5B",
                    "#688A28",
                ]),
                curValue
            )
        );
        ImageUtils.setLeadSkillIcon(this.skillIcon, skillData.res);
        this.skillName.label(skillData.name);
    }

    protected updateStartList(): void {
        const first = TBWingStar.getInstance().getFirst();
        const last = TBWingStar.getInstance().getLast();
        const data: { wingId: number; star: number }[] = [];
        for (let i = first.star; i <= last.star; i++) {
            data.push({ wingId: this.wingId, star: i });
        }
        this.list.setListData(data);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }

    protected onClickPreview(): void {
        this.previewNode.active = !this.previewNode.active;
        this.blockInput.active = !this.blockInput.active;
        if (this.previewNode.active) {
            this.updateStartList();
        }
    }

    protected onClickFate(): void {
        UI.getInstance().open("FloatWingFatePreview", this.wingId);
    }
}
