/*
 * @Author: chenx
 * @Date: 2024-03-20 10:25:27
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:51:27
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { IItemInfo } from "../../protobuf/proto";
import { DungeonType } from "../game/Combat";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";

/**
 * 界面参数
 */
interface IFloatDungeonEndArgs {
    type: DungeonType; // 类型
    damage: number; // 伤害
    maxDamage: number; // 最大伤害
    rewardData: IItemInfo[]; // 奖励数据
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatDungeonEnd extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null;
    @property(cc.Node)
    name1: cc.Node = null;
    @property(cc.Node)
    value1: cc.Node = null;
    @property(cc.Node)
    name2: cc.Node = null;
    @property(cc.Node)
    value2: cc.Node = null;
    @property(cc.Node)
    rewards: cc.Node = null;

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private initData: IFloatDungeonEndArgs = null; // 初始化数据

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/result/spResultTitle15",
            },
        ];
    }

    protected onLoad(): void {
        this.initData = this.args;
        this.initInfo();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        this.value1.label(NumberUtils.format(this.initData.damage));
        this.value2.label(NumberUtils.format(this.initData.maxDamage));
        ItemUtils.refreshView(this.rewards, this.prefabItem, this.initData.rewardData);
    }

    protected onToggleAuto(toggle: cc.Toggle): void {}

    protected onClickClose(): void {
        switch (this.initData.type) {
            case DungeonType.Thief:
                UI.getInstance().closeToWindow("UIDungeonCombatThief");
                break;
            default:
                break;
        }
        UI.getInstance().close();
    }
}
