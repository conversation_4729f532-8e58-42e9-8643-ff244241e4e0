/*
 * @Author: chenx
 * @Date: 2024-10-09 16:41:38
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-10 11:02:56
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import i18n from "../config/i18n/I18n";
import TBPrincess from "../data/parser/TBPrincess";
import ImageUtils from "../utils/ImageUtils";

/**
 * 界面参数
 */
export interface IFloatConfidantDialogArgs {
    confidantId: number; // 知己id
    dialog: string; // 对话
    getExp: number; // 获得经验
}

/**
 * 刷新间隔
 */
const REFRESH_DURATION = 0.02;

const { ccclass, property } = cc._decorator;

/**
 * 知己-知己对话
 */
@ccclass
export default class FloatConfidantDialog extends I18nComponent {
    @property(cc.Node)
    nodeDialogBox: cc.Node = null; // 对话框
    @property(cc.Node)
    spIcon: cc.Node = null; // 知己icon
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Label)
    lbtDialog: cc.Label = null; // 对话
    @property(cc.Node)
    nodeExpIcon: cc.Node = null; // 经验icon
    @property(cc.Label)
    lbtExpTips: cc.Label = null; // 经验tips
    @property(cc.Node)
    nodeCloseTips: cc.Node = null; // 关闭tips
    @property(cc.Button)
    btnClose: cc.Button = null; // 关闭按钮

    initData: IFloatConfidantDialogArgs = null; // 初始数据
    dialogIndex: number = -1; // 对话索引
    refreshTime: number = 0; // 刷新时间

    protected onLoad(): void {
        this.initData = this.args;
        this.initInfo();
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.dialogIndex++;
                this.lbtDialog.string = this.initData.dialog.substring(0, this.dialogIndex);
                if (this.dialogIndex >= this.initData.dialog.length) {
                    this.nodeCloseTips.active = true;
                    cc.tween(this.nodeCloseTips)
                        .repeatForever(
                            cc
                                .tween()
                                .to(1, { opacity: 0 }, { easing: cc.easing.sineIn })
                                .to(1, { opacity: 255 }, { easing: cc.easing.sineOut })
                        )
                        .start();
                }

                this.dialogIndex < this.initData.dialog.length && (this.refreshTime = REFRESH_DURATION);
            }
        }
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        const confidantInfo = TBPrincess.getInstance().getDataById(this.initData.confidantId);
        ImageUtils.setConfidantIcon2(this.spIcon, confidantInfo.res);
        this.lbtName.string = confidantInfo.name;
        this.dialogIndex = 0;
        this.refreshTime = REFRESH_DURATION;
        this.nodeExpIcon.active = this.initData.getExp !== 0;
        this.lbtExpTips.string =
            this.initData.getExp !== 0
                ? TextUtils.format(i18n.confidant0013, confidantInfo.name, this.initData.getExp)
                : "";

        cc.tween(this.nodeDialogBox)
            .to(0.3, { opacity: 255 })
            .call(() => {
                CocosExt.setButtonEnable(this.btnClose, true);
            })
            .start();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        if (this.dialogIndex === -1) {
            return;
        }
        if (this.dialogIndex < this.initData.dialog.length) {
            this.refreshTime = 0;
            this.dialogIndex = this.initData.dialog.length;
            this.lbtDialog.string = this.initData.dialog.substring(0, this.dialogIndex);
            this.nodeCloseTips.active = true;
            cc.tween(this.nodeCloseTips)
                .repeatForever(
                    cc
                        .tween()
                        .to(1, { opacity: 0 }, { easing: cc.easing.sineIn })
                        .to(1, { opacity: 255 }, { easing: cc.easing.sineOut })
                )
                .start();
            return;
        }

        UI.getInstance().close();
    }
}
