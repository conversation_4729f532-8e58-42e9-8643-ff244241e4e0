/* 挖矿
 * @Author: wangym
 * @Date: 2024-04-12 09:28:12
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-17 14:45:03
 */
import Audio from "../../nsn/audio/Audio";
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import Time, { HOUR_TO_SECOND } from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import Tips from "../../nsn/util/Tips";
import {
    AdsWatchRet,
    BagUpdateRet,
    IItemInfo,
    IMapRowInfo,
    IShowItemInfo,
    MiningStart,
    MiningStartRet,
    MiningTakeAward,
    MiningTakeAwardRet,
    MiningUpdateLastRecoverAtRet,
    PlayerInitRet,
    ShowItemInfo,
} from "../../protobuf/proto";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import i18n from "../config/i18n/I18n";
import { EnumMiningPropType } from "../data/base/BaseMiningProp";
import { EnumMiningSpaceType } from "../data/base/BaseMiningSpace";
import { EnumPrivilegeGroupType } from "../data/base/BasePrivilegeGroup";
import TBAdvertisement, { AdConfigId } from "../data/parser/TBAdvertisement";
import TBItem, { ITEM_ID } from "../data/parser/TBItem";
import TBMiningProp from "../data/parser/TBMiningProp";
import TBMiningSpace from "../data/parser/TBMiningSpace";
import TBPrivilegeGroup from "../data/parser/TBPrivilegeGroup";
import { RECHARGE_ID } from "../data/parser/TBRecharge";
import TBShop from "../data/parser/TBShop";
import Ad from "../game/Ad";
import Bag from "../game/Bag";
import EconomyAttribute, { EconomyAttributeEvent } from "../game/EconomyAttribute";
import Mining, { EnumMiningAutoExcavateRule, MiningEvent } from "../game/Mining";
import Player, { PLAYER_INIT_TYPE } from "../game/Player";
import Privilege from "../game/Privilege";
import Shop from "../game/Shop";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";

const { ccclass, property } = cc._decorator;

// 挖掘点（相对坐标）
interface IMiningPoint {
    row: number; // 行
    col: number; // 列
}

const MAX_COLUMN = 6; // 挖矿页面每列最大方格数

const MAX_ROW = 7; // 挖矿页面最大行数

const TIME_INTERVAL = 1;

// 资源栏显示的道具列表
const RES_CONFIG = [ITEM_ID.DIAMOND, ITEM_ID.RESEARCH_STONE, ITEM_ID.MINING_GOLD_PICKAXE, ITEM_ID.MINING_IRON_PICKAXE];

@ccclass
export default class UIMining extends I18nComponent {
    @property(cc.Node)
    nodeScrollView: cc.Node = null; // 显示区域
    @property(cc.Node)
    nodeContent: cc.Node = null; // 容器
    @property(cc.Node)
    nodeRowItem: cc.Node = null; // 行节点Item
    @property(cc.Node)
    nodeItem: cc.Node = null; // 节点Item
    @property(cc.Node)
    lbtDepth: cc.Node = null; // 深度文本
    @property(cc.Node)
    spTitle: cc.Node = null; // 标题

    // => 资源栏
    @property(cc.Node)
    nodeRes: cc.Node = null; // 资源栏
    @property(cc.Node)
    nodeResItem: cc.Node = null; // 资源栏Item节点

    // => 工具栏
    @property([cc.Node])
    nodeButton: cc.Node[] = [];
    @property([cc.Node])
    btnAds: cc.Node[] = [];
    @property(cc.Node)
    btnAuto: cc.Node = null; // 自动挖掘按钮

    // => 奖励
    @property(cc.Node)
    nodeRewardContent: cc.Node = null;
    @property(cc.Node)
    nodeRewardItem: cc.Node = null;

    // => 动效表现
    @property(sp.Skeleton)
    spineBomb: sp.Skeleton = null; // 炸药爆炸
    @property(cc.Node)
    spineBombContent: cc.Node = null; // 炸药爆炸特效容器

    @property(sp.Skeleton)
    spinePickCollision: sp.Skeleton = null; // 矿镐挖掘敲击动画
    @property(cc.Node)
    nodePickaxeAni: cc.Node = null; // 稿子动画节点
    @property([cc.SpriteFrame])
    resPickaxe: cc.SpriteFrame[] = []; // 镐挥动动画资源

    @property(sp.Skeleton)
    spineBit: sp.Skeleton = null; // 钻头

    @property(sp.Skeleton)
    spineExcavateSoil: sp.Skeleton = null; // 土块
    @property(sp.Skeleton)
    spineExcavateStone: sp.Skeleton = null; // 石块
    @property(sp.Skeleton)
    spineScrollLeft: sp.Skeleton = null; // 滚动时播放的土块下滑动效（左侧）
    @property(sp.Skeleton)
    spineScrollRight: sp.Skeleton = null; // 滚动时播放的土块下滑动效（右侧）

    // => 获得奖励
    @property(cc.Node)
    nodeParticleItem: cc.Node = null; // 奖励飞行节点
    @property(cc.Node)
    nodeParticleContent: cc.Node = null; // 奖励飞行容器

    // => 拖动选中
    @property(cc.Node)
    spDragTool: cc.Node = null;
    @property(sp.Skeleton)
    spineFire: sp.Skeleton = null; // 选中挖掘方格时播放的火花

    // => 按钮
    @property(cc.Node)
    spIconBtnAuto: cc.Node = null; // 自动挖矿按钮图标
    @property([cc.SpriteFrame])
    resList: cc.SpriteFrame[] = []; // 自动挖矿开启和关闭时显示的图标资源列表
    @property(sp.Skeleton)
    spineAuto: sp.Skeleton = null; // 自动挖矿开启时显示的动画特效

    private curTool: EnumMiningPropType = EnumMiningPropType.IronPick; // 当前正在使用的挖掘工具类型
    private isAnimation: boolean = false; // 是否正在播放动画
    private isAuto: boolean = false; // 是否已开启自动挖矿
    private curPos: IMiningPoint = null; // 当前已选中的挖掘位置
    private curTime: number = 0; // 当前页面更新间隔延时
    private roomConfig: { id: number; src: IMiningPoint }[] = []; // 宝藏房配置列表
    private autoExcavatePath: IMiningPoint[] = null; // 当前正在执行的自动挖矿缓存路线表
    private nodeRewardPool: cc.NodePool = null; // 奖励节点池
    private nodeRowPool: cc.NodePool = null; // 行节点池
    private excavatePoint: IMiningPoint = null; // 正在挖掘的点
    private excavateTool: EnumMiningPropType = null; // 本次挖掘使用的工具
    private nodeParticlePool: cc.NodePool = null; // 粒子特效节点池
    private nodeBombSpinePool: cc.NodePool = null; // 炸弹爆炸特效节点池

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/syncUI/mining/spMineTitle",
            },
        ];
    }

    protected onLoad(): void {
        this.init();
        this.updateButtons();
        this.updateContent();
        this.updateRes();
        this.updateDepth();
    }

    protected onEnable(): void {
        this.nodeScrollView.on(cc.Node.EventType.TOUCH_START, this.onTouchStartOrMove.bind(this), this);
        this.nodeScrollView.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchStartOrMove.bind(this), this);
        this.nodeScrollView.on(cc.Node.EventType.TOUCH_END, this.onTouchEndOrCancel.bind(this), this);
        this.nodeScrollView.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEndOrCancel.bind(this), this);
    }

    protected onDisable(): void {
        this.nodeScrollView.off(cc.Node.EventType.TOUCH_START);
        this.nodeScrollView.off(cc.Node.EventType.TOUCH_MOVE);
        this.nodeScrollView.off(cc.Node.EventType.TOUCH_END);
        this.nodeScrollView.off(cc.Node.EventType.TOUCH_CANCEL);
    }

    protected update(dt: number): void {
        if (this.curTime >= TIME_INTERVAL) {
            this.curTime -= TIME_INTERVAL;
            this.updateRes();
            this.updateButtons();
        } else {
            this.curTime += dt;
        }
    }

    protected registerHandler(): void {
        Mining.getInstance().on(
            MiningStartRet.prototype.clazzName,
            (removeRowIndex: number[]) => {
                this.playToolSpineEffect(this.excavatePoint, this.excavateTool);
                this.playBreakSpineEffect(this.excavatePoint);

                // 添加底部显示的行
                const data = Mining.getInstance().getMiningData();
                this.roomConfig = [];
                const max = Math.max(this.nodeContent.childrenCount, MAX_ROW + removeRowIndex.length);
                for (let i = 0; i < max; i++) {
                    let nodeRow = this.nodeContent.children[i];
                    if (!nodeRow) {
                        if (this.nodeRowPool.size()) {
                            nodeRow = this.nodeRowPool.get();
                        } else {
                            nodeRow = Loader.getInstance().instantiate(this.nodeRowItem);
                        }
                        nodeRow.parent = this.nodeContent;
                    }
                    if (i < removeRowIndex.length && nodeRow) {
                        // 这些地块被移除后不可点击，移除点击参数
                        CocosExt.setNodeOrCompData(nodeRow, "row", null);
                        nodeRow.children.forEach((item) => {
                            item.button("");
                        });
                        continue;
                    }
                    const rowData = data?.miningMap?.rows[i - removeRowIndex.length];
                    if (!rowData) {
                        nodeRow.active = false;
                        continue;
                    }
                    nodeRow.active = true;
                    nodeRow.y = -(i * nodeRow.height + nodeRow.height / 2);
                    CocosExt.setNodeOrCompData(nodeRow, "row", rowData.rowIndex);
                    this.updateLine(nodeRow, rowData, i - removeRowIndex.length);
                }
                this.scheduleOnce(() => {
                    this.playScrollAnimation(removeRowIndex);
                }, 0.1);
            },
            this
        );

        Mining.getInstance().on(
            MiningEvent.MiningFailed,
            () => {
                this.isAnimation = false;
            },
            this
        );

        Mining.getInstance().on(
            MiningTakeAwardRet.prototype.clazzName,
            () => {
                this.isAnimation = false;
                this.updateContent();
                this.updateAutoExcavate(0); // 领取奖励不会导致地图行数发生变化
            },
            this
        );

        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (msg: BagUpdateRet) => {
                this.updateButtons();
                if (
                    [MiningTakeAward.prototype.clazzName, MiningStart.prototype.clazzName].includes(msg.srcReq) &&
                    this.excavatePoint
                ) {
                    // 在挖掘方格处播放奖励图标和数量上升的动效
                    (msg.showItem && msg.showItem.length > 0
                        ? msg.showItem.map((item) => item)
                        : msg.gotItem.map((itemInfo) => ({
                              itemSrc: ShowItemInfo.ItemSrc.NONE,
                              itemInfo,
                          }))
                    )
                        .sort((a, b) => {
                            if (a.itemInfo.itemInfoId !== b.itemInfo.itemInfoId) {
                                return a.itemInfo.itemInfoId - b.itemInfo.itemInfoId;
                            }
                            const srcA = a.itemSrc === ShowItemInfo.ItemSrc.NONE ? 1 : 0;
                            const srcB = b.itemSrc === ShowItemInfo.ItemSrc.NONE ? 1 : 0;
                            return srcA - srcB;
                        })
                        .forEach((item, index) => this.playRewardAnimation(this.excavatePoint, item, index));
                    // 在资源栏中显示的道具，播放飞到资源栏中的动效
                    this.playRewardParticle(
                        this.excavatePoint,
                        msg.gotItem.filter((item) => this.getRewardParticleEnd(item.itemInfoId) !== null)
                    );
                }
            },
            this
        );

        Ad.getInstance().on(
            AdsWatchRet.prototype.clazzName,
            (msg: AdsWatchRet) => {
                if (
                    [AdConfigId.MiningGetBit, AdConfigId.MiningGetExplosive, AdConfigId.MiningGetGoldPick].includes(
                        msg.adInfo.id
                    )
                ) {
                    const shopConfig = TBShop.getInstance().getDataByAdvertisementId(msg.adInfo.id);
                    Shop.getInstance().sendShopPurchase(shopConfig.id);
                }
                this.updateButtons();
            },
            this
        );

        Mining.getInstance().on(
            MiningUpdateLastRecoverAtRet.prototype.clazzName,
            () => {
                this.updateRes();
            },
            this
        );

        Player.getInstance().on(
            PlayerInitRet.prototype.clazzName,
            (payload: string) => {
                if (payload === PLAYER_INIT_TYPE.NONE) {
                    // 断线重连时，处理自动挖矿的状态
                    if (this.isAuto) {
                        this.onClickAuto();
                        this.isAnimation = false;
                    }
                }
            },
            this
        );
        // 经济属性-更新
        EconomyAttribute.getInstance().on(
            EconomyAttributeEvent.Update,
            () => {
                this.updateRes();
                this.updateButtons();
            },
            this
        );
    }

    private updateRes(): void {
        for (let i = 0; i < RES_CONFIG.length; i++) {
            let node = this.nodeRes.children[i];
            if (!node) {
                node = Loader.getInstance().instantiate(this.nodeResItem);
                node.parent = this.nodeRes;
            }
            const itemInfoId = RES_CONFIG[i];
            ImageUtils.setItemIcon(node.child("spIcon"), itemInfoId);
            node.button(itemInfoId);
            const tool = Mining.getInstance().getMiningToolTypeByItemId(itemInfoId);
            const propConfig = TBMiningProp.getInstance().getDataById(tool);
            if (tool && propConfig && propConfig.recoveryTime) {
                // 随着时间恢复的挖掘工具类型
                const miningData = Mining.getInstance().getMiningData();
                const itemCount = Mining.getInstance().getMiningToolItemCountByType(tool);
                const limitCount = propConfig.dailyLimit + Mining.getInstance().getMiningToolExtraMax(tool);
                node.child("lbtCount").label(`${itemCount}/${limitCount}`);
                node.child("nodeTime").active = itemCount < limitCount;
                if (itemCount < limitCount) {
                    const recoveryTime = Mining.getInstance().getMiningToolRecoveryTime(tool) * 1000; // 恢复1个道具所需时间(ms)
                    const time = recoveryTime - ((Time.getInstance().now() - miningData.lastRecoverAt) % recoveryTime); // 已经过的时间
                    node.child("nodeTime")
                        .child("lbtTime")
                        .label(
                            TimeFormat.getInstance().getTextByDuration(
                                time,
                                time >= 1 * HOUR_TO_SECOND * 1000
                                    ? TimeDurationFormatType.HH_MM_SS
                                    : TimeDurationFormatType.MM_SS
                            )
                        );
                }
            } else {
                // 其他道具类型
                node.child("nodeTime").active = false;
                node.child("lbtCount").label(Bag.getInstance().getCountFormatById(itemInfoId));
            }
        }
    }

    private updateContent(): void {
        const miningData = Mining.getInstance().getMiningData();
        this.roomConfig = [];
        const max = Math.max(miningData.miningMap.rows.length, this.nodeContent.childrenCount);
        for (let i = 0; i < max; i++) {
            let nodeRow = this.nodeContent.children[i];
            if (!nodeRow) {
                if (this.nodeRowPool.size()) {
                    nodeRow = this.nodeRowPool.get();
                } else {
                    nodeRow = Loader.getInstance().instantiate(this.nodeRowItem);
                }
                nodeRow.parent = this.nodeContent;
            }
            const rowData = miningData.miningMap.rows[i];
            if (!rowData) {
                nodeRow.active = false;
                CocosExt.setNodeOrCompData(nodeRow, "row", null);
                continue;
            }
            nodeRow.active = true;
            nodeRow.y = -(i * nodeRow.height + nodeRow.height / 2);
            CocosExt.setNodeOrCompData(nodeRow, "row", rowData.rowIndex);
            this.updateLine(nodeRow, rowData, i);
        }
    }

    private updateLine(nodeRow: cc.Node, rowData: IMapRowInfo, row: number): void {
        for (let col = 0; col < MAX_COLUMN; col++) {
            let node = nodeRow.children[col];
            if (!node) {
                node = Loader.getInstance().instantiate(this.nodeItem);
                node.parent = nodeRow;
            }
            const cellData = rowData?.cells[col];
            const miningSpaceConfig = TBMiningSpace.getInstance().getDataById(cellData?.id);
            node.active = true;
            node.child("spIcon").active = true;
            node.child("spMaskEnable").active = false; // 选中可挖掘的节点时显示
            node.child("spMaskDisable").active = false; // 选中但不可挖掘时显示
            node.child("spProgress").active =
                cellData && cellData.times > 0 && cellData.times < miningSpaceConfig.cleanTimes;
            if (cellData) {
                node.child("spProgress").getComponent(cc.ProgressBar).progress =
                    cellData.times / miningSpaceConfig.cleanTimes;
            }
            node.child("spInvisible").active = !cellData?.visible;
            node.button(`${row}#${col}`);
            node.child("lbtReward").label(
                cellData && cellData.rewards && cellData.rewards.length > 0 && !cellData.hasTake
                    ? cellData.rewards
                          .map(
                              (item) =>
                                  miningSpaceConfig.id +
                                  "\n" +
                                  TBItem.getInstance().getDataById(item.itemInfoId).name +
                                  "x" +
                                  item.num
                          )
                          .join("")
                    : ""
            );
            CocosExt.setNodeOrCompData(node, "spaceId", miningSpaceConfig.id);
            CocosExt.setNodeOrCompData(node, "times", cellData.times || 0);
            if (miningSpaceConfig && miningSpaceConfig.type === EnumMiningSpaceType.Room) {
                // 如果显示为密室，则该方块无论如何都会有背景
                node.child("spBg").spriteAsync(
                    `texture/mining/room/${miningSpaceConfig.res}${this.getRoomIconName(miningSpaceConfig.id, {
                        row,
                        col,
                    })}`
                );
                if (cellData && cellData.rewards && cellData.rewards.length > 0 && !cellData.hasTake) {
                    // 密室中带有奖励的方块，在未被挖掘的时候才显示
                    ImageUtils.setItemIcon(node.child("spIcon"), cellData.rewards[0].itemInfoId);
                    node.child("lbtCount").label("x" + cellData.rewards[0].num);
                } else {
                    // 如果是已经挖掘过了，则不显示奖励图标以及奖励数量
                    node.child("spIcon").sprite(null);
                    node.child("lbtCount").label("");
                }
            } else if (!cellData || cellData.times <= 0) {
                // 如果该方块原本就为空地，或者是该方块已被全部挖掘，则不显示背景
                node.child("spBg").sprite(null);
                node.child("spIcon").sprite(null);
                node.child("lbtCount").label("");
            } else if (miningSpaceConfig.cleanTimes > 1) {
                // 如果是由于挖掘次数的消耗会改变形态的矿物质资源，需要根据挖掘次数更换背景
                node.child("spIcon").sprite(null);
                node.child("spBg").spriteAsync(
                    `texture/mining/excavate/${miningSpaceConfig.res}${
                        cellData.times >= miningSpaceConfig.cleanTimes ? "1" : "2"
                    }`
                );
                node.child("lbtCount").label("");
            } else {
                // 其他方块直接设置背景即可
                node.child("spIcon").sprite(null);
                node.child("spBg").spriteAsync(`texture/mining/excavate/${miningSpaceConfig.res}`);
                node.child("lbtCount").label("");
            }
        }
    }

    /**
     * 初始化部分节点状态
     */
    private init(): void {
        this.nodeRewardPool = new cc.NodePool();
        this.nodeRowPool = new cc.NodePool();
        this.nodeParticlePool = new cc.NodePool();
        this.nodeBombSpinePool = new cc.NodePool();

        this.spineBit.node.active = false;
        this.spineBomb.node.active = false;
        this.nodePickaxeAni.active = false;

        this.spinePickCollision.node.active = false;
        this.spineExcavateSoil.node.active = false;
        this.spineExcavateStone.node.active = false;
        this.spineScrollLeft.node.active = false;
        this.spineScrollRight.node.active = false;

        this.spineFire.node.active = false;
        this.spDragTool.active = false;

        this.spineAuto.node.active = false;
    }

    /**
     * 更新工具按钮节点的触摸事件
     * @param node 按钮节点
     * @param open 是否监听
     * @returns {void}
     */
    private updateToolButtonTouchEvent(node: cc.Node, open: boolean): void {
        const mask = node.getChildByName("maskTouch");
        if (!mask) {
            return;
        }
        const data = CocosExt.getNodeOrCompData(mask, "touch");
        if (!!data === open) {
            return;
        }
        mask.active = open;
        if (open) {
            mask.on(cc.Node.EventType.TOUCH_START, this.onTouchStartOrMove.bind(this), this);
            mask.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchStartOrMove.bind(this), this);
            mask.on(cc.Node.EventType.TOUCH_END, this.onTouchEndOrCancel.bind(this), this);
            mask.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEndOrCancel.bind(this), this);
        } else {
            mask.off(cc.Node.EventType.TOUCH_START);
            mask.off(cc.Node.EventType.TOUCH_MOVE);
            mask.off(cc.Node.EventType.TOUCH_END);
            mask.off(cc.Node.EventType.TOUCH_CANCEL);
        }
        CocosExt.setNodeOrCompData(mask, "touch", open ? "1" : "");
    }

    /**
     * 更新工具按钮集合
     */
    private updateButtons(): void {
        if (Mining.getInstance().getMiningToolItemCountByType(this.curTool) <= 0) {
            switch (this.curTool) {
                case EnumMiningPropType.IronPick:
                case EnumMiningPropType.Bit:
                case EnumMiningPropType.Explosive:
                    this.curTool = EnumMiningPropType.GoldPick;
                    break;
                default:
                    break;
            }
        }

        // 当铁镐的数量恢复后，自动切换回铁镐
        if (
            this.curTool === EnumMiningPropType.GoldPick &&
            Mining.getInstance().getMiningToolItemCountByType(EnumMiningPropType.IronPick) > 0
        ) {
            this.curTool = EnumMiningPropType.IronPick;
        }

        // 工具按钮
        for (const btn of this.nodeButton) {
            const tool = parseInt(CocosExt.getButtonData(btn));
            ImageUtils.setItemIcon(btn.child("spIcon"), Mining.getInstance().getMiningToolItemIdByType(tool));
            btn.child("lbtCount").label(Mining.getInstance().getMiningToolItemCountByType(tool) + "");
            btn.child("spSelect").active = tool === this.curTool;
            btn.active = this.isBtnDisplay(tool);
            this.updateToolButtonTouchEvent(btn, tool === this.curTool);
        }

        // 广告工具按钮
        for (const btn of this.btnAds) {
            const tool = parseInt(CocosExt.getButtonData(btn));
            const adId = this.getAdvertisementIdByTool(tool);
            const adConfig = TBAdvertisement.getInstance().getDataById(adId);
            const shopConfig = TBShop.getInstance().getDataByAdvertisementId(adId);

            btn.active = this.isAdBtnDisplay(tool);
            btn.child("lbtCount").label(
                `(${adConfig.count - Ad.getInstance().getWatchTimesById(adId)}/${adConfig.count})`
            );
            btn.child("lbtReward").label("×" + shopConfig.goods[0][1]);
            ImageUtils.setItemIcon(btn.child("lbtRewardIcon"), shopConfig.goods[0][0]);
        }
    }

    /**
     * 更新历史进度值
     * @param str 动画过程中显示的进度值
     */
    private updateDepth(str?: string): void {
        if (str) {
            this.lbtDepth.label(str);
            return;
        }
        const data = Mining.getInstance().getMiningData();
        const dep = data?.miningMap?.historyDepth || 0;
        this.lbtDepth.label((dep >= 6 ? dep : 6) + "M");
    }

    /**
     * 更新挖矿后自动挖矿的行数变化
     * @param length 被移除的行数
     */
    private updateAutoExcavate(length: number): void {
        if (this.autoExcavatePath && this.autoExcavatePath.length > 1) {
            this.autoExcavatePath.splice(0, 1);
        } else {
            this.autoExcavatePath = null;
        }
        if (this.isAuto && this.autoExcavatePath) {
            let disable = false; // 是否由于自动滚动将奖励消除
            for (let i = 0; i < this.autoExcavatePath.length; i++) {
                const point = this.autoExcavatePath[i];
                const lastPoint: IMiningPoint = { row: point.row - length, col: point.col };
                if (lastPoint.row < 0) {
                    disable = true;
                    break;
                }
                this.autoExcavatePath[i] = lastPoint;
            }
            disable && (this.autoExcavatePath = null); // 路径不可用
        }
        if (this.isAuto) {
            // 继续向下深挖
            this.auto();
        } else {
            // 自动切换挖掘工具
            if ([EnumMiningPropType.Bit, EnumMiningPropType.Explosive].includes(this.curTool)) {
                this.curTool = EnumMiningPropType.GoldPick;
                this.updateButtons();
            }
        }
    }

    /**
     * 清除地块（本地计算）挖矿导致某些方块被移除的行无法通过服务端的数据更新，需要客户端自行更新
     */
    private updateClearBlock(): void {
        const pointList = this.getToolExcavateRange(this.curTool, this.excavatePoint);
        for (const point of pointList) {
            const node = this.nodeContent.children[point.row].children[point.col];
            const spaceId = CocosExt.getNodeOrCompData(node, "spaceId");
            const miningSpaceConfig = TBMiningSpace.getInstance().getDataById(spaceId);
            const rowData = CocosExt.getNodeOrCompData(node.parent, "row");
            node.child("spInvisible").active = false;
            node.child("spIcon").sprite(null);
            if (miningSpaceConfig.type !== EnumMiningSpaceType.Room) {
                const toolClean = TBMiningProp.getInstance().getPropCleanTimeByType(this.curTool);
                const times = CocosExt.getNodeOrCompData(node, "times");
                // 已被移除的方块（rowData已变为null）的才需要处理，否则表示服务端仍然记录着当前方块的数据，不需要前端自行更新
                const clean = Number.isNaN(Number.parseInt(rowData)) ? toolClean : 0;
                if (times - clean <= 0) {
                    node.child("spBg").sprite(null);
                } else {
                    node.child("spBg").spriteAsync(
                        `texture/mining/excavate/${miningSpaceConfig.res}${
                            miningSpaceConfig.cleanTimes > 1 ? (times >= miningSpaceConfig.cleanTimes ? "1" : "2") : ""
                        }`
                    );
                }
                node.child("spProgress").active = times > 0 && times < miningSpaceConfig.cleanTimes;
                node.child("spProgress").getComponent(cc.ProgressBar).progress = times / miningSpaceConfig.cleanTimes;
            }
        }
    }

    /**
     * 播放自动挖矿按钮的动画效果
     */
    private playBtnAnimation(): void {
        if (this.isAuto) {
            this.spIconBtnAuto.sprite(this.resList[1]);
            this.spineAuto.node.active = true;
            this.spineAuto.setAnimation(0, "wait", true);
            cc.tween(this.spIconBtnAuto)
                .repeatForever(
                    cc
                        .tween()
                        .to(2, { angle: 360 })
                        .call(() => {
                            this.spIconBtnAuto.angle = 0;
                        })
                )
                .start();
        } else {
            this.spIconBtnAuto.sprite(this.resList[0]);
            this.spIconBtnAuto.angle = 0;
            this.spIconBtnAuto.stopAllActions();
            this.spineAuto.clearTracks();
            this.spineAuto.node.active = false;
        }
    }

    /**
     * 播放挖掘动效
     * @param point
     * @param tool
     */
    private playToolSpineEffect(point: IMiningPoint, tool: EnumMiningPropType): void {
        let spine: sp.Skeleton = null; // 在挖掘方格播放的动效
        let aniName = "wait";
        let aniPos = cc.v2(
            this.nodeScrollView.x + (point.col - MAX_COLUMN / 2) * this.nodeItem.width + this.nodeItem.width / 2,
            this.nodeScrollView.y - point.row * this.nodeItem.height - this.nodeItem.height / 2
        );
        let audio = ""; // 音效
        let bombList: { point: IMiningPoint; delay: number }[] = []; // 爆炸动效
        let spineCollision: sp.Skeleton = null; // 敲击动效
        switch (tool) {
            case EnumMiningPropType.IronPick:
                spineCollision = this.spinePickCollision;
                break;
            case EnumMiningPropType.GoldPick:
                aniName = "wait2"; // 仅修改动画播放名称
                spineCollision = this.spinePickCollision;
                break;
            case EnumMiningPropType.Bit:
                spine = this.spineBit;
                aniPos = cc.v2(
                    this.nodeScrollView.x +
                        (point.col - MAX_COLUMN / 2) * this.nodeItem.width +
                        this.nodeItem.width / 2,
                    this.nodeScrollView.y - (MAX_ROW - 1) * this.nodeItem.height - this.nodeItem.height / 2
                ); // 修改动画播放位置
                bombList = this.getToolExcavateRange(tool, point).map((point) => {
                    return {
                        point,
                        delay: 0.0666 * point.row,
                    };
                }); // 添加爆炸特效
                audio = AUDIO_EFFECT_TYPE.MINING_BIT;
                break;
            case EnumMiningPropType.Explosive:
                spine = this.spineBomb;
                bombList = this.getToolExcavateRange(tool, point)
                    .filter((p) => p.row !== point.row || p.col !== point.col)
                    .map((point) => {
                        return {
                            point,
                            delay: 0,
                        };
                    }); // 添加爆炸特效
                audio = AUDIO_EFFECT_TYPE.MINING_EXPLOSIVE;
                break;
            default:
                break;
        }
        // 敲击动画
        if (spineCollision) {
            spineCollision.node.active = true;
            spineCollision.node.setPosition(aniPos);
            spineCollision.setAnimation(0, "wait", false);
            spineCollision.setCompleteListener(() => {
                spineCollision.node.active = false;
                spineCollision.node.setPosition(cc.v2(0, 0));
                spineCollision.clearTracks();
                spineCollision.setCompleteListener(null);
            });
            Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.MINING_PICK, AUDIO_EFFECT_PATH.MINING);
        }
        // 爆炸动画
        bombList.forEach((config) => {
            let node: cc.Node = null;
            if (this.nodeBombSpinePool.size()) {
                node = this.nodeBombSpinePool.get();
                node.parent = this.spineBombContent;
            } else {
                node = Loader.getInstance().instantiate(this.spineBomb.node);
                node.parent = this.spineBombContent;
            }
            node.active = true;
            node.setPosition(
                cc.v2(
                    this.nodeScrollView.x +
                        (config.point.col - MAX_COLUMN / 2) * this.nodeItem.width +
                        this.nodeItem.width / 2,
                    this.nodeScrollView.y - config.point.row * this.nodeItem.height - this.nodeItem.height / 2
                )
            );
            const component = node.getComponent(sp.Skeleton);
            component.setCompleteListener(() => {
                node.active = false;
                component.clearTracks();
                component.setCompleteListener(null);
                this.nodeBombSpinePool.put(node);
            });
            this.scheduleOnce(() => {
                component.setAnimation(0, "wait", false);
            }, config.delay);
        });
        if (spine) {
            spine.node.active = true;
            spine.node.setPosition(aniPos);
            spine.setAnimation(0, aniName, false);
            spine.setCompleteListener(() => {
                spine.node.active = false;
                spine.clearTracks();
                spine.setCompleteListener(null);
                this.updateClearBlock();
            });
        } else {
            this.playPickaxeAnimation(aniPos, tool, () => {
                this.updateClearBlock();
            });
        }
        if (audio) {
            Audio.getInstance().playEffect(audio, AUDIO_EFFECT_PATH.MINING);
        }
    }

    /**
     * 播放镐子挖掘动画
     * @param aniPos 动画播放位置
     * @param resIndex 资源
     * @param cb 回调
     */
    private playPickaxeAnimation(aniPos: cc.Vec2, resIndex: number, cb?: () => void): void {
        const spIcon = this.nodePickaxeAni.child("spIcon");
        const startAngle = -90;
        const endAngle = 15;
        const duration = 0.15;
        this.nodePickaxeAni.active = true;
        this.nodePickaxeAni.setPosition(aniPos);
        spIcon.sprite(this.resPickaxe[resIndex]);
        spIcon.angle = 0;
        cc.tween(spIcon)
            .to(duration * 0.5, { angle: startAngle }) // 向后挥动
            .to(duration, { angle: endAngle }) // 向前挥动
            .call(() => {
                cb && cb();
                this.nodePickaxeAni.active = false;
            })
            .start();
    }

    /**
     * 播放方块被破坏的动效
     * @param point
     * @returns
     */
    private playBreakSpineEffect(point: IMiningPoint): void {
        let spine: sp.Skeleton = null;
        const spaceId = CocosExt.getNodeOrCompData(this.nodeContent.children[point.row].children[point.col], "spaceId");
        const miningSpaceConfig = TBMiningSpace.getInstance().getDataById(spaceId);
        switch (miningSpaceConfig.type) {
            case EnumMiningSpaceType.Clod:
            case EnumMiningSpaceType.ClodReward:
                spine = this.spineExcavateSoil;
                break;
            case EnumMiningSpaceType.Stone:
            case EnumMiningSpaceType.StoneReward:
                spine = this.spineExcavateStone;
                break;
            default:
                break;
        }
        if (!spine) {
            return;
        }
        spine.node.active = true;
        spine.node.setPosition(
            cc.v2(
                this.nodeScrollView.x + (point.col - MAX_COLUMN / 2) * this.nodeItem.width + this.nodeItem.width / 2,
                this.nodeScrollView.y - point.row * this.nodeItem.height - this.nodeItem.height / 2
            )
        );
        spine.setAnimation(0, "wait", false);
        spine.setCompleteListener(() => {
            spine.clearTracks();
            spine.node.active = false;
            spine.setCompleteListener(null);
        });
    }

    /**
     * 播放奖励粒子动画
     * @param point
     * @param itemList
     */
    private playRewardParticle(point: IMiningPoint, itemList: IItemInfo[]): void {
        // 特效节点数量
        const rewardCount = itemList.length
            ? itemList.reduce((total, v) => {
                  return total + v.num;
              }, 0)
            : 0;
        const effectMaxCount = rewardCount > 100 ? 8 : 4; // 飞行道具的数量（道具综合大于100个会有8个动效节点，否则是4个）
        let idList: number[] = [];
        for (const item of itemList) {
            // 道具奖励数量
            const num = item.num;
            // 特效数量
            const count = Math.floor((num / rewardCount) * effectMaxCount);
            idList = idList.concat(Array.from({ length: count }, () => item.itemInfoId));
        }
        for (let i = 0; i < idList.length; i++) {
            const itemInfoId = idList[i];
            const angle = Math.random() * (360 / effectMaxCount) + i * (360 / effectMaxCount);
            const radius = Math.random() * (170 - 100) + 100;
            const x = radius * Math.cos((angle * Math.PI) / 180);
            const y = radius * Math.sin((angle * Math.PI) / 180);
            const end = this.getRewardParticleEnd(itemInfoId);
            let node: cc.Node = null;
            if (this.nodeParticlePool.size()) {
                node = this.nodeParticlePool.get();
                node.parent = this.nodeParticleContent;
            } else {
                node = Loader.getInstance().instantiate(this.nodeParticleItem);
                node.parent = this.nodeParticleContent;
            }
            node.active = true;
            node.opacity = 255;
            node.child("effect").angle = -90 + angle;
            node.setPosition(
                cc.v2(
                    this.nodeScrollView.x +
                        (point.col - MAX_COLUMN / 2) * this.nodeItem.width +
                        this.nodeItem.width / 2,
                    this.nodeScrollView.y - point.row * this.nodeItem.height - this.nodeItem.height / 2
                )
            );
            ImageUtils.setItemIcon(node.child("spIcon"), itemInfoId);
            cc.tween(node)
                .to(0.2, { x: x + node.x, y: y + node.y }, { easing: cc.easing.sineOut })
                .call(() => {
                    const turnBackAngle = Math.atan2(end.y - node.y, end.x - node.x) * (180 / Math.PI);
                    node.child("effect").angle = -90 + turnBackAngle;
                })
                .to(0.5, { x: end.x, y: end.y, opacity: 80 }, { easing: cc.easing.sineIn })
                .call(() => {
                    node.stopAllActions();
                    node.active = false;
                    this.nodeParticlePool.put(node);
                })
                .start();
        }
        // 音效
        if (itemList.length && itemList.length > 0) {
            Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.MINING_REWARD, AUDIO_EFFECT_PATH.MINING);
        } else {
            Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.MINING_NO_REWARD, AUDIO_EFFECT_PATH.MINING);
        }
    }

    /**
     * 获取对应粒子特效终点（资源栏的世界坐标）
     * @param itemInfoId 道具ID
     * @returns
     */
    private getRewardParticleEnd(itemInfoId: number): { x: number; y: number } {
        const node = this.nodeRes.children.find((node) => {
            return itemInfoId === CocosExt.getButtonData(node);
        });
        if (!node) {
            return null;
        }
        const pos = this.nodeRes.convertToWorldSpaceAR(node.position);
        return {
            x: pos.x - this.node.width / 2,
            y: pos.y - this.node.height / 2,
        };
    }

    /**
     * 播放滚动动画
     * @param removeRowIndex
     */
    private playScrollAnimation(removeRowIndex: number[]): void {
        if (removeRowIndex.length <= 0) {
            this.isAnimation = false;
            this.updateAutoExcavate(removeRowIndex.length);
            this.updateDepth();
            return;
        }
        this.isAnimation = true;

        // 音效
        let playingAudioId: number = null;
        Audio.getInstance().playEffect(
            AUDIO_EFFECT_TYPE.MINING_ROLLING,
            AUDIO_EFFECT_PATH.MINING,
            false,
            (audioId: number) => {
                playingAudioId = audioId;
            }
        );

        this.spineScrollLeft.node.active = true;
        this.spineScrollRight.node.active = true;
        this.spineScrollLeft.setAnimation(0, "wait", true);
        this.spineScrollRight.setAnimation(0, "wait", true);
        for (let i = 0; i < this.nodeContent.children.length; i++) {
            const node = this.nodeContent.children[i];
            cc.tween(node)
                .by(
                    0.25,
                    { y: removeRowIndex.length * node.height },
                    {
                        // 在滚动时动态更新挖掘深度
                        progress: (s: number, e: number, c: number, r: number) => {
                            if (i === 0) {
                                const data = Mining.getInstance().getMiningData();
                                const dep = data?.miningMap?.historyDepth || 0;
                                this.updateDepth(
                                    Math.floor(Math.max(6, dep - (removeRowIndex.length - removeRowIndex.length * r))) +
                                        "M"
                                );
                            }
                            return s + (e - s) * r;
                        },
                    }
                )
                .call(() => {
                    if (i === this.nodeContent.children.length - 1) {
                        this.spineScrollLeft.node.active = false;
                        this.spineScrollRight.node.active = false;
                        this.spineScrollLeft.clearTracks();
                        this.spineScrollRight.clearTracks();
                        this.isAnimation = false;
                        for (let i = this.nodeContent.childrenCount - 1; i >= 0; i--) {
                            const nodeRow = this.nodeContent.children[i];
                            const row = CocosExt.getNodeOrCompData(nodeRow, "row");
                            if (!row) {
                                nodeRow.active = false;
                                nodeRow.parent = null;
                                this.nodeRowPool.put(nodeRow);
                            }
                        }
                        this.updateAutoExcavate(removeRowIndex.length);

                        // 音效
                        if (playingAudioId) {
                            Audio.getInstance().stopEffect(playingAudioId);
                        }
                    }
                })
                .start();
        }
    }

    /**
     * 播放获得奖励动画
     * @param point 播放奖励动画的点
     * @param showItemInfo 获得的道具信息
     * @param index 播放位置
     */
    private playRewardAnimation(point: IMiningPoint, showItemInfo: IShowItemInfo, index: number = 0): void {
        let node: cc.Node = null;
        if (this.nodeRewardPool.size()) {
            node = this.nodeRewardPool.get();
        } else {
            node = Loader.getInstance().instantiate(this.nodeRewardItem);
        }
        node.parent = this.nodeRewardContent;
        node.active = true;
        node.child("lbtCount").color =
            showItemInfo.itemSrc === ShowItemInfo.ItemSrc.NONE ? cc.color(255, 255, 255) : cc.color(255, 138, 0);

        const itemInfo = showItemInfo.itemInfo;
        ImageUtils.setItemIcon(node.child("spIcon"), itemInfo.itemInfoId);
        node.child("lbtCount").label("+" + itemInfo.num);

        node.setPosition(
            cc.v2(
                this.nodeScrollView.x + (point.col - MAX_COLUMN / 2) * this.nodeItem.width + this.nodeItem.width / 2,
                this.nodeScrollView.y - point.row * this.nodeItem.height - this.nodeItem.height / 2
            )
        );
        node.opacity = 255;
        node.y += index * 60;
        cc.tween(node)
            .by(0.9, { y: 100, opacity: 20 })
            .call(() => {
                node.active = false;
                this.nodeRewardPool.put(node);
            })
            .start();
    }

    /**
     * 获取道具的挖掘范围
     * @param tool 挖掘道具类型
     */
    private getToolExcavateRange(tool: EnumMiningPropType, point: IMiningPoint): IMiningPoint[] {
        let result: IMiningPoint[] = [];
        switch (tool) {
            case EnumMiningPropType.IronPick:
            case EnumMiningPropType.GoldPick:
                // 仅选中的方格
                // o
                result = [point];
                break;
            case EnumMiningPropType.Bit:
                // 选择与选择点相同列坐标的一整列，外加该列底部往左与右拓展1格
                //   o
                //   o
                //   o
                //   o
                //   o
                //   o
                // o o o
                result = Array.from({ length: MAX_ROW }, (v, k) => ({ row: k, col: point.col }))
                    .concat([
                        { row: MAX_ROW - 1, col: point.col - 1 },
                        { row: MAX_ROW - 1, col: point.col + 1 },
                    ])
                    .filter((pos) => !(pos.col < 0 || pos.col >= MAX_COLUMN || pos.row < 0 || pos.row >= MAX_ROW));
                break;
            case EnumMiningPropType.Explosive:
                // 选择一个3×3旋转45度后矩形的爆炸区域，
                //     o
                //   o o o
                // o o o o o
                //   o o o
                //     o
                result = [
                    { row: 0, col: 0 },
                    { row: 0, col: 1 },
                    { row: 0, col: 2 },
                    { row: 0, col: -1 },
                    { row: 0, col: -2 },
                    { row: 1, col: 0 },
                    { row: 2, col: 0 },
                    { row: -1, col: 0 },
                    { row: -2, col: 0 },
                    { row: 1, col: 1 },
                    { row: 1, col: -1 },
                    { row: -1, col: 1 },
                    { row: -1, col: -1 },
                ]
                    .map((pos) => ({ row: point.row + pos.row, col: point.col + pos.col }))
                    .filter((pos) => !(pos.col < 0 || pos.col >= MAX_COLUMN || pos.row < 0 || pos.row >= MAX_ROW));
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 根据工具类型获取广告ID
     * @param tool 工具类型
     * @returns {number}
     */
    private getAdvertisementIdByTool(tool: EnumMiningPropType): number {
        let result: number = null;
        switch (tool) {
            case EnumMiningPropType.IronPick:
            case EnumMiningPropType.GoldPick:
                result = AdConfigId.MiningGetGoldPick;
                break;
            case EnumMiningPropType.Bit:
                result = AdConfigId.MiningGetBit;
                break;
            case EnumMiningPropType.Explosive:
                result = AdConfigId.MiningGetExplosive;
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 根据已记录的密室位置，获取每个密室格子应该显示的背景图片名称Index
     * @param spaceId 密室ID
     * @param pos 位置
     * @returns {string}
     */
    private getRoomIconName(spaceId: number, pos: IMiningPoint): string {
        const roomSize = { width: 4, height: 2 };
        let room = this.roomConfig.find((item) => {
            return (
                pos.row >= item.src.row &&
                pos.row < item.src.row + roomSize.height &&
                pos.col >= item.src.col &&
                pos.col < item.src.col + roomSize.width &&
                spaceId === item.id
            );
        });
        if (!room) {
            // 向下识别一个全新的密室
            let extraRoomRow = 0; // 当前格下方还有密室的层数
            while (extraRoomRow < roomSize.height) {
                const colData = Mining.getInstance().getMiningDataByPos(pos.row + extraRoomRow + 1, pos.col);
                if (!colData) {
                    // 无法通过向下深挖的方式识别密室的全貌
                    break;
                }
                if (spaceId === colData.id) {
                    // 与当前点同属一间密室，则继续向下深挖
                    extraRoomRow += 1; // 向下深挖
                    continue;
                } else {
                    // 当前密室已到达尽头
                    break;
                }
            }
            // 将密室!!左上角!!的坐标置入到密室房间列表中
            room = {
                id: spaceId,
                src: {
                    row: pos.row - (roomSize.height - extraRoomRow - 1),
                    col: pos.col,
                },
            };
            this.roomConfig.push(room);
        }
        let result = 1;
        result = (pos.row - room.src.row) * roomSize.width + (pos.col - room.src.col) + 1;
        return result + "";
    }

    /**
     * 根据规则寻找下一个自动挖掘的点或路径
     * @returns
     */
    private getAutoExcavatePoint(): IMiningPoint[] {
        const rule = Mining.getInstance().getMiningAutoSettingsRule();
        const searchList = Mining.getInstance().getMiningAutoSettingsSearchList();
        const data = Mining.getInstance().getMiningData();
        const effect = TBMiningProp.getInstance().getPropCleanTimeByType(this.curTool);
        let temp: IMiningPoint = null; // 最后优先级的挖掘点（在该点被设置为最后优先级后未找到确切挖掘点时，使用该挖掘点）
        for (let row = 0; row < data.miningMap.rows.length; row++) {
            const rowData = data.miningMap.rows[row];
            for (let col = 0; col < rowData.cells.length; col++) {
                const colData = rowData.cells[col];
                const colConfig = TBMiningSpace.getInstance().getDataById(colData.id);
                const excavateAble = this.excavatable({ row, col });
                const isSearch = searchList.includes(colData.id);
                // 如果找到了密室中未领取的奖励，
                if (
                    colConfig.type === EnumMiningSpaceType.Room && // 密室
                    colData.rewards && // 该密室格子中包含未领取的奖励
                    colData.rewards.length > 0 &&
                    !colData.hasTake &&
                    colData.visible // 该密室可见
                ) {
                    return [{ row, col }];
                }
                if (excavateAble && isSearch) {
                    // 可挖掘，且位于检索列表中时，直接对该地块进行挖掘
                    return [{ row, col }];
                } else if (excavateAble) {
                    // 可挖掘，但不在检索列表中时，进入预选队列（向下挖掘）
                    if (!temp) {
                        temp = { row, col };
                        continue;
                    }
                    // 挖掘点在挖掘后可以到达更深的层次时
                    const tempClear = Mining.getInstance().getMiningDataByPos(temp.row, temp.col)?.times || Infinity;
                    const curClear = Mining.getInstance().getMiningDataByPos(row, col)?.times || Infinity;
                    if (curClear <= tempClear || row - curClear / effect >= temp.row) {
                        temp = { row, col };
                        continue;
                    }
                }
                // 如果奖励已被领取，则跳过该节点挖掘逻辑
                if (colData.hasTake || colData.times <= 0 || !isSearch) {
                    continue;
                }
                // 根据玩家设置的规则检查点是否可达
                switch (rule) {
                    case EnumMiningAutoExcavateRule.SearchRange2Resource:
                        if (
                            this.excavatable({ row: row + 1, col }) && // 可挖掘，避免不可见
                            (Mining.getInstance().getMiningDataByPos(row + 1, col)?.times || Infinity) <= effect // 当前使用的工具效益能达到1次挖掘该方块时
                        ) {
                            // 下方的点优先级最高
                            return Array.from(
                                { length: Mining.getInstance().getMiningDataByPos(row + 1, col).times },
                                (v, k) => {
                                    return { row: row + 1, col };
                                }
                            ).concat([{ row, col }]);
                        } else if (
                            this.excavatable({ row, col: col + 1 }) && // 可挖掘，避免不可见
                            (Mining.getInstance().getMiningDataByPos(row, col + 1)?.times || Infinity) <= effect // 当前使用的工具效益能达到1次挖掘该方块时
                        ) {
                            // 然后是右侧的挖掘点优先级更高
                            return Array.from(
                                { length: Mining.getInstance().getMiningDataByPos(row, col + 1).times },
                                (v, k) => {
                                    return { row, col: col + 1 };
                                }
                            ).concat([{ row, col }]);
                        } else if (
                            this.excavatable({ row, col: col - 1 }) && // 可挖掘，避免不可见
                            (Mining.getInstance().getMiningDataByPos(row, col - 1)?.times || Infinity) <= effect // 当前使用的工具效益能达到1次挖掘该方块时
                        ) {
                            // 左侧的挖掘点优先级较低
                            return Array.from(
                                { length: Mining.getInstance().getMiningDataByPos(row, col - 1).times },
                                (v, k) => {
                                    return { row, col: col - 1 };
                                }
                            ).concat([{ row, col }]);
                        } else if (
                            this.excavatable({ row: row - 1, col }) && // 可挖掘，避免不可见
                            (Mining.getInstance().getMiningDataByPos(row - 1, col)?.times || Infinity) <= effect // 当前使用的工具效益能达到1次挖掘该方块时
                        ) {
                            // 上方的挖掘点优先级最低
                            return Array.from(
                                { length: Mining.getInstance().getMiningDataByPos(row - 1, col).times },
                                (v, k) => {
                                    return { row: row - 1, col };
                                }
                            ).concat([{ row, col }]);
                        }
                        break;
                    case EnumMiningAutoExcavateRule.SearchNoneRangeResource:
                        return [this.getAutoExcavatePointWhitRange({ row, col })];
                    default:
                        break;
                }
            }
        }
        return [temp];
    }

    /**
     * 求与目标挖掘资源距离最短的挖掘点
     * @param point
     * @returns
     */
    private getAutoExcavatePointWhitRange(point: IMiningPoint): IMiningPoint {
        // 当前点可进行挖掘
        if (this.excavatable(point)) {
            return point;
        }
        const effect = TBMiningProp.getInstance().getPropCleanTimeByType(this.curTool);
        let result: {
            row: number; // 缓存点的行坐标
            col: number; // 缓存点的列坐标
            range: number; // 缓存点距离目标点的距离
            time: number; // 缓存点需要进行的挖掘次数
        } = null;
        for (let row = 0; row < MAX_ROW; row++) {
            for (let col = 0; col < MAX_COLUMN; col++) {
                if (!this.excavatable({ row, col })) {
                    continue;
                }
                // 破坏当前挖掘点所需要消耗的道具数量
                const time = Mining.getInstance().getMiningDataByPos(row, col)?.times || Infinity;
                // 两点距离
                const range = Math.sqrt(
                    Math.pow(Math.abs(col - point.col), 2) + Math.pow(Math.abs(row - point.row), 2)
                );
                if (range === 1 && time / effect <= 1) {
                    // 距离和消耗数量全趋于最小，优先选择该点
                    return { row, col };
                }
                if (!result) {
                    result = { row, col, range, time };
                } else if (Math.ceil(result.range) + result.time >= Math.ceil(range) + time) {
                    result = { row, col, range, time };
                }
            }
        }
        return { row: result.row, col: result.col };
    }

    /**
     * 坐标点是否可进行挖掘
     * @param point
     * @param isHint 如果遇到不能挖掘的情况，是否进行提示
     * @param tool 使用的挖掘工具，默认为当前所选的工具
     * @returns {boolean}
     */
    private excavatable(
        point: IMiningPoint,
        isHint: boolean = false,
        tool: EnumMiningPropType = this.curTool
    ): boolean {
        // 先判断坐标点是否合法
        const data = Mining.getInstance().getMiningDataByPos(point.row, point.col);
        if (!data) {
            return false;
        }
        // 只能在可探索的位置使用
        if (!data.visible) {
            isHint && Tips.getInstance().info(i18n.mining0016);
            return false;
        }
        // 如果是密室中的奖励格，则只要有奖励就可以直接领取
        if (
            TBMiningSpace.getInstance().getDataById(data.id).type === EnumMiningSpaceType.Room &&
            data.rewards &&
            data.rewards.length > 0 &&
            !data.hasTake
        ) {
            return true;
        }
        switch (tool) {
            case EnumMiningPropType.IronPick:
            case EnumMiningPropType.GoldPick:
                // 铁镐和金镐，只能在富含矿物质的地形中使用
                if (data.times <= 0) {
                    isHint && Tips.getInstance().info(i18n.mining0017);
                    return false;
                }
                break;
            case EnumMiningPropType.Bit:
            case EnumMiningPropType.Explosive:
                // 钻头和炸弹只能在空地上使用
                if (data.times > 0) {
                    isHint && Tips.getInstance().info(i18n.mining0015);
                    return false;
                }
                break;
            default:
                break;
        }
        // 如果在挖掘范围内，没有任何可挖掘的地块，则该道具也不可使用
        const list = this.getToolExcavateRange(tool, point);
        let result = false;
        for (const item of list) {
            const data = Mining.getInstance().getMiningDataByPos(item.row, item.col);
            if (!data) {
                // 可能是越界数据
                continue;
            }
            if (data.times <= 0) {
                // 已经挖掘过了
                continue;
            }
            const config = TBMiningSpace.getInstance().getDataById(data.id);
            if (config.cleanTimes <= 0) {
                // 空地不需要挖掘
                continue;
            }
            result = true;
        }
        if (!result) {
            isHint && Tips.getInstance().info(i18n.mining0013);
        }
        return result;
    }

    /**
     * 根据条件判定是否显示工具按钮
     * @param tool 工具类型
     * @returns {boolean}
     */
    private isBtnDisplay(tool: EnumMiningPropType): boolean {
        let result = true;
        switch (tool) {
            case EnumMiningPropType.IronPick:
                result = Mining.getInstance().getMiningToolItemCountByType(tool) > 0;
                break;
            case EnumMiningPropType.GoldPick:
                result =
                    Mining.getInstance().getMiningToolItemCountByType(EnumMiningPropType.IronPick) <= 0 &&
                    (Mining.getInstance().getMiningToolItemCountByType(tool) > 0 ||
                        !Ad.getInstance().hasWatchTimesById(AdConfigId.MiningGetGoldPick));
                break;
            case EnumMiningPropType.Explosive:
            case EnumMiningPropType.Bit:
                result =
                    Mining.getInstance().getMiningToolItemCountByType(tool) > 0 ||
                    !Ad.getInstance().hasWatchTimesById(this.getAdvertisementIdByTool(tool));
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 根据条件判定是否显示工具查看广告按钮
     * @param tool 工具类型
     * @returns {boolean}
     */
    private isAdBtnDisplay(tool: EnumMiningPropType): boolean {
        let result = true;
        switch (tool) {
            case EnumMiningPropType.IronPick:
            case EnumMiningPropType.GoldPick:
                result =
                    Mining.getInstance().getMiningToolItemCountByType(EnumMiningPropType.IronPick) <= 0 &&
                    Mining.getInstance().getMiningToolItemCountByType(EnumMiningPropType.GoldPick) <= 0 &&
                    Ad.getInstance().hasWatchTimesById(this.getAdvertisementIdByTool(tool));
                break;
            case EnumMiningPropType.Explosive:
            case EnumMiningPropType.Bit:
                result =
                    Mining.getInstance().getMiningToolItemCountByType(tool) <= 0 &&
                    Ad.getInstance().hasWatchTimesById(this.getAdvertisementIdByTool(tool));
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 挖掘
     * @param point
     * @returns
     */
    private excavate(point: IMiningPoint): void {
        const data = Mining.getInstance().getMiningDataByPos(point.row, point.col);
        const isRoom =
            TBMiningSpace.getInstance().getDataById(data.id).type === EnumMiningSpaceType.Room &&
            data.rewards &&
            data.rewards.length > 0 &&
            !data.hasTake;
        if (!isRoom && Mining.getInstance().getMiningToolItemCountByType(this.curTool) <= 0) {
            const id = this.getToolsId(this.curTool);
            UI.getInstance().open("FloatItemSource", id);
            return;
        }
        if (!this.excavatable(point, true)) {
            return;
        }
        this.isAnimation = true;
        this.excavatePoint = point;
        this.excavateTool = this.curTool;
        const colData = Mining.getInstance().getMiningDataByPos(point.row, point.col);
        const colConfig = TBMiningSpace.getInstance().getDataById(colData.id);
        if (
            colConfig.type === EnumMiningSpaceType.Room &&
            colData.rewards &&
            colData.rewards.length > 0 &&
            !colData.hasTake
        ) {
            // 领取奖励
            Mining.getInstance().sendMiningTakeAward(point.row, point.col);
        } else {
            // 挖掘
            Mining.getInstance().sendMiningStart(point.row, point.col, this.curTool);
        }
    }

    /**
     * 自动挖掘
     * @returns {void}
     */
    private auto(): void {
        this.updateButtons();
        if (![EnumMiningPropType.IronPick, EnumMiningPropType.GoldPick].includes(this.curTool)) {
            this.isAuto = false;
            this.playBtnAnimation();
            Tips.getInstance().info(i18n.mining0025);
            return;
        }
        if (Mining.getInstance().getMiningToolItemCountByType(this.curTool) <= 0) {
            this.isAuto = false;
            this.playBtnAnimation();
            Tips.getInstance().info(i18n.mining0018);
            return;
        }
        this.autoExcavatePath = this.autoExcavatePath || this.getAutoExcavatePoint();
        this.excavatePoint = this.autoExcavatePath[0];
        this.excavate(this.autoExcavatePath[0]);
    }

    /**
     * 手指开始触摸，或者触摸滑动选定不同的方格
     * @param event
     */
    private onTouchStartOrMove(event: cc.Event.EventTouch): void {
        if (this.isAnimation) {
            return;
        }
        const location = event.getLocation();
        const point = this.nodeScrollView.convertToNodeSpaceAR(location);
        const col = Math.floor(point.x / this.nodeItem.width) + MAX_COLUMN / 2;
        const row = Math.floor(-point.y / this.nodeItem.height);
        this.onChoose({ col, row });
        this.onDrag(location, { row, col });
    }

    /**
     * 拖拽时，显示正在使用的挖掘工具
     * @param location
     * @returns
     */
    private onDrag(location: cc.Vec2, point?: IMiningPoint): void {
        // 如果选中了密室中的道具，不需要消耗挖掘道具即可获取，在拖拽时不需要显示图标
        if (point) {
            const colData = Mining.getInstance().getMiningDataByPos(point.row, point.col);
            if (colData) {
                const miningSpaceConfig = TBMiningSpace.getInstance().getDataById(colData.id);
                if (
                    miningSpaceConfig.type === EnumMiningSpaceType.Room &&
                    colData.rewards &&
                    colData.rewards.length > 0 &&
                    !colData.hasTake
                ) {
                    this.spDragTool.active = false;
                    CocosExt.setNodeOrCompData(this.spDragTool, "item", null);
                    this.spineFire.node.active = false;
                    this.spineFire.clearTracks();
                    return;
                }
            }
        }
        // 取消选中时，清除动效
        let pos: cc.Vec2 = null;
        if (location) {
            pos = this.node.child("aniNode").convertToNodeSpaceAR(location);
        }
        if (!location || pos.y > this.nodeScrollView.y || pos.y < this.nodeScrollView.y - this.nodeScrollView.height) {
            this.spDragTool.active = false;
            CocosExt.setNodeOrCompData(this.spDragTool, "item", null);
            this.spineFire.node.active = false;
            this.spineFire.clearTracks();
            return;
        }
        this.spDragTool.setPosition(pos);
        this.spDragTool.active = true;

        const itemInfoId = Mining.getInstance().getMiningToolItemIdByType(this.curTool);
        const data = CocosExt.getNodeOrCompData(this.spDragTool, "item");
        if (data !== itemInfoId) {
            switch (this.curTool) {
                case EnumMiningPropType.IronPick:
                case EnumMiningPropType.GoldPick:
                    this.spDragTool.sprite(this.resPickaxe[this.curTool]);
                    break;
                case EnumMiningPropType.Bit:
                case EnumMiningPropType.Explosive:
                    ImageUtils.setItemIcon(this.spDragTool, itemInfoId);
                    break;
                default:
                    break;
            }
            CocosExt.setNodeOrCompData(this.spDragTool, "item", itemInfoId);
            if (this.curTool === EnumMiningPropType.Explosive) {
                this.spineFire.node.active = true;
                this.spineFire.setAnimation(0, "wait", true);
            } else {
                this.spineFire.node.active = false;
                this.spineFire.clearTracks();
            }
        }
    }

    /**
     * 选定一个方格
     * @param point 选定方格点
     * @returns {void}
     */
    private onChoose(point: IMiningPoint): void {
        // 选点越界
        if (!point || point.row >= MAX_ROW || point.col >= MAX_COLUMN || point.row < 0 || point.col < 0) {
            this.curPos = null;
            this.updateContent();
            return;
        }
        // 选择相同点
        if (this.curPos && point.col === this.curPos.col && point.row === this.curPos.row) {
            return;
        }
        this.curPos = point;
        const able = this.excavatable(point);
        const pointList = this.getToolExcavateRange(this.curTool, this.curPos);
        for (const nodeLine of this.nodeContent.children) {
            for (const node of nodeLine.children) {
                const data: string = CocosExt.getButtonData(node);
                const [row, col] = data.split("#").map((item) => Number.parseInt(item));
                const index = pointList.findIndex((item) => item.row === row && item.col === col);
                node.child("spMaskEnable").active = index >= 0 && able;
                node.child("spMaskDisable").active = index >= 0 && !able;
            }
        }
    }

    /**
     * 触摸被松开停止时，即确定在当前方格使用挖掘工具
     * @param event
     */
    private onTouchEndOrCancel(event: cc.Event.EventTouch): void {
        let out = false;
        if (this.curPos) {
            out =
                this.curPos.row < 0 || this.curPos.row >= MAX_ROW || this.curPos.col < 0 || this.curPos.col >= MAX_ROW;
        }
        if (!this.isAnimation && this.curPos && !out) {
            this.excavate(this.curPos);
        }
        this.curPos = null;
        this.onDrag(null);
        for (const nodeLine of this.nodeContent.children) {
            for (const node of nodeLine.children) {
                node.child("spMaskEnable").active = false;
                node.child("spMaskDisable").active = false;
            }
        }
    }

    /**
     * 点击进行挖掘
     * @param sender
     * @param pos 挖掘位置
     * @returns
     */
    protected onClickExcavate(sender: cc.Event.EventTouch, pos: string): void {
        if (Mining.getInstance().getMiningToolItemCountByType(this.curTool) <= 0) {
            const id = this.getToolsId(this.curTool);
            UI.getInstance().open("FloatItemSource", id);
            return;
        }
        const [row, col] = pos.split("#").map((n) => Number.parseInt(n));
        if (!this.excavatable({ row, col })) {
            return;
        }
        Mining.getInstance().sendMiningStart(row, col, this.curTool);
    }

    /**
     * 点击更换挖掘工具
     * @param sender
     * @param data 挖掘工具类型
     * @returns
     */
    protected onClickSwitchTools(sender: cc.Event.EventTouch, data: string): void {
        if (this.isAuto) {
            Tips.getInstance().info(i18n.mining0012);
            return;
        }
        const tool = Number.parseInt(data);
        if (Mining.getInstance().getMiningToolItemCountByType(tool) <= 0) {
            const id = this.getToolsId(tool);
            UI.getInstance().open("FloatItemSource", id);
            return;
        }
        this.curTool = tool;
        this.updateButtons();
    }

    /**
     * 点击观看广告获得挖掘道具
     * @param sender
     * @param data 挖掘道具ID
     * @returns {void}
     */
    protected onClickWatchAdvertisement(sender: cc.Event.EventTouch, data: string): void {
        if (this.isAnimation) {
            // 正在播放动画
            if (this.isAuto) {
                // 如果是因为自动挖矿导致的按钮无法点击，进行提示
                Tips.getInstance().info(i18n.mining0032);
            }
            return;
        }
        const tool = Number.parseInt(data);
        const adId = this.getAdvertisementIdByTool(tool);
        if (!Ad.getInstance().hasWatchTimesById(adId)) {
            Tips.getInstance().info(i18n.ads0003);
            return;
        }
        if (Ad.getInstance().checkAdPrivilege(adId)) {
            Ad.getInstance().sendAdsWatch(adId);
        } else {
            let text = "";
            const adConfig = TBAdvertisement.getInstance().getDataById(adId);
            const shopConfig = TBShop.getInstance().getDataByAdvertisementId(adConfig.id);
            for (const reward of shopConfig.goods) {
                const [itemInfoId, num] = reward;
                const itemConfig = TBItem.getInstance().getDataById(itemInfoId);
                text += (text.length <= 0 ? "" : ",") + TextUtils.format(i18n.mining0024, num, itemConfig?.name || "");
            }
            UI.getInstance().open("FloatMiningWatchAdvertisement", {
                adId,
                adText: i18n.mining0026,
                tips: text ? TextUtils.format(i18n.mining0014, text) : "",
                res: "texture/mining/ad/spMinePicture",
            });
        }
    }

    /**
     * 点击开始自动挖掘
     * @returns {void}
     */
    protected onClickAuto(): void {
        if (this.isAuto) {
            // 关闭自动挖矿
            this.isAuto = false;
            this.playBtnAnimation();
            this.autoExcavatePath = null;
            Tips.getInstance().info(i18n.mining0023);
            return;
        }
        if (this.isAnimation) {
            // 正在播放动画期间无法操作
            return;
        }
        const privilegeConfigList = TBPrivilegeGroup.getInstance().getDataByType(EnumPrivilegeGroupType.MiningCard);
        if (privilegeConfigList.findIndex((item) => Privilege.getInstance().isUnlock(item.id)) < 0) {
            // 未解锁特权卡，无法自动挖矿
            UI.getInstance().open("PopupPrivilegeCard", RECHARGE_ID.CARD_AUTO_MINING);
            return;
        }
        if (![EnumMiningPropType.IronPick, EnumMiningPropType.GoldPick].includes(this.curTool)) {
            Tips.getInstance().info(i18n.mining0022);
            return;
        }
        if (
            Mining.getInstance().getMiningToolItemCountByType(EnumMiningPropType.IronPick) <= 0 &&
            Mining.getInstance().getMiningToolItemCountByType(EnumMiningPropType.GoldPick) <= 0
        ) {
            Tips.getInstance().info(i18n.mining0018);
            return;
        }
        UI.getInstance().open("PopupMiningAuto", {
            confirmCallBack: () => {
                if (!this.isAuto) {
                    this.isAuto = true;
                    this.playBtnAnimation();
                    this.auto();
                }
            },
        });
    }

    private getToolsId(tool: EnumMiningPropType | number): number {
        let itemId = 0;
        switch (tool) {
            case EnumMiningPropType.IronPick:
                itemId = ITEM_ID.MINING_IRON_PICKAXE;
                break;
            case EnumMiningPropType.GoldPick:
                itemId = ITEM_ID.MINING_GOLD_PICKAXE;
                break;
            case EnumMiningPropType.Bit:
                itemId = ITEM_ID.MINING_BIT;
                break;
            case EnumMiningPropType.Explosive:
                itemId = ITEM_ID.MINING_EXPLOSIVE;
                break;
            default:
                break;
        }
        return itemId;
    }

    /**
     * 点击打开矿山商店
     */
    protected onClickOpenShop(): void {
        UI.getInstance().open("PopupPrivilegeCard", RECHARGE_ID.CARD_AUTO_MINING);
    }

    protected onClickOpenMiningResearch(): void {
        UI.getInstance().open("PopupMiningResearch");
    }

    /**
     * 点击打开道具详情
     * @param sender
     * @param itemInfoId
     */
    protected onClickItemInfo(sender: cc.Event.EventTouch, itemInfoId: number): void {
        ItemUtils.showInfo(itemInfoId);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
