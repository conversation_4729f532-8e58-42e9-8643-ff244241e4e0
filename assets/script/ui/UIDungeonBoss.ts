/*
 * @Author: chenx
 * @Date: 2024-09-18 14:45:28
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:40:10
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { DungeonBossRewardRet, DungeonBossSweetRewardRet, ShopPurchaseRet } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import { EnumDungeonType } from "../data/base/BaseDungeon";
import TBDungeon from "../data/parser/TBDungeon";
import TBDungeonBoss from "../data/parser/TBDungeonBoss";
import TBPrivilegeConfig, { PRIVILEGE_ID } from "../data/parser/TBPrivilegeConfig";
import Bag from "../game/Bag";
import { DungeonType } from "../game/Combat";
import DungeonBoss from "../game/DungeonBoss";
import Privilege from "../game/Privilege";
import Shop from "../game/Shop";
import ImageUtils from "../utils/ImageUtils";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class UIDungeonBoss extends I18nComponent {
    @property(cc.Node)
    nodeTitle: cc.Node = null;
    @property(cc.Node)
    title: cc.Node = null;
    @property(cc.Node)
    monster: cc.Node = null;
    @property(cc.Node)
    lock: cc.Node = null;

    @property(cc.Node)
    btnLeft: cc.Node = null;
    @property(cc.Node)
    btnRight: cc.Node = null;

    @property(cc.Node)
    rewards: cc.Node = null;

    @property(cc.Node)
    btnCombat: cc.Node = null;
    @property(cc.Node)
    btnCombatRed: cc.Node = null;
    @property(cc.Node)
    btnSweep: cc.Node = null;
    @property(cc.Node)
    btnSweepRed: cc.Node = null;
    @property(cc.Node)
    costIcon: cc.Node = null;
    @property(cc.Node)
    costCount: cc.Node = null;
    @property(cc.Node)
    tips: cc.Node = null;
    @property(cc.Node)
    nodeSweep2: cc.Node = null; // 扫荡

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private curSelectedDungeonId: number = 0;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.btnCombat,
                url: "texture/syncUI/dungeon/btnInstanceFight1",
            },
            {
                sprite: this.btnSweep,
                url: "texture/syncUI/dungeon/btnInstanceFight2",
            },
            {
                sprite: this.nodeTitle,
                url: "texture/syncUI/dungeon/spBBZJSystemTitle",
            },
            {
                sprite: this.nodeSweep2,
                url: "texture/syncUI/dungeon/btnInstanceFight4",
            },
        ];
    }

    protected onLoad(): void {
        this.curSelectedDungeonId = DungeonBoss.getInstance().getCurDungeonId();
        this.updateUI();
    }

    protected registerHandler(): void {
        DungeonBoss.getInstance().on(
            DungeonBossRewardRet.prototype.clazzName,
            () => {
                this.curSelectedDungeonId = DungeonBoss.getInstance().getCurDungeonId();
                this.updateUI();
            },
            this
        );
        DungeonBoss.getInstance().on(
            DungeonBossSweetRewardRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
        Shop.getInstance().on(
            ShopPurchaseRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        const curDungeonId = DungeonBoss.getInstance().getCurDungeonId();
        const data = TBDungeonBoss.getInstance().getDataById(this.curSelectedDungeonId);
        const unlockDungeonIds = DungeonBoss.getInstance().getUnlockDungeonIds();
        const isPass = unlockDungeonIds.includes(this.curSelectedDungeonId);
        this.title.label(TextUtils.format(i18n.dungeon0042, data.grade));
        this.monster.color = this.curSelectedDungeonId > curDungeonId ? cc.color(80, 80, 80) : cc.color(255, 255, 255);
        SpineUtils.setMonster(this.monster, data.monster[data.monster.length - 1][0][0], "wait");
        this.rewards.destroyAllChildren();
        if (!isPass) {
            for (const e of data.firstReward) {
                const item = Loader.getInstance().instantiate(this.prefabItem);
                item.getComponent(this.prefabItem.name).updateData(e, true);
                item.parent = this.rewards;
            }
        }
        for (const e of data.reward) {
            const item = Loader.getInstance().instantiate(this.prefabItem);
            item.getComponent(this.prefabItem.name).updateData(e, false);
            item.parent = this.rewards;
        }

        const preDungeon = TBDungeonBoss.getInstance().getDataById(this.curSelectedDungeonId - 1);
        const nextDungeon = TBDungeonBoss.getInstance().getDataById(this.curSelectedDungeonId + 1);
        this.btnLeft.active = !!preDungeon;
        this.btnRight.active = this.curSelectedDungeonId + 1 <= curDungeonId + 10 && !!nextDungeon;
        this.lock.active = this.curSelectedDungeonId > curDungeonId;
        this.btnCombat.active = !unlockDungeonIds.includes(curDungeonId);
        if (this.btnCombat.active) {
            this.btnCombat.getComponent(GrayComp).gray = this.curSelectedDungeonId > curDungeonId;
            this.btnCombat.getComponent(cc.Button).interactable = this.curSelectedDungeonId <= curDungeonId;
        }
        this.btnSweep.active = isPass;

        const dungeonData = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonBoss);
        const [costId, costCount] = dungeonData.cost[0];

        const maxCount = this.getMaxCount();
        const count = Bag.getInstance().getItemCountById(costId);
        ImageUtils.setItemIcon(this.costIcon, costId);
        this.costCount.richText(
            TextUtils.format(count >= costCount ? i18n.common0072 : i18n.common0073, count, maxCount)
        );
        this.tips.label(TextUtils.format(i18n.dungeon0047, maxCount));

        this.btnCombatRed.active =
            this.curSelectedDungeonId <= curDungeonId && Bag.getInstance().isEnough(costId, costCount);
        this.btnSweepRed.active = Bag.getInstance().isEnough(costId, costCount);

        this.nodeSweep2.active = curDungeonId > 1 && this.curSelectedDungeonId === curDungeonId && !isPass;
    }

    private getMaxCount(): number {
        const data = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonBoss);
        let maxCount = 0;
        maxCount += data.recoveryTime[0];
        const privilegeIds = [PRIVILEGE_ID.DUNGEON_BOSS_MONTHLY, PRIVILEGE_ID.DUNGEON_BOSS_FOREVER];
        for (const e of privilegeIds) {
            if (Privilege.getInstance().hasPrivilege(e)) {
                const data = TBPrivilegeConfig.getInstance().getDataById(e);
                maxCount += data.para.value;
            }
        }
        return maxCount;
    }

    protected onClickLeft(): void {
        this.curSelectedDungeonId--;
        this.updateUI();
    }

    protected onClickRight(): void {
        this.curSelectedDungeonId++;
        this.updateUI();
    }

    protected onClickCombat(): void {
        const data = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonBoss);
        const [costId, costCount] = data.cost[0];
        if (!Bag.getInstance().isEnough(costId, costCount)) {
            UI.getInstance().open("FloatItemSource", costId);
            return;
        }

        UI.getInstance().open("UIDungeonCombatBoss", {
            type: DungeonType.Boss,
            levelId: this.curSelectedDungeonId,
        });
    }

    protected onClickSweep(): void {
        const data = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonBoss);
        const [costId, costCount] = data.cost[0];
        if (!Bag.getInstance().isEnough(costId, costCount)) {
            UI.getInstance().open("FloatItemSource", costId);
            return;
        }

        DungeonBoss.getInstance().sendDungeonBossSweetReward(this.curSelectedDungeonId, 1);
    }

    /**
     * 扫荡
     */
    protected onClickSweep2(): void {
        const dungeonId = DungeonBoss.getInstance().getCurDungeonId();
        if (dungeonId <= 1 || this.curSelectedDungeonId !== dungeonId) {
            return;
        }
        const dungeonInfo = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonBoss);
        const [costId, costNum] = dungeonInfo.cost[0];
        if (!Bag.getInstance().isEnough(costId, costNum)) {
            UI.getInstance().open("FloatItemSource", costId);
            return;
        }

        DungeonBoss.getInstance().sendDungeonBossSweetReward(this.curSelectedDungeonId - 1, 1);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
