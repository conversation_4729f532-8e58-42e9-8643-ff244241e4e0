/*
 * @Author: chenx
 * @Date: 2024-03-20 10:25:14
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:48:46
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import Combat, { CombatEvent, DungeonType } from "../game/Combat";

const { ccclass } = cc._decorator;

/**
 * 副本-暂停
 */
@ccclass
export default class FloatDungeonPause extends I18nComponent {
    private type: DungeonType = null; // 类型

    protected onLoad(): void {
        this.type = this.args;
    }

    /**
     * 退出
     */
    protected onClickQuit(): void {
        switch (this.type) {
            case DungeonType.Boss:
                UI.getInstance().closeToWindow("UIDungeonCombatBoss");
                UI.getInstance().close();
                break;
            case DungeonType.Cloud:
                UI.getInstance().closeToWindow("UIDungeonCombatCloud");
                UI.getInstance().close();
                break;
            case DungeonType.Thief:
                UI.getInstance().closeToWindow("UIDungeonCombatThief");
                UI.getInstance().close();
                break;
            case DungeonType.Tower:
                UI.getInstance().closeToWindow("UIDungeonCombatTower");
                UI.getInstance().close();
                break;
            case DungeonType.Union:
                UI.getInstance().closeToWindow("UIDungeonCombatUnion");
                UI.getInstance().close();
                UI.getInstance().open("PopupUnionBoss");
                break;
            case DungeonType.Trial:
                UI.getInstance().closeToWindow("UIDungeonCombatTrial");
                UI.getInstance().close();
                break;
            default:
                break;
        }
    }

    /**
     * 重置
     */
    protected onClickReset(): void {
        UI.getInstance().closeAllFloatWindow();
        Combat.getInstance().emit(CombatEvent.Reset);
    }

    /**
     * 取消暂停
     */
    protected onClickCancelPause(): void {
        UI.getInstance().closeAllFloatWindow();
        Combat.getInstance().emit(CombatEvent.CancelPause);
    }
}
