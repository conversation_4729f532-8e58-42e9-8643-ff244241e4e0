/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-07-18 10:13:44
 */

import I18nComponent from "../../nsn/i18n/I18nComponent";
import Minigame from "../../nsn/platform/Minigame";
import UI from "../../nsn/ui/UI";
import { MiniTakeAwardRet, MiniUpdateMomentsInfoRet } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import WechatMinigame from "../config/channel/channel/WechatMinigame";
import i18n from "../config/i18n/I18n";
import TBMiniGame, { MINIGAME_ID } from "../data/parser/TBMiniGame";
import GameClub, { GameClubDataType, GameClubScene } from "../game/GameClub";
import ItemUtils from "../utils/ItemUtils";

const { ccclass, property } = cc._decorator;

enum UIType {
    FollowAndLike,
    Share,
}

const TAB_ENUM = [UIType.FollowAndLike, UIType.Share];

@ccclass
export default class PopupGameClub extends I18nComponent {
    @property([cc.Node])
    btns: cc.Node[] = [];
    @property([cc.Node])
    nodes: cc.Node[] = [];

    @property(cc.Node)
    node1Rewards: cc.Node = null;
    @property(cc.Node)
    node1BtnGet: cc.Node = null;

    @property(cc.Node)
    node2Rewards: cc.Node = null;
    @property(cc.Node)
    node2BtnGet: cc.Node = null;
    @property(cc.Node)
    node2BtnShare: cc.Node = null;

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private curTab: UIType = UIType.FollowAndLike;
    private isClickShare: boolean = false;

    protected onLoad(): void {
        for (let i = 0; i < this.btns.length; i++) {
            this.btns[i].button(TAB_ENUM[i]);
        }
        this.updateUI();
    }

    protected registerHandler(): void {
        cc.game.on(
            cc.game.EVENT_SHOW,
            () => {
                this.onGameShow();
            },
            this
        );
        GameClub.getInstance().on(
            [MiniUpdateMomentsInfoRet.prototype.clazzName, MiniTakeAwardRet.prototype.clazzName],
            () => {
                this.updateUI();
            },
            this
        );
    }

    private onGameShow(): void {
        GameClub.getInstance().request((res) => {
            GameClub.getInstance().sendMiniUpdateMomentsInfo(res.encryptedData, res.iv, GameClubScene.UpdateInfo);
        });
    }

    private updateUI(): void {
        const info = GameClub.getInstance().getMiniGameInfo();
        switch (this.curTab) {
            case UIType.FollowAndLike:
                {
                    const data = TBMiniGame.getInstance().getDataById(MINIGAME_ID.FOLLOW_AND_LIKE);
                    ItemUtils.refreshView(this.node1Rewards, this.prefabItem, data.rewards);

                    const isGot = info.normalRecords.find((v) => v.awardId === MINIGAME_ID.FOLLOW_AND_LIKE);
                    const data1 = GameClub.getInstance().getClubDataByType(GameClubDataType.JoinClub);
                    const data2 = GameClub.getInstance().getClubDataByType(GameClubDataType.Like);
                    const canGet = data1 > 0 && data2 >= data.num;

                    const text = this.node1BtnGet.child("text");
                    if (isGot) {
                        text.label(i18n.common0089);
                        this.node1BtnGet.getComponent(cc.Button).interactable = false;
                        this.node1BtnGet.getComponent(GrayComp).gray = true;
                    } else {
                        text.label(i18n.common0030);
                        this.node1BtnGet.getComponent(cc.Button).interactable = canGet;
                        this.node1BtnGet.getComponent(GrayComp).gray = !canGet;
                    }
                }
                break;
            case UIType.Share:
                {
                    const data = TBMiniGame.getInstance().getDataById(MINIGAME_ID.SHARE);
                    ItemUtils.refreshView(this.node2Rewards, this.prefabItem, data.rewards);
                    const isGot = info.normalRecords.find((v) => v.awardId === MINIGAME_ID.SHARE);

                    if (isGot) {
                        this.node2BtnGet.active = true;
                        const text = this.node2BtnGet.child("text");
                        text.label(i18n.common0089);
                        this.node2BtnGet.getComponent(cc.Button).interactable = false;
                        this.node2BtnGet.getComponent(GrayComp).gray = true;
                        this.node2BtnShare.active = false;
                    } else {
                        const systemInfo = wx.getSystemInfoSync();
                        let isEnterFromWechatShare = false;
                        if (systemInfo.platform === "windows" || systemInfo.platform === "mac") {
                            isEnterFromWechatShare = this.isClickShare;
                        } else {
                            isEnterFromWechatShare = WechatMinigame.getInstance().isEnterFromWechatShare();
                        }

                        if (isEnterFromWechatShare) {
                            this.node2BtnGet.active = true;
                            const text = this.node2BtnGet.child("text");
                            text.label(i18n.common0030);
                            this.node2BtnGet.getComponent(cc.Button).interactable = true;
                            this.node2BtnGet.getComponent(GrayComp).gray = false;
                            this.node2BtnShare.active = false;
                        } else {
                            this.node2BtnGet.active = false;
                            this.node2BtnShare.active = true;
                        }
                    }
                }
                break;
            default:
                break;
        }
    }

    protected onClickTab(sender: cc.Event.EventTouch, tab: UIType): void {
        if (this.curTab === tab) {
            return;
        }

        this.curTab = tab;
        for (let i = 0; i < this.nodes.length; i++) {
            this.nodes[i].active = this.curTab === i;
        }
        this.updateUI();
    }

    protected onClickGetReward(): void {
        switch (this.curTab) {
            case UIType.FollowAndLike:
                GameClub.getInstance().request((res) => {
                    GameClub.getInstance().sendMiniUpdateMomentsInfo(
                        res.encryptedData,
                        res.iv,
                        GameClubScene.TakeAward
                    );
                });
                break;
            case UIType.Share:
                GameClub.getInstance().sendMiniTakeAward(MINIGAME_ID.SHARE);
                break;
            default:
                break;
        }
    }

    protected onClickShare(): void {
        const systemInfo = wx.getSystemInfoSync();
        if (systemInfo.platform === "windows" || systemInfo.platform === "mac") {
            this.isClickShare = true;
            this.updateUI();
        } else {
            Minigame.getInstance().share(null);
        }
    }

    protected onClickOpenGameClub(): void {
        WechatMinigame.getInstance().openGameClub();
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
