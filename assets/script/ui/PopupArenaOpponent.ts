/*
 * @Author: <PERSON>y<PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:51:37
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { NodeUtils } from "../../nsn/util/NodeUtils";
import TextUtils from "../../nsn/util/TextUtils";
import Time from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import { ArenaRefreshRet, BagUpdateRet, IChallengerInfo } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import Guide from "../core/Guide";
import { EnumArenaPara } from "../data/base/BaseArena";
import TBArena from "../data/parser/TBArena";
import { ITEM_ID } from "../data/parser/TBItem";
import { RANK_ID } from "../data/parser/TBRank";
import Arena from "../game/Arena";
import Bag from "../game/Bag";
import { DungeonType } from "../game/Combat";
import Rank from "../game/Rank";
import CombatScoreUtils from "../utils/CombatScoreUtils";
import ImageUtils from "../utils/ImageUtils";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupArenaOpponent extends I18nComponent {
    @property([cc.Node])
    rewardIcon: cc.Node[] = [];
    @property([cc.Node])
    rewardCount: cc.Node[] = [];

    @property(cc.Node)
    opponents: cc.Node = null;

    @property(cc.Node)
    mine: cc.Node = null;

    @property(cc.Node)
    resIcon: cc.Node = null;
    @property(cc.Node)
    resCount: cc.Node = null;
    @property(cc.Node)
    btnResOp: cc.Node = null;

    @property(cc.Node)
    btnRefresh: cc.Node = null;
    @property(cc.Node)
    btnRefreshText: cc.Node = null;

    private dt: number = 1;

    protected onLoad(): void {
        this.updateInfo();
        this.updateRes();
        const challengerInfo = Arena.getInstance().getChallengeInfo();
        if (challengerInfo.length) {
            this.updateList();
        } else {
            Arena.getInstance().sendArenaRefresh();
            UI.getInstance().showSoftLoading(NodeUtils.getWorldPosOfCenter(this.opponents));
        }
    }

    protected onDestroy(): void {
        UI.getInstance().hideSoftLoading();
    }

    protected registerHandler(): void {
        Arena.getInstance().on(
            ArenaRefreshRet.prototype.clazzName,
            () => {
                this.updateList();
            },
            this
        );

        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            () => {
                this.updateRes();
            },
            this
        );
    }

    protected update(dt: number): void {
        this.dt += dt;
        if (this.dt < 1) {
            return;
        }
        this.dt--;
        this.updateTime();
    }

    private updateInfo(): void {
        const reward = TBArena.getInstance().getValueByPara(EnumArenaPara.ChallengeWinReward);
        for (let i = 0; i < reward.length; i++) {
            ImageUtils.setItemIcon(this.rewardIcon[i], reward[i][0]);
            this.rewardCount[i].label(reward[i][1]);
        }

        const { myRank } = Rank.getInstance().getDataById(RANK_ID.ARENA);
        const icon = this.mine.child("icon");
        const layout = this.mine.child("layout");
        const name = layout.child("name");
        const combatScore = layout.child("combatScore");
        const score = this.mine.child("score");
        const scoreIcon = score.child("icon");
        const scoreCount = score.child("count");
        PlayerInfoUtils.updateHead(icon, myRank.player);
        name.label(myRank.player.name);
        CombatScoreUtils.update(combatScore, myRank.player.combatScore);
        scoreCount.label(myRank.score + "");
        ImageUtils.setItemIcon(scoreIcon, ITEM_ID.ARENA_SCORE);
    }

    private updateRes(): void {
        const freeTimes = TBArena.getInstance().getValueByPara(EnumArenaPara.PkFreeTimes);
        const usedTimes = Arena.getInstance().getUsedFreeChallengeTimes();
        this.resCount.label(
            freeTimes > usedTimes
                ? TextUtils.format(i18n.arena0009, freeTimes - usedTimes, freeTimes)
                : Bag.getInstance().getCountFormatById(ITEM_ID.ARENA_TICKET)
        );
        ImageUtils.setItemIcon(this.resIcon, ITEM_ID.ARENA_TICKET);
        this.btnResOp.active = usedTimes >= freeTimes;
    }

    private updateList(): void {
        const { myRank } = Rank.getInstance().getDataById(RANK_ID.ARENA);
        const challengerInfo = Arena.getInstance().getChallengeInfo();
        for (let i = 0; i < this.opponents.childrenCount; i++) {
            const node = this.opponents.children[i];
            if (challengerInfo[i]) {
                node.active = true;

                const icon = node.child("icon");
                const layout = node.child("layout");
                const name = layout.child("name");
                const combatScore = layout.child("combatScore");
                const score = node.child("score");
                const scoreIcon = score.child("icon");
                const btnCombat = node.child("btnCombat");
                const scoreCount = score.child("count");
                PlayerInfoUtils.updateHead(icon, challengerInfo[i].playerInfo);
                name.label(challengerInfo[i].playerInfo.name);
                CombatScoreUtils.update(combatScore, challengerInfo[i].playerInfo.combatScore, {
                    color: challengerInfo[i].playerInfo.combatScore < myRank.player.combatScore ? "#688a28" : "a5413c",
                });
                scoreCount.label(challengerInfo[i].score + "");
                ImageUtils.setItemIcon(scoreIcon, ITEM_ID.ARENA_SCORE);
                node.button(challengerInfo[i].playerInfo.playerId);
                btnCombat.button(challengerInfo[i]);
                Guide.getInstance().setNodeData(btnCombat, i + "");
            } else {
                node.active = false;
            }
        }
    }

    private updateTime(): void {
        const cd = 6000; // 刷新时间
        const requestTime = Arena.getInstance().getRequestTime();
        const now = Time.getInstance().now();
        if (now - requestTime >= cd) {
            this.btnRefresh.getComponent(GrayComp).gray = false;
            this.btnRefresh.getComponent(cc.Button).interactable = true;
            this.btnRefreshText.label(i18n.common0024);
        } else {
            this.btnRefresh.getComponent(GrayComp).gray = true;
            this.btnRefresh.getComponent(cc.Button).interactable = false;
            this.btnRefreshText.label(
                TimeFormat.getInstance().getTextByDuration(requestTime + cd - now, TimeDurationFormatType.D_H_M_S_0)
            );
        }
    }

    protected onClickResOperation(): void {
        const shopId = TBArena.getInstance().getValueByPara(EnumArenaPara.PVPTicket);
        UI.getInstance().open("FloatShopItemDetail", { shopId });
    }

    protected onClickPlayer(sender: cc.Event.EventTouch, playerId: string): void {
        UI.getInstance().open("FloatOtherPlayerInfo", playerId);
    }

    protected onClickCombat(sender: cc.Event.EventTouch, opponent: IChallengerInfo): void {
        const freeTimes = TBArena.getInstance().getValueByPara(EnumArenaPara.PkFreeTimes);
        const usedTimes = Arena.getInstance().getUsedFreeChallengeTimes();
        const cost = TBArena.getInstance().getValueByPara(EnumArenaPara.PkCost);
        if (usedTimes >= freeTimes && !Bag.getInstance().isEnough(cost[0][0], cost[0][1])) {
            UI.getInstance().open("FloatItemSource", cost[0][0]);
            return;
        }

        Arena.getInstance().setPlayerCombatData(opponent);
        UI.getInstance().open("UIDungeonCombatArena", {
            type: DungeonType.Arena,
            resultCb: (isWin: boolean) => {
                Arena.getInstance().sendArenaChallenge(opponent.playerInfo.playerId, isWin);
            },
        });
    }

    protected onClickRefresh(): void {
        Arena.getInstance().sendArenaRefresh();
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
