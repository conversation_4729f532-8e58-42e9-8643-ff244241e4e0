/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-24 20:03:58
 */

import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatArenaEntry extends I18nComponent {
    @property(cc.Node)
    mineIcon: cc.Node = null;
    @property(cc.Node)
    mineName: cc.Node = null;
    @property(cc.Node)
    otherIcon: cc.Node = null;
    @property(cc.Node)
    otherName: cc.Node = null;

    protected onLoad(): void {
        const { playerName, leadId, playerName2, leadId2 } = this.args;

        const mySkinData = TBLeadSkin.getInstance().getDataById(leadId);
        SpineUtils.setLead(this.mineIcon, mySkinData.res);
        this.mineName.label(playerName);

        const otherSkinData = TBLeadSkin.getInstance().getDataById(leadId2);
        SpineUtils.setLead(this.otherIcon, otherSkinData.res);
        this.otherName.label(playerName2);

        this.scheduleOnce(() => {
            UI.getInstance().closeFloatWindow(this.node.name);
        }, 2);
    }
}
