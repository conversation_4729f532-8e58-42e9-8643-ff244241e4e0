/*
 * @Author: Jacky<PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-14 17:20:34
 */
import LocalStorage from "../../nsn/core/LocalStorage";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import { BoxOpen, BoxOpenRet, BoxProgressBatchRewardRet, BoxProgressRewardRet, IItemInfo } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import { LocalStorageKey } from "../config/LocalStorageConfig";
import { EnumUniversalPara } from "../data/base/BaseUniversal";
import TBBoxSystem from "../data/parser/TBBoxSystem";
import TBBoxSystemProgress from "../data/parser/TBBoxSystemProgress";
import TBItem from "../data/parser/TBItem";
import TBUniversal from "../data/parser/TBUniversal";
import Bag from "../game/Bag";
import Box, { BoxEvent } from "../game/Box";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import TweenUtil from "../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupBox extends I18nComponent {
    @property(cc.Node)
    progressDesc: cc.Node = null;
    @property(cc.Node)
    progress: cc.Node = null;
    @property(cc.Node)
    progressText: cc.Node = null;
    @property(cc.Node)
    itemIcon: cc.Node = null;
    @property(cc.Node)
    mask: cc.Node = null; // 进度条动画遮罩
    @property(cc.Node)
    fly1: cc.Node = null;
    @property(cc.Node)
    fly2: cc.Node = null;
    @property(cc.Node)
    fly3: cc.Node = null;
    @property(cc.Node)
    fly4: cc.Node = null;
    @property(cc.Node)
    fly5: cc.Node = null;
    @property(cc.Node)
    fly6: cc.Node = null;
    @property(cc.Node)
    progressLight: cc.Node = null;
    @property(cc.Node)
    progressIcon: cc.Node = null;
    @property(cc.Node)
    spBoxName: cc.Node = null;
    @property(cc.Node)
    desc: cc.Node = null;
    @property(cc.Node)
    boxIconSpine: cc.Node = null;
    @property(cc.Node)
    btnBox: cc.Node = null;
    @property(cc.Node)
    btnBoxText: cc.Node = null;
    @property(cc.Toggle)
    toggle: cc.Toggle = null;
    @property(cc.Node)
    blockInputNode: cc.Node = null;

    @property([cc.Node])
    btns: cc.Node[] = [];

    private curId: number = 0; // 当前选中宝箱id

    protected onLoad(): void {
        const data = TBBoxSystem.getInstance()
            .getList()
            .map((e) => e.id);
        for (let i = 0; i < this.btns.length; i++) {
            this.btns[i].button(data[i]);
        }
        this.curId = data[0];
        const localData = LocalStorage.getInstance().getItem(LocalStorageKey.BoxSkinAni);
        this.toggle.isChecked = localData ? JSON.parse(localData) : false;
        this.updateTabUI();
        this.updateUI();
        this.updateRed();
    }

    protected registerHandler(): void {
        Box.getInstance().on(
            [BoxProgressRewardRet.prototype.clazzName, BoxProgressBatchRewardRet.prototype.clazzName],
            () => {
                this.updateTabUI();
                this.updateUI();
                this.updateRed();
            },
            this
        );

        Box.getInstance().on(
            BoxOpenRet.prototype.clazzName,
            (showItems: IItemInfo[]) => {
                this.blockInput(true);
                this.updateTabUI();
                this.updateOpenBtnUI();
                this.updateRed();
                if (!this.toggle.isChecked) {
                    const boxData = TBBoxSystem.getInstance().getDataById(this.curId);
                    const spine = this.boxIconSpine.getComponent(sp.Skeleton);
                    spine.clearTracks();
                    CocosExt.setSkeletonAsync(this.boxIconSpine, `spine/box/efChest${boxData.res}`, () => {
                        spine.setAnimation(0, "wait2", true);
                    });
                    this.scheduleOnce(() => {
                        spine.setAnimation(0, "wait", true);
                        UI.getInstance().open("FloatReward", {
                            gotItem: showItems,
                            srcReq: BoxOpen.prototype.clazzName,
                        });
                        this.blockInput(false);
                    }, 1.1);
                } else {
                    UI.getInstance().open("FloatReward", {
                        gotItem: showItems,
                        srcReq: BoxOpen.prototype.clazzName,
                    });
                    this.blockInput(false);
                }
            },
            this
        );

        Box.getInstance().on(
            BoxEvent.Close,
            () => {
                this.updateRed();
                const boxData = TBBoxSystem.getInstance().getDataById(this.curId);
                if (boxData.points !== 0) {
                    this.playFlyItemAni();
                } else {
                    this.updateTabUI();
                    this.updateUI();
                }
            },
            this
        );
    }

    private updateUI(progressAni: boolean = false): void {
        const boxInfo = Box.getInstance().getBoxInfo();
        const data = TBBoxSystemProgress.getInstance().getDataById(boxInfo.boxProgressId);
        const progressSpill = Box.getInstance().getProgressSpill();
        const progressObj = Box.getInstance().getCurrentIdProgressObj();
        const itemData = TBItem.getInstance().getDataById(data.reward[0][0]);

        if (progressAni) {
            cc.tween(this.progress.getComponent(cc.ProgressBar))
                .to(
                    0.6,
                    {
                        progress: progressObj.allProgress / progressObj.nowProgress,
                    },
                    {
                        progress: (start, end, current, ratio) => {
                            const currentProgress = start + (end - start) * ratio; // 计算当前进度
                            this.progressText.label(
                                `${Math.floor(currentProgress * progressObj.nowProgress)}/${progressObj.nowProgress}` // 更新文本数值
                            );
                            return currentProgress;
                        },
                    }
                )
                .call(() => {
                    if (progressSpill) {
                        this.progressDesc.richText(TextUtils.format(i18n.box0002, itemData.name));
                    } else {
                        this.progressDesc.richText(
                            TextUtils.format(
                                i18n.box0001,
                                progressObj.nowProgress - progressObj.allProgress,
                                itemData.name
                            )
                        );
                    }
                    this.progress.progress(progressObj.allProgress / progressObj.nowProgress);
                    this.progressText.label(progressObj.allProgress + "/" + progressObj.nowProgress); // 确保数值正确
                })
                .start();
        } else {
            if (progressSpill) {
                this.progressDesc.richText(TextUtils.format(i18n.box0002, itemData.name));
            } else {
                this.progressDesc.richText(
                    TextUtils.format(i18n.box0001, progressObj.nowProgress - progressObj.allProgress, itemData.name)
                );
            }
            this.progress.progress(progressObj.allProgress / progressObj.nowProgress);
            this.progressText.label(progressObj.allProgress + "/" + progressObj.nowProgress);
        }

        ImageUtils.setItemIcon(this.progressIcon, Number(itemData.res));
        const red = this.progressIcon.child("red");
        if (progressObj.allProgress >= progressObj.nowProgress) {
            this.mask.active = true;
            this.progressLight.stopAllActions();
            this.progressLight.x = -88;
            cc.tween(this.progressLight)
                .repeatForever(
                    cc
                        .tween()
                        .to(2, {
                            x: 636,
                        })
                        .call(() => {
                            this.progressLight.x = -352;
                        })
                )
                .start();
            TweenUtil.swing(this.progressIcon, true);
            red.active = true;
        } else {
            this.mask.active = false;
            red.active = false;
            TweenUtil.swing(this.progressIcon, false, 10);
        }

        this.updateOpenBtnUI();
    }

    private updateOpenBtnUI(): void {
        const boxData = TBBoxSystem.getInstance().getDataById(this.curId);
        const isEnough = Bag.getInstance().isEnough(boxData.id, 1);
        CocosExt.setSkeletonAsync(this.boxIconSpine, `spine/box/efChest${boxData.res}`, () => {
            this.boxIconSpine.getComponent(sp.Skeleton).setAnimation(0, "wait", false);
            this.boxIconSpine.color = !isEnough ? "#BEBEBE" : "FFFFFF";
        });
        ImageUtils.setBoxNameIcon(this.spBoxName, boxData.res);
        this.desc.richText(boxData.desc);

        const hasCount = Bag.getInstance().getItemCountById(boxData.id);
        if (hasCount < boxData.openUpperLimit) {
            this.btnBoxText.label(i18n.box0003 + "×" + hasCount);
        } else {
            this.btnBoxText.label(i18n.box0003 + "×" + boxData.openUpperLimit);
        }
        CocosExt.setButtonEnable(this.btnBox, isEnough);
        this.btnBox.getComponent(GrayComp).gray = !isEnough;
    }

    private updateRed(): void {
        const data = TBBoxSystem.getInstance()
            .getList()
            .map((e) => e.id);
        for (let i = 0; i < this.btns.length; i++) {
            const red = this.btns[i].child("red");
            const info = TBBoxSystem.getInstance().getDataById(data[i]);
            red.active = Bag.getInstance().isEnough(info.id, 1);
        }
        const boxData = TBBoxSystem.getInstance().getDataById(this.curId);
        this.btnBox.child("red").active = Bag.getInstance().isEnough(boxData.id, 1);
    }

    private playFlyItemAni(): void {
        this.fly1.active = true;
        this.fly1.scale = 0;
        cc.tween(this.fly1)
            .parallel(
                cc.tween().bezierTo(0.3, cc.v2(-50, 100), cc.v2(-40, -30), cc.v2(-150, 100)),
                cc.tween().to(0.3, { scale: 1.2 })
            )
            .delay(0)
            .parallel(
                cc.tween().bezierTo(0.4, cc.v2(-420, 200), cc.v2(-380, 400), cc.v2(-390, 624)),
                cc.tween().to(0.4, { scale: 0.6 })
            )
            .call(() => {
                this.fly1.active = false;
                this.fly1.x = 0;
                this.fly1.y = 0;
                this.fly1.scale = 0;
                this.playItemIconAni();
                this.updateTabUI();
                this.updateUI(true);
            })
            .start();

        this.fly2.active = true;
        this.fly2.scale = 0;
        cc.tween(this.fly2)
            .parallel(
                cc.tween().bezierTo(0.3, cc.v2(160, 100), cc.v2(240, 30), cc.v2(280, 120)),
                cc.tween().to(0.3, { scale: 1.2 })
            )
            .delay(0.15)
            .parallel(
                cc.tween().bezierTo(0.4, cc.v2(-420, 200), cc.v2(-380, 400), cc.v2(-390, 624)),
                cc.tween().to(0.4, { scale: 0.6 })
            )
            .call(() => {
                this.fly2.active = false;
                this.fly2.x = 0;
                this.fly2.y = 0;
                this.fly2.scale = 0;
                this.playItemIconAni();
            })
            .start();

        this.fly3.active = true;
        this.fly3.scale = 0;
        cc.tween(this.fly3)
            .parallel(
                cc.tween().bezierTo(0.3, cc.v2(60, 100), cc.v2(40, 30), cc.v2(100, 140)),
                cc.tween().to(0.3, { scale: 1.2 })
            )
            .delay(0.3)
            .parallel(
                cc.tween().bezierTo(0.4, cc.v2(-420, 200), cc.v2(-380, 400), cc.v2(-390, 624)),
                cc.tween().to(0.4, { scale: 0.6 })
            )
            .call(() => {
                this.fly3.active = false;
                this.fly3.x = 0;
                this.fly3.y = 0;
                this.fly3.scale = 0;
                this.playItemIconAni();
            })
            .start();

        this.fly4.active = true;
        this.fly4.scale = 0;
        cc.tween(this.fly4)
            .parallel(
                cc.tween().bezierTo(0.3, cc.v2(-10, -100), cc.v2(-140, -30), cc.v2(-100, 20)),
                cc.tween().to(0.3, { scale: 1.2 })
            )
            .delay(0.45)
            .parallel(
                cc.tween().bezierTo(0.4, cc.v2(-420, 200), cc.v2(-380, 400), cc.v2(-390, 624)),
                cc.tween().to(0.4, { scale: 0.6 })
            )
            .call(() => {
                this.fly4.active = false;
                this.fly4.x = 0;
                this.fly4.y = 0;
                this.fly4.scale = 0;
                this.playItemIconAni();
            })
            .start();

        this.fly5.active = true;
        this.fly5.scale = 0;
        cc.tween(this.fly5)
            .parallel(
                cc.tween().bezierTo(0.3, cc.v2(160, 300), cc.v2(140, 230), cc.v2(170, 180)),
                cc.tween().to(0.3, { scale: 1.2 })
            )
            .delay(0.6)
            .parallel(
                cc.tween().bezierTo(0.4, cc.v2(-420, 200), cc.v2(-380, 400), cc.v2(-390, 624)),
                cc.tween().to(0.4, { scale: 0.6 })
            )
            .call(() => {
                this.fly5.active = false;
                this.fly5.x = 0;
                this.fly5.y = 0;
                this.fly5.scale = 0;
                this.playItemIconAni();
            })
            .start();

        this.fly6.active = true;
        this.fly6.scale = 0;
        cc.tween(this.fly6)
            .parallel(
                cc.tween().bezierTo(0.3, cc.v2(-160, 100), cc.v2(-140, -30), cc.v2(-120, 200)),
                cc.tween().to(0.3, { scale: 1.2 })
            )
            .delay(0.75)
            .parallel(
                cc.tween().bezierTo(0.4, cc.v2(-420, 200), cc.v2(-380, 400), cc.v2(-390, 624)),
                cc.tween().to(0.4, { scale: 0.6 })
            )
            .call(() => {
                this.fly6.active = false;
                this.fly6.x = 0;
                this.fly6.y = 0;
                this.fly6.scale = 0;
                this.playItemIconAni();
            })
            .start();
    }

    private updateTabUI(): void {
        const data = TBBoxSystem.getInstance()
            .getList()
            .map((e) => e.id);
        for (let i = 0; i < this.btns.length; i++) {
            const check = this.btns[i].child("check");
            const light = this.btns[i].child("light");
            const icon = this.btns[i].child("icon");
            const number = this.btns[i].child("number");
            const spine1 = this.btns[i].child("spine1");
            const spine2 = this.btns[i].child("spine2");
            if (data[i] === this.curId) {
                check.active = true;
                light.active = true;
                spine1.active = true;
                spine2.active = true;
            } else {
                check.active = false;
                light.active = false;
                spine1.active = false;
                spine2.active = false;
            }
            const info = TBBoxSystem.getInstance().getDataById(data[i]);
            ImageUtils.setBoxIcon(icon, info.res);
            number.label("x" + Bag.getInstance().getItemCountById(info.id));
        }
    }

    private blockInput(enable: boolean): void {
        this.blockInputNode.active = enable;
    }

    private playItemIconAni(): void {
        this.itemIcon.stopAllActions();
        cc.tween(this.itemIcon).to(0.05, { scale: 1.1 }).to(0.05, { scale: 1 }).start();
    }

    private playTabAni(oldId: number, newId: number): void {
        for (const e of this.btns) {
            const id = CocosExt.getButtonData(e);
            const icon = e.child("icon");
            if (newId === id) {
                icon.stopAllActions();
                cc.tween(icon).to(0.15, { y: 54 }, { easing: "smooth" }).start();
            } else {
                if (oldId === id) {
                    icon.stopAllActions();
                    cc.tween(icon).to(0.15, { y: 34 }, { easing: "smooth" }).start();
                }
            }
        }
    }

    protected onClickTab(sender: cc.Event.EventTouch, id: number): void {
        if (this.curId === id) {
            return;
        }
        this.playTabAni(this.curId, id);
        this.curId = id;
        this.updateTabUI();
        this.updateUI();
        this.updateRed();
    }

    protected onClickOpenBox(): void {
        const boxData = TBBoxSystem.getInstance().getDataById(this.curId);
        const hasCount = Bag.getInstance().getItemCountById(boxData.id);
        if (hasCount <= 0) {
            return;
        }

        if (hasCount >= boxData.openUpperLimit) {
            Box.getInstance().sendBoxOpen(this.curId, boxData.openUpperLimit);
        } else {
            Box.getInstance().sendBoxOpen(this.curId, hasCount);
        }
    }

    protected onClickBtnProgress(): void {
        const progressObj = Box.getInstance().getCurrentIdProgressObj();
        if (progressObj.allProgress < progressObj.nowProgress) {
            const boxInfo = Box.getInstance().getBoxInfo();
            const data = TBBoxSystemProgress.getInstance().getDataById(boxInfo.boxProgressId);
            ItemUtils.showInfo(data.reward[0][0]);
            return;
        }

        const uniData = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.BoxSystemConvenient);
        if (this.getProgressRewardLength() >= uniData) {
            Box.getInstance().sendBoxProgressBatchReward();
            return;
        }
        Box.getInstance().sendBoxProgressReward();
    }

    protected getProgressRewardLength(): number {
        const data = Box.getInstance().getCurrentIdProgressObj();
        const boxInfo = Box.getInstance().getBoxInfo();
        const progressArr = TBBoxSystemProgress.getInstance().getProgressArr(); // 当前进度
        let curIndex = progressArr.findIndex((e) => e[0] === boxInfo.boxProgressId); // 当前进度索引
        let count = 0;
        let allProgress = data.allProgress;
        while (true) {
            if (allProgress <= 0 || allProgress <= progressArr[curIndex][1]) {
                break;
            }
            allProgress -= progressArr[curIndex][1];
            curIndex = curIndex >= progressArr.length - 1 ? 0 : ++curIndex;
            count++;
        }
        return count;
    }

    protected onClickToggle(): void {
        LocalStorage.getInstance().setItem(LocalStorageKey.BoxSkinAni, JSON.stringify(this.toggle.isChecked));
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
