/*
 * @Author: chenx
 * @Date: 2024-09-26 17:38:47
 * @Last Modified by: chenx
 * @Last Modified time: 2024-09-29 09:36:38
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import i18n from "../config/i18n/I18n";
import DataRule from "../data/extend/DataRule";
import TBRule, { RULE_ID } from "../data/parser/TBRule";

/**
 * 页签类型
 */
enum TabType {
    Confidant = 1, // 知己
    Travel = 2, // 游历
    Child = 3, // 王储
}

/**
 * 页签类型
 */
const TAB_TYPE = [TabType.Confidant, TabType.Travel, TabType.Child];

const { ccclass, property } = cc._decorator;

/**
 * 知己
 */
@ccclass
export default class UIConfidant extends I18nComponent {
    @property(cc.Label)
    lbtTitle: cc.Label = null; // 标题
    @property(cc.Node)
    nodeModuleContent: cc.Node = null; // 模块content

    @property([cc.Node])
    nodeTab: cc.Node[] = []; // 页签
    @property([cc.Prefab])
    prefabModule: cc.Prefab[] = []; // 模块

    tabType: TabType = TabType.Confidant; // 页签类型
    moduleData: cc.Node[] = []; // 模块数据

    protected onLoad(): void {
        this.updateTabState(true);
        this.updateModuleInfo();
    }

    /**
     * 更新页签状态
     * @param isInit 是否为初始化调用
     */
    private updateTabState(isInit: boolean = false): void {
        if (isInit) {
            this.nodeTab.forEach((e, i) => e.button(TAB_TYPE[i]));
        }

        this.nodeTab.forEach((e) => {
            e.child("select").active = CocosExt.getButtonData(e) === this.tabType;
        });

        switch (this.tabType) {
            case TabType.Confidant:
                this.lbtTitle.string = i18n.confidant0001;
                break;
            case TabType.Travel:
                this.lbtTitle.string = i18n.confidant0002;
                break;
            case TabType.Child:
                this.lbtTitle.string = i18n.confidant0003;
                break;
            default:
                break;
        }
    }

    /**
     * 更新模块信息
     */
    private updateModuleInfo(): void {
        if (!this.moduleData[this.tabType]) {
            const nodeModule = Loader.getInstance().instantiate(this.prefabModule[this.tabType]);
            this.nodeModuleContent.addChild(nodeModule);
            this.moduleData[this.tabType] = nodeModule;
        }
        this.moduleData.forEach((e, i) => {
            e.active = i === this.tabType;
        });
    }

    /**
     * 选择页签
     * @param event 事件
     * @param type 页签类型
     */
    protected onClickSelectTab(event: cc.Event.EventTouch, type: TabType): void {
        if (!type || this.tabType === type) {
            return;
        }

        this.tabType = type;
        this.updateTabState();
        this.updateModuleInfo();
    }

    /**
     * 规则
     */
    protected onClickRule(): void {
        let ruleInfo: DataRule = null;
        switch (this.tabType) {
            case TabType.Confidant:
                ruleInfo = TBRule.getInstance().getDataById(RULE_ID.CONFIDANT_CONFIDANT);
                break;
            case TabType.Travel:
                ruleInfo = TBRule.getInstance().getDataById(RULE_ID.CONFIDANT_TRAVEL);
                break;
            case TabType.Child:
                ruleInfo = TBRule.getInstance().getDataById(RULE_ID.CONFIDANT_CHILD);
                break;
            default:
                break;
        }
        UI.getInstance().open("FloatRule", ruleInfo);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
