/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-06-25 18:01:46
 */

import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import i18n from "../config/i18n/I18n";
import { EnumSkillType } from "../data/base/BaseSkill";
import TBAttribute from "../data/parser/TBAttribute";
import TBItem from "../data/parser/TBItem";
import TBSkill, { EnumSkillParamLevelType } from "../data/parser/TBSkill";
import TBTank from "../data/parser/TBTank";
import TBTankStar from "../data/parser/TBTankStar";

import Skill from "../game/Skill";
import ColorUtils from "../utils/ColorUtils";
import ImageUtils from "../utils/ImageUtils";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatTankPreviewDetail extends I18nComponent {
    @property(cc.Node)
    quality: cc.Node = null;
    @property(cc.Node)
    nameText: cc.Node = null;
    @property(cc.Node)
    attrText: cc.Node = null; // 拥有/上阵加成
    @property(cc.Node)
    fateNode: cc.Node = null;
    @property(cc.Node)
    spine: cc.Node = null;
    @property(cc.Node)
    starts: cc.Node = null;
    @property(cc.Node)
    desc: cc.Node = null;
    @property(cc.Node)
    skillIcon: cc.Node = null;
    @property(cc.Node)
    skillName: cc.Node = null;
    @property(cc.Node)
    skillTime: cc.Node = null;
    @property(cc.Node)
    skillDesc: cc.Node = null;

    @property(cc.Node)
    previewNode: cc.Node = null; // 预览
    @property(cc.Node)
    blockInput: cc.Node = null;
    @property(ListView)
    list: ListView = null;

    private tankId: number = -1;

    protected onLoad(): void {
        this.tankId = this.args;
        this.updateFateAttr();
        this.updateUI();
    }

    private updateUI(): void {
        const data = TBTank.getInstance().getDataById(this.tankId);
        const itemData = TBItem.getInstance().getDataById(this.tankId);
        const starData = TBTankStar.getInstance().getDataByQuality(data.quality);
        const attribute = starData[0].attribute.find((e) => e[1] !== 0);
        const { name, value } = TBAttribute.getInstance().formatAttribute(attribute);
        ImageUtils.setQuality6(this.quality, data.quality);
        this.nameText.label(data.name);
        this.attrText.label(TextUtils.format(i18n.common0087, name + value));
        ImageUtils.setStarsIcon(this.starts, starData[starData.length - 1].star);
        this.desc.label(itemData.desc);
        SpineUtils.setTank(this.spine, data.res);
        const curValue = Skill.getInstance().getSkillValueById(data.skillId[0], {
            [EnumSkillParamLevelType.TankStar]: starData[starData.length - 1].star,
        });
        const skillData = TBSkill.getInstance().getDataById(data.skillId[0]);
        this.skillDesc.richText(
            TextUtils.format(
                ColorUtils.replaceTextColors(skillData.desc, [
                    "#615A5B",
                    "#688A28",
                    "#615A5B",
                    "#688A28",
                    "#615A5B",
                    "#688A28",
                    "#615A5B",
                    "#688A28",
                ]),
                curValue
            )
        );
        ImageUtils.setLeadSkillIcon(this.skillIcon, skillData.res);
        this.skillName.label(skillData.name);
        if (skillData.type === EnumSkillType.TankASkill) {
            this.skillTime.active = true;
            this.skillTime.label(TextUtils.format(i18n.common0062, skillData.cd));
        } else {
            this.skillTime.active = false;
        }
    }

    private updateFateAttr(): void {
        const last = TBTankStar.getInstance().getLast();
        if (last.attribute.length > 1) {
            const fateAttr = TBAttribute.getInstance().getFateAttrs(last.attribute);
            if (fateAttr) {
                this.fateNode.active = true;
                const { name, value } = TBAttribute.getInstance().formatAttribute(fateAttr);
                this.fateNode.child("text").label(name + "+" + value);
            } else {
                this.fateNode.active = false;
            }
        } else {
            this.fateNode.active = false;
        }
    }

    protected updateStartList(): void {
        const first = TBTankStar.getInstance().getFirst();
        const last = TBTankStar.getInstance().getLast();
        const data: { tankId: number; star: number }[] = [];
        for (let i = first.star; i <= last.star; i++) {
            data.push({ tankId: this.tankId, star: i });
        }
        this.list.setListData(data);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }

    protected onClickPreview(): void {
        this.previewNode.active = !this.previewNode.active;
        this.blockInput.active = !this.blockInput.active;
        if (this.previewNode.active) {
            this.updateStartList();
        }
    }

    protected onClickFate(): void {
        UI.getInstance().open("FloatTankFatePreview", this.tankId);
    }
}
