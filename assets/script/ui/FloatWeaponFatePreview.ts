/*
 * @Author: JackyFu
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-25 15:06:21
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TBWeapon from "../data/parser/TBWeapon";
import TBWeaponStar from "../data/parser/TBWeaponStar";
import Weapon from "../game/Weapon";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatWeaponFatePreview extends I18nComponent {
    @property(ListView)
    list: ListView = null;

    private weaponId: number = -1;
    private isMax: boolean = false; // 是否满星

    protected onLoad(): void {
        this.weaponId = this.args.weaponId;
        this.isMax = this.args.isMax;
        this.updateList();
    }

    private updateList(): void {
        const data = TBWeapon.getInstance().getDataById(this.weaponId);
        const starDataArr = TBWeaponStar.getInstance().getDataByQuality(data.quality);
        this.list.setListData(starDataArr);
        if (this.isMax) {
            this.list.scrollTo(starDataArr.length - 1);
        } else {
            const info = Weapon.getInstance().getDataById(this.weaponId);
            const star = info ? info.star : 0;
            const index = starDataArr.findIndex((e) => e.star === star);
            this.list.scrollTo(index);
        }
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
