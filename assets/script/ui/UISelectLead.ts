/*
 * @Author: chenx
 * @Date: 2025-07-17 15:39:52
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-22 11:19:00
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import { LeadSkinSelectRet } from "../../protobuf/proto";
import Reset, { ResetEvent } from "../core/Reset";
import { EnumUniversalPara } from "../data/base/BaseUniversal";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import TBUniversal from "../data/parser/TBUniversal";
import LeadSkin from "../game/LeadSkin";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

/**
 * 选择主角
 */
@ccclass
export default class UISelectLead extends I18nComponent {
    @property(cc.Sprite)
    spLead: cc.Sprite = null; // 主角
    @property(sp.Skeleton)
    spineLead: sp.Skeleton = null; // 主角
    @property(cc.Sprite)
    spTitle: cc.Sprite = null; // 标题
    @property(cc.Node)
    nodeBoy: cc.Node = null; // 男孩按钮
    @property(cc.Node)
    nodeGirl: cc.Node = null; // 女孩按钮
    @property(cc.Node)
    nodeStart: cc.Node = null; // 开始按钮

    private isSelectedBoy: boolean = true; // 是否已选择男孩
    private closeCb: () => void = null; // 关闭回调

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/syncUI/selectLead/spCastingTitle",
            },
            {
                sprite: this.nodeStart,
                url: "texture/syncUI/selectLead/btnCastingPlay",
            },
        ];
    }

    protected onLoad(): void {
        this.closeCb = this.args;
        this.initInfo();
        this.updateLeadInfo();
    }

    protected registerHandler(): void {
        // 重置-玩家数据
        Reset.getInstance().on(
            ResetEvent.ResetPlayerData,
            () => {
                LeadSkin.getInstance().getLeadSelectState() && this.closeCb();
            },
            this
        );
        // 主角-选择主角
        LeadSkin.getInstance().on(
            LeadSkinSelectRet.prototype.clazzName,
            () => {
                LeadSkin.getInstance().getLeadSelectState() && this.closeCb();
            },
            this
        );
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        const para: number[] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.InitialCastingID);
        const leadInfo = TBLeadSkin.getInstance().getDataById(para[0][0]);
        this.nodeBoy.spriteAsync(`texture/selectLead/spCasting${leadInfo.res}`);
        const leadInfo2 = TBLeadSkin.getInstance().getDataById(para[1][0]);
        this.nodeGirl.spriteAsync(`texture/selectLead/spCasting${leadInfo2.res}`);
    }

    /**
     * 更新主角信息
     */
    private updateLeadInfo(): void {
        const para: number[] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.InitialCastingID);
        const leadId = para[this.isSelectedBoy ? 0 : 1][0];
        const leadInfo = TBLeadSkin.getInstance().getDataById(leadId);
        this.spLead.node.spriteAsync(`texture/selectLead/spCastingRole${leadInfo.res}`);
        SpineUtils.setLeadWithoutAniName(this.spineLead, leadInfo.res, () => {
            this.spineLead.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                if (trackEntry.animation.name === "exclusivewait") {
                    this.spineLead.setAnimation(0, "wait", false);
                    this.spineLead.addAnimation(0, "wait", false);
                    this.spineLead.addAnimation(0, "wait", false);
                    this.spineLead.addAnimation(0, "wait", false);
                    this.spineLead.addAnimation(0, "wait", false);
                    this.spineLead.addAnimation(0, "exclusivewait", false);
                }
            });
            this.spineLead.setAnimation(0, "exclusivewait", false);
        });
        this.nodeBoy.child("nodeSelected").active = this.isSelectedBoy;
        this.nodeBoy.child("nodeUnselected").active = !this.isSelectedBoy;
        this.nodeGirl.child("nodeSelected").active = !this.isSelectedBoy;
        this.nodeGirl.child("nodeUnselected").active = this.isSelectedBoy;
    }

    /**
     * 选择男孩
     */
    protected onClickSelectBoy(): void {
        this.isSelectedBoy = true;
        this.updateLeadInfo();
    }

    /**
     * 选择女孩
     */
    protected onClickSelectGirl(): void {
        this.isSelectedBoy = false;
        this.updateLeadInfo();
    }

    /**
     * 开始游戏
     */
    protected onClickStartGame(): void {
        const para: number[] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.InitialCastingID);
        LeadSkin.getInstance().sendLeadSkinSelect(para[this.isSelectedBoy ? 0 : 1][0]);
    }
}
