/*
 * @Author: chenx
 * @Date: 2025-02-08 15:16:22
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:52:59
 */
import Audio from "../../nsn/audio/Audio";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { IItemInfo, IPlayerInfo } from "../../protobuf/proto";
import TipsToCloseComp from "../comp/TipsToCloseComp";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import i18n from "../config/i18n/I18n";
import { EnumArenaPara } from "../data/base/BaseArena";
import TBArena from "../data/parser/TBArena";
import { DungeonType } from "../game/Combat";
import ItemUtils from "../utils/ItemUtils";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";

/**
 * 界面参数
 */
interface IFloatDungeonFailureArenaArgs {
    type: DungeonType; // 副本类型
    roleData: IPlayerInfo; // 角色数据
    score: number; // 积分
    roleData2: IPlayerInfo; // 角色数据
    score2: number; // 积分
    rewardData: IItemInfo[]; // 奖励数据
}

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 竞技场副本-失败
 */
@ccclass
export default class FloatDungeonFailureArena extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null; // 标题
    @property(cc.Node)
    nodeHead: cc.Node = null; // 头像
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.RichText)
    rtScore: cc.RichText = null; // 积分
    @property(cc.Node)
    nodeHead2: cc.Node = null; // 头像
    @property(cc.Label)
    lbtName2: cc.Label = null; // 名称
    @property(cc.RichText)
    rtScore2: cc.RichText = null; // 积分
    @property(cc.Node)
    nodeRewardContent: cc.Node = null; // 奖励content
    @property(TipsToCloseComp)
    compCloseTips: TipsToCloseComp = null; // 关闭提示

    @property(cc.Prefab)
    prefabRewardItem: cc.Prefab = null; // 奖励item

    private initData: IFloatDungeonFailureArenaArgs = null; // 初始化数据
    private autoTime: number = 0; // 自动时间-关闭
    private refreshTime: number = 0; // 刷新时间

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/result/spResultTitle13",
            },
        ];
    }

    protected onLoad(): void {
        this.initData = this.args;
        this.initInfo();
        Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.DUNGEON_PVP_FAILURE, AUDIO_EFFECT_PATH.DUNGEON);
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.autoTime -= 1;
                this.updateAutoTimeState();
            }
        }
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        PlayerInfoUtils.updateHead(this.nodeHead, this.initData.roleData);
        this.lbtName.string = this.initData.roleData.name;
        const para: number = TBArena.getInstance().getValueByPara(EnumArenaPara.DefendFailPoint);
        if (para === 0) {
            this.rtScore.string = `${this.initData.score}（${para}）`;
        } else if (para > 0) {
            this.rtScore.string = `${this.initData.score}（<color=#93FFA5>+${para}</c>）`;
        } else {
            this.rtScore.string = `${this.initData.score}（<color=#FF5F56>${para}</c>）`;
        }

        PlayerInfoUtils.updateHead(this.nodeHead2, this.initData.roleData2);
        this.lbtName2.string = this.initData.roleData2.name;
        const para2: number = TBArena.getInstance().getValueByPara(EnumArenaPara.ChallengeWinPoint);
        if (para2 === 0) {
            this.rtScore2.string = `${this.initData.score2}（${para2}）`;
        } else if (para2 > 0) {
            this.rtScore2.string = `${this.initData.score2}（<color=#93FFA5>+${para2}</c>）`;
        } else {
            this.rtScore2.string = `${this.initData.score2}（<color=#FF5F56>${para2}</c>）`;
        }

        ItemUtils.refreshView(this.nodeRewardContent, this.prefabRewardItem, this.initData.rewardData);

        this.updateAutoTimeState(true);
    }

    /**
     * 更新自动时间状态-关闭
     * @param isInit 是否为初始化调用
     */
    private updateAutoTimeState(isInit: boolean = false): void {
        if (isInit) {
            this.autoTime = TBArena.getInstance().getValueByPara(EnumArenaPara.PkCloseTime);
        }
        if (this.autoTime === 0) {
            switch (this.initData.type) {
                case DungeonType.Arena:
                    UI.getInstance().closeToWindow("UIDungeonCombatArena");
                    break;
                default:
                    break;
            }
            UI.getInstance().close();
        } else {
            this.compCloseTips.initText(TextUtils.format(i18n.dungeon0006, this.autoTime));

            this.refreshTime = REFRESH_DURATION;
        }
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        if (this.autoTime === 0) {
            return;
        }

        this.refreshTime = 0;

        switch (this.initData.type) {
            case DungeonType.Arena:
                UI.getInstance().closeToWindow("UIDungeonCombatArena");
                break;
            default:
                break;
        }
        UI.getInstance().close();
    }
}
