/*
 * @Author: <PERSON>y<PERSON><PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-18 14:45:41
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import Time, { DAY_TO_SECOND, HOUR_TO_SECOND } from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import {
    IUnionSiegeShowObj,
    PlayerInfoQueryRet,
    UnionSiegeEndChallengeNoticeRet,
    UnionSiegeEndChallengeRet,
    UnionSiegeGetRet,
    UnionSiegeStartChallengeNoticeRet,
    UnionSiegeStartChallengeRet,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumShopType } from "../data/base/BaseShop";
import { EnumUnionPara } from "../data/base/BaseUnion";
import TBActivity from "../data/parser/TBActivity";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import { RANK_ID } from "../data/parser/TBRank";
import TBUnion from "../data/parser/TBUnion";
import TBWeapon from "../data/parser/TBWeapon";
import TBWing from "../data/parser/TBWing";
import Player from "../game/Player";
import Union from "../game/Union";
import UnionSiege, { EnumUnionSiegeStatus } from "../game/UnionSiege";
import ImageUtils from "../utils/ImageUtils";
import SpineUtils from "../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class UIActivityUnionDefense extends I18nComponent {
    @property([cc.Node])
    myUnionLeads: cc.Node[] = []; // 我方
    @property([cc.Node])
    enemyUnionLeads: cc.Node[] = []; // 敌方
    @property(cc.Node)
    spTitle: cc.Node = null;
    @property([cc.Node])
    unionInfo: cc.Node[] = []; // 我方、敌方公会信息
    @property(cc.Node)
    myServeName: cc.Node = null;
    @property(cc.Node)
    enemyServeName: cc.Node = null;
    @property(cc.Node)
    grayUnionInfoNode: cc.Node = null; // 轮空或未参赛时需要显示

    @property(cc.Node)
    btnAchieved: cc.Node = null; // 挑战成就
    @property(cc.Node)
    status1: cc.Node = null; // 匹配期间
    @property(cc.Node)
    status2: cc.Node = null; // 备战期间
    @property(cc.Node)
    status3: cc.Node = null; // 对决期间
    @property(cc.Node)
    status4: cc.Node = null; // 结算期间
    @property(cc.Node)
    status5: cc.Node = null; // 领奖期间
    @property(cc.Node)
    status6: cc.Node = null; // 本次轮空
    @property(cc.Node)
    status7: cc.Node = null; // 报名期已结束->本轮未参赛
    @property(cc.Node)
    time: cc.Node = null; // 倒计时
    @property(cc.Node)
    tip1: cc.Node = null; // 失败提示
    @property(cc.Node)
    tip2: cc.Node = null; // 可领奖提示
    @property(cc.Node)
    winNode: cc.Node = null;
    @property(cc.Node)
    winFlag: cc.Node = null;
    @property(cc.Node)
    winUnionName: cc.Node = null;
    @property(cc.Node)
    winUnionIcon: cc.Node = null;
    @property(cc.Node)
    btnGo: cc.Node = null;
    @property(cc.Node)
    nextTaskText1: cc.Node = null; // 下次参赛条件1
    @property(cc.Node)
    nextTaskText2: cc.Node = null; // 下次参赛条件2

    private activityId: number = 0;
    private dt: number = 1;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: `texture/syncUI/activity/union/defense/spUBSystemTitle`,
            },
            {
                sprite: this.status1,
                url: `texture/syncUI/activity/union/defense/spUBStatus1`,
            },
            {
                sprite: this.status2,
                url: `texture/syncUI/activity/union/defense/spUBStatus2`,
            },
            {
                sprite: this.status3,
                url: `texture/syncUI/activity/union/defense/spUBStatus3`,
            },
            {
                sprite: this.status4,
                url: `texture/syncUI/activity/union/defense/spUBStatus4`,
            },
            {
                sprite: this.status5,
                url: `texture/syncUI/activity/union/defense/spUBStatus5`,
            },
            {
                sprite: this.status6,
                url: `texture/syncUI/activity/union/defense/spUBStatus6`,
            },
            {
                sprite: this.status7,
                url: `texture/syncUI/activity/union/defense/spUBStatus7`,
            },
            {
                sprite: this.btnGo,
                url: `texture/syncUI/activity/union/defense/btnUBGo`,
            },
            {
                sprite: this.winUnionIcon,
                url: `texture/syncUI/activity/union/defense/spUBResultTxt`,
            },
        ];
    }

    protected onLoad(): void {
        this.activityId = this.args;
        this.updateTopUI(true);
        this.updateNextPkUI();
        this.updateStatusUI();
        this.updateLeadAndGaryUI();
    }

    protected update(dt: number): void {
        this.dt += dt;
        if (this.dt > 1) {
            this.dt -= 1;
            this.updateStatusUI();

            // gvg结算奖励
            const isSelfEmpty = UnionSiege.getInstance().isSelfEmpty();
            if (!isSelfEmpty) {
                return;
            }
            const isUnionEmpty = UnionSiege.getInstance().isUnionEmpty();
            if (!isUnionEmpty) {
                return;
            }
            const detailObj = UnionSiege.getInstance().getMyUnionSingeDetailObj();
            if (detailObj && detailObj.isRewarded) {
                return;
            }

            // 未轮空
            const status = UnionSiege.getInstance().getStatus(this.activityId);
            if (status === EnumUnionSiegeStatus.Status5 && detailObj && !detailObj.isRewarded) {
                UnionSiege.getInstance().sendUnionSiegeReward(this.activityId);
            }

            // 轮空
            if (status === EnumUnionSiegeStatus.Status6) {
                const weekZeroDay = Time.getInstance().getWeekZero();
                const { prepareTime, totalTime } = TBActivity.getInstance().getDataByActivityId(this.activityId);
                const time1 = weekZeroDay + (totalTime - prepareTime) * HOUR_TO_SECOND * 1000;
                const time2 = weekZeroDay + totalTime * HOUR_TO_SECOND * 1000;
                const now = Time.getInstance().now();
                if (now > time1 && now < time2) {
                    UnionSiege.getInstance().sendUnionSiegeReward(this.activityId);
                }
            }
        }
    }

    protected registerHandler(): void {
        UnionSiege.getInstance().on(
            UnionSiegeGetRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );

        UnionSiege.getInstance().on(
            [
                UnionSiegeStartChallengeRet.prototype.clazzName,
                UnionSiegeStartChallengeNoticeRet.prototype.clazzName,
                UnionSiegeEndChallengeRet.prototype.clazzName,
                UnionSiegeEndChallengeNoticeRet.prototype.clazzName,
            ],
            () => {
                this.updateUI();
            },
            this
        );

        Player.getInstance().on(
            PlayerInfoQueryRet.prototype.clazzName,
            (data: PlayerInfoQueryRet) => {
                const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
                const unionInfo = Union.getInstance().getInfo();
                const leaderInfo = Union.getInstance().getMembersUnionLead();
                const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
                const rivalUnionInfo = unionSiegeInfo.find((e) => e.rivalUnionId === unionInfo.id); // 对方
                if (
                    data.playerInfo.playerId === myInfo?.leaderInfo?.playerId ||
                    data.playerInfo.playerId === leaderInfo.player.playerId
                ) {
                    this.myServeName.label(data.serverName);
                } else if (data.playerInfo.playerId === rivalUnionInfo?.leaderInfo?.playerId) {
                    this.enemyServeName.label(data.serverName);
                }
            },
            this
        );
    }

    private updateUI(): void {
        this.updateTopUI();
        this.updateNextPkUI();
        this.updateStatusUI();
        this.updateLeadAndGaryUI();
    }

    private updateTopUI(initServeName: boolean = false): void {
        const isRound = UnionSiege.getInstance().isUnionRoundEmpty();
        const isUnionEmpty = UnionSiege.getInstance().isUnionEmpty();
        const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
        const unionInfo = Union.getInstance().getInfo();
        const leaderInfo = Union.getInstance().getMembersUnionLead();
        const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
        const rivalUnionInfo = unionSiegeInfo.find((e) => e.rivalUnionId === unionInfo.id); // 对方

        if (unionSiegeInfo.length <= 0 || !isUnionEmpty || isRound) {
            for (let i = 0; i < this.unionInfo.length; i++) {
                const e = this.unionInfo[i];
                const unionFlag = e.child("flag");
                const unionName = e.child("unionName");
                const unionServe = e.child("unionServe");
                const unionCount = e.child("layout").child("count");
                const unionStar = e.child("layout").child("star");
                if (i === 0) {
                    unionFlag.active = true;
                    unionName.active = true;
                    unionServe.active = true;
                    unionStar.active = true;
                    unionCount.active = true;
                    ImageUtils.setUnionFlag(unionFlag, unionInfo.flag);
                    unionFlag.button(unionInfo.id);
                    unionName.label(unionInfo.name);
                    initServeName && Player.getInstance().sendPlayerInfoQuery(leaderInfo.player.playerId);
                    unionStar.spriteAsync("texture/activity/union/defense/spUBStar2");
                    unionCount.label(0 + ""); // 没有星星
                } else {
                    unionFlag.active = false;
                    unionName.active = false;
                    unionServe.active = false;
                    unionStar.active = false;
                    unionCount.active = false;
                }
            }
            return;
        }

        for (let i = 0; i < this.unionInfo.length; i++) {
            const e = this.unionInfo[i];
            const unionFlag = e.child("flag");
            const unionName = e.child("unionName");
            const unionServe = e.child("unionServe");
            const unionCount = e.child("layout").child("count");
            const unionStar = e.child("layout").child("star");
            unionFlag.active = true;
            unionName.active = true;
            unionServe.active = true;
            unionStar.active = true;
            unionCount.active = true;

            if (i === 0) {
                ImageUtils.setUnionFlag(unionFlag, myInfo.unionInfo.flag);
                unionFlag.button(myInfo.unionInfo.id);
                unionName.label(myInfo.unionInfo.name);
                initServeName && Player.getInstance().sendPlayerInfoQuery(myInfo.leaderInfo.playerId);
                unionStar.spriteAsync("texture/activity/union/defense/spUBStar2");
                unionCount.label(myInfo.totalStarCount + "");
            } else if (i === 1) {
                ImageUtils.setUnionFlag(unionFlag, rivalUnionInfo.unionInfo.flag);
                unionFlag.button(rivalUnionInfo.unionInfo.id);
                unionName.label(rivalUnionInfo.unionInfo.name);
                initServeName && Player.getInstance().sendPlayerInfoQuery(rivalUnionInfo.leaderInfo.playerId);
                unionStar.spriteAsync("texture/activity/union/defense/spUBStar2");
                unionCount.label(rivalUnionInfo.totalStarCount + "");
            }
        }
    }

    private updateLeadAndGaryUI(): void {
        const isRound = UnionSiege.getInstance().isUnionRoundEmpty();
        const isUnionEmpty = UnionSiege.getInstance().isUnionEmpty();
        if (isRound) {
            for (const e of this.myUnionLeads) {
                e.active = false;
            }
            for (const e of this.enemyUnionLeads) {
                e.active = false;
            }
            this.grayUnionInfoNode.active = true;
            return;
        }

        // 公会未参赛
        if (!isUnionEmpty) {
            for (const e of this.myUnionLeads) {
                e.active = false;
            }
            for (const e of this.enemyUnionLeads) {
                e.active = false;
            }
            this.grayUnionInfoNode.active = true;
            return;
        }

        this.grayUnionInfoNode.active = false;
        const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
        const unionInfo = Union.getInstance().getInfo();
        const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
        const rivalUnionInfo = unionSiegeInfo.find((e) => e.rivalUnionId === unionInfo.id); // 对方
        const myDetailObjs = UnionSiege.getInstance().getTop4UnionDetailsObj(myInfo.unionId);
        const rivalDetailObjs = UnionSiege.getInstance().getTop4UnionDetailsObj(rivalUnionInfo.unionId);

        for (let i = 0; i < this.myUnionLeads.length; i++) {
            const node = this.myUnionLeads[i];
            const data = myDetailObjs[i]?.showInfo;
            if (data) {
                node.active = true;
                this.setLeadUI(node, data);
            } else {
                node.active = false;
            }
        }

        for (let i = 0; i < this.enemyUnionLeads.length; i++) {
            const node = this.enemyUnionLeads[i];
            const data = rivalDetailObjs[i]?.showInfo;
            if (data) {
                node.active = true;
                this.setLeadUI(node, data);
            } else {
                node.active = false;
            }
        }
    }

    private setLeadUI(node: cc.Node, data: IUnionSiegeShowObj): void {
        const spineLead = node.child("spineLead").getComponent(sp.Skeleton);
        const spineWeapon = node.child("nodeWeapon").child("spineWeapon").getComponent(sp.Skeleton);
        const spineWing = node.child("nodeWing").child("spineWing").getComponent(sp.Skeleton);

        // 主角
        const skinData = TBLeadSkin.getInstance().getDataById(data.leadSkinId);
        SpineUtils.setLeadWithoutAniName(spineLead, skinData.res, () => {
            // @ts-ignore
            const attachUtil = spineLead.attachUtil;
            attachUtil.destroyAllAttachedNodes();
            attachUtil.generateAllAttachedNodes();

            spineLead.setAnimation(0, "attackWait", true);
        });

        // 神器
        if (data.weaponId !== 0) {
            node.child("nodeWeapon").active = true;
            const weaponData = TBWeapon.getInstance().getDataById(data.weaponId);
            SpineUtils.setWeaponWithoutAniName(spineWeapon, weaponData.res, () => {
                // @ts-ignore
                const attachUtil = spineWeapon.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                const boneHand = spineWeapon.findBone("hand");
                spineWeapon.node.setPosition(cc.v2(-boneHand.x / boneHand.scaleX, -boneHand.y / boneHand.scaleY));
                spineWeapon.setAnimation(0, "attackWait", true);
            });
        } else {
            node.child("nodeWeapon").active = false;
        }

        // 背饰
        if (data.wingsId !== 0) {
            node.child("nodeWing").active = true;
            const wingData = TBWing.getInstance().getDataById(data.wingsId);
            SpineUtils.setWingWithoutAniName(spineWing, wingData.res, () => {
                spineWing.setAnimation(0, "attackWait", true);
            });
        } else {
            node.child("nodeWing").active = false;
        }
    }

    /**
     * 更新下一轮参赛条件UI
     */
    private updateNextPkUI(): void {
        const unionData = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseCondition);
        const member = Union.getInstance().getMembers();
        const nowUnionActive = UnionSiege.getInstance().getMyUnionActive(); // 当前公会活跃度
        this.nextTaskText1.richText(
            member.length >= unionData[0]
                ? TextUtils.format(i18n.unionDefense0003, member.length, unionData[0])
                : TextUtils.format(i18n.unionDefense0002, member.length, unionData[0])
        );
        this.nextTaskText2.richText(
            nowUnionActive >= unionData[1]
                ? TextUtils.format(i18n.unionDefense0005, nowUnionActive, unionData[1])
                : TextUtils.format(i18n.unionDefense0004, nowUnionActive, unionData[1])
        );
    }

    private updateStatusUI(): void {
        const status = UnionSiege.getInstance().getStatus(this.activityId);
        const weekZeroDay = Time.getInstance().getWeekZero(); // 本周零点
        const dayZero = Time.getInstance().getTodayZero(); // 本日零点
        const unionData1 = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseBattleTime); // 获取对决期配置
        const { prepareTime } = TBActivity.getInstance().getDataByActivityId(this.activityId);
        const now = Time.getInstance().now();

        const statuses = [
            this.status1,
            this.status2,
            this.status3,
            this.status4,
            this.status5,
            this.status6,
            this.status7,
        ];
        statuses.forEach((s, index) => {
            s.active = index === status;
        });

        switch (status) {
            case EnumUnionSiegeStatus.Status1: // 匹配期间
                this.btnAchieved.active = true;
                this.time.active = true;
                const time1 = weekZeroDay + prepareTime * HOUR_TO_SECOND * 1000;
                this.time.label(
                    TextUtils.format(
                        i18n.unionDefense0006,
                        TimeFormat.getInstance().getTextByDuration(time1 - now, TimeDurationFormatType.D_H_M_S_3)
                    )
                );
                this.tip1.active = false;
                this.tip2.active = false;
                this.winNode.active = false;
                this.btnGo.active = false;
                break;
            case EnumUnionSiegeStatus.Status2: // 备战期间
                this.btnAchieved.active = true;
                this.time.active = true;
                const time2 = dayZero + unionData1[0] * HOUR_TO_SECOND * 1000;
                this.time.label(
                    TextUtils.format(
                        i18n.unionDefense0006,
                        TimeFormat.getInstance().getTextByDuration(time2 - now, TimeDurationFormatType.D_H_M_S_3)
                    )
                );
                this.tip1.active = false;
                this.tip2.active = false;
                this.winNode.active = false;
                this.btnGo.active = false;
                break;
            case EnumUnionSiegeStatus.Status3: // 对决期间
                this.btnAchieved.active = true;
                this.time.active = true;
                const time3 = dayZero + unionData1[1] * HOUR_TO_SECOND * 1000;
                this.time.label(
                    TextUtils.format(
                        i18n.unionDefense0006,
                        TimeFormat.getInstance().getTextByDuration(time3 - now, TimeDurationFormatType.D_H_M_S_3)
                    )
                );
                this.tip1.active = false;
                this.tip2.active = false;
                this.winNode.active = false;
                this.btnGo.active = true; // 前往pk
                break;
            case EnumUnionSiegeStatus.Status4: // 结算期间
                this.btnAchieved.active = true;
                this.time.active = true;
                const time4 = dayZero + 24 * HOUR_TO_SECOND * 1000;
                this.time.label(
                    TextUtils.format(
                        i18n.unionDefense0006,
                        TimeFormat.getInstance().getTextByDuration(time4 - now, TimeDurationFormatType.D_H_M_S_3)
                    )
                );
                this.tip1.active = false;
                this.tip2.active = false;
                this.winNode.active = false;
                this.btnGo.active = false;
                break;
            case EnumUnionSiegeStatus.Status5: // 领奖期间
                this.btnAchieved.active = true;
                this.time.active = true;
                const time5 = dayZero + 24 * HOUR_TO_SECOND * 1000;
                this.time.label(
                    TextUtils.format(
                        i18n.unionDefense0006,
                        TimeFormat.getInstance().getTextByDuration(time5 - now, TimeDurationFormatType.D_H_M_S_3)
                    )
                );
                this.tip1.active = false;
                this.tip2.active = true;
                this.tip2.label(i18n.unionDefense0008);
                const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
                const unionInfo = Union.getInstance().getInfo();
                const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
                const rivalUnionInfo = unionSiegeInfo.find((e) => e.rivalUnionId === unionInfo.id); // 对方
                this.winNode.active = true;
                if (
                    myInfo.totalStarCount > rivalUnionInfo.totalStarCount ||
                    (myInfo.totalStarCount === rivalUnionInfo.totalStarCount &&
                        myInfo.starTime < rivalUnionInfo.starTime)
                ) {
                    ImageUtils.setUnionFlag(this.winFlag, myInfo.unionInfo.flag);
                    this.winUnionName.label(myInfo.unionInfo.name);
                } else {
                    ImageUtils.setUnionFlag(this.winFlag, rivalUnionInfo.unionInfo.flag);
                    this.winUnionName.label(rivalUnionInfo.unionInfo.name);
                }

                this.btnGo.active = false;
                break;
            case EnumUnionSiegeStatus.Status6: // 本轮轮空
                this.btnAchieved.active = false;
                this.time.active = false;
                this.winNode.active = false;
                this.btnGo.active = false;

                const myDetailObjs = UnionSiege.getInstance().getMyUnionSingeDetailObj();
                if (!myDetailObjs) {
                    statuses.forEach((s) => {
                        s.active = false;
                    });
                    this.status7.active = true;
                    this.tip1.active = true;
                    this.tip2.active = false;
                    this.tip1.label(i18n.unionDefense0010);
                } else {
                    this.tip1.active = false;
                    this.tip2.active = true;
                    this.tip2.label(i18n.unionDefense0009);
                }
                break;
            case EnumUnionSiegeStatus.Status7: // 本轮未参赛
                this.btnAchieved.active = false;
                this.time.active = true;
                const time6 = weekZeroDay + 7 * DAY_TO_SECOND * 1000; // 下一周0点
                this.time.label(
                    TextUtils.format(
                        i18n.unionDefense0011,
                        TimeFormat.getInstance().getTextByDuration(time6 - now, TimeDurationFormatType.D_H_M_S_2)
                    )
                );
                this.tip1.active = true;
                this.tip1.label(i18n.unionDefense0010);
                this.tip2.active = false;
                this.winNode.active = false;
                this.btnGo.active = false;
                break;
            default:
                // 活动已结束
                this.btnAchieved.active = false;
                this.time.active = false;
                this.tip1.active = false;
                this.tip2.active = false;
                this.winNode.active = false;
                this.btnGo.active = false;
                break;
        }
    }

    /**
     * 显示公会信息
     */
    protected onClickUnionInfo(sender: cc.Event.EventTouch, unionId: number): void {
        const isRoundEmpty = UnionSiege.getInstance().isUnionRoundEmpty();
        const isUnionEmpty = UnionSiege.getInstance().isUnionEmpty();
        if (!isUnionEmpty || isRoundEmpty) {
            return; // 没有数据的情况
        }

        UI.getInstance().open("PopupActivityUnionDefenseUnionDetails", unionId);
    }

    /**
     * 前往pk
     */
    protected onClickPK(): void {
        UI.getInstance().open("UIActivityUnionDefenseStart", this.activityId);
    }

    /**
     * 打开奖励
     */
    protected onClickReward(): void {
        UI.getInstance().open("PopupActivityUnionDefenseResultReward");
    }

    /**
     * 打开挑战成就
     */
    protected onClickAchieved(): void {
        UI.getInstance().open("PopupActivityUnionDefenseAchievement", this.activityId);
    }

    /**
     * 打开排行榜
     */
    protected onClickRank(): void {
        UI.getInstance().open("PopupRank", RANK_ID.UNION_DEFENSE);
    }

    /**
     * 打开日志
     */
    protected onClickLog(): void {
        UI.getInstance().open("PopupActivityUnionDefenseDayLog");
    }

    /**
     * 打开商店
     */
    protected onClickShop(): void {
        UI.getInstance().open("PopupShop", { types: [{ type: EnumShopType.UnionDefenseShop }] });
    }

    /**
     * 打开宝库
     */
    protected onClickBoxReward(): void {
        UI.getInstance().open("PopupActivityUnionDefenseTreasury", this.activityId);
    }

    /**
     * 打开报名期结束提示文本
     */
    protected onClickTip(sender: cc.Event.EventTouch): void {
        const pos = sender.target.convertToWorldSpaceAR(cc.v2());
        UI.getInstance().open("FloatTextTips", {
            pos: cc.v2(pos.x, pos.y - 250),
            text: i18n.unionDefense0022,
            arrow: false,
        });
    }

    protected onClickClose(): void {
        UI.getInstance().close();
        UI.getInstance().open("PopupActivityUnion");
    }
}
