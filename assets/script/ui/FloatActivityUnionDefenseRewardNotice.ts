/*
 * @Author: JackyF<PERSON>
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-18 14:42:38
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import i18n from "../config/i18n/I18n";
import TBItem from "../data/parser/TBItem";
import Union from "../game/Union";
import UnionSiege from "../game/UnionSiege";
import ImageUtils from "../utils/ImageUtils";

const { ccclass, property } = cc._decorator;
@ccclass
export default class FloatActivityUnionDefenseRewardNotice extends I18nComponent {
    @property(cc.Node)
    list: cc.Node = null;
    @property(cc.Node)
    content: cc.Node = null;
    @property(cc.Node)
    textNode: cc.Node = null;
    @property(cc.Node)
    none: cc.Node = null;

    protected onLoad(): void {
        this.textNode.parent = null;

        this.updateList();
    }

    protected onDestroy(): void {
        this.textNode.destroy();
    }

    private updateList(): void {
        const unionInfo = Union.getInstance().getInfo();
        const siegeInfo = UnionSiege.getInstance().getUnionSiegeDetails(unionInfo.id);
        if (siegeInfo.length <= 0) {
            this.none.active = true;
            this.list.active = false;
            return;
        }

        let count = 0;

        siegeInfo.sort((a, b) => {
            return b.receiveBoxRewardTime - a.receiveBoxRewardTime;
        });

        for (const e of siegeInfo) {
            if (e.isBoxRewarded) {
                count++;
                const node = Loader.getInstance().instantiate(this.textNode);
                node.parent = this.content;
                node.active = true;
                const item = TBItem.getInstance().getDataById(e.showProps[0].itemInfoId);
                ImageUtils.setRichTextItemIcons(node, ["prop" + item.res], () => {
                    node.richText(
                        TextUtils.format(
                            i18n.unionDefense0015,
                            e.playerInfo.name,
                            "prop" + item.res,
                            e.showProps[0].num
                        )
                    );
                });
            }
        }

        this.none.active = count === 0;
        this.list.active = count !== 0;
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
