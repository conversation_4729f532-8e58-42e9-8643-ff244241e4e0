/*
 * @Author: JackyFu
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-17 16:40:06
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import TBLeadSkinStar from "../data/parser/TBLeadSkinStar";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatSkinFatePreview extends I18nComponent {
    @property(ListView)
    list: ListView = null;

    private skinId: number = -1;

    protected onLoad(): void {
        this.skinId = this.args;
        this.updateList();
    }

    private updateList(): void {
        const data = TBLeadSkin.getInstance().getDataById(this.skinId);
        const starDataArr = TBLeadSkinStar.getInstance().getDataByQuality(data.quality);
        this.list.setListData(starDataArr);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
