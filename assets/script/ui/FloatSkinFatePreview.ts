/*
 * @Author: JackyFu
 * @Date: 2023-07-26 10:16:02
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-25 15:05:26
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TBLeadSkin from "../data/parser/TBLeadSkin";
import TBLeadSkinStar from "../data/parser/TBLeadSkinStar";
import LeadSkin from "../game/LeadSkin";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatSkinFatePreview extends I18nComponent {
    @property(ListView)
    list: ListView = null;

    private skinId: number = -1;
    private isMax: boolean = false; // 是否满星

    protected onLoad(): void {
        this.skinId = this.args.skinId;
        this.isMax = this.args.isMax;
        this.updateList();
    }

    private updateList(): void {
        const data = TBLeadSkin.getInstance().getDataById(this.skinId);
        const starDataArr = TBLeadSkinStar.getInstance().getDataByQuality(data.quality);
        this.list.setListData(starDataArr);
        if (this.isMax) {
            this.list.scrollTo(starDataArr.length - 1);
        } else {
            const info = LeadSkin.getInstance().getDataById(this.skinId);
            const star = info ? info.star : 0;
            const index = starDataArr.findIndex((e) => e.star === star);
            this.list.scrollTo(index);
        }
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
