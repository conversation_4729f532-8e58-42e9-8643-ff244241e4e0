/*
 * @Author: chenx
 * @Date: 2024-10-11 18:00:40
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-02 15:09:11
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import Time from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import Tips from "../../nsn/util/Tips";
import {
    ChildSearchType,
    ConfidantChildMarryApplyRet,
    ConfidantChildPublishRet,
    ConfidantChildSearch,
    ConfidantChildSearchRet,
    IConfidantSearchInfo,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumPrincessChildSex } from "../data/base/BasePrincessChild";
import { EnumPrincessTotalPara } from "../data/base/BasePrincessTotal";
import TBAttribute from "../data/parser/TBAttribute";
import TBItem from "../data/parser/TBItem";
import TBPrincessChild from "../data/parser/TBPrincessChild";
import TBPrincessTotal from "../data/parser/TBPrincessTotal";
import Confidant, { IConfidantChildData } from "../game/Confidant";
import ImageUtils from "../utils/ImageUtils";
import NumberUtils from "../utils/NumberUtils";
import { IFloatChildPreviewArgs } from "./FloatChildPreview";

/**
 * 页签类型
 */
enum TabType {
    Random = 1, // 随机
    Friend = 2, // 好友
    Union = 3, // 公会
    Search = 4, // 搜索
}

/**
 * 页签类型
 */
const TAB_TYPE = [TabType.Random, TabType.Friend, TabType.Union, TabType.Search];

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 知己-王储提亲
 */
@ccclass
export default class PopupConfidantChildRaise extends I18nComponent {
    @property(cc.Node)
    nodePlayerInfo: cc.Node = null; // 玩家信息
    @property(ListView)
    listPlayer: ListView = null; // 玩家列表
    @property(cc.Node)
    nodeNoDataTips: cc.Node = null; // 无数据提示

    @property(cc.Node)
    nodePlayerInfo2: cc.Node = null; // 玩家信息
    @property(cc.EditBox)
    edbSearch: cc.EditBox = null; // 搜索
    @property(ListView)
    listPlayer2: ListView = null; // 玩家列表
    @property(cc.Node)
    nodeNoDataTips2: cc.Node = null; // 无数据提示

    @property(cc.Node)
    nodeRefresh: cc.Node = null; // 刷新按钮
    @property(cc.Label)
    lbtRefresh: cc.Label = null; // 刷新按钮

    @property([cc.Node])
    nodeTab: cc.Node[] = []; // 页签

    childData: IConfidantChildData = null; // 王储数据
    tabType: TabType = TabType.Random; // 页签类型
    tabData: { [tabType: number]: { playerData: IConfidantSearchInfo[]; isSent: boolean } } = {}; // 页签数据
    playerData: IConfidantSearchInfo[] = null; // 玩家数据
    clickTime: number = 0; // 点击时间-刷新按钮
    refreshTime: number = 0; // 刷新时间-刷新按钮

    protected onLoad(): void {
        this.childData = this.args;
        this.updateTabState(true);
        this.updateListInfo();
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                const duration = Time.getInstance().now() - this.clickTime;
                const duration2 = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.RandomCd) * 1000;
                const isClick = duration >= duration2;
                if (!isClick) {
                    this.lbtRefresh.string = TimeFormat.getInstance().getTextByDuration(
                        duration2 - duration,
                        TimeDurationFormatType.D_H_M_S_0
                    );

                    this.refreshTime = REFRESH_DURATION;
                } else {
                    this.lbtRefresh.string = i18n.confidant0026;
                }
            }
        }
    }

    protected registerHandler(): void {
        Confidant.getInstance().on(
            ConfidantChildSearchRet.prototype.clazzName,
            (data: ConfidantChildSearchRet) => {
                let tabType: TabType = null;
                switch (data.childSearchType) {
                    case ChildSearchType.ChildReCommend:
                        tabType = TabType.Random;
                        break;
                    case ChildSearchType.ChildFriend:
                        tabType = TabType.Friend;
                        break;
                    case ChildSearchType.ChildGuild:
                        tabType = TabType.Union;
                        break;
                    case ChildSearchType.ChildAppointSearch:
                        tabType = TabType.Search;
                        break;
                    default:
                        break;
                }
                this.tabData[tabType].playerData = data.searchInfos;
                tabType === this.tabType && this.updateListInfo();
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildPublishRet.prototype.clazzName,
            (data: ConfidantChildPublishRet) => {
                if (this.childData.data.childUuid === data.childUuid) {
                    UI.getInstance().close();
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildMarryApplyRet.prototype.clazzName,
            (data: ConfidantChildMarryApplyRet) => {
                if (this.childData.data.childUuid === data.myChildUuid) {
                    UI.getInstance().close();
                    const childData = Confidant.getInstance().getChildData(data.myChildUuid);
                    if (childData.companionInfo) {
                        UI.getInstance().open("PopupChildMarriage", childData);
                    }
                }
            },
            this
        );
    }

    /**
     * 更新页签状态
     * @param isInit 是否为初始化调用
     */
    private updateTabState(isInit: boolean = false): void {
        if (isInit) {
            this.nodeTab.forEach((e, i) => e.button(TAB_TYPE[i]));
        }

        this.nodeTab.forEach((e) => {
            e.child("select").active = CocosExt.getButtonData(e) === this.tabType;
        });
    }

    /**
     * 更新列表信息-玩家列表
     */
    private updateListInfo(): void {
        this.nodePlayerInfo.opacity = this.tabType !== TabType.Search ? 255 : 0;
        this.nodePlayerInfo.x = this.tabType !== TabType.Search ? 0 : cc.winSize.width;
        this.nodePlayerInfo2.opacity = this.tabType === TabType.Search ? 255 : 0;
        this.nodePlayerInfo2.x = this.tabType === TabType.Search ? 0 : cc.winSize.width;
        this.nodeRefresh.active = this.tabType !== TabType.Search;

        let tabData = this.tabData[this.tabType];
        if (!tabData) {
            this.tabData[this.tabType] = { isSent: true, playerData: null };
            tabData = this.tabData[this.tabType];
            const sexType =
                this.childData.info.sex === EnumPrincessChildSex.Boy
                    ? ConfidantChildSearch.SexType.Girl
                    : ConfidantChildSearch.SexType.Boy;
            switch (this.tabType) {
                case TabType.Random:
                    Confidant.getInstance().sendChildSearch(sexType, ChildSearchType.ChildReCommend);
                    break;
                case TabType.Friend:
                    Confidant.getInstance().sendChildSearch(sexType, ChildSearchType.ChildFriend);
                    break;
                case TabType.Union:
                    Confidant.getInstance().sendChildSearch(sexType, ChildSearchType.ChildGuild);
                    break;
                case TabType.Search:
                    tabData.playerData = [];
                    break;
                default:
                    break;
            }
        }
        if (!tabData.playerData) {
            return;
        }

        this.playerData = tabData.playerData;
        if (this.tabType !== TabType.Search) {
            this.playerData.sort((a, b) => b.childInfo.proposeExpireTime - a.childInfo.proposeExpireTime);

            this.listPlayer.scrollView.stopAutoScroll();
            this.listPlayer.setListData(this.playerData);
            this.nodeNoDataTips.active = this.playerData.length === 0;
        } else {
            this.playerData.sort((a, b) => {
                const childInfoA = TBPrincessChild.getInstance().getDataById(a.childInfo.childId);
                let score = 0;
                childInfoA.attribute.forEach(([attrId, init, step]) => {
                    const attrInfo = TBAttribute.getInstance().getDataById(attrId);
                    score = score + (init + step * (a.childInfo.level - 1)) * attrInfo.combat;
                });
                const childInfoB = TBPrincessChild.getInstance().getDataById(b.childInfo.childId);
                let score2 = 0;
                childInfoB.attribute.forEach(([attrId, init, step]) => {
                    const attrInfo = TBAttribute.getInstance().getDataById(attrId);
                    score2 = score2 + (init + step * (b.childInfo.level - 1)) * attrInfo.combat;
                });
                if (score !== score2) {
                    return score > score2 ? -1 : 1;
                }
                const itemInfoA = TBItem.getInstance().getDataById(a.childInfo.childId);
                const itemInfoB = TBItem.getInstance().getDataById(b.childInfo.childId);
                if (itemInfoA.quality !== itemInfoB.quality) {
                    return itemInfoB.quality - itemInfoA.quality;
                }
                return a.childInfo.childId - b.childInfo.childId;
            });

            this.listPlayer2.scrollView.stopAutoScroll();
            this.listPlayer2.setListData(this.playerData);
            this.nodeNoDataTips2.active = this.playerData.length === 0;
        }
    }

    /**
     * 监听渲染事件-玩家列表
     * @param nodeItem 列表item
     * @param index 列表index
     */
    protected onRenderEvent(nodeItem: cc.Node, index: number): void {
        index = Math.abs(index);
        const playerData = this.playerData[index];
        const nodeHead = nodeItem.child("spHead");
        nodeHead.button(playerData.childInfo.childUuid);
        let tempRes = "";
        const childInfo = TBPrincessChild.getInstance().getDataById(playerData.childInfo.childId);
        for (const [level, res] of childInfo.res) {
            if (playerData.childInfo.level >= level) {
                tempRes = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantHead(nodeHead, tempRes);
        nodeItem.child("lbtName").label(playerData.childInfo.name);
        let score = 0;
        childInfo.attribute.forEach(([attrId, init, step]) => {
            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
            score = score + (init + step * (playerData.childInfo.level - 1)) * attrInfo.combat;
        });
        nodeItem.child("lbtScore").label(TextUtils.format(i18n.confidant0015, NumberUtils.format(score, 1, 0)));
        nodeItem.child("btnApply").button(playerData.childInfo.childUuid);
    }

    /**
     * 王储信息
     * @param event 事件
     * @param childUuid 王储uuid
     */
    protected onClickChildInfo(event: cc.Event.EventTouch, childUuid: string): void {
        if (!childUuid) {
            return;
        }

        const playerData = this.playerData.find((e) => e.childInfo.childUuid === childUuid);
        const initData: IFloatChildPreviewArgs = {
            childId: playerData.childInfo.childId,
            playerData: playerData.playerInfo,
        };
        UI.getInstance().open("FloatChildPreview", initData);
    }

    /**
     * 申请
     * @param event 事件
     * @param childUuid 王储uuid
     */
    protected onClickApply(event: cc.Event.EventTouch, childUuid: string): void {
        if (!childUuid) {
            return;
        }

        UI.getInstance().open("FloatInfo", {
            text: i18n.confidant0025,
            confirmCb: () => {
                if (cc.isValid(this.node)) {
                    Confidant.getInstance().sendChildApply(this.childData.data.childUuid, childUuid);
                }
            },
        });
    }

    /**
     * 搜索
     */
    protected onClickSearch(): void {
        if (this.tabType !== TabType.Search) {
            return;
        }
        const gameId = parseInt(this.edbSearch.string);
        if (isNaN(gameId)) {
            Tips.getInstance().info(i18n.confidant0027);
            return;
        }

        const sexType =
            this.childData.info.sex === EnumPrincessChildSex.Boy
                ? ConfidantChildSearch.SexType.Girl
                : ConfidantChildSearch.SexType.Boy;
        Confidant.getInstance().sendChildSearch(sexType, ChildSearchType.ChildAppointSearch, gameId);
    }

    /**
     * 分享
     */
    protected onClickShare(): void {
        const nowTime = Time.getInstance().now();
        if (this.childData.data.proposeExpireTime > nowTime) {
            return;
        }

        UI.getInstance().open("FloatInfo", {
            text: i18n.confidant0024,
            confirmCb: () => {
                if (cc.isValid(this.node)) {
                    Confidant.getInstance().sendChildShare(this.childData.data.childUuid);
                }
            },
        });
    }

    /**
     * 刷新
     */
    protected onClickRefresh(): void {
        if (this.tabType === TabType.Search) {
            return;
        }
        const nowTime = Time.getInstance().now();
        const cdTime = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.RandomCd) * 1000;
        if (this.clickTime !== 0) {
            if (nowTime - this.clickTime < cdTime) {
                return;
            }
        }

        const sexType =
            this.childData.info.sex === EnumPrincessChildSex.Boy
                ? ConfidantChildSearch.SexType.Girl
                : ConfidantChildSearch.SexType.Boy;
        switch (this.tabType) {
            case TabType.Random:
                Confidant.getInstance().sendChildSearch(sexType, ChildSearchType.ChildReCommend);
                break;
            case TabType.Friend:
                Confidant.getInstance().sendChildSearch(sexType, ChildSearchType.ChildFriend);
                break;
            case TabType.Union:
                Confidant.getInstance().sendChildSearch(sexType, ChildSearchType.ChildGuild);
                break;
            default:
                break;
        }

        this.clickTime = nowTime;
        this.lbtRefresh.string = TimeFormat.getInstance().getTextByDuration(
            cdTime - (nowTime - this.clickTime),
            TimeDurationFormatType.D_H_M_S_0
        );

        this.refreshTime = REFRESH_DURATION;
    }

    /**
     * 选择页签
     * @param event 事件
     * @param type 页签类型
     */
    protected onClickSelectTab(event: cc.Event.EventTouch, type: TabType): void {
        if (!type || this.tabType === type) {
            return;
        }

        this.tabType = type;
        this.updateTabState();
        this.updateListInfo();
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
