/*
 * @Author: chenx
 * @Date: 2024-10-14 18:43:16
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-14 19:52:56
 */
import Audio from "../../nsn/audio/Audio";
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { IItemInfo } from "../../protobuf/proto";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import TBPrincess from "../data/parser/TBPrincess";
import ImageUtils from "../utils/ImageUtils";

/**
 * 界面参数
 */
export interface IFloatTravelRewardArgs {
    confidantData: { id: number; exp: number }[]; // 知己数据
    gotItem: IItemInfo[]; // 奖励数据
}

const { ccclass, property } = cc._decorator;

/**
 * 知己-游历奖励
 */
@ccclass
export default class FloatTravelReward extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null; // 标题
    @property(sp.Skeleton)
    spineTitleBg: sp.Skeleton = null; // 标题bg

    @property(cc.Node)
    nodeContent: cc.Node = null; // 奖励content
    @property(cc.Node)
    nodeItem: cc.Node = null; // 奖励item

    initData: IFloatTravelRewardArgs = null; // 初始数据

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/result/spResultTitle5",
            },
        ];
    }

    protected onLoad(): void {
        this.nodeItem.parent = null;

        this.initData = this.args;
        this.initInfo();
        Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.REWARD, AUDIO_EFFECT_PATH.COMMON);
    }

    protected onDestroy(): void {
        this.nodeItem.destroy();
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        this.initData.confidantData.forEach((e) => {
            const nodeItem = Loader.getInstance().instantiate(this.nodeItem);
            this.nodeContent.addChild(nodeItem);

            nodeItem.scale = 0;
            const confidantInfo = TBPrincess.getInstance().getDataById(e.id);
            ImageUtils.setConfidantHead(nodeItem.child("spHead"), confidantInfo.res);
            nodeItem.child("spExpTag").active = true;
            nodeItem.child("lbtExp").label(`+${e.exp}`);
        });
        this.initData.gotItem.forEach((e) => {
            const nodeItem = Loader.getInstance().instantiate(this.nodeItem);
            this.nodeContent.addChild(nodeItem);

            nodeItem.scale = 0;
            ImageUtils.setItemQuality(nodeItem.child("spBg"), e.itemInfoId);
            ImageUtils.setItemIcon(nodeItem.child("spIcon"), e.itemInfoId);
            nodeItem.child("lbtCount").label(e.num + "");
        });
        this.nodeContent.children.forEach((e, i) => {
            cc.tween(e)
                .delay(0.02 * i)
                .to(0.2, { scale: 1 })
                .start();
        });

        this.spineTitleBg.setAnimation(0, "wait", false);
        this.spineTitleBg.addAnimation(0, "wait2", true);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
