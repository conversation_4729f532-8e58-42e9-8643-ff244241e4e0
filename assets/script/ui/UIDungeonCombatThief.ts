/*
 * @Author: chenx
 * @Date: 2025-01-18 09:12:52
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-07 17:22:30
 */
import Loader from "../../nsn/core/Loader";
import UI from "../../nsn/ui/UI";
import MathUtils from "../../nsn/util/MathUtils";
import { BagUpdateRet, DungeonThiefReward } from "../../protobuf/proto";
import Bag from "../game/Bag";
import { CombatMemberState } from "../game/combat/CombatMember";
import CombatMemberMonster from "../game/combat/CombatMemberMonster";
import { CombatPlayerType } from "../game/combat/CombatMemberPlayer";
import DungeonThief from "../game/DungeonThief";
import PrefabCombatDungeonPve from "../prefab/combat/PrefabCombatDungeonPve";

const { ccclass, property } = cc._decorator;

/**
 * 怪盗副本
 */
@ccclass
export default class UIDungeonCombatThief extends PrefabCombatDungeonPve {
    @property(cc.Node)
    nodeReward: cc.Node = null; // 奖励
    @property(cc.Label)
    lbtRewardNum: cc.Label = null; // 奖励数量

    @property(cc.Node)
    nodeCollectAni: cc.Node = null; // 收集动画
    @property(cc.Node)
    nodeBoom: cc.Node = null; // 爆炸
    @property(cc.Node)
    nodeStar: cc.Node = null; // 星星

    private rewardNum: number = -1; // 奖励数量
    private rewardPos: cc.Vec2 = null; // 奖励位置
    private isPlayAniByBoom: boolean = false; // 是否正在播放动画-爆炸
    private poolBoom: cc.Node[] = []; // 爆炸
    private poolStar: cc.Node[] = []; // 星星

    protected onLoad(): void {
        super.onLoad();

        this.nodeBoom.parent = null;
        this.nodeStar.parent = null;
    }

    protected onDestroy(): void {
        super.onDestroy();

        this.nodeBoom.destroy();
        this.nodeStar.destroy();
    }

    protected registerHandler(): void {
        super.registerHandler();

        // 背包-更新
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                if (data.srcReq !== DungeonThiefReward.prototype.clazzName) {
                    return;
                }

                const baseData = this.dungeonPveCtl.getBaseData();
                baseData.resetTime = 0;
                const allPlayer = this.dungeonPveCtl.getAllPlayer();
                const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
                const playerData = player.getData();
                UI.getInstance().open("FloatDungeonEnd", {
                    type: baseData.type,
                    damage: playerData.damage,
                    maxDamage: Math.max(DungeonThief.getInstance().getHistoryMaxScore(), playerData.damage),
                    rewardData: data.gotItem,
                });
            },
            this
        );
    }

    /**
     * 更新怪兽血量状态-多血条
     * @param monster 怪兽
     * @param isInit 是否为初始化调用
     * @param isQuickLoseHp 是否快速掉血
     */
    protected updateMonsterHpStateByMultipleHp(
        monster: CombatMemberMonster,
        isInit: boolean,
        isQuickLoseHp: boolean = false
    ): void {
        super.updateMonsterHpStateByMultipleHp(monster, isInit, isQuickLoseHp);

        const pveBaseData = this.dungeonPveCtl.getBaseData();
        const pveData = this.dungeonPveCtl.getData();
        const allPlayer = this.dungeonPveCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerBaseData = player.getBaseData();
        const monsterBaseData = monster.getBaseData();

        const totalHp = pveData.monsterHpData[pveData.monsterHpIndex][0];
        const startHp = pveData.monsterHpIndex !== 0 ? pveData.monsterHpData[pveData.monsterHpIndex - 1][1] : 0;
        const damage = monsterBaseData.totalHp - monsterBaseData.hp;
        const curHp = totalHp - Math.min(damage - startHp, totalHp);
        const progressHp = MathUtils.floor(curHp / totalHp, 3);
        let rewardNum = pveData.monsterHpIndex;
        pveData.monsterHpIndex === pveData.monsterHpData.length - 1 && progressHp === 0 && rewardNum++;
        if (this.rewardNum === rewardNum) {
            return;
        }
        this.rewardNum = rewardNum;
        if (this.rewardNum === 0) {
            this.lbtRewardNum.string = `x${rewardNum}`;
            return;
        }

        const compMonsterItem = this.sceneCtl.getMonsterItem(monsterBaseData.uuid);
        let pos = compMonsterItem.getBeAttackedPoint2();
        pos = this.nodeCollectAni.convertToNodeSpaceAR(pos);
        if (!this.isPlayAniByBoom) {
            this.isPlayAniByBoom = true;

            const nodeBoom = this.getEffectBoom();
            nodeBoom.setPosition(pos);
            nodeBoom.opacity = 255;
            const spineBoom = nodeBoom.getComponent(sp.Skeleton);
            spineBoom.setCompleteListener(() => {
                spineBoom.setCompleteListener(null);

                nodeBoom.opacity = 0;
                spineBoom.clearTrack(0);
                this.poolBoom.push(nodeBoom);

                this.isPlayAniByBoom = false;
            });
            spineBoom.setAnimation(0, "wait", false);
        }

        const starNum = 5;
        let pos2 = compMonsterItem.getBeAttackedPoint();
        pos2 = this.nodeCollectAni.convertToNodeSpaceAR(pos2);
        if (!this.rewardPos) {
            this.rewardPos = this.nodeReward.convertToWorldSpaceAR(cc.v2());
            this.rewardPos = this.nodeCollectAni.convertToNodeSpaceAR(this.rewardPos);
        }
        for (let i = 0; i < starNum; i++) {
            const nodeStar = this.getEffectStar();
            const curPos = cc.v2(pos.x + MathUtils.getRandomInt(-150, 150), pos2.y + MathUtils.getRandomInt(-50, 50));
            nodeStar.setPosition(curPos);
            const flyDuration = MathUtils.floor(0.4 + MathUtils.getRandomValue(-0.1, 0.1), 1);
            const transPos =
                i % 2
                    ? cc.v2(curPos.x - (curPos.x - this.rewardPos.x) / 3, curPos.y)
                    : cc.v2(curPos.x, curPos.y + (this.rewardPos.y - curPos.y) / 3);
            transPos.x += MathUtils.getRandomInt(-50, 50);
            transPos.y += MathUtils.getRandomInt(-50, 50);
            cc.tween(nodeStar)
                .delay(0.05 * i)
                .to(0.1, { opacity: 255 })
                .delay(0.1)
                .bezierTo(flyDuration, transPos, transPos, this.rewardPos)
                .to(0.05, { opacity: 0 })
                .call(() => {
                    this.poolStar.push(nodeStar);

                    if (i === starNum - 1) {
                        this.lbtRewardNum.string = `x${rewardNum}`;

                        if (rewardNum === pveData.monsterHpData.length) {
                            this.compSceneBgMove.setMoveState(false);
                            const compTankItem = this.sceneCtl.getTankItem(playerBaseData.uuid);
                            compTankItem.setState(CombatMemberState.Wait);
                            compMonsterItem.playDieAni(pveBaseData.isSkipingCombat);
                        }
                    }
                })
                .start();
        }
    }

    /**
     * 获取特效-爆炸
     * @returns
     */
    private getEffectBoom(): cc.Node {
        let nodeEffect: cc.Node = null;
        if (this.poolBoom.length > 0) {
            nodeEffect = this.poolBoom.shift();
        } else {
            nodeEffect = Loader.getInstance().instantiate(this.nodeBoom);
            this.nodeCollectAni.addChild(nodeEffect);
        }

        return nodeEffect;
    }

    /**
     * 获取特效-星星
     * @returns
     */
    private getEffectStar(): cc.Node {
        let nodeEffect: cc.Node = null;
        if (this.poolStar.length > 0) {
            nodeEffect = this.poolStar.shift();
        } else {
            nodeEffect = Loader.getInstance().instantiate(this.nodeStar);
            this.nodeCollectAni.addChild(nodeEffect);
        }

        return nodeEffect;
    }
}
