/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-23 16:20:01
 */

import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { UnionSiegeGetActiveRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumUnionPara } from "../data/base/BaseUnion";
import TBUnion from "../data/parser/TBUnion";
import Union from "../game/Union";
import UnionSiege from "../game/UnionSiege";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatActivityUnionEntryDetails extends I18nComponent {
    @property(cc.Node)
    text1: cc.Node = null;
    @property(cc.Node)
    text2: cc.Node = null;
    @property(cc.Node)
    text3: cc.Node = null;

    protected onLoad(): void {
        UI.getInstance().showSoftLoading();
        UnionSiege.getInstance().sendUnionSiegeGetActive();
    }

    protected registerHandler(): void {
        UnionSiege.getInstance().on(
            UnionSiegeGetActiveRet.prototype.clazzName,
            (personalActivePoint: number, sumActivePoint: number) => {
                const unionData = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseCondition);
                const personUnionData = TBUnion.getInstance().getValueByPara(EnumUnionPara.PersonEnliven);
                const member = Union.getInstance().getMembers();
                this.text1.richText(
                    member.length >= unionData[0]
                        ? TextUtils.format(i18n.unionDefense0003, member.length, unionData[0])
                        : TextUtils.format(i18n.unionDefense0002, member.length, unionData[0])
                );
                this.text2.richText(
                    sumActivePoint >= unionData[1]
                        ? TextUtils.format(i18n.unionDefense0005, sumActivePoint, unionData[1])
                        : TextUtils.format(i18n.unionDefense0004, sumActivePoint, unionData[1])
                );
                this.text3.richText(
                    personalActivePoint >= personUnionData
                        ? TextUtils.format(i18n.unionDefense00027, personalActivePoint, personUnionData)
                        : TextUtils.format(i18n.unionDefense00026, personalActivePoint, personUnionData)
                );
            },
            this
        );
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
