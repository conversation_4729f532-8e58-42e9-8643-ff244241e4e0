/*
 * @Author: chenx
 * @Date: 2024-08-15 15:52:37
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:53:07
 */
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { IItemInfo, IPlayerInfo } from "../../protobuf/proto";
import { DungeonType } from "../game/Combat";
import Player from "../game/Player";
import ImageUtils from "../utils/ImageUtils";
import PlayerInfoUtils from "../utils/PlayerInfoUtils";

/**
 * 界面参数
 */
export interface IFloatCombatPvpParkResultArgs {
    type: DungeonType; // 类型
    playerData: IPlayerInfo; // 玩家数据-对手
    rewardData: IItemInfo[]; // 奖励数据
    isWin: boolean; // 是否胜利
}

const { ccclass, property } = cc._decorator;

/**
 * pvp战斗-停车场结果
 */
@ccclass
export default class FloatCombatPvpParkResult extends I18nComponent {
    @property(cc.Node)
    nodeWinInfo: cc.Node = null; // 胜利信息
    @property(cc.Node)
    nodeWinBg: cc.Node = null; // 胜利背景
    @property(cc.Node)
    spTitle: cc.Node = null; // 标题

    @property(cc.Node)
    nodeLoseInfo: cc.Node = null; // 失败信息
    @property(cc.Node)
    nodeLoseBg: cc.Node = null; // 失败背景
    @property(cc.Node)
    spTitle2: cc.Node = null; // 标题

    @property(cc.Node)
    nodeHead: cc.Node = null; // 头像
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    power: cc.Node = null;
    @property(cc.Node)
    nodeHead2: cc.Node = null; // 头像
    @property(cc.Label)
    lbtName2: cc.Label = null; // 名称
    @property(cc.Node)
    power2: cc.Node = null;
    @property(cc.Node)
    nodeContent: cc.Node = null; // 奖励content
    @property(cc.Node)
    nodeItem: cc.Node = null; // 奖励item

    initData: IFloatCombatPvpParkResultArgs = null; // 初始数据

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/result/spResultTitle12",
            },
            {
                sprite: this.spTitle2,
                url: "texture/result/spResultTitle13",
            },
        ];
    }

    protected onLoad(): void {
        this.nodeItem.parent = null;

        this.initData = this.args;
        this.init();
    }

    protected onDestroy(): void {
        this.nodeItem.destroy();
    }

    /**
     * 初始化
     */
    private init(): void {
        this.nodeWinInfo.active = this.initData.isWin;
        this.nodeLoseInfo.active = !this.initData.isWin;
        this.nodeWinBg.active = this.initData.isWin;
        this.nodeLoseBg.active = !this.initData.isWin;

        PlayerInfoUtils.updateHead(this.nodeHead, Player.getInstance().getInfo());
        this.lbtName.string = Player.getInstance().getName();
        PlayerInfoUtils.updateHead(this.nodeHead2, this.initData.playerData);
        this.lbtName2.string = this.initData.playerData.name;
        this.initData.rewardData.forEach((v) => {
            const nodeItem = Loader.getInstance().instantiate(this.nodeItem);
            this.nodeContent.addChild(nodeItem);

            ImageUtils.setItemQuality(nodeItem.child("spBg"), v.itemInfoId);
            ImageUtils.setItemIcon(nodeItem.child("spIcon"), v.itemInfoId);
            nodeItem.child("lbtCount").label(v.num !== 1 ? v.num + "" : "");
        });
        PlayerInfoUtils.updatePower(this.power, Player.getInstance().getInfo()); // 本人王权信息
        PlayerInfoUtils.updatePower(this.power2, this.initData.playerData); // 对方王权信息
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().closeToWindow("UIDungeonCombatPark");
        UI.getInstance().close();
    }
}
