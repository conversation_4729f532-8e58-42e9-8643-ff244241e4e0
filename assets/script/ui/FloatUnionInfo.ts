/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-18 14:19:03
 */

import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import { IUnionInfo, IUnionMember, UnionApplySubmit, UnionApplySubmitRet, UnionGetRet } from "../../protobuf/proto";
import GrayComp from "../comp/GrayComp";
import i18n from "../config/i18n/I18n";
import TBPower from "../data/parser/TBPower";
import TBUnionLevel from "../data/parser/TBUnionLevel";
import Power from "../game/Power";
import Union from "../game/Union";
import ImageUtils from "../utils/ImageUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloatUnionInfo extends I18nComponent {
    @property(cc.Node)
    flag: cc.Node = null;
    @property(cc.Node)
    unionId: cc.Node = null;
    @property(cc.Node)
    unionLv: cc.Node = null;
    @property(cc.Node)
    unionName: cc.Node = null;
    @property(cc.Node)
    unionCount: cc.Node = null;
    @property(cc.Node)
    leadName: cc.Node = null;
    @property(cc.Node)
    unionDetails: cc.Node = null;
    @property(ListView)
    list: ListView = null;
    @property(cc.Node)
    btnApply: cc.Node = null;
    @property(cc.Node)
    nodeApplying: cc.Node = null;
    @property(cc.Node)
    nodeCondition: cc.Node = null;

    private unionInfo: IUnionInfo = null;

    protected onLoad(): void {
        const { unionInfo, members, status } = this.args;
        this.unionInfo = unionInfo;
        this.updateInfo(unionInfo, members, status);
    }

    protected registerHandler(): void {
        Union.getInstance().on(
            UnionApplySubmitRet.prototype.clazzName,
            (unionId: number) => {
                if (this.unionInfo.id === unionId) {
                    this.btnApply.active = false;
                    this.nodeApplying.active = true;
                    this.nodeCondition.active = false;
                }
            },
            this
        );
    }

    protected updateInfo(unionInfo: IUnionInfo, members: IUnionMember[], status: UnionGetRet.Status): void {
        const level = TBUnionLevel.getInstance().getLevelByExp(unionInfo.exp);
        const data = TBUnionLevel.getInstance().getDataById(level);

        ImageUtils.setUnionFlag(this.flag, unionInfo.flag);
        this.unionId.label("ID：" + unionInfo.id);
        this.unionLv.label("Lv." + level);
        this.unionName.label(unionInfo.name);
        this.unionCount.label(members.length + "/" + data.peopleLimit);
        this.leadName.label(unionInfo.ownerName);
        this.unionDetails.label(unionInfo.publicize);
        switch (status) {
            case UnionGetRet.Status.InUnion:
                this.btnApply.active = true;
                this.nodeApplying.active = false;
                this.nodeCondition.active = false;
                this.btnApply.getComponent(GrayComp).gray = true;
                this.btnApply.getComponent(cc.Button).interactable = false;
                break;
            case UnionGetRet.Status.Applying:
                this.btnApply.active = false;
                this.nodeApplying.active = true;
                this.nodeCondition.active = false;
                break;
            case UnionGetRet.Status.NoneStatus:
                this.nodeApplying.active = false;
                this.btnApply.active = true;
                const { kingLevelId } = Power.getInstance().getPowerInfo();
                if (!unionInfo.lowestLevel || kingLevelId >= unionInfo.lowestLevel) {
                    this.nodeCondition.active = false;
                    this.btnApply.getComponent(GrayComp).gray = false;
                    this.btnApply.getComponent(cc.Button).interactable = true;
                } else {
                    this.nodeCondition.active = true;
                    const powerData = TBPower.getInstance().getDataById(unionInfo.lowestLevel);
                    this.nodeCondition.richText(TextUtils.format(i18n.union0052, powerData.name));
                    this.btnApply.getComponent(GrayComp).gray = true;
                    this.btnApply.getComponent(cc.Button).interactable = false;
                }
                break;
            default:
                break;
        }

        members.sort((a, b) => {
            if (a.unionPositionId !== b.unionPositionId) {
                return a.unionPositionId - b.unionPositionId;
            }
            return b.player.combatScore - a.player.combatScore;
        });
        this.list.setListData(members);
    }

    protected onClickApply(): void {
        Union.getInstance().sendUnionApplySubmit(UnionApplySubmit.SubmitType.None, this.unionInfo.id);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
