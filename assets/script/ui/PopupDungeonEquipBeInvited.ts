/*
 * @Author: chenx
 * @Date: 2024-09-02 13:48:39
 * @Last Modified by: chenx
 * @Last Modified time: 2024-09-19 18:13:31
 */
import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { EquipDungeonInviteNoticeRet } from "../../protobuf/proto";
import DungeonEquip, { DungeonEquipEvent } from "../game/DungeonEquip";
import { IDungeonEquipBeInvitedData } from "../prefab/dungeon/PrefabDungeonEquipBeInvitedItem";

const { ccclass, property } = cc._decorator;

/**
 * 装备副本-被邀请
 */
@ccclass
export default class PopupDungeonEquipBeInvited extends I18nComponent {
    @property(ListView)
    listBeInvited: ListView = null; // 被邀请列表

    beInvitedData: IDungeonEquipBeInvitedData[] = null; // 被邀请数据

    protected onLoad(): void {
        this.beInvitedData = this.args;
        this.updateListInfo();
    }

    protected registerHandler(): void {
        DungeonEquip.getInstance().on(
            EquipDungeonInviteNoticeRet.prototype.clazzName,
            (data: EquipDungeonInviteNoticeRet) => {
                this.beInvitedData.push({
                    playerData: data.playerInfo,
                    teamId: data.teamId,
                    levelId: data.dungeonId,
                    time: 0,
                });
                this.updateListInfo();
            },
            this
        );
        DungeonEquip.getInstance().on(
            DungeonEquipEvent.DeleteBeInviteData,
            (playerId: string) => {
                const index = this.beInvitedData.findIndex((e) => e.playerData.playerId === playerId);
                if (index !== -1) {
                    this.beInvitedData.splice(index, 1);
                    this.updateListInfo();
                }
            },
            this
        );
    }

    /**
     * 更新列表信息-被邀请列表
     */
    private updateListInfo(): void {
        this.listBeInvited.scrollView.stopAutoScroll();
        this.listBeInvited.cleanList();
        this.listBeInvited.setListData(this.beInvitedData);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
