/*
 * @Author: chenx
 * @Date: 2024-09-29 15:33:28
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-11-29 17:32:48
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import MathUtils from "../../nsn/util/MathUtils";
import TextUtils from "../../nsn/util/TextUtils";
import Tips from "../../nsn/util/Tips";
import {
    BagUpdateRet,
    ConfidantCreatePrincessNotice,
    ConfidantFavorType,
    ConfidantPrincessFavorRet,
    ConfidantPrincessMarry,
    ConfidantPrincessMarryRet,
    ConfidantPrincessUpgradeRet,
    IItemInfo,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumPrincessTotalPara } from "../data/base/BasePrincessTotal";
import TBAttribute from "../data/parser/TBAttribute";
import TBEvent from "../data/parser/TBEvent";
import TBItem from "../data/parser/TBItem";
import TBPrincess from "../data/parser/TBPrincess";
import TBPrincessChildGroup from "../data/parser/TBPrincessChildGroup";
import TBPrincessTotal from "../data/parser/TBPrincessTotal";
import Bag from "../game/Bag";
import Confidant from "../game/Confidant";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import { IFloatConfidantUpgradeArgs } from "./FloatConfidantUpgrade";
import { IPopupConfidantLevelInfoArgs } from "./PopupConfidantLevelInfo";

/**
 * 界面参数
 */
export interface IUIConfidantInfoArgs {
    confidantId: number[]; // 知己id
    confidantIndex: number; // 知己index
}

const { ccclass, property } = cc._decorator;

/**
 * 知己-知己信息
 */
@ccclass
export default class UIConfidantInfo extends I18nComponent {
    @property(cc.Sprite)
    spIcon: cc.Sprite = null; // 知己icon
    @property(cc.Sprite)
    spQualityIcon: cc.Sprite = null; // 品质icon
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    nodeDialogBox: cc.Node = null; // 对话框
    @property(cc.Label)
    lbtDialog: cc.Label = null; // 对话

    @property(cc.Node)
    nodeLevel: cc.Node = null; // 等级
    @property(cc.Label)
    lbtLevel: cc.Label = null; // 等级
    @property(cc.ProgressBar)
    prgExp: cc.ProgressBar = null; // 经验
    @property(cc.Label)
    lbtExp: cc.Label = null; // 经验
    @property(cc.RichText)
    rtNextAttr: cc.RichText = null; // 下一等级属性
    @property(cc.Node)
    nodeChildProb: cc.Node = null; // 王储概率按钮

    @property(cc.Button)
    btnUnlock: cc.Button = null; // 解锁按钮
    @property(cc.Button)
    btnUpgrade: cc.Button = null; // 升级按钮
    @property(cc.Button)
    btnMarry: cc.Button = null; // 结婚按钮
    @property(cc.Button)
    btnFavor: cc.Button = null; // 宠幸按钮

    @property(cc.Node)
    nodePre: cc.Node = null; // 切换上一个按钮
    @property(cc.Node)
    nodeNext: cc.Node = null; // 切换下一个按钮

    @property(cc.Node)
    nodeBlockEvent: cc.Node = null; // 全局拦截事件

    initData: IUIConfidantInfoArgs = null; // 初始数据
    eventId: number = -1; // 事件id
    eventReward: IItemInfo[] = null; // 事件奖励

    protected onLoad(): void {
        this.initData = this.args;
        this.updateConfidantInfo();
        this.updateConfidantState();
    }

    protected registerHandler(): void {
        Confidant.getInstance().on(
            ConfidantCreatePrincessNotice.prototype.clazzName,
            (data: ConfidantCreatePrincessNotice) => {
                if (this.initData.confidantId[this.initData.confidantIndex] === data.princessInfo.princessId) {
                    this.updateConfidantState();
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantPrincessUpgradeRet.prototype.clazzName,
            (data: ConfidantPrincessUpgradeRet) => {
                if (this.initData.confidantId[this.initData.confidantIndex] === data.princessInfo.princessId) {
                    this.updateConfidantState();
                }

                if (data.princessInfo.progress === 0) {
                    const initData: IFloatConfidantUpgradeArgs = {
                        confidantId: data.princessInfo.princessId,
                        childId: -1,
                        level: data.princessInfo.level,
                    };
                    UI.getInstance().open("FloatConfidantUpgrade", initData);
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantPrincessMarryRet.prototype.clazzName,
            (data: ConfidantPrincessMarryRet) => {
                if (this.initData.confidantId[this.initData.confidantIndex] === data.princessId) {
                    this.updateConfidantState();
                }

                this.eventId = data.eventId;
                if (this.eventId !== -1 && this.eventReward) {
                    cc.Tween.stopAllByTarget(this.nodeDialogBox);
                    this.nodeDialogBox.scale = 0;

                    this.nodeBlockEvent.active = true;
                    const eventInfo = TBEvent.getInstance().getDataById(this.eventId);
                    this.lbtDialog.string = eventInfo.text;
                    cc.tween(this.nodeDialogBox)
                        .to(0.2, { scale: 1.1 }, { easing: cc.easing.sineOut })
                        .to(0.1, { scale: 1 }, { easing: cc.easing.sineIn })
                        .delay(2)
                        .call(() => {
                            this.nodeDialogBox.scale = 0;
                            this.nodeBlockEvent.active = false;

                            UI.getInstance().open("FloatReward", { gotItem: this.eventReward });

                            this.eventId = -1;
                            this.eventReward = null;
                        })
                        .start();
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantPrincessFavorRet.prototype.clazzName,
            (data: ConfidantPrincessFavorRet) => {
                if (data.favorType !== ConfidantFavorType.Appoint) {
                    return;
                }

                if (this.initData.confidantId[this.initData.confidantIndex] === data.princessId) {
                    this.updateConfidantState();
                }

                cc.Tween.stopAllByTarget(this.nodeDialogBox);
                this.nodeDialogBox.scale = 0;

                this.nodeBlockEvent.active = true;
                const childGetGroupInfo = TBPrincessChildGroup.getInstance().getDataById(data.princessChildGroupId);
                const dialog = childGetGroupInfo.text.split("#");
                const index = MathUtils.getRandomInt(0, dialog.length - 1);
                this.lbtDialog.string = dialog[index];
                cc.tween(this.nodeDialogBox)
                    .to(0.2, { scale: 1.1 }, { easing: cc.easing.sineOut })
                    .to(0.1, { scale: 1 }, { easing: cc.easing.sineIn })
                    .delay(2)
                    .call(() => {
                        this.nodeDialogBox.scale = 0;
                        this.nodeBlockEvent.active = false;

                        UI.getInstance().open("FloatConfidantGetChild", data.childInfos[0].childId);
                    })
                    .start();
            },
            this
        );
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                if (data.srcReq === ConfidantPrincessMarry.prototype.clazzName) {
                    this.eventReward = data.gotItem;
                    if (this.eventId !== -1 && this.eventReward) {
                        cc.Tween.stopAllByTarget(this.nodeDialogBox);
                        this.nodeDialogBox.scale = 0;

                        this.nodeBlockEvent.active = true;
                        const eventInfo = TBEvent.getInstance().getDataById(this.eventId);
                        this.lbtDialog.string = eventInfo.text;
                        cc.tween(this.nodeDialogBox)
                            .to(0.2, { scale: 1.1 }, { easing: cc.easing.sineOut })
                            .to(0.1, { scale: 1 }, { easing: cc.easing.sineIn })
                            .delay(2)
                            .call(() => {
                                this.nodeDialogBox.scale = 0;
                                this.nodeBlockEvent.active = false;

                                UI.getInstance().open("FloatReward", { gotItem: this.eventReward });

                                this.eventId = -1;
                                this.eventReward = null;
                            })
                            .start();
                    }
                }
            },
            this
        );
    }

    /**
     * 更新知己信息
     */
    protected updateConfidantInfo(): void {
        const confidantId = this.initData.confidantId[this.initData.confidantIndex];
        const confidantInfo = TBPrincess.getInstance().getDataById(confidantId);
        ImageUtils.setConfidantIcon2(this.spIcon, confidantInfo.res);
        const itemInfo = TBItem.getInstance().getDataById(confidantId);
        ImageUtils.setConfidantQualityIcon(this.spQualityIcon, itemInfo.quality);
        this.lbtName.string = confidantInfo.name;

        this.nodePre.active = this.initData.confidantIndex !== 0;
        this.nodeNext.active = this.initData.confidantIndex !== this.initData.confidantId.length - 1;
    }

    /**
     * 更新知己状态
     */
    private updateConfidantState(): void {
        const confidantId = this.initData.confidantId[this.initData.confidantIndex];
        const confidantData = Confidant.getInstance().getConfidantData(confidantId);
        const confidantInfo = TBPrincess.getInstance().getDataById(confidantId);

        this.nodeLevel.active = !!confidantData;
        if (confidantData) {
            this.lbtLevel.string = confidantData.level + "";
            if (confidantData.level === confidantInfo.levelLimit) {
                this.prgExp.progress = 1;
                this.lbtExp.string = i18n.bless0001;
                this.rtNextAttr.string = `<outline color=#000000 width=2>${i18n.confidant0004}</o>`;
            } else {
                const totalExp =
                    confidantInfo.upgradeRequired[1] + confidantInfo.upgradeRequired[2] * (confidantData.level - 1);
                this.prgExp.progress = Math.floor((confidantData.progress / totalExp) * 1000) / 1000;
                this.lbtExp.string = `${confidantData.progress}/${totalExp}`;
                const [attrId, init, step] = confidantInfo.attribute[0];
                const attrData = TBAttribute.getInstance().formatAttribute([
                    attrId,
                    init + step * (confidantData.level - 1),
                ]);
                this.rtNextAttr.string = `<outline color=#000000 width=2>${i18n.confidant0005}<color=#FF7301>${attrData.name}+${attrData.value}</c></o>`;
            }
        }

        this.btnUnlock.node.active = !confidantData;
        if (!confidantData) {
            const nodeCostContent = this.btnUnlock.node.child("contentCost");
            const nodeIcon = nodeCostContent.child("spIcon");
            nodeIcon.button(confidantInfo.unLock[0][0]);
            ImageUtils.setItemIcon(nodeIcon, confidantInfo.unLock[0][0]);
            const haveCount = Bag.getInstance().getItemCountById(confidantInfo.unLock[0][0]);
            nodeCostContent.child("lbtCount").label(`${haveCount}/${confidantInfo.unLock[0][1]}`);
        }

        const isShowUpgrade = confidantData && confidantData.level < confidantInfo.levelLimit;
        this.btnUpgrade.node.active = isShowUpgrade;
        if (isShowUpgrade) {
            const nodeCost = this.btnUpgrade.node.child("nodeCost");
            const nodeIcon = nodeCost.child("spIcon");
            const nodeCount = nodeCost.child("lbtCount");

            nodeIcon.button(confidantInfo.upgradeRequired[0]);
            ImageUtils.setItemIcon(nodeIcon, confidantInfo.upgradeRequired[0]);
            const haveCount = Bag.getInstance().getItemCountById(confidantInfo.upgradeRequired[0]);
            const totalExp =
                confidantInfo.upgradeRequired[1] + confidantInfo.upgradeRequired[2] * (confidantData.level - 1);
            const needExp = totalExp - confidantData.progress;
            if (haveCount > 0 && haveCount < needExp) {
                nodeCount.label(`${haveCount}/${haveCount}`);
            } else {
                nodeCount.label(`${haveCount}/${needExp}`);
            }
        }

        const isShowMarry = confidantData && !confidantData.isMarry;
        this.btnMarry.node.active = isShowMarry;
        if (isShowMarry) {
            const nodeCost = this.btnMarry.node.child("nodeCost");
            const nodeIcon = nodeCost.child("spIcon");
            const nodeCount = nodeCost.child("lbtCount");

            const marryLevel = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.MarriageLevel);
            const [costId, count] = TBPrincessTotal.getInstance().getValueByPara(
                EnumPrincessTotalPara.MarriageExpenses
            )[0];
            const isMarry = confidantData.level >= marryLevel;
            nodeIcon.active = isMarry;
            if (isMarry) {
                nodeIcon.button(costId);
                ImageUtils.setItemIcon(nodeIcon, costId);
                nodeCount.label(TextUtils.format(i18n.confidant0037, marryLevel));
            } else {
                const haveCount = Bag.getInstance().getItemCountById(costId);
                nodeCount.label(`${haveCount}/${count}`);
            }
            CocosExt.setButtonEnable(this.btnMarry, isMarry);
        }

        this.btnFavor.node.active = confidantData && confidantData.isMarry;
        if (confidantData && confidantData.isMarry) {
            const nodeCostContent = this.btnFavor.node.child("contentCost");
            const [costId, count] = TBPrincessTotal.getInstance().getValueByPara(
                EnumPrincessTotalPara.HoneymoonConsumption
            )[0];
            const nodeIcon = nodeCostContent.child("spIcon");
            nodeIcon.button(costId);
            ImageUtils.setItemIcon(nodeIcon, costId);
            const haveCount = Bag.getInstance().getItemCountById(costId);
            nodeCostContent.child("lbtCount").label(`${haveCount}/${count}`);
        }

        this.nodeChildProb.active = confidantData && confidantData.isMarry;
    }

    /**
     * 等级信息
     */
    protected onClickLevelInfo(): void {
        const confidantId = this.initData.confidantId[this.initData.confidantIndex];
        const confidantData = Confidant.getInstance().getConfidantData(confidantId);
        if (!confidantData) {
            return;
        }

        const initData: IPopupConfidantLevelInfoArgs = {
            confidantId,
            childUuid: "",
        };
        UI.getInstance().open("PopupConfidantLevelInfo", initData);
    }

    /**
     * 王储概率
     */
    protected onClickChildProb(): void {
        const confidantId = this.initData.confidantId[this.initData.confidantIndex];
        const confidantData = Confidant.getInstance().getConfidantData(confidantId);
        if (!confidantData) {
            return;
        }
        if (!confidantData.isMarry) {
            return;
        }

        const initData = {
            title: i18n.confidant0006,
            qualityName: [
                i18n.confidant0007,
                i18n.confidant0008,
                i18n.confidant0009,
                i18n.confidant0010,
                i18n.confidant0011,
            ],
            prob: [],
        };
        const confidantInfo = TBPrincess.getInstance().getDataById(confidantId);
        const childGetGroupInfo = TBPrincessChildGroup.getInstance().getDataByGroupAndLevel(
            confidantInfo.randomGetChild[0][0],
            confidantData.level
        );
        childGetGroupInfo.rewardShow.forEach((e) => initData.prob.push(`${e}%`));
        UI.getInstance().open("PopupEquipRate", initData);
    }

    /**
     * 解锁
     */
    protected onClickUnlock(): void {
        const confidantId = this.initData.confidantId[this.initData.confidantIndex];
        const confidantData = Confidant.getInstance().getConfidantData(confidantId);
        if (confidantData) {
            return;
        }
        const confidantInfo = TBPrincess.getInstance().getDataById(confidantId);
        if (!Bag.getInstance().isEnough(confidantInfo.unLock[0][0], confidantInfo.unLock[0][1])) {
            Tips.getInstance().info(i18n.common0025);
            return;
        }

        Confidant.getInstance().sendUnlockByConfidant(confidantId);
    }

    /**
     * 升级
     */
    protected onClickUpgrade(): void {
        const confidantId = this.initData.confidantId[this.initData.confidantIndex];
        const confidantData = Confidant.getInstance().getConfidantData(confidantId);
        if (!confidantData) {
            return;
        }
        const confidantInfo = TBPrincess.getInstance().getDataById(confidantId);
        if (confidantData.level >= confidantInfo.levelLimit) {
            return;
        }
        const haveCount = Bag.getInstance().getItemCountById(confidantInfo.upgradeRequired[0]);
        if (haveCount < 1) {
            Tips.getInstance().info(i18n.common0025);
            return;
        }

        const totalExp =
            confidantInfo.upgradeRequired[1] + confidantInfo.upgradeRequired[2] * (confidantData.level - 1);
        const needExp = totalExp - confidantData.progress;
        Confidant.getInstance().sendUpgradeByConfidant(confidantId, Math.min(needExp, haveCount));
    }

    /**
     * 结婚
     */
    protected onClickMarry(): void {
        const confidantId = this.initData.confidantId[this.initData.confidantIndex];
        const confidantData = Confidant.getInstance().getConfidantData(confidantId);
        if (!confidantData) {
            return;
        }
        if (confidantData.isMarry) {
            return;
        }
        const marryLevel = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.MarriageLevel);
        if (confidantData.level < marryLevel) {
            return;
        }
        const [costId, count] = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.MarriageExpenses)[0];
        if (!Bag.getInstance().isEnough(costId, count)) {
            Tips.getInstance().info(i18n.common0025);
            return;
        }

        Confidant.getInstance().sendMarryByConfidant(confidantId);
    }

    /**
     * 宠幸
     */
    protected onClickFavor(): void {
        const confidantId = this.initData.confidantId[this.initData.confidantIndex];
        const confidantData = Confidant.getInstance().getConfidantData(confidantId);
        if (!confidantData) {
            return;
        }
        if (!confidantData.isMarry) {
            return;
        }
        const [costId, count] = TBPrincessTotal.getInstance().getValueByPara(
            EnumPrincessTotalPara.HoneymoonConsumption
        )[0];
        if (!Bag.getInstance().isEnough(costId, count)) {
            Tips.getInstance().info(i18n.common0025);
            return;
        }

        Confidant.getInstance().sendFavorByConfidant(confidantId, ConfidantFavorType.Appoint);
    }

    /**
     * 切换上一个
     */
    protected onClickPre(): void {
        if (this.initData.confidantIndex === 0) {
            return;
        }

        this.initData.confidantIndex--;
        this.updateConfidantInfo();
        this.updateConfidantState();
    }

    /**
     * 切换下一个
     */
    protected onClickNext(): void {
        if (this.initData.confidantIndex === this.initData.confidantId.length - 1) {
            return;
        }

        this.initData.confidantIndex++;
        this.updateConfidantInfo();
        this.updateConfidantState();
    }

    /**
     * 道具信息
     * @param event 事件
     * @param itemId 道具id
     */
    protected onClickItemInfo(event: cc.Event.EventTouch, itemId: number): void {
        if (!itemId) {
            return;
        }

        ItemUtils.showInfo(itemId);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
