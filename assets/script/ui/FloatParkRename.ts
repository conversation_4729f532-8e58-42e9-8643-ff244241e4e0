/*
 * @Author: chenx
 * @Date: 2024-07-29 11:36:37
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-22 15:08:46
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { PlotModifyNameRet } from "../../protobuf/proto";
import Park from "../game/Park";

/**
 * 界面参数
 */
export interface IFloatParkRenameArgs {
    title: string; // 标题
    renameCb: (name: string) => void; // 重命名回调
}

const { ccclass, property } = cc._decorator;

/**
 * 停车场-重命名
 */
@ccclass
export default class FloatParkRename extends I18nComponent {
    @property(cc.Label)
    lbtTitle: cc.Label = null; // 标题
    @property(cc.EditBox)
    edbRename: cc.EditBox = null; // 重命名

    initData: IFloatParkRenameArgs = null; // 初始数据

    protected onLoad(): void {
        this.initData = this.args;
        this.lbtTitle.string = this.initData.title;
    }

    protected registerHandler(): void {
        Park.getInstance().on(
            PlotModifyNameRet.prototype.clazzName,
            () => {
                UI.getInstance().close();
            },
            this
        );
    }

    /**
     * 确认
     */
    protected onClickConfirm(): void {
        const name = this.edbRename.string;
        this.initData.renameCb(name);
    }

    /**
     * 关闭界面
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
