/*
 * @Author: <PERSON><PERSON>d
 * @Date: 2024-03-18 11:35:03
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-15 15:12:19
 */

import ListView from "../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { RankUnionGetRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import TBRank, { RANK_ID } from "../data/parser/TBRank";
import TBUnionLevel from "../data/parser/TBUnionLevel";
import Rank from "../game/Rank";
import ImageUtils from "../utils/ImageUtils";
import NumberUtils from "../utils/NumberUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupUnionRank extends I18nComponent {
    @property(cc.Node)
    title: cc.Node = null;

    @property([cc.Node])
    top3: cc.Node[] = [];
    @property(cc.Node)
    mine: cc.Node = null;

    @property(cc.Node)
    scoreTitle: cc.Node = null;
    @property(ListView)
    list: ListView = null;
    @property(cc.Node)
    none: cc.Node = null;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.title,
                url: "texture/syncUI/rank/spRankPopTitleTxt",
            },
        ];
    }

    protected onLoad(): void {
        if (Rank.getInstance().isUnionRankOutdated(RANK_ID.UNION_COMBAT)) {
            UI.getInstance().showSoftLoading();
            Rank.getInstance().sendRankUnionGetRet(RANK_ID.UNION_COMBAT);
        } else {
            this.updateUI();
        }
    }

    protected onDestroy(): void {
        UI.getInstance().hideSoftLoading();
    }

    protected registerHandler(): void {
        Rank.getInstance().on(
            RankUnionGetRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateTop3();
        this.updateListInfo();
        this.updateMyRank();
    }

    private updateTop3(): void {
        const data = Rank.getInstance().getUnionRankDataById(RANK_ID.UNION_COMBAT);
        for (let i = 0; i < 3; i++) {
            const node = this.top3[i];
            const info = data.ranks[i];
            const role = node.child("role");
            const none = node.child("none");
            if (data.ranks[i]) {
                role.active = true;
                none.active = false;
                ImageUtils.setUnionFlag(role.child("flag"), info.union.flag);
                const name = role.child("name");
                const btn = role.child("btn");
                name.label(info.union.name);
                btn.button(info.union.id);
            } else {
                role.active = false;
                none.active = true;
            }
        }
    }

    private updateListInfo(): void {
        const data = Rank.getInstance().getUnionRankDataById(RANK_ID.UNION_COMBAT);
        const rankData = TBRank.getInstance().getDataById(RANK_ID.UNION_COMBAT);
        this.scoreTitle.label(rankData.title);
        this.list.setListData(data.ranks);
        this.none.active = !data.ranks.length;
    }

    private updateMyRank(): void {
        const { myRank } = Rank.getInstance().getUnionRankDataById(RANK_ID.UNION_COMBAT);
        const rankData = TBRank.getInstance().getDataById(RANK_ID.UNION_COMBAT);
        const unionLevel = TBUnionLevel.getInstance().getLevelByExp(myRank.union.exp);

        const rank = this.mine.child("rank");
        const icon = this.mine.child("icon");
        const name = this.mine.child("name");
        const scoreCount = this.mine.child("lv").child("text");
        const combat = this.mine.child("combat");

        const maxRank = rankData.maxRank;
        rank.label(myRank.rankNo ? (myRank.rankNo <= maxRank ? myRank.rankNo + "" : maxRank + "+") : i18n.rank0003);
        ImageUtils.setUnionFlag(icon, myRank.union.flag);
        name.label(myRank.union.name);
        scoreCount.label("Lv." + unionLevel);
        combat.label(NumberUtils.format(Number(myRank.union.combatScore), 1, 0));
    }

    protected onClickUnion(sender: cc.Event.EventTouch, unionId: number): void {
        UI.getInstance().open("FloatUnionRankDetails", unionId);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
