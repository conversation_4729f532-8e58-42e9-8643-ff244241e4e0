/*
 * @Author: chenx
 * @Date: 2024-09-29 15:33:23
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-02 15:13:29
 */
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import CocosExt from "../../nsn/util/CocosExt";
import TextUtils from "../../nsn/util/TextUtils";
import Tips from "../../nsn/util/Tips";
import { ConfidantChildModifyNameRet, ConfidantChildUpgradeRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumPrincessChildSex } from "../data/base/BasePrincessChild";
import TBAttribute from "../data/parser/TBAttribute";
import TBItem from "../data/parser/TBItem";
import TBPrincessChild from "../data/parser/TBPrincessChild";
import Bag from "../game/Bag";
import Confidant from "../game/Confidant";
import ImageUtils from "../utils/ImageUtils";
import ItemUtils from "../utils/ItemUtils";
import NumberUtils from "../utils/NumberUtils";
import { IFloatConfidantUpgradeArgs } from "./FloatConfidantUpgrade";
import { IFloatParkRenameArgs } from "./FloatParkRename";
import { IPopupConfidantLevelInfoArgs } from "./PopupConfidantLevelInfo";

/**
 * 界面参数
 */
export interface IUIConfidantChildInfoArgs {
    childUuid: string[]; // 王储uuid
    childIndex: number; // 王储index
}

const { ccclass, property } = cc._decorator;

/**
 * 知己-王储信息
 */
@ccclass
export default class UIConfidantChildInfo extends I18nComponent {
    @property(cc.Sprite)
    spIcon: cc.Sprite = null; // 王储icon
    @property(cc.Sprite)
    spQualityIcon: cc.Sprite = null; // 品质icon
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    nodeMenTag: cc.Node = null; // 男性tag
    @property(cc.Node)
    nodeWomenTag: cc.Node = null; // 女性tag
    @property(cc.Label)
    lbtLevel: cc.Label = null; // 等级
    @property(cc.Label)
    lbtScore: cc.Label = null; // 评分
    @property(cc.RichText)
    rtNextAttr: cc.RichText = null; // 下一等级属性

    @property(cc.Button)
    btnUpgrade: cc.Button = null; // 升级按钮

    @property(cc.Node)
    nodePre: cc.Node = null; // 切换上一个按钮
    @property(cc.Node)
    nodeNext: cc.Node = null; // 切换下一个按钮

    initData: IUIConfidantChildInfoArgs = null; // 初始数据

    protected onLoad(): void {
        this.initData = this.args;
        this.updateChildInfo();
        this.updateChildState();
    }

    protected registerHandler(): void {
        Confidant.getInstance().on(
            ConfidantChildModifyNameRet.prototype.clazzName,
            (data: ConfidantChildModifyNameRet) => {
                const childUuid = this.initData.childUuid[this.initData.childIndex];
                if (childUuid === data.childUuid) {
                    const childData = Confidant.getInstance().getChildData(childUuid);
                    this.lbtName.string = childData.name;
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildUpgradeRet.prototype.clazzName,
            (data: ConfidantChildUpgradeRet) => {
                if (this.initData.childUuid[this.initData.childIndex] === data.childUuid) {
                    this.updateChildState(true);
                }

                const childData = Confidant.getInstance().getChildData(data.childUuid);
                const initData: IFloatConfidantUpgradeArgs = {
                    confidantId: -1,
                    childId: childData.childId,
                    level: childData.level,
                };
                UI.getInstance().open("FloatConfidantUpgrade", initData);
            },
            this
        );
    }

    /**
     * 更新王储信息
     */
    protected updateChildInfo(): void {
        const childUuid = this.initData.childUuid[this.initData.childIndex];
        const childData = Confidant.getInstance().getChildData(childUuid);
        const childInfo = TBPrincessChild.getInstance().getDataById(childData.childId);
        let tempRes = "";
        for (const [level, res] of childInfo.res) {
            if (childData.level >= level) {
                tempRes = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantIcon2(this.spIcon, tempRes);
        const itemInfo = TBItem.getInstance().getDataById(childData.childId);
        ImageUtils.setConfidantQualityIcon(this.spQualityIcon, itemInfo.quality);
        this.lbtName.string = childData.name;
        this.nodeMenTag.active = childInfo.sex === EnumPrincessChildSex.Boy;
        this.nodeWomenTag.active = childInfo.sex === EnumPrincessChildSex.Girl;

        this.nodePre.active = this.initData.childIndex !== 0;
        this.nodeNext.active = this.initData.childIndex !== this.initData.childUuid.length - 1;
    }

    /**
     * 更新王储状态
     * @param isUpgrade 是否为升级调用
     */
    private updateChildState(isUpgrade: boolean = false): void {
        const childUuid = this.initData.childUuid[this.initData.childIndex];
        const childData = Confidant.getInstance().getChildData(childUuid);
        const childInfo = TBPrincessChild.getInstance().getDataById(childData.childId);

        if (isUpgrade) {
            const index = childInfo.res.findIndex(([level]) => level === childData.level);
            index !== -1 && ImageUtils.setConfidantIcon2(this.spIcon, childInfo.res[index][1] + "");
        }
        this.lbtLevel.string = childData.level + "";
        let score = 0;
        childInfo.attribute.forEach(([attrId, init, step]) => {
            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
            score = score + (init + step * (childData.level - 1)) * attrInfo.combat;
        });
        this.lbtScore.string = TextUtils.format(i18n.confidant0015, NumberUtils.format(score, 1, 0));
        if (childData.level === childInfo.levelLimit) {
            this.rtNextAttr.string = `<outline color=#000000 width=2>${i18n.confidant0004}</o>`;
        } else {
            const [attrId, init, step] = childInfo.attribute[0];
            const attrData = TBAttribute.getInstance().formatAttribute([attrId, init + step * (childData.level - 1)]);
            this.rtNextAttr.string = `<outline color=#000000 width=2>${i18n.confidant0014}<color=#FF7301>${attrData.name}+${attrData.value}</c></o>`;
        }

        const isMaxLevel = childData.level === childInfo.levelLimit;
        const nodeCostContent = this.btnUpgrade.node.child("contentCost");
        const nodeIcon = nodeCostContent.child("spIcon");
        const nodeCount = nodeCostContent.child("lbtCount");
        if (isMaxLevel) {
            nodeIcon.button(null);
            nodeIcon.sprite(null);
            nodeCount.label("");
        } else {
            const [costId, init, step] = childInfo.upgradeRequired;
            nodeIcon.button(costId);
            ImageUtils.setItemIcon(nodeIcon, costId);
            nodeCount.label(`${Bag.getInstance().getItemCountById(costId)}/${init + step * (childData.level - 1)}`);
        }
        CocosExt.setButtonEnable(this.btnUpgrade, !isMaxLevel);
    }

    /**
     * 等级信息
     */
    protected onClickLevelInfo(): void {
        const initData: IPopupConfidantLevelInfoArgs = {
            confidantId: -1,
            childUuid: this.initData.childUuid[this.initData.childIndex],
        };
        UI.getInstance().open("PopupConfidantLevelInfo", initData);
    }

    /**
     * 重命名
     */
    protected onClickRename(): void {
        const initData: IFloatParkRenameArgs = {
            title: i18n.confidant0016,
            renameCb: (name) => {
                if (cc.isValid(this.node)) {
                    Confidant.getInstance().sendRenameByChild(this.initData.childUuid[this.initData.childIndex], name);
                }
            },
        };
        UI.getInstance().open("FloatParkRename", initData);
    }

    /**
     * 升级
     */
    protected onClickUpgrade(): void {
        const childUuid = this.initData.childUuid[this.initData.childIndex];
        const childData = Confidant.getInstance().getChildData(childUuid);
        const childInfo = TBPrincessChild.getInstance().getDataById(childData.childId);
        if (childData.level === childInfo.levelLimit) {
            return;
        }
        const [costId, init, step] = childInfo.upgradeRequired;
        if (!Bag.getInstance().isEnough(costId, init + step * (childData.level - 1))) {
            Tips.getInstance().info(i18n.common0025);
            return;
        }

        Confidant.getInstance().sendUpgradeByChild(childUuid);
    }

    /**
     * 切换上一个
     */
    protected onClickPre(): void {
        if (this.initData.childIndex === 0) {
            return;
        }

        this.initData.childIndex--;
        this.updateChildInfo();
        this.updateChildState();
    }

    /**
     * 切换下一个
     */
    protected onClickNext(): void {
        if (this.initData.childIndex === this.initData.childUuid.length - 1) {
            return;
        }

        this.initData.childIndex++;
        this.updateChildInfo();
        this.updateChildState();
    }

    /**
     * 道具信息
     * @param event 事件
     * @param itemId 道具id
     */
    protected onClickItemInfo(event: cc.Event.EventTouch, itemId: number): void {
        if (!itemId) {
            return;
        }

        ItemUtils.showInfo(itemId);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
