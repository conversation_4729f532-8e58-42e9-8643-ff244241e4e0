/*
 * @Author: chenx
 * @Date: 2024-03-20 10:25:21
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:48:50
 */
import Audio from "../../nsn/audio/Audio";
import Loader from "../../nsn/core/Loader";
import I18nComponent from "../../nsn/i18n/I18nComponent";
import UI from "../../nsn/ui/UI";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import { EnumUniversalPara } from "../data/base/BaseUniversal";
import TBGameSwitch from "../data/parser/TBGameSwitch";
import TBJump from "../data/parser/TBJump";
import TBUniversal from "../data/parser/TBUniversal";
import { DungeonType } from "../game/Combat";
import ImageUtils from "../utils/ImageUtils";
import JumpUtils from "../utils/JumpUtils";

const { ccclass, property } = cc._decorator;

/**
 * 副本-失败
 */
@ccclass
export default class FloatDungeonFailure extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null; // 标题
    @property(cc.Node)
    list: cc.Node = null; // 系统列表
    @property(cc.Node)
    systemNode: cc.Node = null;

    private type: DungeonType = null; // 类型

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: "texture/result/spResultTitle13",
            },
        ];
    }

    protected onLoad(): void {
        this.systemNode.parent = null;

        this.type = this.args;
        this.initInfo();
        Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.COMBAT_PVP_FAILURE, AUDIO_EFFECT_PATH.DUNGEON);
    }

    protected onDestroy(): void {
        this.systemNode.destroy();
    }

    protected registerHandler(): void {}

    /**
     * 初始化信息
     */
    private initInfo(): void {
        const systemData: number[][] = TBUniversal.getInstance().getValueByPara(
            EnumUniversalPara.FailureImprovementGuidance
        );
        systemData.forEach(([jumpId, isRecommend]) => {
            const nodeItem = Loader.getInstance().instantiate(this.systemNode);
            this.list.addChild(nodeItem);

            nodeItem.button(jumpId);
            const jumpInfo = TBJump.getInstance().getDataById(jumpId);
            const gameSwitchInfo = TBGameSwitch.getInstance().getDataById(jumpInfo.gameSwitchID);
            ImageUtils.setGamePlayPreviewIcon(nodeItem.child("spIcon"), gameSwitchInfo.res);
            nodeItem.child("recommend").active = isRecommend === 1;
            nodeItem.child("text").label(jumpInfo.name);
        });
    }

    protected onClickJump(event: cc.Event.EventTouch, jumpId: number): void {
        if (!jumpId) {
            return;
        }

        JumpUtils.jumpByJumpId(jumpId);
    }

    /**
     * 关闭
     */
    protected onClickClose(): void {
        switch (this.type) {
            case DungeonType.Boss:
                UI.getInstance().closeToWindow("UIDungeonCombatBoss");
                break;
            case DungeonType.Cloud:
                UI.getInstance().closeToWindow("UIDungeonCombatCloud");
                break;
            case DungeonType.Tower:
                UI.getInstance().closeToWindow("UIDungeonCombatTower");
                break;
            case DungeonType.Trial:
                UI.getInstance().closeToWindow("UIDungeonCombatTrial");
                break;
            default:
                break;
        }
        UI.getInstance().close();
    }
}
