import Channel from "../../nsn/config/Channel";
import Whitelist from "../../nsn/config/Whitelist";
import { IHttpResp } from "../../nsn/core/Http";
import Language from "../../nsn/core/Language";
import Loader from "../../nsn/core/Loader";
import Reporter from "../../nsn/core/Reporter";
import Socket, { SocketEvent } from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Time from "../../nsn/util/Time";
import { NetPong } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { REPORTER_ID } from "../config/ReporterConfig";
import AntiAddiction from "../core/AntiAddiction";
import Game from "../core/Game";
import { GameServer } from "../core/GameServer";
import Login, { LoginConnectScene, LoginEvent } from "../core/Login";
import Bulletin from "../game/Bulletin";
import LeadSkin from "../game/LeadSkin";
import SceneUtils from "../utils/SceneUtils";
import ScreenAdapterUtils from "../utils/ScreenAdapterUtils";

const { ccclass } = cc._decorator;

@ccclass
export default class GameScene extends cc.Component {
    protected onLoad(): void {
        ScreenAdapterUtils.getInstance().apply();
        Game.getInstance().initInGameScene();

        this.registerHandler();
        if (!LeadSkin.getInstance().getLeadSelectState()) {
            UI.getInstance().open("UISelectLead", () => {
                if (cc.isValid(this.node)) {
                    UI.getInstance().open("BaseHome");
                    this.initEachModule();
                }
            });
        } else {
            UI.getInstance().open("BaseHome");
            this.initEachModule();
        }
    }

    protected start(): void {
        // 补单查询
        Channel.getInstance().getSdk().queryMissOrder();
        // 防沉迷
        AntiAddiction.getInstance().showGameTimeTip();
        // 请求公告
        Bulletin.getInstance().request();
    }

    /**
     * 注册消息
     */
    private registerHandler(): void {
        Socket.getInstance().on(
            SocketEvent.Connected,
            () => {
                UI.getInstance().hideLoading();
            },
            this
        );

        Socket.getInstance().on(
            SocketEvent.ReconnectFailed,
            () => {
                UI.getInstance().hideLoading();
                SceneUtils.exitInGameScene();
            },
            this
        );

        Socket.getInstance().on(
            SocketEvent.ReconnectStart,
            (count: number) => {
                UI.getInstance().showLoading(i18n.network0002);
                if (Channel.getInstance().getConfig().supportSdkRealNameCert()) {
                    Channel.getInstance().getSdk().queryRealNameInfo();
                } else {
                    this.connect();
                }
            },
            this
        );

        Socket.getInstance().on(
            NetPong.prototype.clazzName,
            (data: NetPong) => {
                const { timestamp } = data;
                Time.getInstance().syncTime(timestamp);
            },
            this
        );

        Login.getInstance().on(
            LoginEvent.Connect,
            () => {
                this.connect();
            },
            this
        );
    }

    private connect(): void {
        const server = GameServer.getInstance().getCurrent();
        Reporter.logBugEvent(REPORTER_ID.BUG.RECONNECT, JSON.stringify(Socket.getInstance().getStatus()));
        Login.getInstance().connect(
            {
                serverId: server.serverLogicId,
                language: Language.getInstance().getLanguage(),
                isWhiteDevice: Whitelist.getInstance().getWhitelist(),
                isRealName: AntiAddiction.getInstance().isRealName(),
                isAdult: AntiAddiction.getInstance().isAdult(),
                scene: LoginConnectScene.Game,
            },
            (success: boolean, result: IHttpResp) => {
                UI.getInstance().hideLoading();
                if (success) {
                    // 如果已经连上则不再重连
                    if (Socket.getInstance().isConnected()) {
                        return;
                    }
                    Socket.getInstance().connect(server.url, result.data.socketToken);
                } else {
                    SceneUtils.exitInGameScene();
                }
            },
            false
        );
    }

    /**
     * 初始化各模块
     */
    private initEachModule(): void {
        const nodeMsg = this.node.child("msgNode");

        // 战斗力
        Loader.getInstance().loadPrefab("prefab/msg/PrefabMsgCombatScore", (prefab) => {
            if (cc.isValid(nodeMsg)) {
                const node = Loader.getInstance().instantiate(prefab);
                nodeMsg.addChild(node);

                node.zIndex = 1;
            }
        });

        // 主线任务
        Loader.getInstance().loadPrefab("prefab/msg/PrefabMsgMainTask", (prefab) => {
            if (cc.isValid(nodeMsg)) {
                const node = Loader.getInstance().instantiate(prefab);
                nodeMsg.addChild(node);

                node.zIndex = 3;
            }
        });

        // 公会互助
        Loader.getInstance().loadPrefab("prefab/msg/PrefabMsgUnionHelp", (prefab) => {
            if (cc.isValid(nodeMsg)) {
                const node = Loader.getInstance().instantiate(prefab);
                nodeMsg.addChild(node);

                node.zIndex = 2;
            }
        });

        // 跑马灯
        Loader.getInstance().loadPrefab("prefab/msg/PrefabMsgNotice", (prefab) => {
            if (cc.isValid(nodeMsg)) {
                const node = Loader.getInstance().instantiate(prefab);
                nodeMsg.addChild(node);

                node.zIndex = 101;
            }
        });
    }
}
