/*
 * @Author: chenx
 * @Date: 2025-05-24 10:02:29
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-16 15:03:58
 */
import Singleton from "../../../nsn/util/Singleton";
import Attribute, { AttributeEvent } from "../../game/Attribute";
import Bag from "../../game/Bag";
import EconomyAttribute, { EconomyAttributeEvent } from "../../game/EconomyAttribute";
import IBaseTimer, { GameTimerType } from "./BaseTimer";

export default class BagTimer extends Singleton implements IBaseTimer {
    /**
     * 开启
     * @returns
     */
    public start(): void {}

    /**
     * 停止
     * @returns
     */
    public stop(): void {}

    /**
     * 清除
     * @returns
     */
    public clear(): void {}

    /**
     * 获取执行时间间隔
     */
    public getTimerType(): GameTimerType {
        return GameTimerType.Per300Second;
    }

    /**
     * 执行方法
     */
    public execute(): void {
        Bag.getInstance().checkLimitTimeItemState();
        Attribute.getInstance().emit(AttributeEvent.Update);
        EconomyAttribute.getInstance().emit(EconomyAttributeEvent.Update);
    }
}
