/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-01 18:28:09
 * @Last Modified by: chenx
 * @Last Modified time: 2025-05-24 10:27:36
 */
import ActivityTimer from "./ActivityTimer";
import AntiAddictionTimer from "./AntiAddictionTimer";
import BagTimer from "./BagTimer";
import IBaseTimer from "./BaseTimer";
import BlessTimer from "./BlessTimer";
import CollectionGameTimer from "./CollectionGameTimer";
import CommonPopupTimer from "./CommonPopupTimer";
import DungeonBoxTimer from "./DungeonBoxTimer";
import DungeonTowerTimer from "./DungeonTowerTimer";
import EquipTimer from "./EquipTimer";
import ForgeTimer from "./ForgeTimer";
import IdleRewardTimer from "./IdleRewardTimer";
import ParkTimer from "./ParkTimer";
import PrivilegeTimer from "./PrivilegeTimer";
import RedPointTimer1 from "./RedPointTimer1";
import RedPointTimer10 from "./RedPointTimer10";
import RedPointTimer3 from "./RedPointTimer3";
import RedPointTimer60 from "./RedPointTimer60";
import SeparateCommandTimer from "./SeparateCommandTimer";
import SocketHeartTimer from "./SocketHeartTimer";
import TaskTimer from "./TaskTimer";

/**
 * 定时器
 */
export const TIMER_CONFIG: IBaseTimer[] = [
    SocketHeartTimer.getInstance(),
    SeparateCommandTimer.getInstance(),
    ActivityTimer.getInstance(),
    RedPointTimer1.getInstance(),
    RedPointTimer3.getInstance(),
    RedPointTimer10.getInstance(),
    RedPointTimer60.getInstance(),
    BlessTimer.getInstance(),
    TaskTimer.getInstance(),
    CommonPopupTimer.getInstance(),
    PrivilegeTimer.getInstance(),
    ParkTimer.getInstance(),
    IdleRewardTimer.getInstance(),
    DungeonTowerTimer.getInstance(),
    DungeonBoxTimer.getInstance(),
    ForgeTimer.getInstance(),
    EquipTimer.getInstance(),
    CollectionGameTimer.getInstance(),
    AntiAddictionTimer.getInstance(),
    BagTimer.getInstance(),
];
