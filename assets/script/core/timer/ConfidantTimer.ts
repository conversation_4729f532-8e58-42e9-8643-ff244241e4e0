/*
 * @Author: chenx
 * @Date: 2024-10-14 16:46:10
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-14 17:56:46
 */
import Singleton from "../../../nsn/util/Singleton";
import Confidant from "../../game/Confidant";
import IBaseTimer, { GameTimerType } from "./BaseTimer";

/**
 * 知己
 */
export default class ConfidantTimer extends Singleton implements IBaseTimer {
    /**
     * 开启
     * @returns
     */
    public start(): void {}

    /**
     * 停止
     * @returns
     */
    public stop(): void {}

    /**
     * 清除
     * @returns
     */
    public clear(): void {}

    /**
     * 获取执行时间间隔
     */
    public getTimerType(): GameTimerType {
        return GameTimerType.Per60Second;
    }

    /**
     * 执行方法
     */
    public execute(): void {
        Confidant.getInstance().updateTravelPowerState();
    }
}
