export enum RedPointId {
    None = 0, // 空

    // 每个系统预留100个红点
    // zjr 10000
    MailAll = 10000, // 邮件总红点
    MailUnread = 10001, // 邮件-未读

    GameSwitch = 10100, // 玩法预告

    RechargeGift = 10301, // 充值好礼
    RechargeFund = 10302, // 充值好礼-战令
    RechargePrivilege = 10303, // 充值好礼-特权卡
    RechargeOnSale = 10304, // 充值好礼-充值特惠

    // cx 20000
    Park = 20101, // 停车场
    ParkRecord = 20102, // 停车场-记录
    ParkSkin = 20103, // 停车场-皮肤
    ParkSkinSpace = 20104, // 停车场-皮肤-车位
    ParkSkinDoor = 20105, // 停车场-皮肤-大门
    ParkSkinWall = 20106, // 停车场-皮肤-围墙
    ParkSkinLamp = 20107, // 停车场-皮肤-路灯
    ParkOrder = 20108, // 停车场-订单
    ParkSpace = 20109, // 停车场-车位
    ParkMyTank = 20110, // 停车场-我的战车

    EquipRecoverFatigueByAd = 20401, // 装备-恢复疲劳-广告

    HomeRightStorage = 20501, // 主界面-右侧收纳

    HomeAutoSet = 20601, // 主界面-自动设置
    MakeCostUnlock = 20602, // 制作消耗解锁
    GameSpeedUnlock = 20603, // 游戏速度解锁

    CollectionGame = 20701, // 藏品玩法

    DungeonMainSkipLevel = 20801, // 主线副本-跳过关卡

    ShopArena = 20901, // 商店-竞技场
    ShopUnion = 20902, // 商店-公会
    ShopPark = 20903, // 商店-停车场
    ShopLimitPlay = 20904, // 商店-限时玩法
    ShopSprint = 20905, // 商店-竞速

    Chat = 21001, // 聊天
    ChatRedPacket = 21002, // 聊天-红包
    ChatRedPacketWaitOpen = 21003, // 聊天-红包-待开启
    ChatRedPacketWaitGive = 21004, // 聊天-红包-待发出

    // wym 30000
    DailyTaskTotal = 30001, // 日常任务总红点
    DailyTask = 30002, // 每日任务
    WeeklyTask = 30003, // 每周任务
    MiningResearch = 30101, // 家园 => 科技树
    Mining = 30102, // 家园 => 挖矿
    RechargeDailyMustBuy = 30201, // 充值好礼 => 每日必买
    MiningShop = 30501, // 挖矿 => 矿山商店（矿山特权卡）

    // fsj
    UnionAll = 40000, // 公会-总红点
    UnionHall = 40001, // 公会-主页
    UnionApply = 40002, // 公会-主页-申请列表申请条目待处理
    UnionDonate = 40003, // 公会-主页-捐赠
    UnionTask = 40004, // 公会-任务
    UnionTaskGetReward = 40005, // 公会-任务-达成奖励可领取
    UnionActiveGetReward = 40006, // 公会-任务-活跃达成奖励可领取
    UnionBoss = 40007, // 公会-boss
    UnionBossFight = 40008, // 公会-boss-可挑战
    UnionBossGetReward = 40009, // 公会-boss-奖励可领取
    UnionTreasureShop = 40010, // 公会-贸易商人
    UnionHelp = 40011, // 公会-互助

    SignInReward = 40100, // 签到
    SignInAll = 40101, // 签到-总红点

    ActivityOpeningCelebrationAll = 40200, // 开服庆典
    ActivityOCTask = 40201, // 开服庆典-任务
    ActivityOCFund = 40202, // 开服庆典-基金
    ActivityOCExchange = 40203, // 开服庆典-兑换
    ActivityOCGroupGift = 40204, // 开服庆典-特惠

    FirstRechargeGift = 40300, // 首充-赠礼
    FirstRechargeAll = 40301, // 首充-总红点
    FirstRecharge = 40302, // 首充-每日领奖

    LeadPersonalizedAll = 40408, //  主角-个性化
    LeadPersonalizedLead = 40409, // 主角-个性化-形象
    LeadPersonalizedSys = 40410, // 主角-个性化-个性
    LeadPersonalizedSysTank = 40411, // 主角-个性化-个性-战车
    LeadPersonalizedSysWeapon = 40412, // 主角-个性化-个性-神器
    LeadPersonalizedOthers = 40413, // 主角-个性化-其他
    LeadPersonalizedOthersTitle = 40414, // 主角-个性化-其他-称号
    LeadPersonalizedOthersAvatarFrame = 40415, // 主角-个性化-其他-头像框
    LeadPersonalizedOthersAvatar = 40416, // 主角-个性化-其他-头像
    LeadPersonalizedSysWing = 40417, // 主角-个性化-个性-背饰
    LeadPersonalizedSysPet = 40418, // 主角-个性化-个性-宠物

    ActivitySprintAll = 40500, // 活动-限时竞速
    ActivitySprint = 40501, // 活动-限时竞速

    EquipStrengthenAll = 41001, // 装备-总强化
    EquipStrengthen = 41002, // 装备-单件强化
    EquipMaster = 41003, // 装备-强化大师
    EquipSuit = 41004, // 装备-套装

    SkinAll = 41100, // 皮肤

    LeadAll = 41300, // 角色

    PartnerAll = 41400, // 伙伴
    PartnerTeam = 41401, // 伙伴-阵容
    PartnerGroup = 41402, // 伙伴-羁绊
    PartnerHandbook = 41403, // 伙伴-图鉴
    PartnerStrengthen = 41404, // 伙伴-阵容-可强化
    PartnerUpTeam = 41405, // 伙伴-阵容-可上阵

    PetAll = 41500, // 宠物
    PetTeam = 41501, // 宠物-页签
    PetInfo = 41502, // 宠物-信息
    PetFreeEgg = 41504, // 宠物-免费蛋
    PetEgg = 41505, // 宠物-蛋
    PetNewPet = 41506, // 宠物-新宠物
    PetUpgrade = 41507, // 宠物-升级
    PetUpStar = 41508, // 宠物-吞噬
    PetRefine = 41509, // 宠物-洗练
    PetAdEgg = 41510, // 宠物-广告蛋
    PetHighQualityPet = 41511, // 宠物-高品质宠物

    IdleReward = 41600, // 挂机
    IdleRewardHasReward = 41601, // 挂机-有奖励
    IdleRewardFreeTimes = 41602, // 挂机-免费次数

    Mall = 41700, // 商城
    MallLimit = 41701, // 商城-限时礼包
    MallDiamond = 41702, // 商城-钻石

    Wing = 41800, // 背饰
    WingLevel = 41801, // 背饰-升级
    WingStar = 41802, // 背饰-升星
    WingEnchant = 41803, // 背饰-附魔

    Weapon = 41900, // 神器
    WeaponLevel = 41901, // 神器-升级
    WeaponStar = 41902, // 神器-升星

    GamePlayDaily = 42000, // 每日玩法
    GamePlayLimit = 42001, // 限时玩法
    GamePlayLimitExpedition = 42003, // 限时玩法-游历
    GamePlayLimitExpeditionTask = 42004, // 限时玩法-游历-任务
    GamePlayLimitExpeditionGame = 42005, // 限时玩法-游历-游戏

    ArenaAll = 42100, // 竞技场
    Arena = 42101, // 竞技场

    ActivityNewbieTrial = 42200, // 新手试炼

    LinkGift = 42300, // 链式礼包

    PlayerInfoAll = 42400, // 玩家信息
    PlayerInfo = 42401, // 玩家信息-个人信息
    PlayerInfoSettings = 42402, // 玩家信息-设置

    Social = 42500, // 社交

    PowerFund = 42600, // 王权基金

    // zwj
    WeeklyExchangeEnough = 50001, // 周兑换-可兑换
    CrazyDraw = 50100, // 活动-狂送x抽
    BulletinUnRead = 50201, // 公告-未读
    FriendAll = 50300, // 好友-总红点
    FriendGetReward = 50301, // 好友-可领取奖励
    FriendApply = 50302, // 好友-申请
    FriendChatUnread = 50303, // 好友-未读消息
    SocialAll = 50400, // 社群福利-总红点
    SocialShare = 50401, // 社群福利-分享奖励
    SocialFacebook = 50402, // 社群福利-facebook
    SocialLine = 50403, // 社群福利-line
    SocialMessenger = 50404, // 社群福利-messenger
    SystemEntryHome = 50500, // 系统入口折叠红点-主界面
    BagAll = 50600, // 背包-总红点
    BagBox = 50601, // 背包-道具可使用
    BagExchange = 50602, // 背包-道具可合成

    // ttq
    TimeBackAll = 60100, // 时空回溯总红点
    TimeBackTask = 60101, // 时空回溯-任务
    TimeBackPack = 60102, // 时空回溯-礼包
    TimeBackProgress = 60103, // 时空回溯-进度奖励
    TimeBackTicket = 60104, // 时空回溯-剩余抽奖卷
    TimeBackAdCount = 60105, // 时空回溯-广告次数

    TankDrawAll = 60200, // 战车抽卡总红点
    TankDrawTask = 60201, // 战车抽卡-任务
    TankDrawPack = 60202, // 战车抽卡-礼包
    TankDrawTicket = 60203, // 战车抽卡-剩余抽奖卷
    TankDrawAdCount = 60204, // 战车抽卡-广告次数

    HamsterParkour = 60400, // 活动玩法-仓鼠冲刺

    PowerAll = 60500, // 王权总红点
    PowerUpgrade = 60501, // 王权-提升
    PowerBreak = 60502, // 王权-突破
    PowerPromote = 60503, // 王权-晋升
    PowerAwarded = 60504, // 王权-可领取
    PowerPeak = 60505, // 王权-王权之巅

    MagicAll = 60600, // 魔法总红点

    TankAll = 60700, // 战车总红点
    TankIsOneClickUpgrade = 60701, // 战车-可一键升级
    TankIsNewTank = 60702, // 战车-新战车
    TankIsUpStar = 60703, // 战车-可升星

    MagicDrawAll = 60800, // 魔法抽卡总红点
    MagicDrawAd = 60801, // 广告
    MagicDrawDayRed = 60802, // 每日免费一抽

    PartnerDrawAll = 60900, // 伙伴抽卡总红点
    PartnerDrawAd = 60901, // 广告
    PartnerDrawDayRed = 60902, // 每日免费一抽

    TotalRechargeAll = 61000, // 累天累充充值总红点
    TotalRechargeDay = 61001, // 累天累充-每日
    TotalRechargeTotal = 61002, // 累天累充-累计

    HomeMainLead = 62000, // 主菜单-主角
    HomeMainGrowth = 62001, // 主菜单-培养
    HomeMainGamePlay = 62002, // 主菜单-玩法(副本)
    HomeMainHomeLand = 62003, // 主菜单-家园

    WeaponDrawCardAll = 62100, // 神器抽卡总红点
    WeaponDrawCardTask = 62101, // 神器抽卡-任务
    WeaponDrawCardPack = 62102, // 神器抽卡-礼包
    WeaponDrawCardTicket = 62103, // 神器抽卡-剩余抽奖卷
    WeaponDrawCardAdCount = 62104, // 神器抽卡-广告次数

    WingDrawCardAll = 62200, // 背饰抽卡总红点
    WingDrawCardTask = 62201, // 背饰抽卡-任务
    WingDrawCardPack = 62202, // 背饰抽卡-礼包
    WingDrawCardTicket = 62203, // 背饰抽卡-剩余抽奖卷
    WingDrawCardAdCount = 62204, // 背饰抽卡-广告次数

    ForgeAll = 62400, // 锻造总红点
    ForgeNewForge = 62401, // 获得新锻造台
    ForgeUpgradeCost = 62402, // 升级材料足够
    ForgePromote = 62403, // 锻造台可升阶

    BoxAll = 62500, // 宝箱系统总红点

    CollectionAll = 62700, // 藏品总红点
    CollectionUpGradeAndUpStar = 62701, // 藏品可升级升星
    CollectionBookInfoReward = 62702, // 藏品图鉴奖励可领取
    CollectionWaitExp = 62703, // 藏品有未领取经验
    CollectionUpStar = 62704, // 藏品可升星
    CollectionDrawAd = 62705, // 广告
    CollectionDrawDayRed = 62706, // 每日免费一抽
    CollectionDrawAll = 62707, // 藏品抽奖总红点
    CollectionGroupReward = 62708, // 藏品套装奖励可领取

    MatchGameAll = 62800, // 咕嘟药水总红点
    MatchGameReward = 62801, // 咕嘟药水奖励
    MatchGameTip = 62802, // 咕嘟药水登陆提示红点

    ActivityPersonSprintAll = 62900, // 活动-个人竞速
    ActivityPersonSprint = 62901, // 活动-个人竞速
    ShopPersonSprint = 62902, // 商店-个人竞速商店

    ActivityUnionAll = 63000, // 公会活动-总红点
    ActivityUnionDefenseAll = 64000, // 公会活动-守卫战-总红点
    ActivityUnionDefenseAchieve = 64001, // 公会活动-守卫战-成就可领
    ActivityUnionDefenseReward = 64002, // 公会活动-守卫战-宝库可领
    ActivityUnionDefenseHasCount = 64003, // 公会活动-守卫战-可挑战
    ShopActivityUnionDefense = 64004, // 商店-公会攻防战商店

    WeekFundAll = 65000, // 周战令总红点
}
