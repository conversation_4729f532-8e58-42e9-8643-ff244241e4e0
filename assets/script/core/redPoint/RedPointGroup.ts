import { RedPointGroup } from "./RedPointEnum";
import { RedPointId } from "./RedPointId";

export interface IRedPointGroupType {
    [propertyName: number | RedPointGroup]: RedPointId[];
}

/**
 * 分组
 */
export const RED_POINT_GROUP_CONFIG: IRedPointGroupType = {
    [RedPointGroup.ZeroReset]: [
        RedPointId.HomeMainHomeLand,
        RedPointId.SignInReward,
        RedPointId.FirstRechargeGift,
        RedPointId.MiningShop,
        RedPointId.TimeBackAdCount,
        RedPointId.TimeBackAdCount,
        RedPointId.WingDrawCardAdCount,
        RedPointId.WeaponDrawCardAdCount,
        RedPointId.PowerPeak,
        RedPointId.ArenaAll,
        RedPointId.ShopArena,
        RedPointId.UnionAll,
        RedPointId.ShopUnion,
        RedPointId.Park,
        RedPointId.ShopPark,
        RedPointId.HomeMainGamePlay,
        RedPointId.GamePlayLimit,
        RedPointId.ShopLimitPlay,
        RedPointId.ActivitySprintAll,
        RedPointId.ShopSprint,
        RedPointId.ActivityPersonSprintAll,
        RedPointId.ShopPersonSprint,
        RedPointId.ShopActivityUnionDefense,
        RedPointId.ActivityUnionAll,
        RedPointId.ActivityUnionDefenseAll,
    ],
    [RedPointGroup.ItemChanged]: [
        RedPointId.LeadAll,
        RedPointId.MiningResearch,
        RedPointId.SystemEntryHome,
        RedPointId.BagAll,
        RedPointId.BagBox,
        RedPointId.BagExchange,
        RedPointId.ActivityOCExchange,
        RedPointId.ActivityOpeningCelebrationAll,
        RedPointId.TimeBackTicket,
        RedPointId.TankDrawTicket,
        RedPointId.HomeMainHomeLand,
        RedPointId.Park,
        RedPointId.ParkSkin,
        RedPointId.ParkSkinSpace,
        RedPointId.ParkSkinDoor,
        RedPointId.ParkSkinWall,
        RedPointId.ParkSkinLamp,
        RedPointId.EquipStrengthen,
        RedPointId.EquipStrengthenAll,
        RedPointId.PowerAll,
        RedPointId.PowerUpgrade,
        RedPointId.PartnerStrengthen,
        RedPointId.PartnerTeam,
        RedPointId.PartnerAll,
        RedPointId.PetTeam,
        RedPointId.PetAll,
        RedPointId.PetUpgrade,
        RedPointId.PetInfo,
        RedPointId.PetUpStar,
        RedPointId.PetRefine,
        RedPointId.MagicAll,
        RedPointId.TankAll,
        RedPointId.TankIsOneClickUpgrade,
        RedPointId.TankIsUpStar,
        RedPointId.WingLevel,
        RedPointId.WingStar,
        RedPointId.WingEnchant,
        RedPointId.Wing,
        RedPointId.WeaponLevel,
        RedPointId.WeaponStar,
        RedPointId.Weapon,
        RedPointId.HomeMainLead,
        RedPointId.HomeMainGrowth,
        RedPointId.HomeMainGamePlay,
        RedPointId.WeaponDrawCardTicket,
        RedPointId.WingDrawCardTicket,
        RedPointId.ActivityNewbieTrial,
        RedPointId.ForgeAll,
        RedPointId.ForgeUpgradeCost,
        RedPointId.ForgePromote,
        RedPointId.BoxAll,
        RedPointId.CollectionAll,
        RedPointId.CollectionUpGradeAndUpStar,
        RedPointId.CollectionUpStar,
        RedPointId.GamePlayDaily,
        RedPointId.GamePlayLimit,
        RedPointId.GamePlayLimitExpedition,
        RedPointId.ArenaAll,
        RedPointId.ShopArena,
        RedPointId.UnionAll,
        RedPointId.ShopUnion,
        RedPointId.ShopPark,
        RedPointId.ShopLimitPlay,
        RedPointId.ActivitySprintAll,
        RedPointId.ShopSprint,
        RedPointId.ActivityPersonSprintAll,
        RedPointId.ShopPersonSprint,
        RedPointId.ShopActivityUnionDefense,
        RedPointId.ActivityUnionDefenseAll,
        RedPointId.ActivityUnionAll,
    ],
    [RedPointGroup.Shop]: [
        RedPointId.RechargeOnSale,
        RedPointId.HomeRightStorage,
        RedPointId.TotalRechargeAll,
        RedPointId.TotalRechargeDay,
        RedPointId.TotalRechargeTotal,
        RedPointId.SystemEntryHome,
        RedPointId.ActivityOCExchange,
        RedPointId.ActivityOpeningCelebrationAll,
        RedPointId.ActivityOCGroupGift,
        RedPointId.TimeBackPack,
        RedPointId.TimeBackAll,
        RedPointId.TankDrawPack,
        RedPointId.TankDrawAll,
        RedPointId.RechargeDailyMustBuy,
        RedPointId.MallDiamond,
        RedPointId.Mall,
        RedPointId.HomeMainLead,
        RedPointId.HomeMainGrowth,
        RedPointId.HomeMainGamePlay,
        RedPointId.HomeMainHomeLand,
        RedPointId.WeaponDrawCardPack,
        RedPointId.WeaponDrawCardAll,
        RedPointId.WingDrawCardPack,
        RedPointId.WingDrawCardAll,
    ],
    [RedPointGroup.Task]: [
        RedPointId.UnionAll,
        RedPointId.UnionTask,
        RedPointId.ActivityOpeningCelebrationAll,
        RedPointId.ActivitySprintAll,
        RedPointId.ActivitySprint,
        RedPointId.SystemEntryHome,
        RedPointId.ActivityOCTask,
        RedPointId.ActivityOCFund,
        RedPointId.TimeBackTask,
        RedPointId.TimeBackAll,
        RedPointId.TankDrawTask,
        RedPointId.TankDrawAll,
        RedPointId.PowerAll,
        RedPointId.PowerAwarded,
        RedPointId.PowerPromote,
        RedPointId.HomeMainLead,
        RedPointId.HomeMainGrowth,
        RedPointId.HomeMainGamePlay,
        RedPointId.HomeMainHomeLand,
        RedPointId.Mall,
        RedPointId.WeaponDrawCardAll,
        RedPointId.WeaponDrawCardTask,
        RedPointId.WingDrawCardAll,
        RedPointId.WingDrawCardTask,
        RedPointId.PartnerUpTeam,
        RedPointId.PartnerTeam,
        RedPointId.PartnerAll,
        RedPointId.MatchGameAll,
        RedPointId.MatchGameReward,
        RedPointId.MatchGameTip,
        RedPointId.ActivityPersonSprintAll,
        RedPointId.ActivityPersonSprint,
        RedPointId.MagicAll,
        RedPointId.ActivityUnionDefenseAchieve,
        RedPointId.ActivityUnionDefenseAll,
        RedPointId.ActivityUnionAll,
    ],
    [RedPointGroup.Statistic]: [],
    [RedPointGroup.Activity]: [
        RedPointId.GamePlayLimitExpedition,
        RedPointId.GamePlayLimit,
        RedPointId.HomeMainGamePlay,
    ],
    [RedPointGroup.Recharge]: [
        RedPointId.ActivityOpeningCelebrationAll,
        RedPointId.ActivitySprintAll,
        RedPointId.ActivitySprint,
        RedPointId.ActivityOCGroupGift,
        RedPointId.TotalRechargeAll,
        RedPointId.TotalRechargeDay,
        RedPointId.TotalRechargeTotal,
        RedPointId.MiningShop,
        RedPointId.Mining,
        RedPointId.TimeBackPack,
        RedPointId.TimeBackAll,
        RedPointId.TankDrawPack,
        RedPointId.TankDrawAll,
        RedPointId.RechargeFund,
        RedPointId.MallLimit,
        RedPointId.Mall,
        RedPointId.HomeMainLead,
        RedPointId.HomeMainGrowth,
        RedPointId.HomeMainGamePlay,
        RedPointId.HomeMainHomeLand,
        RedPointId.WeaponDrawCardPack,
        RedPointId.WeaponDrawCardAll,
        RedPointId.WingDrawCardPack,
        RedPointId.WingDrawCardAll,
        RedPointId.LinkGift,
        RedPointId.FirstRecharge,
        RedPointId.FirstRechargeGift,
        RedPointId.FirstRechargeAll,
        RedPointId.ActivityPersonSprintAll,
        RedPointId.ActivityPersonSprint,
        RedPointId.HomeRightStorage,
        RedPointId.PowerFund,
        RedPointId.DailyTaskTotal,
        RedPointId.WeekFundAll,
    ],
    [RedPointGroup.Power]: [
        RedPointId.PowerAwarded,
        RedPointId.PowerPromote,
        RedPointId.PartnerUpTeam,
        RedPointId.PartnerTeam,
        RedPointId.PartnerAll,
        RedPointId.MagicAll,
    ],
    [RedPointGroup.RechargeReceivePack]: [
        RedPointId.RechargeFund,
        RedPointId.RechargeGift,
        RedPointId.FirstRecharge,
        RedPointId.FirstRechargeAll,
        RedPointId.HomeRightStorage,
        RedPointId.PowerFund,
        RedPointId.DailyTaskTotal,
        RedPointId.WeekFundAll,
    ],
};
