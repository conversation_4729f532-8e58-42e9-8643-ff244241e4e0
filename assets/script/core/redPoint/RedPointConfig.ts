import Channel from "../../../nsn/config/Channel";
import LocalStorage from "../../../nsn/core/LocalStorage";
import Time, { DAY_TO_SECOND, HOUR_TO_SECOND } from "../../../nsn/util/Time";
import { ItemInfo, TalentTreeType, UnionTreasureShopInfo } from "../../../protobuf/proto";
import { LocalStorageKey } from "../../config/LocalStorageConfig";
import { EnumActivityItemType } from "../../data/base/BaseActivityItem";
import { EnumActivityUniversalPara } from "../../data/base/BaseActivityUniversal";
import { EnumArenaPara } from "../../data/base/BaseArena";
import { EnumDungeonType } from "../../data/base/BaseDungeon";
import { EnumExpandType, EnumExpandUnlockType } from "../../data/base/BaseExpand";
import { EnumGroupType } from "../../data/base/BaseGroup";
import { EnumGroupEffectConditionType } from "../../data/base/BaseGroupEffect";
import { EnumIdleEarningsTotalPara } from "../../data/base/BaseIdleEarningsTotal";
import { EnumItemEffectType, EnumItemTag, EnumItemType } from "../../data/base/BaseItem";
import { EnumLeadEquipPosition } from "../../data/base/BaseLeadEquip";
import { EnumMagicSkillType } from "../../data/base/BaseMagicSkill";
import { EnumMagicSkillLevelType } from "../../data/base/BaseMagicSkillLevel";
import { EnumParkingLotSkinType } from "../../data/base/BaseParkingLotSkin";
import { EnumPetTotalPara } from "../../data/base/BasePetTotal";
import { EnumPrivilegeConfigType } from "../../data/base/BasePrivilegeConfig";
import { EnumProgressRewardType } from "../../data/base/BaseProgressReward";
import { EnumRechargeTabMainTab, EnumRechargeTabTab } from "../../data/base/BaseRechargeTab";
import { EnumShopBuyType, EnumShopType } from "../../data/base/BaseShop";
import { EnumTalentTreeType } from "../../data/base/BaseTalentTree";
import { EnumTankType } from "../../data/base/BaseTank";
import { EnumTaskSeqModule } from "../../data/base/BaseTaskSeq";
import { EnumTotalRechargeType } from "../../data/base/BaseTotalRecharge";
import { EnumUnionPara } from "../../data/base/BaseUnion";
import { EnumUniversalPara } from "../../data/base/BaseUniversal";
import { EnumWeaponType } from "../../data/base/BaseWeapon";
import { EnumWingType } from "../../data/base/BaseWing";
import DataPopup from "../../data/extend/DataPopup";
import TBActivity, { ACTIVITY_ID, ACTIVITY_PERSON_SPRINT_ID, ACTIVITY_SPRINT_ID } from "../../data/parser/TBActivity";
import TBActivityGame from "../../data/parser/TBActivityGame";
import TBActivityItem from "../../data/parser/TBActivityItem";
import TBActivityLogin from "../../data/parser/TBActivityLogin";
import TBActivityUniversal from "../../data/parser/TBActivityUniversal";
import TBAdvertisement, { AdConfigId } from "../../data/parser/TBAdvertisement";
import TBArcher from "../../data/parser/TBArcher";
import TBArcherLevel from "../../data/parser/TBArcherLevel";
import TBArcherStar from "../../data/parser/TBArcherStar";
import TBArena from "../../data/parser/TBArena";
import TBBoxSystem from "../../data/parser/TBBoxSystem";
import TBCollectionHandbook from "../../data/parser/TBCollectionHandbook";
import TBDraw, { DRAW_CARD_ID } from "../../data/parser/TBDraw";
import TBDungeon from "../../data/parser/TBDungeon";
import TBExpand from "../../data/parser/TBExpand";
import TBForgeCultivateLevel from "../../data/parser/TBForgeCultivateLevel";
import TBForgeLevel from "../../data/parser/TBForgeLevel";
import TBFundMarkup from "../../data/parser/TBFundMarkup";
import TBGameSwitch, { GAME_SWITCH_ID } from "../../data/parser/TBGameSwitch";
import TBGroup from "../../data/parser/TBGroup";
import TBGroupEffect from "../../data/parser/TBGroupEffect";
import TBIdleEarningsTotal from "../../data/parser/TBIdleEarningsTotal";
import TBItem, { ITEM_ID } from "../../data/parser/TBItem";
import TBItemChange from "../../data/parser/TBItemChange";
import TBLeadEquipMaster from "../../data/parser/TBLeadEquipMaster";
import TBLeadEquipStrengthen from "../../data/parser/TBLeadEquipStrengthen";
import TBLeadEquipSuit from "../../data/parser/TBLeadEquipSuit";
import TBLeadSkinLevel from "../../data/parser/TBLeadSkinLevel";
import TBLeadSkinStar from "../../data/parser/TBLeadSkinStar";
import TBMagicSkill from "../../data/parser/TBMagicSkill";
import TBMagicSkillLevel from "../../data/parser/TBMagicSkillLevel";
import TBMagicSkillStar from "../../data/parser/TBMagicSkillStar";
import TBPack from "../../data/parser/TBPack";
import TBPetLevel from "../../data/parser/TBPetLevel";
import TBPetStar from "../../data/parser/TBPetStar";
import TBPetTotal from "../../data/parser/TBPetTotal";
import TBPopup from "../../data/parser/TBPopup";
import TBPowerLevel from "../../data/parser/TBPowerLevel";
import TBPowerPeakedness from "../../data/parser/TBPowerPeakedness";
import TBPowerPromotion from "../../data/parser/TBPowerPromotion";
import TBPrivilegeConfig from "../../data/parser/TBPrivilegeConfig";
import TBPrivilegeGroup from "../../data/parser/TBPrivilegeGroup";
import TBProgressReward from "../../data/parser/TBProgressReward";
import TBRecharge, { RECHARGE_ID } from "../../data/parser/TBRecharge";
import TBRechargeGift, { RECHARGE_GIFT_ID } from "../../data/parser/TBRechargeGift";
import TBRechargeTab from "../../data/parser/TBRechargeTab";
import TBShop from "../../data/parser/TBShop";
import TBSystemEntry, { SYSTEM_ENTRY_ID } from "../../data/parser/TBSystemEntry";
import TBTalentLeaf from "../../data/parser/TBTalentLeaf";
import TBTalentLeafGroup from "../../data/parser/TBTalentLeafGroup";
import TBTalentTree from "../../data/parser/TBTalentTree";
import TBTank from "../../data/parser/TBTank";
import TBTankLevel from "../../data/parser/TBTankLevel";
import TBTankStar from "../../data/parser/TBTankStar";
import TBTaskDetail from "../../data/parser/TBTaskDetail";
import TBTaskGroup from "../../data/parser/TBTaskGroup";
import TBTaskSeq from "../../data/parser/TBTaskSeq";
import TBTotalRecharge from "../../data/parser/TBTotalRecharge";
import TBTrialBoss from "../../data/parser/TBTrialBoss";
import TBUnion from "../../data/parser/TBUnion";
import TBUniversal from "../../data/parser/TBUniversal";
import TBWeapon from "../../data/parser/TBWeapon";
import TBWeaponLevel from "../../data/parser/TBWeaponLevel";
import TBWeaponStar from "../../data/parser/TBWeaponStar";
import TBWing from "../../data/parser/TBWing";
import TBWingEnchant from "../../data/parser/TBWingEnchant";
import TBWingLevel, { WING_FEATHER_TYPE } from "../../data/parser/TBWingLevel";
import TBWingStar from "../../data/parser/TBWingStar";
import Activity from "../../game/Activity";
import ActivityGame from "../../game/ActivityGame";
import Ad from "../../game/Ad";
import Archer from "../../game/Archer";
import Arena from "../../game/Arena";
import Bag from "../../game/Bag";
import Box from "../../game/Box";
import Bulletin, { BULLETIN_MAX_LENGTH } from "../../game/Bulletin";
import Collection from "../../game/Collection";
import CollectionGame from "../../game/CollectionGame";
import DrawCard from "../../game/DrawCard";
import Equip from "../../game/Equip";
import Forge from "../../game/Forge";
import Friend from "../../game/Friend";
import GameSwitch from "../../game/GameSwitch";
import IdleReward from "../../game/IdleReward";
import LeadSkin from "../../game/LeadSkin";
import Magical from "../../game/Magical";
import Mail from "../../game/Mail";
import MakeArrow from "../../game/MakeArrow";
import Mining from "../../game/Mining";
import Park from "../../game/Park";
import Pet from "../../game/Pet";
import Player from "../../game/Player";
import Power from "../../game/Power";
import PowerPeak from "../../game/PowerPeak";
import Privilege from "../../game/Privilege";
import Progress from "../../game/Progress";
import Recharge from "../../game/Recharge";
import RedPacket from "../../game/RedPacket";
import Shop from "../../game/Shop";
import Talent from "../../game/Talent";
import Tank from "../../game/Tank";
import Task from "../../game/Task";
import Union from "../../game/Union";
import UnionHelp from "../../game/UnionHelp";
import UnionSiege, { EnumUnionSiegeStatus } from "../../game/UnionSiege";
import Weapon from "../../game/Weapon";
import Welfare from "../../game/Welfare";
import Wing from "../../game/Wing";
import ActivityExpedition from "../../game/activity/ActivityExpedition";
import ActivityNewbieTrial from "../../game/activity/ActivityNewbieTrial";
import ActivityOpenCelebrate from "../../game/activity/ActivityOpenCelebrate";
import ActivitySignIn from "../../game/activity/ActivitySignIn";
import Chat from "../Chat";
import CommonPopup from "../commonPopup/CommonPopup";
import RedPoint from "./RedPoint";
import { IRedPointConfig, RedPointIntervalType, RedPointType } from "./RedPointEnum";
import { RedPointId } from "./RedPointId";

/**
 * 红点配置
 */

const RP_CONFIG_ZJR: IRedPointConfig[] = [
    {
        id: RedPointId.None,
        name: "空红点",
        condition: (red: RedPointId) => {
            return true;
        },
    },
    {
        id: RedPointId.MailAll,
        name: "邮件-总红点",
        deps: [RedPointId.MailUnread],
        interval: RedPointIntervalType.Seconds1,
        condition: (red: RedPointId) => {
            return RedPoint.getInstance().checkDepends(red);
        },
    },
    {
        id: RedPointId.MailUnread,
        name: "邮件-未读",
        condition: (red: RedPointId) => {
            const mails = Mail.getInstance().getMails();
            const now = Time.getInstance().now();
            for (const mail of mails) {
                const expired = now - mail.expireAt > 0;
                if (!mail.isReaded && !expired) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.GameSwitch,
        name: "玩法预告",
        condition: (red: RedPointId) => {
            const allList = TBGameSwitch.getInstance().getList();
            const list = allList.filter((v) => v.preview && !!v.reward.length);
            for (const data of list) {
                const { result } = GameSwitch.getInstance().check(data.id);
                const received = GameSwitch.getInstance().isReceived(data.id);
                if (result && !received) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.RechargeGift,
        name: "充值好礼",
        deps: [RedPointId.RechargeFund, RedPointId.RechargeOnSale, RedPointId.RechargeDailyMustBuy],
        interval: RedPointIntervalType.Seconds1,
        condition: (red: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.RECHARGE_GIFT);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(red);
        },
    },
    {
        id: RedPointId.RechargeFund,
        name: "充值好礼-战令",
        condition: (red: RedPointId) => {
            const list = TBRechargeTab.getInstance().getDataByMainTab(EnumRechargeTabMainTab.Fund);
            for (const tab of list) {
                const base = TBRechargeTab.getInstance().getDataById(tab.id);
                for (const rechargeId of base.tabPack) {
                    const red = Recharge.getInstance().getReceivablePackIdByRechargeId(rechargeId);
                    if (red.length > 0) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.RechargePrivilege,
        name: "充值好礼-特权卡",
        condition: (red: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MONTH_CARD);
            if (!result) {
                return false;
            }
            const list = TBRechargeTab.getInstance().getDataByMainTab(EnumRechargeTabMainTab.Privilege);
            for (const e of list) {
                for (const v of e.tabPack) {
                    const reTB = TBRecharge.getInstance().getDataById(v);
                    if (!Privilege.getInstance().isUnlock(reTB.privilegeGroup)) {
                        continue;
                    }
                    const dailyRwdId = TBPrivilegeGroup.getInstance().getDailyRwdConfigId(reTB.privilegeGroup);
                    if (!dailyRwdId) {
                        continue;
                    }
                    if (!Privilege.getInstance().isReceived(reTB.privilegeGroup, dailyRwdId)) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.RechargeOnSale,
        name: "充值好礼-超值特惠",
        condition: (red: RedPointId) => {
            const list = TBRechargeTab.getInstance().getDataByMainTab(EnumRechargeTabMainTab.SuperValueSpecial);
            for (const base of list) {
                const shopId = base.para[0];
                if (shopId && !Shop.getInstance().isSellOut(shopId)) {
                    return true;
                }
            }
            return false;
        },
    },
];

const RP_CONFIG_CX: IRedPointConfig[] = [
    {
        id: RedPointId.Park,
        name: "停车场",
        deps: [
            RedPointId.ParkRecord,
            RedPointId.ParkSkin,
            RedPointId.ParkOrder,
            RedPointId.ParkSpace,
            RedPointId.ShopPark,
        ],
        condition: (redId: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARK);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(redId);
        },
    },
    {
        id: RedPointId.ParkRecord,
        name: "停车场-记录",
        type: RedPointType.Daily,
        condition: (redId: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARK);
            if (!result) {
                return false;
            }
            return !RedPoint.getInstance().isRecord(redId);
        },
    },
    {
        id: RedPointId.ParkSkin,
        name: "停车场-皮肤",
        deps: [RedPointId.ParkSkinSpace, RedPointId.ParkSkinDoor, RedPointId.ParkSkinWall, RedPointId.ParkSkinLamp],
        condition: (redId: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARK);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(redId);
        },
    },
    {
        id: RedPointId.ParkSkinSpace,
        name: "停车场-皮肤-车位",
        condition: () => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARK);
            if (!result) {
                return false;
            }
            return Park.getInstance().isSkinRedByType(EnumParkingLotSkinType.Stall);
        },
    },
    {
        id: RedPointId.ParkSkinDoor,
        name: "停车场-皮肤-大门",
        condition: () => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARK);
            if (!result) {
                return false;
            }
            return Park.getInstance().isSkinRedByType(EnumParkingLotSkinType.Gate);
        },
    },
    {
        id: RedPointId.ParkSkinWall,
        name: "停车场-皮肤-围墙",
        condition: () => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARK);
            if (!result) {
                return false;
            }
            return Park.getInstance().isSkinRedByType(EnumParkingLotSkinType.Fence);
        },
    },
    {
        id: RedPointId.ParkSkinLamp,
        name: "停车场-皮肤-路灯",
        condition: () => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARK);
            if (!result) {
                return false;
            }
            return Park.getInstance().isSkinRedByType(EnumParkingLotSkinType.StreetLamp);
        },
    },
    {
        id: RedPointId.ParkOrder,
        name: "停车场-订单",
        interval: RedPointIntervalType.Seconds60,
        condition: () => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARK);
            if (!result) {
                return false;
            }
            return Park.getInstance().isOrderRed();
        },
    },
    {
        id: RedPointId.ParkSpace,
        name: "停车场-车位",
        interval: RedPointIntervalType.Seconds60,
        condition: () => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARK);
            if (!result) {
                return false;
            }
            return Park.getInstance().isSpaceRed();
        },
    },
    {
        id: RedPointId.ParkMyTank,
        name: "停车场-我的战车",
        interval: RedPointIntervalType.Seconds60,
        condition: () => {
            return Park.getInstance().getRedStateByMyTank();
        },
    },
    {
        id: RedPointId.EquipRecoverFatigueByAd,
        name: "装备-恢复疲劳-广告",
        type: RedPointType.Daily,
        condition: (redId: RedPointId) => {
            return !RedPoint.getInstance().isRecord(redId);
        },
    },
    {
        id: RedPointId.HomeRightStorage,
        name: "主界面-右侧收纳",
        deps: [
            RedPointId.DailyTaskTotal,
            RedPointId.ActivityNewbieTrial,
            RedPointId.RechargeGift,
            RedPointId.CrazyDraw,
            RedPointId.SignInAll,
            RedPointId.RechargePrivilege,
            RedPointId.TotalRechargeAll,
            RedPointId.PowerFund,
        ],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.HOME_FOLD_MENU);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.HomeAutoSet,
        name: "主界面-自动设置",
        deps: [RedPointId.MakeCostUnlock, RedPointId.GameSpeedUnlock],
        condition: (redId: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.AUTO_MAKE_ARROW);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(redId);
        },
    },
    {
        id: RedPointId.MakeCostUnlock,
        name: "制作消耗解锁",
        type: RedPointType.Forever,
        condition: (redId: RedPointId) => {
            return RedPoint.getInstance().isRecord(redId);
        },
    },
    {
        id: RedPointId.GameSpeedUnlock,
        name: "游戏速度解锁",
        type: RedPointType.Forever,
        condition: (redId: RedPointId) => {
            return RedPoint.getInstance().isRecord(redId);
        },
    },
    {
        id: RedPointId.CollectionGame,
        name: "藏品玩法",
        interval: RedPointIntervalType.Seconds60,
        condition: () => {
            const powerData = CollectionGame.getInstance().getPowerData();
            const dungeonInfo = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonCollection);
            const [powerLimit] = dungeonInfo.recoveryTime;
            const power = powerLimit - powerData.power;
            if (power > 0) {
                return true;
            }
            const adInfo = TBAdvertisement.getInstance().getDataById(AdConfigId.CollectionGame);
            if (adInfo.cd > 0) {
                if (
                    Time.getInstance().now() <
                    Ad.getInstance().getLastWatchTimeById(AdConfigId.CollectionGame) + adInfo.cd * 1000
                ) {
                    return false;
                }
            }
            return Ad.getInstance().hasWatchTimesById(AdConfigId.CollectionGame);
        },
    },
    {
        id: RedPointId.DungeonMainSkipLevel,
        name: "主线副本-跳过关卡",
        type: RedPointType.Daily,
        condition: () => {
            return false;
        },
    },
    {
        id: RedPointId.ShopArena,
        name: "商店-竞技场",
        type: RedPointType.Daily,
        condition: (redId: RedPointId) => {
            return Shop.getInstance().getRedStateByShop(EnumShopType.PVPShop, redId);
        },
    },
    {
        id: RedPointId.ShopUnion,
        name: "商店-公会",
        type: RedPointType.Daily,
        condition: (redId: RedPointId) => {
            return Shop.getInstance().getRedStateByShop(EnumShopType.UnionShop, redId);
        },
    },
    {
        id: RedPointId.ShopPark,
        name: "商店-停车场",
        type: RedPointType.Daily,
        condition: (redId: RedPointId) => {
            return Shop.getInstance().getRedStateByShop(EnumShopType.ParkShop, redId);
        },
    },
    {
        id: RedPointId.ShopLimitPlay,
        name: "商店-限时玩法",
        type: RedPointType.Daily,
        condition: (redId: RedPointId) => {
            return Shop.getInstance().getRedStateByShop(EnumShopType.LimitedPlayShow, redId);
        },
    },
    {
        id: RedPointId.ShopSprint,
        name: "商店-竞速",
        type: RedPointType.Daily,
        condition: (redId: RedPointId) => {
            return Shop.getInstance().getRedStateByShop(EnumShopType.SprintShop, redId);
        },
    },
    {
        id: RedPointId.Chat,
        name: "聊天",
        deps: [RedPointId.FriendChatUnread, RedPointId.ChatRedPacket],
        condition: (redId: RedPointId) => {
            return RedPoint.getInstance().checkDepends(redId);
        },
    },
    {
        id: RedPointId.ChatRedPacket,
        name: "聊天-红包",
        deps: [RedPointId.ChatRedPacketWaitOpen, RedPointId.ChatRedPacketWaitGive],
        condition: (redId: RedPointId) => {
            return RedPoint.getInstance().checkDepends(redId);
        },
    },
    {
        id: RedPointId.ChatRedPacketWaitOpen,
        name: "聊天-红包-待开启",
        interval: RedPointIntervalType.Seconds60,
        condition: () => {
            const openedNum = RedPacket.getInstance().getOpenedNum();
            const limitNum: number = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.RedPacket);
            if (openedNum >= limitNum) {
                return false;
            }
            return RedPacket.getInstance().getWaitOpenData().length > 0;
        },
    },
    {
        id: RedPointId.ChatRedPacketWaitGive,
        name: "聊天-红包-待发出",
        interval: RedPointIntervalType.Seconds60,
        condition: () => {
            return RedPacket.getInstance().getWaitGiveData().length > 0;
        },
    },
];

const RP_CONFIG_WYM: IRedPointConfig[] = [
    {
        id: RedPointId.DailyTaskTotal,
        name: "日常任务总红点",
        deps: [RedPointId.DailyTask, RedPointId.WeeklyTask],
        condition: (red: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.DAILY_TASKS);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(red);
        },
    },
    {
        id: RedPointId.DailyTask,
        name: "日常任务 => 每日任务",
        condition: (red: RedPointId) => {
            let isRed = false;
            const taskSeqConfigList = TBTaskSeq.getInstance().getDataListByModule(EnumTaskSeqModule.DailyTask);
            for (const taskSeqConfig of taskSeqConfigList) {
                const taskGroupConfig = TBTaskGroup.getInstance().getDataById(taskSeqConfig.taskGroup);
                const index = taskGroupConfig.taskID.findIndex((taskDetailId) => {
                    const taskDetailConfig = TBTaskDetail.getInstance().getDataById(taskDetailId);
                    const taskInfo = Task.getInstance().getTaskInfo(taskDetailId, taskSeqConfig.id);
                    // 已领取
                    if (taskInfo && taskInfo.isAwarded) {
                        return false;
                    }
                    // 未完成
                    if (taskInfo.progress < taskDetailConfig.reachValue) {
                        return false;
                    }
                    return true;
                });
                if (index >= 0) {
                    isRed = true;
                    break;
                }
            }
            return isRed;
        },
    },
    {
        id: RedPointId.WeeklyTask,
        name: "日常任务 => 每周任务",
        condition: (red: RedPointId) => {
            let isRed = false;
            const taskSeqConfigList = TBTaskSeq.getInstance().getDataListByModule(EnumTaskSeqModule.WeeklyTask);
            for (const taskSeqConfig of taskSeqConfigList) {
                const taskGroupConfig = TBTaskGroup.getInstance().getDataById(taskSeqConfig.taskGroup);
                const index = taskGroupConfig.taskID.findIndex((taskDetailId) => {
                    const taskDetailConfig = TBTaskDetail.getInstance().getDataById(taskDetailId);
                    const taskInfo = Task.getInstance().getTaskInfo(taskDetailId, taskSeqConfig.id);
                    // 已领取
                    if (taskInfo && taskInfo.isAwarded) {
                        return false;
                    }
                    // 未完成
                    if (taskInfo.progress < taskDetailConfig.reachValue) {
                        return false;
                    }
                    return true;
                });
                if (index >= 0) {
                    isRed = true;
                    break;
                }
            }
            return isRed;
        },
    },
    {
        id: RedPointId.Mining,
        name: "家园 => 挖矿",
        deps: [RedPointId.MiningShop, RedPointId.MiningResearch],
        type: RedPointType.Daily,
        condition: (red: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MINING);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(red);
        },
    },
    {
        id: RedPointId.MiningShop,
        name: "挖矿 => 矿山商店",
        condition: (red: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MINING);
            if (!result) {
                return false;
            }
            const rechargeConfig = TBRecharge.getInstance().getDataById(RECHARGE_ID.CARD_AUTO_MINING);
            if (!rechargeConfig.privilegeGroup) {
                // 未配置特权组
                return false;
            }
            if (!Privilege.getInstance().isUnlock(rechargeConfig.privilegeGroup)) {
                // 特权组未解锁
                return false;
            }
            const dailyRewardId = TBPrivilegeGroup.getInstance().getDailyRwdConfigId(rechargeConfig.privilegeGroup);
            if (!dailyRewardId) {
                // 未配置每日奖励
                return false;
            }
            if (Privilege.getInstance().isReceived(rechargeConfig.privilegeGroup, dailyRewardId)) {
                // 已领取每日奖励
                return false;
            }
            return true;
        },
    },
    {
        id: RedPointId.MiningResearch,
        name: "家园 => 科技树",
        interval: RedPointIntervalType.Seconds10,
        condition: (red: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MINING);
            if (!result) {
                return false;
            }
            const leftTime = Mining.getInstance().getResearchLeftTime();
            if (leftTime > 0) {
                return false;
            }
            const talentTreeInfo = TBTalentTree.getInstance().getDataByType(EnumTalentTreeType.MiningTalent);
            for (const e of talentTreeInfo.talentleafId) {
                if (!Mining.getInstance().isResearchUnlock(e)) {
                    // 已开始研究
                    continue;
                }
                const level = Talent.getInstance().getLevel(TalentTreeType.MiningTree, e);
                const talentInfo = TBTalentLeaf.getInstance().getDataById(e);
                const nextAttrConfig = TBTalentLeafGroup.getInstance().getDataByGroupAndLevel(
                    talentInfo.group,
                    level + 1
                );
                if (!nextAttrConfig) {
                    // 等级已达到上限
                    continue;
                }
                let isItemEnough = true;
                for (const consume of nextAttrConfig.cost) {
                    const data = ItemInfo.create({ itemInfoId: consume[0], num: consume[1] });
                    if (!Bag.getInstance().isEnough(data.itemInfoId, data.num)) {
                        isItemEnough = false;
                        break;
                    }
                }
                if (!isItemEnough) {
                    // 道具不足
                    continue;
                }
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.RechargeDailyMustBuy,
        name: "充值好礼 => 每日必买",
        condition: (red: RedPointId) => {
            const tabConfig = TBRechargeTab.getInstance().getDataByMainTabAndTab(
                EnumRechargeTabMainTab.DailyMustBuy,
                EnumRechargeTabTab.NoTab
            );
            const [shopId]: number[] = tabConfig.para;
            return !Shop.getInstance().isSellOut(shopId);
        },
    },
];

const RP_CONFIG_FSJ: IRedPointConfig[] = [
    {
        id: RedPointId.UnionAll,
        name: "公会-总红点",
        deps: [
            RedPointId.UnionHall,
            RedPointId.UnionTask,
            RedPointId.UnionTreasureShop,
            RedPointId.ShopUnion,
            RedPointId.UnionHelp,
            RedPointId.ActivityUnionAll,
        ],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.UnionTask,
        name: "公会-任务",
        deps: [RedPointId.UnionActiveGetReward, RedPointId.UnionTaskGetReward],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.UnionTaskGetReward,
        name: "公会-任务-达成奖励可领取",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            const taskSeqData = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.UnionDailyTask);
            const taskGroupData = TBTaskGroup.getInstance().getDataById(taskSeqData.taskGroup);
            for (const taskId of taskGroupData.taskID) {
                const data = TBTaskDetail.getInstance().getDataById(taskId);
                const info = Task.getInstance().getTaskInfo(taskId);
                if (info.progress < data.reachValue) {
                    continue;
                }
                if (info.isAwarded) {
                    continue;
                }
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.UnionActiveGetReward,
        name: "公会-任务-活跃达成奖励可领取",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            const data = TBProgressReward.getInstance().getDataByType(EnumProgressRewardType.UnionDevoteTask);
            const curProgress = Union.getInstance().getActiveProgress();
            const rewardIds = Union.getInstance().getActiveRewardIds();
            for (const e of data) {
                if (curProgress < e.progress[0]) {
                    continue;
                }
                if (rewardIds.includes(e.id)) {
                    continue;
                }
                return true;
            }

            return false;
        },
    },
    {
        id: RedPointId.UnionBoss,
        name: "公会-boss",
        deps: [RedPointId.UnionBossFight, RedPointId.UnionBossGetReward],
        condition: (id: RedPointId) => {
            const data1 = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!data1.result) {
                return false;
            }
            const data2 = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION_BOSS);
            if (!data2.result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.UnionBossFight,
        name: "公会-boss-可挑战",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            const thisToday = Time.getInstance().getTodayZero();
            const endPointTime = TBUnion.getInstance().getValueByPara(EnumUnionPara.BossChallengeEndTime);
            const endTime = thisToday + endPointTime * HOUR_TO_SECOND * 1000;
            if (endTime < Time.getInstance().now()) {
                return false;
            }
            const totalCount = TBUnion.getInstance().getValueByPara(EnumUnionPara.BossChallengeCount);
            const attackTimes = Union.getInstance().getRoleAttackTimes();
            return attackTimes < totalCount;
        },
    },
    {
        id: RedPointId.UnionBossGetReward,
        name: "公会-boss-奖励可领取",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            const reward = Union.getInstance().getCanReceiveBossReward();
            return reward.bigBox > 0 || reward.smallBox > 0;
        },
    },
    {
        id: RedPointId.UnionHall,
        name: "公会-主页",
        deps: [RedPointId.UnionApply, RedPointId.UnionDonate],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.UnionApply,
        name: "公会-主页-申请列表申请条目待处理",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            return Union.getInstance().getApplyLogs().length > 0;
        },
    },
    {
        id: RedPointId.UnionDonate,
        name: "公会-主页-捐赠",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            const maxFreeTimes = TBUnion.getInstance().getValueByPara(EnumUnionPara.FreeTimes);
            const freeTimes = Union.getInstance().getDonateFreeTimes();
            return freeTimes < maxFreeTimes;
        },
    },
    {
        id: RedPointId.UnionTreasureShop,
        name: "公会-贸易商人",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.UNION);
            if (!result) {
                return false;
            }
            const union = Union.getInstance().getInfo();
            if (!union) {
                return false;
            }
            const treasureShop = Union.getInstance().getTreasureShop();
            if (!treasureShop) {
                return false;
            }
            return treasureShop.status !== UnionTreasureShopInfo.Status.Purchased;
        },
    },
    {
        id: RedPointId.UnionHelp,
        name: "公会-互助",
        condition: () => {
            const helpData = UnionHelp.getInstance().getHelpData();
            const playerId = Player.getInstance().getId();
            const index = helpData.findIndex((e) => e.playInfo.playerId !== playerId);
            return index !== -1;
        },
    },
    {
        id: RedPointId.SignInAll,
        name: "签到-总红点",
        deps: [RedPointId.SignInReward],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.SIGN_IN);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.SignInReward,
        name: "签到",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.SIGN_IN);
            if (!result) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(ACTIVITY_ID.SIGN_IN);
            if (!activity) {
                return false;
            }
            const { takedIds } = ActivitySignIn.getInstance().getDataByActivityId(ACTIVITY_ID.SIGN_IN);
            const sign = TBActivityLogin.getInstance().getDataByActivityId(ACTIVITY_ID.SIGN_IN);
            const openTime = activity.openTime;
            const openDay = (Time.getInstance().getTodayZero() - openTime) / (DAY_TO_SECOND * 1000) + 1;
            return takedIds.length < openDay && takedIds.length < sign.length;
        },
    },
    {
        id: RedPointId.ActivityOpeningCelebrationAll,
        name: "开服庆典-总红点",
        deps: [
            RedPointId.ActivityOCExchange,
            RedPointId.ActivityOCTask,
            RedPointId.ActivityOCFund,
            RedPointId.ActivityOCGroupGift,
        ],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.OPENING_CELEBRATION);
            if (!result) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(ACTIVITY_ID.OPENING_CELEBRATION);
            if (!activity) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.ActivityOCTask,
        name: "开服庆典-任务",
        condition: (id: RedPointId) => {
            const activityId = ACTIVITY_ID.OPENING_CELEBRATION;
            const activity = Activity.getInstance().getDataById(activityId);
            if (!activity) {
                return false;
            }
            const openDays = Activity.getInstance().getOpenDay(activityId);
            const taskActivityId1 = TBActivity.getInstance().getItemByIdAndType(activityId, EnumActivityItemType.Task);
            const dataSeq1 = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId1
            );
            const taskGroup1 = TBTaskGroup.getInstance().getDataById(dataSeq1.taskGroup);
            for (const taskId of taskGroup1.taskID) {
                const taskDetailData = TBTaskDetail.getInstance().getDataById(taskId);
                if (taskDetailData.days <= openDays) {
                    const info = Task.getInstance().getTaskInfo(taskId);
                    if (!info.isAwarded && info.progress >= taskDetailData.reachValue) {
                        return true;
                    }
                }
            }

            const taskActivityId2 = TBActivity.getInstance().getItemByIdAndType(
                activityId,
                EnumActivityItemType.Progress
            );
            const dataSeq2 = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId2
            );
            const taskGroup2 = TBTaskGroup.getInstance().getDataById(dataSeq2.taskGroup);
            for (const e of taskGroup2.taskID) {
                if (Task.getInstance().isTaskCanGetReward(e)) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.ActivityOCFund,
        name: "开服庆典-基金",
        condition: (id: RedPointId) => {
            const activityId = ACTIVITY_ID.OPENING_CELEBRATION;
            const activity = Activity.getInstance().getDataById(activityId);
            if (!activity) {
                return false;
            }
            const taskActivityIds = TBActivity.getInstance().getItemsByIdAndType(activityId, EnumActivityItemType.Fund);
            const baseTaskSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityIds[0]
            );
            const baseTaskGroupIds = TBTaskGroup.getInstance().getDataById(baseTaskSeq.taskGroup).taskID;
            const advanceTaskSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityIds[1]
            );

            let hasReward = false;
            for (const e of baseTaskGroupIds) {
                if (Task.getInstance().isTaskCanGetReward(e)) {
                    hasReward = true;
                    break;
                }
            }
            if (hasReward) {
                return true;
            }

            const rechargeId = TBActivityItem.getInstance().getDataById(taskActivityIds[1]).typeValue[0];
            if (Recharge.getInstance().isRecharged(rechargeId)) {
                let hasReward = false;
                const advanceTaskGroupIds = TBTaskGroup.getInstance().getDataById(advanceTaskSeq.taskGroup).taskID;
                for (const e of advanceTaskGroupIds) {
                    if (Task.getInstance().isTaskCanGetReward(e)) {
                        hasReward = true;
                        break;
                    }
                }
                if (hasReward) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.ActivityOCExchange,
        name: "开服庆典-兑换",
        type: RedPointType.Login,
        condition: (id: RedPointId) => {
            const activityId = ACTIVITY_ID.OPENING_CELEBRATION;
            const activity = Activity.getInstance().getDataById(activityId);
            if (!activity) {
                return false;
            }
            const isRecord = RedPoint.getInstance().isRecord(id);
            if (isRecord) {
                return false;
            }
            const firstOpenTips = ActivityOpenCelebrate.getInstance().getFirstOpenTips();
            if (firstOpenTips) {
                return false;
            }

            const shopActivityId = TBActivity.getInstance().getItemByIdAndType(
                activityId,
                EnumActivityItemType.Exchange
            );
            const data = TBShop.getInstance().getDataByActivityId(shopActivityId);
            for (const e of data) {
                if (Shop.getInstance().isSellOut(e.id)) {
                    continue;
                }
                const [costId, costCount] = e.buyCost[0];
                if (!Bag.getInstance().isEnough(costId, costCount)) {
                    continue;
                }
                return true;
            }

            return false;
        },
    },
    {
        id: RedPointId.ActivityOCGroupGift,
        name: "开服庆典-特惠",
        condition: (id: RedPointId) => {
            const activityId = ACTIVITY_ID.OPENING_CELEBRATION;
            const activity = Activity.getInstance().getDataById(activityId);
            if (!activity) {
                return false;
            }
            const activityItemId = TBActivity.getInstance().getItemByIdAndType(
                activityId,
                EnumActivityItemType.GroupGift
            );

            const activityData = TBActivityItem.getInstance().getDataById(activityItemId);
            const rechargeId = activityData.typeValue[0];
            const isSellOut = Recharge.getInstance().isSellOut(rechargeId);
            if (isSellOut) {
                return false;
            }

            const data = TBShop.getInstance().getDataByActivityId(activityItemId);
            for (const e of data) {
                if (Shop.getInstance().isSellOut(e.id)) {
                    continue;
                }
                if (e.buyType === EnumShopBuyType.Free || e.buyType === EnumShopBuyType.Advertisement) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.FirstRechargeAll,
        name: "首充-总红点",
        deps: [RedPointId.FirstRechargeGift, RedPointId.FirstRecharge],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.FirstRechargeGift,
        name: "首充-赠礼",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.FIRST_RECHARGE);
            if (!result) {
                return false;
            }
            let lastAt = 0;
            let buyNum = 0;
            let lastId = 0;
            const rechargeGiftData = TBRechargeGift.getInstance().getDataById(RECHARGE_GIFT_ID.FIRST_RECHARGE);
            const rechargeIds = rechargeGiftData.pack;
            for (const id of rechargeIds) {
                const { buyCount, rechargeAt } = Recharge.getInstance().getRechargeInfo(id);
                if (!buyCount) {
                    continue;
                }
                buyNum++;
                if (rechargeAt > lastAt) {
                    lastAt = rechargeAt;
                    lastId = id;
                }
            }
            const rechargeDay = buyNum === rechargeIds.length ? Recharge.getInstance().getRechargeDay(lastId) : 0;
            for (const e of rechargeGiftData.giftID) {
                const { receivePara } = TBPack.getInstance().getDataById(e);
                const isReceived = Recharge.getInstance().isRechargeGiftReceived(e);
                const canGet = rechargeDay >= receivePara[0] && !isReceived;
                if (canGet) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.FirstRecharge,
        name: "首充-每日领奖",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.FIRST_RECHARGE);
            if (!result) {
                return false;
            }
            const FIRST_RECHARGE = [
                RECHARGE_ID.FIRST_RECHARGE_1,
                RECHARGE_ID.FIRST_RECHARGE_2,
                RECHARGE_ID.FIRST_RECHARGE_3,
            ];
            for (const e of FIRST_RECHARGE) {
                const isRecharged = Recharge.getInstance().isRecharged(e);
                if (!isRecharged) {
                    continue;
                }
                const rechargeData = TBRecharge.getInstance().getDataById(e);
                const day = Recharge.getInstance().getRechargeDay(e);
                for (let i = 0; i < rechargeData.pack.length; i++) {
                    const isReceived = Recharge.getInstance().getBuyPackReceived(rechargeData.pack[i]);
                    if (!isReceived && day > i) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.LeadPersonalizedAll,
        name: "主角-个性化",
        deps: [RedPointId.LeadPersonalizedLead, RedPointId.LeadPersonalizedSys, RedPointId.LeadPersonalizedOthers],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.LEAD_PERSONALIZED);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedLead,
        name: "主角-个性化-形象",
        type: RedPointType.Other,
        record: (id: RedPointId, args?: any) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewLead);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            if (!newData.includes(args)) {
                newData.push(args);
                LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewLead, JSON.stringify(newData));
            }
        },
        isRecord: (id: RedPointId) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewLead);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            return newData.length !== 0;
        },
        cancelRecord: (id: RedPointId) => {
            LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewLead, JSON.stringify([]));
        },
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedSys,
        name: "主角-个性化-个性",
        deps: [
            RedPointId.LeadPersonalizedSysTank,
            RedPointId.LeadPersonalizedSysWeapon,
            RedPointId.LeadPersonalizedSysWing,
            RedPointId.LeadPersonalizedSysPet,
        ],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedSysTank,
        name: "主角-个性化-个性-战车",
        type: RedPointType.Other,
        record: (id: RedPointId, args?: any) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewTank);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            if (!newData.includes(args)) {
                newData.push(args);
                LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewTank, JSON.stringify(newData));
            }
        },
        isRecord: (id: RedPointId) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewTank);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            return newData.length !== 0;
        },
        cancelRecord: (id: RedPointId) => {
            LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewTank, JSON.stringify([]));
        },
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.TANK);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedSysWeapon,
        name: "主角-个性化-个性-神器",
        type: RedPointType.Other,
        record: (id: RedPointId, args?: any) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewWeapon);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            if (!newData.includes(args)) {
                newData.push(args);
                LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewWeapon, JSON.stringify(newData));
            }
        },
        isRecord: (id: RedPointId) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewWeapon);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            return newData.length !== 0;
        },
        cancelRecord: (id: RedPointId) => {
            LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewWeapon, JSON.stringify([]));
        },
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.WEAPON);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedSysWing,
        name: "主角-个性化-个性-背饰",
        type: RedPointType.Other,
        record: (id: RedPointId, args?: any) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewWing);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            if (!newData.includes(args)) {
                newData.push(args);
                LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewWing, JSON.stringify(newData));
            }
        },
        isRecord: (id: RedPointId) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewWing);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            return newData.length !== 0;
        },
        cancelRecord: (id: RedPointId) => {
            LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewWing, JSON.stringify([]));
        },
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.WING);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedSysPet,
        name: "主角-个性化-个性-宠物",
        type: RedPointType.Other,
        record: (id: RedPointId, args?: any) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewPet);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            if (!newData.includes(args)) {
                newData.push(args);
                LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewPet, JSON.stringify(newData));
            }
        },
        isRecord: (id: RedPointId) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewPet);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            return newData.length !== 0;
        },
        cancelRecord: (id: RedPointId) => {
            LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewPet, JSON.stringify([]));
        },
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedOthers,
        name: "主角-个性化-其他",
        deps: [
            RedPointId.LeadPersonalizedOthersTitle,
            RedPointId.LeadPersonalizedOthersAvatarFrame,
            RedPointId.LeadPersonalizedOthersAvatar,
        ],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedOthersTitle,
        name: "主角-个性化-其他-称号",
        type: RedPointType.Other,
        record: (id: RedPointId, args?: any) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewTitle);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            if (!newData.includes(args)) {
                newData.push(args);
                LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewTitle, JSON.stringify(newData));
            }
        },
        isRecord: (id: RedPointId) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewTitle);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            return newData.length !== 0;
        },
        cancelRecord: (id: RedPointId) => {
            LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewTitle, JSON.stringify([]));
        },
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedOthersAvatarFrame,
        name: "主角-个性化-其他-头像框",
        type: RedPointType.Other,
        record: (id: RedPointId, args?: any) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewAvatarFrame);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            if (!newData.includes(args)) {
                newData.push(args);
                LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewAvatarFrame, JSON.stringify(newData));
            }
        },
        isRecord: (id: RedPointId) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewAvatarFrame);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            return newData.length !== 0;
        },
        cancelRecord: (id: RedPointId) => {
            LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewAvatarFrame, JSON.stringify([]));
        },
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.LeadPersonalizedOthersAvatar,
        name: "主角-个性化-其他-头像",
        type: RedPointType.Other,
        record: (id: RedPointId, args?: any) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewAvatar);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            if (!newData.includes(args)) {
                newData.push(args);
                LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewAvatar, JSON.stringify(newData));
            }
        },
        isRecord: (id: RedPointId) => {
            const newDataStr = LocalStorage.getInstance().getItem(LocalStorageKey.PersonalizedNewAvatar);
            const newData = newDataStr ? JSON.parse(newDataStr) : [];
            return newData.length !== 0;
        },
        cancelRecord: (id: RedPointId) => {
            LocalStorage.getInstance().setItem(LocalStorageKey.PersonalizedNewAvatar, JSON.stringify([]));
        },
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.ActivitySprintAll,
        name: "限时竞速-总红点",
        deps: [RedPointId.ActivitySprint, RedPointId.ShopSprint],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.ACTIVITY_SPRINT);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.ActivitySprint,
        name: "限时竞速",
        condition: () => {
            for (const e of ACTIVITY_SPRINT_ID) {
                const activity = Activity.getInstance().isValidById(e);
                if (!activity) {
                    continue;
                }
                const taskActivityId = TBActivity.getInstance().getItemByIdAndType(e, EnumActivityItemType.Task);
                const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                    EnumTaskSeqModule.ActivityTask,
                    taskActivityId
                );
                const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
                for (const e of taskGroup.taskID) {
                    if (Task.getInstance().isTaskCanGetReward(e)) {
                        return true;
                    }
                }
                const shopActivityId = TBActivity.getInstance().getItemByIdAndType(e, EnumActivityItemType.Shop);
                const data = TBShop.getInstance().getDataByActivityId(shopActivityId);
                for (const e of data) {
                    if (e.buyType === EnumShopBuyType.Free && !Shop.getInstance().isSellOut(e.id)) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.EquipStrengthenAll,
        name: "装备-总强化",
        deps: [RedPointId.EquipStrengthen, RedPointId.EquipMaster],
        condition: (id: RedPointId) => {
            const data1 = GameSwitch.getInstance().check(GAME_SWITCH_ID.EQUIP);
            if (!data1.result) {
                return false;
            }
            const data2 = GameSwitch.getInstance().check(GAME_SWITCH_ID.EQUIP_STRENGTHEN);
            if (!data2.result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.EquipStrengthen,
        name: "装备-强化单件",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.EQUIP);
            if (!result) {
                return false;
            }
            let canStrengthen = false;
            for (let i = EnumLeadEquipPosition.Head; i < EnumLeadEquipPosition.Shoes; i++) {
                const info = Equip.getInstance().getPositionInfo(i);
                const nextData = TBLeadEquipStrengthen.getInstance().getDataByPositionAndLevel(
                    info.position,
                    info.level + 1
                );
                if (
                    nextData &&
                    Bag.getInstance().isEnough(nextData.strengthenCost[0][0], nextData.strengthenCost[0][1])
                ) {
                    canStrengthen = true;
                    break;
                }
            }
            return canStrengthen;
        },
    },
    {
        id: RedPointId.EquipMaster,
        name: "装备—强化大师",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.EQUIP);
            if (!result) {
                return false;
            }
            const masterId = Equip.getInstance().getMasterId();
            const nextData = TBLeadEquipMaster.getInstance().getDataById(masterId + 1);
            if (nextData) {
                let count = 0;
                for (let i = EnumLeadEquipPosition.Head; i <= EnumLeadEquipPosition.Shoes; i++) {
                    const posInfo = Equip.getInstance().getPositionInfo(i);
                    if (posInfo.level >= nextData.strengthenLevel) {
                        count++;
                    }
                }

                if (count >= EnumLeadEquipPosition.Shoes) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.EquipSuit,
        name: "装备—套装",
        condition: (id: RedPointId) => {
            const data1 = GameSwitch.getInstance().check(GAME_SWITCH_ID.EQUIP);
            if (!data1.result) {
                return false;
            }
            const data2 = GameSwitch.getInstance().check(GAME_SWITCH_ID.EQUIP_SUIT);
            if (!data2.result) {
                return false;
            }
            const SUIT_NUM = [2, 4, 8];
            const suitInfos = Equip.getInstance().getSuitInfos();
            for (const e of SUIT_NUM) {
                const { num, suitId } = suitInfos.find((v) => v.num === e);
                const curSuitId = suitId;
                const nextSuitId = curSuitId ? curSuitId + 1 : TBLeadEquipSuit.getInstance().getDataBySuitNum(num).id;
                const nextSuit = TBLeadEquipSuit.getInstance().getDataById(nextSuitId);
                if (nextSuit) {
                    const count = Equip.getInstance().getCountByLowestQuality(nextSuit.quality);
                    if (count >= num) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.LeadAll,
        name: "角色",
        deps: [RedPointId.SkinAll, RedPointId.EquipStrengthenAll, RedPointId.EquipSuit],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.LEAD);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.SkinAll,
        name: "皮肤",
        condition: (id: RedPointId) => {
            const data1 = GameSwitch.getInstance().check(GAME_SWITCH_ID.LEAD);
            if (!data1.result) {
                return false;
            }
            const data2 = GameSwitch.getInstance().check(GAME_SWITCH_ID.LEAD_CHECK);
            if (!data2.result) {
                return false;
            }

            const skinIds = LeadSkin.getInstance().getAllIds();
            const skinId = LeadSkin.getInstance().getId();
            for (const e of skinIds) {
                const info = LeadSkin.getInstance().getDataById(e);
                if (!info.isActivate) {
                    return true;
                }

                const nextStarData = TBLeadSkinStar.getInstance().getDataByQualityAndStar(info.quality, info.star + 1);
                if (nextStarData && Bag.getInstance().isEnough(e, nextStarData.cost)) {
                    return true;
                }

                if (skinId === info.leadSkinId) {
                    const nextLevelData = TBLeadSkinLevel.getInstance().getDataByQualityAndLevel(
                        info.quality,
                        info.level + 1
                    );
                    if (
                        nextLevelData &&
                        Bag.getInstance().isEnough(nextLevelData.upgradeCost[0][0], nextLevelData.upgradeCost[0][1])
                    ) {
                        return true;
                    }
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.PartnerAll,
        name: "伙伴",
        deps: [RedPointId.PartnerTeam, RedPointId.PartnerGroup, RedPointId.PartnerHandbook],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARTNER);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PartnerTeam,
        name: "伙伴-阵容",
        deps: [RedPointId.PartnerStrengthen, RedPointId.PartnerUpTeam],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARTNER);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PartnerStrengthen,
        name: "伙伴-阵容-可强化",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARTNER);
            if (!result) {
                return false;
            }
            const archers = Archer.getInstance().getAllArcherInfo();
            const team = Archer.getInstance().getTeamIds();
            for (const info of archers) {
                const data = TBArcher.getInstance().getDataById(info.archerId);
                const nextStarData = TBArcherStar.getInstance().getDataByQualityAndStar(info.quality, info.star + 1);
                if (nextStarData && Bag.getInstance().isEnough(data.shardId, nextStarData.cost)) {
                    return true;
                }
                if (team.includes(info.archerId)) {
                    const archerStarData = TBArcherStar.getInstance().getDataByQualityAndStar(info.quality, info.star);
                    if (info.level >= archerStarData.levelLimit) {
                        continue;
                    }
                    const nextLevelData = TBArcherLevel.getInstance().getDataByQualityAndLevel(
                        info.quality,
                        info.level + 1
                    );
                    if (
                        nextLevelData &&
                        Bag.getInstance().isEnough(nextLevelData.upgradeCost[0][0], nextLevelData.upgradeCost[0][1])
                    ) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.PartnerUpTeam,
        name: "伙伴-阵容-可上阵",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARTNER);
            if (!result) {
                return false;
            }
            const archers = Archer.getInstance().getAllArcherInfo();
            const team = Archer.getInstance().getTeamIds();
            let unlockCount = 0;
            const expandData = TBExpand.getInstance().getDataByType(EnumExpandType.ArcherTeamPositionUnlock);

            for (let i = 0; i < 6; i++) {
                const expand = expandData[i];
                switch (expand.unlockType) {
                    case EnumExpandUnlockType.MainTask:
                        const seqInfo = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.MainLineTask);
                        const info = Task.getInstance().getTaskInfo(expand.unlockValue, seqInfo.id);
                        if (info.isAwarded) {
                            unlockCount++;
                        }
                        break;
                    case EnumExpandUnlockType.PowerLevel:
                        const { kingLevelId } = Power.getInstance().getPowerInfo();
                        if (kingLevelId >= expand.unlockValue) {
                            unlockCount++;
                        }
                        break;
                    case EnumExpandUnlockType.Acquiesce:
                        unlockCount++;
                        break;
                    default:
                        break;
                }
            }

            return team.length < unlockCount && archers.length >= unlockCount;
        },
    },
    {
        id: RedPointId.PartnerGroup,
        name: "伙伴-羁绊",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARTNER);
            if (!result) {
                return false;
            }
            const list = TBGroup.getInstance().getDataByType(EnumGroupType.Archer);
            const groupEffectIds = Archer.getInstance().getGroupEffectIds();
            for (const groupData of list) {
                const data = TBGroupEffect.getInstance().getDataByGroupId(groupData.id);
                for (const e of data) {
                    if (!groupEffectIds.includes(e.id)) {
                        switch (e.conditionType) {
                            case EnumGroupEffectConditionType.ArcherId:
                                const own = Archer.getInstance().getDataById(e.condition);
                                if (own) {
                                    return true;
                                }
                                break;
                            case EnumGroupEffectConditionType.ArcherStar:
                                let count = 0;
                                for (const e of groupData.groupItemId) {
                                    const info = Archer.getInstance().getDataById(e);
                                    if (info) {
                                        count += info.star;
                                    }
                                }
                                if (count >= e.condition) {
                                    return true;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.PartnerHandbook,
        name: "伙伴-图鉴",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARTNER);
            if (!result) {
                return false;
            }
            const archers = Archer.getInstance().getAllArcherInfo();
            return !!archers.find((v) => !v.isActivate);
        },
    },

    {
        id: RedPointId.PetAll,
        name: "宠物",
        deps: [RedPointId.PetTeam, RedPointId.PetInfo],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PetTeam,
        name: "宠物-页签",
        deps: [RedPointId.PetHighQualityPet, RedPointId.PetFreeEgg, RedPointId.PetEgg, RedPointId.PetAdEgg],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PetInfo,
        name: "宠物-信息",
        deps: [RedPointId.PetNewPet, RedPointId.PetUpgrade, RedPointId.PetUpStar, RedPointId.PetRefine],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PetFreeEgg,
        name: "宠物-免费蛋",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            const freeEggCd = parseInt(TBPetTotal.getInstance().getDataByParam(EnumPetTotalPara.FreeEggTime));
            const lastReceiveAt = Pet.getInstance().getFreeEggLastTime();
            const now = Time.getInstance().now();
            const gap = now - lastReceiveAt;
            return gap >= freeEggCd * 1000;
        },
    },
    {
        id: RedPointId.PetEgg,
        name: "宠物-蛋",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            const itemData = TBItem.getInstance().getDataByType(EnumItemType.PetEgg);
            for (const e of itemData) {
                if (Bag.getInstance().getItemCountById(e.id) > 0) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.PetAdEgg,
        name: "宠物-广告蛋",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            const adEggTip = Pet.getInstance().getAdEggTip();
            if (!adEggTip) {
                return false;
            }
            const itemData = TBItem.getInstance().getDataByType(EnumItemType.PetEgg);
            if (Bag.getInstance().getItemCountById(itemData[0].id) > 0) {
                return false;
            }
            if (!Ad.getInstance().hasWatchTimesById(AdConfigId.PetEgg)) {
                return false;
            }
            return true;
        },
    },
    {
        id: RedPointId.PetNewPet,
        name: "宠物-新宠物",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            return !!Pet.getInstance().getNewPets().length;
        },
    },
    {
        id: RedPointId.PetHighQualityPet,
        name: "宠物-高品质宠物",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            const count = Number.parseInt(TBPetTotal.getInstance().getDataByParam(EnumPetTotalPara.PetBattleNum));
            const pets = Pet.getInstance().getAllPetInfo();
            const battles = Pet.getInstance().getTeam();
            if (!!pets.length && battles.length < count) {
                return true;
            }
            if (Pet.getInstance().getHighQualityPets().length) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.PetUpgrade,
        name: "宠物-升级",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            const battles = Pet.getInstance().getTeamIds();
            if (!battles.length) {
                return false;
            }
            for (const e of battles) {
                const info = Pet.getInstance().getDataById(e);
                const { levelLimit } = TBPetStar.getInstance().getDataByQualityAndStar(info.quality, info.star);
                if (info.level >= levelLimit) {
                    continue;
                }
                const nextData = TBPetLevel.getInstance().getDataByQualityAndLevel(info.quality, info.level + 1);
                if (!nextData) {
                    continue;
                }
                if (!Bag.getInstance().isEnough(nextData.upgradeCost[0][0], nextData.upgradeCost[0][1])) {
                    continue;
                }
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.PetUpStar,
        name: "宠物-吞噬",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            const team = Pet.getInstance().getTeamIds();
            if (!team.length) {
                return false;
            }

            for (const e of team) {
                const info = Pet.getInstance().getDataById(e);
                const maxQuality = Number.parseInt(
                    TBPetTotal.getInstance().getDataByParam(EnumPetTotalPara.PetMaxQuality)
                );
                if (info.quality < maxQuality) {
                    continue;
                }
                const maxStar = Number.parseInt(TBPetTotal.getInstance().getDataByParam(EnumPetTotalPara.PetMaxStar));
                if (info.star >= maxStar) {
                    continue;
                }

                const specialCost = Number.parseInt(
                    TBPetTotal.getInstance().getDataByParam(EnumPetTotalPara.PetStarSpecialCost)
                );
                if (Bag.getInstance().getItemCountById(specialCost)) {
                    return true;
                }
                const pets = Pet.getInstance()
                    .getAllPetInfo()
                    .filter((v) => !team.includes(v.uuid))
                    .filter((v) => v.quality === info.quality)
                    .filter((v) => v.star < maxStar);
                if (pets.length) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.PetRefine,
        name: "宠物-洗练",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PET);
            if (!result) {
                return false;
            }
            const refineTip = Pet.getInstance().getPetRefineTip();
            if (!refineTip) {
                return false;
            }
            const team = Pet.getInstance().getTeamIds();
            if (!team.length) {
                return false;
            }
            for (const e of team) {
                const info = Pet.getInstance().getDataById(e);
                const maxStar = Number.parseInt(TBPetTotal.getInstance().getDataByParam(EnumPetTotalPara.PetMaxStar));
                if (info.star < maxStar) {
                    continue;
                }
                const cost: number[][] = TBPetTotal.getInstance().getDataByParam(EnumPetTotalPara.PetRefiningCost);
                const minCost = Math.min(...cost.map((v) => v[1]));
                if (Bag.getInstance().isEnough(cost[0][0], minCost)) {
                    return true;
                }
            }
            return false;
        },
    },

    {
        id: RedPointId.IdleReward,
        name: "挂机奖励",
        deps: [RedPointId.IdleRewardHasReward, RedPointId.IdleRewardFreeTimes],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.IDLE_REWARD);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.IdleRewardHasReward,
        name: "挂机奖励-有奖励",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.IDLE_REWARD);
            if (!result) {
                return false;
            }
            const earningsTime = IdleReward.getInstance().getEarningsTime();
            const idleTime =
                parseFloat(TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.ProfitTime)) *
                1000;
            const now = Time.getInstance().now();
            return now - earningsTime >= idleTime;
        },
    },
    {
        id: RedPointId.IdleRewardFreeTimes,
        name: "挂机奖励-免费次数",
        condition: (id: RedPointId) => {
            const data1 = GameSwitch.getInstance().check(GAME_SWITCH_ID.IDLE_REWARD);
            if (!data1.result) {
                return false;
            }
            const data2 = GameSwitch.getInstance().check(GAME_SWITCH_ID.IDLE_REWARD_RAPID);
            if (!data2.result) {
                return false;
            }
            let freeCount = parseInt(
                TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.FreeProfitNum)
            );

            let privilegeCount = 0;
            const privileges = TBPrivilegeConfig.getInstance().getDataListByType(EnumPrivilegeConfigType.QuickAFKTimes);
            for (const e of privileges) {
                if (Privilege.getInstance().hasPrivilege(e.id)) {
                    privilegeCount += e.para.value;
                }
            }
            freeCount += privilegeCount;
            const freeTimes = IdleReward.getInstance().getFreeTimes();
            return freeTimes < freeCount;
        },
    },
    {
        id: RedPointId.Mall,
        name: "商城",
        deps: [RedPointId.MallLimit, RedPointId.MallDiamond],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.HOME_MAIN_MENU_MALL);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.MallLimit,
        name: "商城-限时",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.HOME_MAIN_MENU_MALL);
            if (!result) {
                return false;
            }
            const rechargeTabData = TBRechargeTab.getInstance().getDataByMainTab(
                EnumRechargeTabMainTab.LimitedTimeShop
            );
            for (const e1 of rechargeTabData) {
                for (const e2 of e1.tabPack) {
                    const data = TBRecharge.getInstance().getDataById(e2);
                    if (data.price <= 0 && !Recharge.getInstance().isSellOut(e2)) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.MallDiamond,
        name: "商城-钻石",
        type: RedPointType.Login,
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.HOME_MAIN_MENU_MALL);
            if (!result) {
                return false;
            }
            const isRecord = RedPoint.getInstance().isRecord(id);
            if (isRecord) {
                return false;
            }
            const rechargeTabData = TBRechargeTab.getInstance().getDataByMainTabAndTab(
                EnumRechargeTabMainTab.MainStore,
                EnumRechargeTabTab.NoTab
            );
            for (const e of rechargeTabData.tabPack) {
                const data = TBShop.getInstance().getDataById(e);
                if (data.buyType === EnumShopBuyType.Advertisement && !Shop.getInstance().isSellOut(e)) {
                    return true;
                }

                if (data.buyType === EnumShopBuyType.Free && !Shop.getInstance().isSellOut(e)) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.Wing,
        name: "背饰",
        deps: [RedPointId.WingLevel, RedPointId.WingStar, RedPointId.WingEnchant],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.WING);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.WingLevel,
        name: "背饰-升级",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.WING);
            if (!result) {
                return false;
            }
            const tip = Wing.getInstance().getLevelUpgradeTip();
            if (!tip) {
                return false;
            }

            const levels = [];
            for (const e of WING_FEATHER_TYPE) {
                let level = 0;
                const curFeatherType = e;
                const info = Wing.getInstance().getFeatherByType(e);
                const data = TBWingLevel.getInstance().getDataByTypeAndLevel(curFeatherType, info.level);
                const maxLevel = TBWingLevel.getInstance().getMaxLevelByType(curFeatherType);
                if (info.level >= maxLevel) {
                    levels.push(0);
                    continue;
                }
                const [type1, type2, needLevel] = data.levelLimit;
                const otherInfo1 = Wing.getInstance().getFeatherByType(type1);
                const otherInfo2 = Wing.getInstance().getFeatherByType(type2);
                if (otherInfo1.level >= needLevel && otherInfo2.level >= needLevel) {
                    const nextData1 = TBWingLevel.getInstance().getDataByTypeAndLevel(type1, data.levelInterval[1] + 1);
                    if (!nextData1) {
                        levels.push(needLevel - info.level);
                        continue;
                    }
                    const nextData2 = TBWingLevel.getInstance().getDataByTypeAndLevel(
                        type1,
                        nextData1.levelInterval[1] + 1
                    );
                    if (
                        otherInfo1.level >= nextData1.levelInterval[1] &&
                        otherInfo2.level >= nextData1.levelInterval[1]
                    ) {
                        level = nextData2.levelInterval[1] - info.level;
                    } else {
                        level = nextData1.levelInterval[1] - info.level;
                    }
                } else {
                    if (info.level >= data.levelInterval[1]) {
                        level = 0;
                    } else {
                        level = data.levelInterval[1] - info.level;
                    }
                }
                levels.push(level);
            }

            for (let i = 0; i < levels.length; i++) {
                const curFeatherType = WING_FEATHER_TYPE[i];
                const info = Wing.getInstance().getFeatherByType(curFeatherType);
                const data = TBWingLevel.getInstance().getDataByTypeAndLevel(curFeatherType, info.level);
                let upgradeLevel = levels[i];
                if (upgradeLevel <= 0) {
                    continue;
                }

                let totalCost = 0; // 总消耗
                let currentLevel = info.level; // 当前等级
                while (upgradeLevel > 0) {
                    const segmentData = TBWingLevel.getInstance().getDataByTypeAndLevel(curFeatherType, currentLevel);
                    if (!segmentData) {
                        break;
                    }

                    const [minLevel, maxLevel] = segmentData.levelInterval;
                    const [, baseCost, incrementalCost] = segmentData.upgradeCost[0];
                    const segmentUpgradeLevel = Math.min(upgradeLevel, maxLevel - currentLevel + 1);
                    if (segmentUpgradeLevel <= 0) {
                        break;
                    }
                    for (let i = 0; i < segmentUpgradeLevel; i++) {
                        const levelOffset = currentLevel + i - minLevel;
                        totalCost += baseCost + incrementalCost * levelOffset;
                    }
                    currentLevel += segmentUpgradeLevel;
                    upgradeLevel -= segmentUpgradeLevel;
                }

                const bagCount = Bag.getInstance().getItemCountById(data.upgradeCost[0][0]);
                if (bagCount >= totalCost - info.progress) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.WingStar,
        name: "背饰-升星",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.WING);
            if (!result) {
                return false;
            }
            const wingIds = Wing.getInstance().getWingIds();
            for (const e of wingIds) {
                const data = TBWing.getInstance().getDataById(e);
                if (data.type !== EnumWingType.AdvancedWing) {
                    continue;
                }
                const info = Wing.getInstance().getDataById(e);
                const nextData = TBWingStar.getInstance().getDataByQualityAndStar(data.quality, info.star + 1);
                if (!nextData) {
                    continue;
                }
                if (Bag.getInstance().isEnough(data.id, nextData.upgradeCost)) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.WingEnchant,
        name: "背饰-附魔",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.WING);
            if (!result) {
                return false;
            }
            for (let i = 0; i < 4; i++) {
                const type = i + 1;
                const level = Wing.getInstance().getEnchantLevelByType(type);

                const nextLevel = level + 1;
                const nextData = TBWingEnchant.getInstance().getDataByTypeAndLevel(type, nextLevel);
                if (nextData) {
                    const data = TBWingEnchant.getInstance().getDataByTypeAndLevel(type, level);
                    const upgradeCost = data.upgradeCost[0];
                    if (Bag.getInstance().isEnough(upgradeCost[0], upgradeCost[1])) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.Weapon,
        name: "神器",
        deps: [RedPointId.WeaponLevel, RedPointId.WeaponStar],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.WEAPON);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.WeaponLevel,
        name: "神器-升级",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.WEAPON);
            if (!result) {
                return false;
            }
            const tip = Weapon.getInstance().getLevelUpgradeTip();
            if (!tip) {
                return false;
            }
            const curLevel = Weapon.getInstance().getLevel();
            const nextLevel = curLevel + 1;
            const nextData = TBWeaponLevel.getInstance().getDataById(nextLevel);
            if (!nextData) {
                return false;
            }
            if (!Bag.getInstance().isEnough(nextData.upgradeCost[0][0], nextData.upgradeCost[0][1])) {
                return false;
            }
            return true;
        },
    },
    {
        id: RedPointId.WeaponStar,
        name: "神器-升星",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.WEAPON);
            if (!result) {
                return false;
            }
            const weaponIds = Weapon.getInstance().getWeaponIds();
            for (const e of weaponIds) {
                const data = TBWeapon.getInstance().getDataById(e);
                if (data.type !== EnumWeaponType.AdvancedWeapon) {
                    continue;
                }
                const info = Weapon.getInstance().getDataById(e);
                const nextData = TBWeaponStar.getInstance().getDataByQualityAndStar(data.quality, info.star + 1);
                if (!nextData) {
                    continue;
                }
                if (Bag.getInstance().isEnough(data.id, nextData.upgradeCost)) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.GamePlayDaily,
        name: "每日玩法",
        condition: (id: RedPointId) => {
            const dungeons = TBDungeon.getInstance().getList();
            for (const e of dungeons) {
                if (e.show === 0) {
                    continue;
                }
                const { result } = GameSwitch.getInstance().check(e.switchID);
                if (!result) {
                    continue;
                }

                switch (e.type) {
                    case EnumDungeonType.DungeonTower:
                        if (Bag.getInstance().isEnough(e.cost[0][0], e.recoveryTime[0])) {
                            return true;
                        }
                        break;
                    case EnumDungeonType.DungeonCollection:
                        const powerData = CollectionGame.getInstance().getPowerData();
                        const [powerLimit] = e.recoveryTime;
                        const power = powerLimit - powerData.power;
                        if (power > 0) {
                            return true;
                        }
                        break;
                    default:
                        if (Bag.getInstance().isEnough(e.cost[0][0], e.cost[0][1])) {
                            return true;
                        }
                        break;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.GamePlayLimit,
        name: "限时玩法",
        deps: [
            RedPointId.GamePlayLimitExpedition,
            RedPointId.HamsterParkour,
            RedPointId.MatchGameAll,
            RedPointId.ShopLimitPlay,
        ],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.GamePlayLimitExpedition,
        name: "限时玩法-游历",
        deps: [RedPointId.GamePlayLimitExpeditionTask, RedPointId.GamePlayLimitExpeditionGame],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.EXPEDITION);
            if (!result) {
                return false;
            }
            const isValid = Activity.getInstance().isValidById(ACTIVITY_ID.EXPEDITION);
            if (!isValid) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.GamePlayLimitExpeditionTask,
        name: "限时玩法-游历-任务",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.EXPEDITION);
            if (!result) {
                return false;
            }
            const isValid = Activity.getInstance().isValidById(ACTIVITY_ID.EXPEDITION);
            if (!isValid) {
                return false;
            }

            const taskActivityId = TBActivity.getInstance().getItemByIdAndType(
                ACTIVITY_ID.EXPEDITION,
                EnumActivityItemType.Task
            );
            const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId
            );
            const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
            for (const e of taskGroup.taskID) {
                const taskDetailData = TBTaskDetail.getInstance().getDataById(e);
                const info = Task.getInstance().getTaskInfo(e);

                const { reachValue } = taskDetailData;
                const { progress, isAwarded } = info;
                if (!isAwarded && progress >= reachValue) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.GamePlayLimitExpeditionGame,
        name: "限时玩法-游历-游戏",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.EXPEDITION);
            if (!result) {
                return false;
            }
            const isValid = Activity.getInstance().isValidById(ACTIVITY_ID.EXPEDITION);
            if (!isValid) {
                return false;
            }
            const data = ActivityExpedition.getInstance().getData();
            return !!data.rewardTimes || Bag.getInstance().getItemCountById(ITEM_ID.EXPEDITION_FOOD) > 0;
        },
    },
    {
        id: RedPointId.ArenaAll,
        name: "竞技场-总红点",
        deps: [RedPointId.Arena, RedPointId.ShopArena],
        condition: (redId: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.ARENA);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(redId);
        },
    },
    {
        id: RedPointId.Arena,
        name: "竞技场",
        condition: () => {
            const freeTimes = TBArena.getInstance().getValueByPara(EnumArenaPara.PkFreeTimes);
            const usedTimes = Arena.getInstance().getUsedFreeChallengeTimes();
            return usedTimes < freeTimes;
        },
    },
    {
        id: RedPointId.ActivityNewbieTrial,
        name: "新手试炼",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.NEWBIE_TRIAL);
            if (!result) {
                return false;
            }
            const isValid = Activity.getInstance().isValidById(ACTIVITY_ID.NEWBIE_TRIAL);
            if (!isValid) {
                return false;
            }
            const info = ActivityNewbieTrial.getInstance().getInfo();
            if (!Time.getInstance().isSameDay(info.receiveDate, Time.getInstance().now())) {
                return true;
            }
            if (Ad.getInstance().hasWatchTimesById(AdConfigId.NewbieTrial)) {
                return true;
            }
            const para: number[][] = TBActivityUniversal.getInstance().getValueByPara(
                EnumActivityUniversalPara.TrialCostItem
            );
            const [costId, costCount] = para[0];
            const isEnough = Bag.getInstance().isEnough(costId, costCount);
            const data = TBTrialBoss.getInstance().getList();
            return info.dungeonId <= data.length && isEnough;
        },
    },
    {
        id: RedPointId.LinkGift,
        name: "链式礼包",
        condition: (id: RedPointId) => {
            const popupTB = TBPopup.getInstance().getDataListByUIName("FloatLinkGift");
            const data: DataPopup[] = [];
            for (const item of popupTB) {
                if (CommonPopup.getInstance().isActiveById(item)) {
                    data.push(item);
                }
            }
            if (data.length <= 0) {
                return false;
            }
            for (const e1 of data) {
                const linkData = TBRecharge.getInstance().getLinkDataByFirstRechargeId(e1.popupValue[0]);
                for (const e2 of linkData) {
                    if (!Recharge.getInstance().isRecharged(e2.id)) {
                        const rechargeData = TBRecharge.getInstance().getDataById(e2.id);
                        if (rechargeData.price) {
                            break;
                        }
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.PowerFund,
        name: "王权基金",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.POWER_FUND);
            if (!result) {
                return false;
            }
            const isRecharge = Recharge.getInstance().isRecharged(RECHARGE_ID.POWER_FUND);
            if (!isRecharge) {
                return false;
            }
            const list = TBFundMarkup.getInstance().getList();
            const { pack } = TBRecharge.getInstance().getDataById(RECHARGE_ID.POWER_FUND);
            for (let i = 0; i < list.length - 1; i++) {
                const isEnough = MakeArrow.getInstance().getForgeIronCostCounts() >= list[i].cost;
                const isGot = Recharge.getInstance().getBuyPackReceived(pack[i]);
                if (isEnough && !isGot) {
                    return true;
                }
            }
            return false;
        },
    },
];

const RP_CONFIG_ZWJ: IRedPointConfig[] = [
    {
        id: RedPointId.CrazyDraw,
        name: "活动-狂送x抽",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.CRAZY_DRAW);
            if (!result) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(ACTIVITY_ID.CRAZY_DRAW);
            if (!activity) {
                return false;
            }
            const { takedIds } = ActivitySignIn.getInstance().getDataByActivityId(ACTIVITY_ID.CRAZY_DRAW);
            const sign = TBActivityLogin.getInstance().getDataByActivityId(ACTIVITY_ID.CRAZY_DRAW);
            const openTime = activity.openTime;
            const openDay = (Time.getInstance().getTodayZero() - openTime) / (DAY_TO_SECOND * 1000) + 1;
            return takedIds.length < openDay && takedIds.length < sign.length;
        },
    },
    {
        id: RedPointId.BulletinUnRead,
        name: "公告-未读",
        condition: (id: RedPointId) => {
            const gameSwitchData = TBGameSwitch.getInstance().getDataById(GAME_SWITCH_ID.BULLETIN);
            const { result } = GameSwitch.getInstance().check(gameSwitchData.id);
            if (!result) {
                return false;
            }
            const data = Bulletin.getInstance().getBulletinInfo();
            if (!data || !data.length) {
                return false;
            }
            const localData = LocalStorage.getInstance().getItem(LocalStorageKey.ReadBulletin, true);
            if (localData) {
                const now = Time.getInstance().now();
                const readData: string[] = JSON.parse(localData);
                const validData = data.filter((v) => v.show && v.expireAt > now);
                const showData =
                    validData.length < BULLETIN_MAX_LENGTH ? validData : validData.slice(0, BULLETIN_MAX_LENGTH - 1);
                return showData.length > readData.length;
            }
            return false;
        },
    },
    {
        id: RedPointId.FriendAll,
        name: "好友-总红点",
        deps: [RedPointId.FriendGetReward, RedPointId.FriendApply],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.FRIEND);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.FriendGetReward,
        name: "好友-可领取奖励",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.FRIEND);
            if (!result) {
                return false;
            }
            const valueAry = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.FriendSystem);
            const curCount = Friend.getInstance().getTakeAwardCount();
            if (curCount >= valueAry[1]) {
                return false;
            }
            const thumbsInfo = Friend.getInstance().getThumbsInfos();
            for (const e of thumbsInfo) {
                if (Friend.getInstance().isFriend(e.sponsor) && !e.isTakeAward) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.FriendApply,
        name: "好友-申请",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.FRIEND);
            if (!result) {
                return false;
            }
            const applies = Friend.getInstance().getApplies();
            return !!applies.length;
        },
    },
    {
        id: RedPointId.FriendChatUnread,
        name: "好友-未读消息",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.FRIEND);
            if (!result) {
                return false;
            }
            const unreadFriendList = Chat.getInstance().getUnreadFriendList();
            return !!unreadFriendList.length;
        },
    },
    {
        id: RedPointId.SocialAll,
        name: "社群福利-总红点",
        deps: [],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.SocialShare,
        name: "社群福利-分享奖励",
        condition: (id: RedPointId) => {
            if (!Channel.getInstance().getConfig().getSocialType().length) {
                return false;
            }
            const time = Welfare.getInstance().getDailyShareReceiveTime();
            return !time || Time.getInstance().getTodayZero() > time;
        },
    },
    {
        id: RedPointId.SocialLine,
        name: "社群福利-line",
        condition: (id: RedPointId) => {
            if (!Channel.getInstance().getConfig().getSocialType().length) {
                return false;
            }
            const day = parseInt(TBUniversal.getInstance().getValueByPara(EnumUniversalPara.GiftCenterRedTip));
            const { openTime } = Player.getInstance().getServerInfo();
            if (openTime + day * DAY_TO_SECOND * 1000 < Time.getInstance().now()) {
                return false;
            }
            const localData = LocalStorage.getInstance().getItem(LocalStorageKey.Social);
            if (!localData) {
                return true;
            }
            const dataParse = JSON.parse(localData) as { id: number; time: number }[];
            const data = dataParse.find((v) => v.id === id);
            if (!data) {
                return true;
            }
            return Time.getInstance().getTodayZero() > data.time;
        },
    },
    {
        id: RedPointId.SocialFacebook,
        name: "社群福利-facebook",
        condition: (id: RedPointId) => {
            if (!Channel.getInstance().getConfig().getSocialType().length) {
                return false;
            }
            const day = parseInt(TBUniversal.getInstance().getValueByPara(EnumUniversalPara.GiftCenterRedTip));
            const { openTime } = Player.getInstance().getServerInfo();
            if (openTime + day * DAY_TO_SECOND * 1000 < Time.getInstance().now()) {
                return false;
            }
            const localData = LocalStorage.getInstance().getItem(LocalStorageKey.Social);
            if (!localData) {
                return true;
            }
            const dataParse = JSON.parse(localData) as { id: number; time: number }[];
            const data = dataParse.find((v) => v.id === id);
            if (!data) {
                return true;
            }
            return Time.getInstance().getTodayZero() > data.time;
        },
    },
    {
        id: RedPointId.SocialMessenger,
        name: "社群福利-messenger",
        condition: (id: RedPointId) => {
            if (!Channel.getInstance().getConfig().getSocialType().length) {
                return false;
            }
            const day = parseInt(TBUniversal.getInstance().getValueByPara(EnumUniversalPara.GiftCenterRedTip));
            const { openTime } = Player.getInstance().getServerInfo();
            if (openTime + day * DAY_TO_SECOND * 1000 < Time.getInstance().now()) {
                return false;
            }
            const localData = LocalStorage.getInstance().getItem(LocalStorageKey.Social);
            if (!localData) {
                return true;
            }
            const dataParse = JSON.parse(localData) as { id: number; time: number }[];
            const data = dataParse.find((v) => v.id === id);
            if (!data) {
                return true;
            }
            return Time.getInstance().getTodayZero() > data.time;
        },
    },
    {
        id: RedPointId.SystemEntryHome,
        name: "系统入口-主界面",
        deps: [], // 动态注入
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PlayerInfoAll,
        name: "玩家信息",
        deps: [RedPointId.PlayerInfo, RedPointId.PlayerInfoSettings],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PlayerInfo,
        name: "玩家信息-个人信息",
        deps: [RedPointId.LeadPersonalizedAll, RedPointId.MailAll, RedPointId.BulletinUnRead],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PlayerInfoSettings,
        name: "玩家信息-设置",
        deps: [RedPointId.Social],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.Social,
        name: "社群",
        type: RedPointType.Forever,
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.SOCIAL);
            if (!result) {
                return false;
            }
            if (RedPoint.getInstance().isRecord(id)) {
                return false;
            }
            const socials = Channel.getInstance().getConfig().getSocialType();
            if (!socials.length) {
                return false;
            }
            return true;
        },
    },
];

const RP_CONFIG_TTQ: IRedPointConfig[] = [
    {
        id: RedPointId.TimeBackAll,
        name: "时空回溯-总红点",
        deps: [RedPointId.TimeBackPack, RedPointId.TimeBackTask, RedPointId.TimeBackAdCount],
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TIME_BACK1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.TimeBackTask,
        name: "时空回溯-任务",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TIME_BACK1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const taskActivityId = TBActivity.getInstance().getItemByIdAndType(
                systemEntry.activityId,
                EnumActivityItemType.Task
            );
            const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId
            );
            const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
            for (const e of taskGroup.taskID) {
                const taskDetailData = TBTaskDetail.getInstance().getDataById(e);
                const info = Task.getInstance().getTaskInfo(e);
                const { reachValue } = taskDetailData;
                const { progress, isAwarded } = info;
                if (progress >= reachValue && !isAwarded) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.TimeBackPack,
        name: "时空回溯-礼包",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TIME_BACK1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const shopActivityId = TBActivity.getInstance().getItemByIdAndType(
                systemEntry.activityId,
                EnumActivityItemType.Shop
            );
            const data = TBShop.getInstance().getDataByActivityId(shopActivityId);
            for (const e of data) {
                if (Shop.getInstance().isSellOut(e.id)) {
                    continue;
                }
                if (e.buyType === EnumShopBuyType.Free || e.buyType === EnumShopBuyType.Advertisement) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.TimeBackProgress,
        name: "时空回溯-进度奖励",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TIME_BACK1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const pveId = TBActivity.getInstance().getItemByIdAndType(
                systemEntry.activityId,
                EnumActivityItemType.BossTrial
            );
            const progressInfos = TBProgressReward.getInstance().getDataByActivityId(pveId);
            const nowProgress = Progress.getInstance().getProgress(pveId);
            for (const data of progressInfos) {
                const { id, activityID, progress } = data;
                const isAwarded = Progress.getInstance().getRewardStatus(id, activityID);
                if (!isAwarded && nowProgress > Math.pow(progress[0], progress[1])) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.TimeBackTicket,
        name: "时空回溯-抽奖卷",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TIME_BACK1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            return Bag.getInstance().isEnough(ITEM_ID.TIME_BACK_TICKET, 10);
        },
    },
    {
        id: RedPointId.TimeBackAdCount,
        name: "时空回溯-广告次数",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TIME_BACK1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const data = TBDraw.getInstance().getDataByActivityId(systemEntry.activityId);
            if (Ad.getInstance().hasWatchTimesById(data.advertisementID)) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.TankDrawAll,
        name: "战车夺宝-总红点",
        deps: [RedPointId.TankDrawTask, RedPointId.TankDrawPack, RedPointId.TankDrawAdCount],
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TANK_TREASURE1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.TankDrawTask,
        name: "战车夺宝-任务",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TANK_TREASURE1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const taskActivityId = TBActivity.getInstance().getItemByIdAndType(
                systemEntry.activityId,
                EnumActivityItemType.Task
            );
            const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId
            );
            const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
            for (const e of taskGroup.taskID) {
                const taskDetailData = TBTaskDetail.getInstance().getDataById(e);
                const info = Task.getInstance().getTaskInfo(e);
                const { reachValue } = taskDetailData;
                const { progress, isAwarded } = info;
                if (progress >= reachValue && !isAwarded) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.TankDrawPack,
        name: "战车夺宝-礼包",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TANK_TREASURE1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }

            const shopActivityId = TBActivity.getInstance().getItemByIdAndType(
                systemEntry.activityId,
                EnumActivityItemType.Shop
            );
            const data = TBShop.getInstance().getDataByActivityId(shopActivityId);
            for (const e of data) {
                if (Shop.getInstance().isSellOut(e.id)) {
                    continue;
                }
                if (e.buyType === EnumShopBuyType.Free || e.buyType === EnumShopBuyType.Advertisement) {
                    return true;
                }
            }
            return false;
        },
    },

    {
        id: RedPointId.TankDrawTicket,
        name: "战车夺宝-抽奖卷",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TANK_TREASURE1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            return Bag.getInstance().isEnough(ITEM_ID.TANK_TREASURE_TICKET, 10);
        },
    },
    {
        id: RedPointId.TankDrawAdCount,
        name: "战车夺宝-广告次数",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.TANK_TREASURE1);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const data = TBDraw.getInstance().getDataByActivityId(systemEntry.activityId);
            if (Ad.getInstance().hasWatchTimesById(data.advertisementID)) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.HamsterParkour,
        name: "限时玩法-仓鼠冲刺",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.HAMSTER_PARKOUR);
            if (!result) {
                return false;
            }
            const isValid = Activity.getInstance().isValidById(ACTIVITY_ID.HAMSTER_PARKOUR);
            if (!isValid) {
                return false;
            }

            const { rewardPara, gameLevel } = TBActivityGame.getInstance().getDataById(ACTIVITY_ID.HAMSTER_PARKOUR);
            const targetValueObj = "targetValue";
            for (let stage = 1; stage <= gameLevel.length; stage++) {
                const reward = rewardPara[stage];
                const targetValue = reward.targetValue;
                const maxScore = ActivityGame.getInstance().getActivityStageScore(ACTIVITY_ID.HAMSTER_PARKOUR, stage);

                // 遍历奖励数组，检查每个奖励是否已被领取
                for (const key in reward) {
                    if (key === targetValueObj) {
                        break;
                    }

                    const rewardIndex = parseInt(key);
                    const isReward = ActivityGame.getInstance().getActivityGameStageAwarded(
                        ACTIVITY_ID.HAMSTER_PARKOUR,
                        stage,
                        rewardIndex
                    );

                    if (maxScore >= targetValue[rewardIndex - 1] && !isReward) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.PowerAll,
        name: "头衔-王权系统",
        deps: [
            RedPointId.PowerUpgrade,
            RedPointId.PowerBreak,
            RedPointId.PowerPromote,
            RedPointId.PowerAwarded,
            RedPointId.PowerPeak,
        ],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.POWER);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PowerUpgrade,
        name: "头衔-升级",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.POWER);
            if (!result) {
                return false;
            }
            const powerInfo = Power.getInstance().getPowerInfo();
            const isPromote = TBPowerLevel.getInstance().isNextPromoteThrough(
                powerInfo.kingLevelId,
                powerInfo.kingLevelId + 1,
                powerInfo.upgradeTimes
            );
            const data = TBPowerLevel.getInstance().getDataById(powerInfo.kingLevelId);
            if (data.upgradeCost.length <= 0) {
                return false;
            }
            const itemCount = Bag.getInstance().getItemCountById(data.upgradeCost[0][0]);
            if (itemCount >= data.upgradeCost[0][1] && !isPromote) {
                return true;
            }

            return false;
        },
    },
    {
        id: RedPointId.PowerBreak,
        name: "头衔-突破",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.POWER);
            if (!result) {
                return false;
            }
            const powerInfo = Power.getInstance().getPowerInfo();
            const isBreach = TBPowerLevel.getInstance().isBreachThrough(powerInfo.kingLevelId);
            return isBreach;
        },
    },
    {
        id: RedPointId.PowerAwarded,
        name: "头衔-可领取",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.POWER);
            if (!result) {
                return false;
            }
            const powerInfo = Power.getInstance().getPowerInfo();
            const titleId = TBPowerLevel.getInstance().getTitleIdById(powerInfo.kingLevelId);
            const taskGroup = TBPowerPromotion.getInstance().getTaskGroupById(titleId);
            for (const e of taskGroup) {
                const taskDetailData = TBTaskDetail.getInstance().getDataById(e);
                const info = Task.getInstance().getTaskInfo(e);
                const { reachValue } = taskDetailData;
                const { progress, isAwarded } = info;
                if (progress >= reachValue && !isAwarded) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.PowerPromote,
        name: "头衔-晋升",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.POWER);
            if (!result) {
                return false;
            }
            const powerInfo = Power.getInstance().getPowerInfo();
            const isPromote1 = TBPowerLevel.getInstance().isNextPromoteThrough(
                powerInfo.kingLevelId,
                powerInfo.kingLevelId + 1,
                powerInfo.upgradeTimes
            );
            const titleId = TBPowerLevel.getInstance().getTitleIdById(powerInfo.kingLevelId);
            const taskGroup = TBPowerPromotion.getInstance().getTaskGroupById(titleId);
            let isPromote2 = true;
            for (const e of taskGroup) {
                const taskDetailData = TBTaskDetail.getInstance().getDataById(e);
                const info = Task.getInstance().getTaskInfo(e);
                const { reachValue } = taskDetailData;
                const { progress, isAwarded } = info;
                if (progress < reachValue) {
                    isPromote2 = false;
                }
                if (!isAwarded) {
                    isPromote2 = false;
                }
            }
            return isPromote1 && isPromote2;
        },
    },
    {
        id: RedPointId.PowerPeak,
        name: "王权-王权之巅",
        condition: () => {
            if (!PowerPeak.getInstance().getWorshipState()) {
                return true;
            }
            const peakInfo = TBPowerPeakedness.getInstance().getList();
            for (const e of peakInfo) {
                const { logData, rewardData } = PowerPeak.getInstance().getPeakData(e.titleId);
                if (logData.length > 0 && (!rewardData || !rewardData.isReward)) {
                    return true;
                }
                if (rewardData && rewardData.isCanReceive && !rewardData.isLimitReward) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.MagicAll,
        name: "魔法",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MAGIC);
            if (!result) {
                return false;
            }
            const magicalInfo = Magical.getInstance().getAllDataMagicSkill(); // 所有魔法
            const battleInfos = Magical.getInstance().getSkillBattleInfo(); // 已上阵魔法
            const expandData1 = TBExpand.getInstance().getDataByType(EnumExpandType.InitiativeMagicUnlock);
            const expandData2 = TBExpand.getInstance().getDataByType(EnumExpandType.PassiveMagicUnlock);
            const expandData = expandData1.concat(expandData2);
            let count = 0;
            for (const e of expandData) {
                switch (e.unlockType) {
                    case EnumExpandUnlockType.MainTask:
                        const seqInfo = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.MainLineTask);
                        const info = Task.getInstance().getTaskInfo(e.unlockValue, seqInfo.id);
                        info.isAwarded && count++;
                        break;
                    case EnumExpandUnlockType.PowerLevel:
                        const { kingLevelId } = Power.getInstance().getPowerInfo();
                        kingLevelId >= e.unlockValue && count++;
                        break;
                    default:
                        break;
                }
            }
            if (count > battleInfos.length) {
                return true; // 有可上阵的位置
            }

            for (const v of magicalInfo) {
                const tbMagicSkill = TBMagicSkill.getInstance().getDataById(v.id);
                const promoteGroup = Magical.getInstance().getCanPromoteMagicByGroupId(tbMagicSkill.groupId); // 非上阵可进阶
                if (promoteGroup.isPromote) {
                    return true;
                }

                const info = Magical.getInstance().getMagicInfoByMagicId(v.id);
                const nextStarData = TBMagicSkillStar.getInstance().getDataByMagicIdAndStarAndQuality(
                    v.id,
                    info.quality,
                    info.star + 1
                );
                if (
                    nextStarData &&
                    Bag.getInstance().isEnough(nextStarData.upgradeCost[0][0], nextStarData.upgradeCost[0][1]) // 非上阵可升星
                ) {
                    return true;
                }
            }

            const magicalBattleInfo = Magical.getInstance().getSkillBattleInfo();
            for (const e of magicalBattleInfo) {
                const tbMagicSkill = TBMagicSkill.getInstance().getDataById(e.magicalId);

                const info = Magical.getInstance().getMagicInfoByMagicId(e.magicalId);
                const skillLevelType =
                    tbMagicSkill.type === EnumMagicSkillType.Initiative
                        ? EnumMagicSkillLevelType.Initiative
                        : EnumMagicSkillLevelType.Passive;
                const nextLevelData = TBMagicSkillLevel.getInstance().getDataByQualityAndTypeAndLevel(
                    info.quality,
                    skillLevelType,
                    info.level + 1
                );
                const curStarData = TBMagicSkillStar.getInstance().getDataByMagicIdAndStarAndQuality(
                    e.magicalId,
                    info.quality,
                    info.star
                );
                const isUpgrade =
                    nextLevelData &&
                    Bag.getInstance().isEnough(nextLevelData.upgradeCost[0][0], nextLevelData.upgradeCost[0][1]);
                if (info.level < curStarData.levelLimit && isUpgrade) {
                    return true; // 上阵可升级
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.TankAll,
        name: "战车系统",
        deps: [RedPointId.TankIsOneClickUpgrade, RedPointId.TankIsNewTank, RedPointId.TankIsUpStar],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.TANK);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.TankIsOneClickUpgrade,
        name: "战车系统-可一键升级",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.TANK);
            if (!result) {
                return false;
            }
            const upgradeTip = Tank.getInstance().getOneClickUpgradeTip();
            if (!upgradeTip) {
                return false;
            }

            const tankData = Tank.getInstance().getData();
            const level = tankData ? tankData.level : 1;
            if (tankData.level === TBTankLevel.getInstance().getCount()) {
                return false;
            }

            const info = TBTankLevel.getInstance().getDataById(level + 1);
            const count = Bag.getInstance().getItemCountById(info.upgradeCost[0][0]);
            if (count >= info.upgradeCost[0][1] * (info.upgradeNum - tankData.count)) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.TankIsNewTank,
        name: "战车系统-新战车",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.TANK);
            if (!result) {
                return false;
            }
            const localData = LocalStorage.getInstance().getItem(LocalStorageKey.TankNewInfos);
            const data = localData ? JSON.parse(localData) : [];
            if (data.length > 0) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.TankIsUpStar,
        name: "战车系统-战车升星",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.TANK);
            if (!result) {
                return false;
            }
            const infos = TBTank.getInstance().getDataByType(EnumTankType.AdvancedTank);
            for (const e of infos) {
                const info = Tank.getInstance().getInfoById(e.id);
                const data = TBTank.getInstance().getDataById(e.id);
                if (info) {
                    const tbStarData = TBTankStar.getInstance().getTankStarByQualityAndStar(
                        data.quality,
                        info.star + 1
                    );
                    const count = Bag.getInstance().getItemCountById(info.tankId);
                    if (tbStarData && count >= tbStarData.upgradeCost) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.MagicDrawAll,
        name: "魔法抽奖总红点",
        deps: [RedPointId.MagicDrawAd, RedPointId.MagicDrawDayRed],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MAGICAL_DRAW_CARD);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.MagicDrawAd,
        name: "魔法抽奖-广告红点",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MAGICAL_DRAW_CARD);
            if (!result) {
                return false;
            }
            const data = TBDraw.getInstance().getDataById(DRAW_CARD_ID.MAGIC);
            if (Ad.getInstance().hasWatchTimesById(data.advertisementID) && !DrawCard.getInstance().getMagicAdTip()) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.MagicDrawDayRed,
        name: "魔法抽奖-每日免费一抽",
        type: RedPointType.Daily,
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MAGICAL_DRAW_CARD);
            if (!result) {
                return false;
            }
            return !RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.PartnerDrawAll,
        name: "伙伴抽奖总红点",
        deps: [RedPointId.PartnerDrawAd, RedPointId.PartnerDrawDayRed],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARTNER_DRAW_CARD);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.PartnerDrawAd,
        name: "伙伴抽奖-广告红点",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARTNER_DRAW_CARD);
            if (!result) {
                return false;
            }
            const data = TBDraw.getInstance().getDataById(DRAW_CARD_ID.PARTNER);
            if (Ad.getInstance().hasWatchTimesById(data.advertisementID) && !DrawCard.getInstance().getPartnerAdTip()) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.PartnerDrawDayRed,
        name: "伙伴抽奖-每日免费一抽",
        type: RedPointType.Daily,
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.PARTNER_DRAW_CARD);
            if (!result) {
                return false;
            }
            return !RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.TotalRechargeAll,
        name: "累天累充-总红点",
        deps: [RedPointId.TotalRechargeTotal, RedPointId.TotalRechargeDay],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.TOTAL_RECHARGE);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.TotalRechargeTotal,
        name: "累天累充-累充",
        condition: (id: RedPointId) => {
            const info = TBTotalRecharge.getInstance().getDataByType(EnumTotalRechargeType.LoopTotalRecharge);
            let result = false;
            for (const e of info) {
                const data = TBTotalRecharge.getInstance().getDataById(e.id);
                const canReceive = Recharge.getInstance().isTotalRewardCanReceive(
                    EnumTotalRechargeType.LoopTotalRecharge,
                    data.sumRecharge
                );
                const isReceived = Recharge.getInstance().isTotalRewardReceived(
                    EnumTotalRechargeType.LoopTotalRecharge,
                    e.id
                );
                if (canReceive && !isReceived) {
                    result = true;
                    break;
                }
            }
            return result;
        },
    },
    {
        id: RedPointId.TotalRechargeDay,
        name: "累天累充-累天",
        condition: (id: RedPointId) => {
            const shopData = TBShop.getInstance().getDataByType(EnumShopType.TotalRecharge);
            const isSellOut = Shop.getInstance().isSellOut(shopData[0].id);
            if (!isSellOut) {
                return true;
            }
            const info = TBTotalRecharge.getInstance().getDataByType(EnumTotalRechargeType.LoopRechargeDays);
            let result = false;
            for (const e of info) {
                const data = TBTotalRecharge.getInstance().getDataById(e.id);
                const canReceive = Recharge.getInstance().isTotalRewardCanReceive(
                    EnumTotalRechargeType.LoopRechargeDays,
                    data.sumRecharge
                );
                const isReceived = Recharge.getInstance().isTotalRewardReceived(
                    EnumTotalRechargeType.LoopRechargeDays,
                    e.id
                );
                if (canReceive && !isReceived) {
                    result = true;
                    break;
                }
            }
            return result;
        },
    },
    {
        id: RedPointId.HomeMainLead,
        name: "主菜单-主角",
        deps: [RedPointId.LeadAll, RedPointId.MagicAll, RedPointId.Weapon, RedPointId.Wing],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.HOME_MAIN_MENU_LEAD);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.HomeMainGrowth,
        name: "主菜单-培养",
        deps: [RedPointId.TankAll, RedPointId.PetAll, RedPointId.PartnerAll],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.HOME_MAIN_MENU_GROWTH);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.HomeMainGamePlay,
        name: "主菜单-玩法",
        deps: [RedPointId.GamePlayDaily, RedPointId.GamePlayLimit],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.HOME_MAIN_MENU_GAMEPLAY);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.HomeMainHomeLand,
        name: "主菜单-家园",
        deps: [
            RedPointId.Mining,
            RedPointId.UnionAll,
            RedPointId.Park,
            RedPointId.MagicDrawAll,
            RedPointId.PartnerDrawAll,
            RedPointId.FriendAll,
        ],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.HOME_MAIN_MENU_HOMELAND);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.WeaponDrawCardAll,
        name: "神器抽卡-总红点",
        deps: [RedPointId.WeaponDrawCardTask, RedPointId.WeaponDrawCardPack, RedPointId.WeaponDrawCardAdCount],
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WEAPON_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.WeaponDrawCardTask,
        name: "神器抽卡-任务",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WEAPON_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const taskActivityId = TBActivity.getInstance().getItemByIdAndType(
                systemEntry.activityId,
                EnumActivityItemType.Task
            );
            const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId
            );
            const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
            for (const e of taskGroup.taskID) {
                const taskDetailData = TBTaskDetail.getInstance().getDataById(e);
                const info = Task.getInstance().getTaskInfo(e);
                const { reachValue } = taskDetailData;
                const { progress, isAwarded } = info;
                if (progress >= reachValue && !isAwarded) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.WeaponDrawCardPack,
        name: "神器抽卡-礼包",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WEAPON_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }

            const shopActivityId = TBActivity.getInstance().getItemByIdAndType(
                systemEntry.activityId,
                EnumActivityItemType.Shop
            );
            const data = TBShop.getInstance().getDataByActivityId(shopActivityId);
            for (const e of data) {
                if (Shop.getInstance().isSellOut(e.id)) {
                    continue;
                }
                if (e.buyType === EnumShopBuyType.Free || e.buyType === EnumShopBuyType.Advertisement) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.WeaponDrawCardTicket,
        name: "神器抽卡-抽奖卷",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WEAPON_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            return Bag.getInstance().isEnough(ITEM_ID.WEAPON_DRAW_CARD_TICKET, 10);
        },
    },
    {
        id: RedPointId.WeaponDrawCardAdCount,
        name: "神器抽卡-广告次数",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WEAPON_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const data = TBDraw.getInstance().getDataByActivityId(systemEntry.activityId);
            if (Ad.getInstance().hasWatchTimesById(data.advertisementID)) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.WingDrawCardAll,
        name: "背饰抽卡-总红点",
        deps: [RedPointId.WingDrawCardTask, RedPointId.WingDrawCardPack, RedPointId.WingDrawCardAdCount],
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WING_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.WingDrawCardTask,
        name: "背饰抽卡-任务",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WING_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const taskActivityId = TBActivity.getInstance().getItemByIdAndType(
                systemEntry.activityId,
                EnumActivityItemType.Task
            );
            const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId
            );
            const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
            for (const e of taskGroup.taskID) {
                const taskDetailData = TBTaskDetail.getInstance().getDataById(e);
                const info = Task.getInstance().getTaskInfo(e);
                const { reachValue } = taskDetailData;
                const { progress, isAwarded } = info;
                if (progress >= reachValue && !isAwarded) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.WingDrawCardPack,
        name: "背饰抽卡-礼包",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WING_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const shopActivityId = TBActivity.getInstance().getItemByIdAndType(
                systemEntry.activityId,
                EnumActivityItemType.Shop
            );
            const data = TBShop.getInstance().getDataByActivityId(shopActivityId);
            for (const e of data) {
                if (Shop.getInstance().isSellOut(e.id)) {
                    continue;
                }
                if (e.buyType === EnumShopBuyType.Free || e.buyType === EnumShopBuyType.Advertisement) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.WingDrawCardTicket,
        name: "背饰抽卡-抽奖卷",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WING_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            return Bag.getInstance().isEnough(ITEM_ID.WEAPON_DRAW_CARD_TICKET, 10);
        },
    },
    {
        id: RedPointId.WingDrawCardAdCount,
        name: "背饰抽卡-广告次数",
        condition: (id: RedPointId) => {
            const systemEntry = TBSystemEntry.getInstance().getDataById(SYSTEM_ENTRY_ID.WING_DRAW_CARD);
            const activityIsOpen = Activity.getInstance().isOpeningById(systemEntry.activityId);
            if (!activityIsOpen) {
                return false;
            }
            const activity = Activity.getInstance().getDataById(systemEntry.activityId);
            if (!activity) {
                return false;
            }
            const data = TBDraw.getInstance().getDataByActivityId(systemEntry.activityId);
            if (Ad.getInstance().hasWatchTimesById(data.advertisementID)) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.BoxAll,
        name: "宝箱系统-总红点",
        condition: (id: RedPointId) => {
            if (Box.getInstance().getProgressSpill()) {
                return true;
            }

            const data = TBBoxSystem.getInstance()
                .getList()
                .map((e) => e.id);
            for (const e of data) {
                if (Bag.getInstance().isEnough(e, 1)) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.ForgeAll,
        name: "锻造台-总红点",
        deps: [RedPointId.ForgeNewForge, RedPointId.ForgeUpgradeCost, RedPointId.ForgePromote],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.FORGE);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.ForgeNewForge,
        name: "锻造台-新锻造台",
        condition: (id: RedPointId) => {
            const localData = LocalStorage.getInstance().getItem(LocalStorageKey.ForgeNewInfos);
            const info = localData ? JSON.parse(localData) : [];
            return info.length > 0;
        },
    },
    {
        id: RedPointId.ForgeUpgradeCost,
        name: "锻造台-升级材料足够",
        condition: (id: RedPointId) => {
            const lv = Forge.getInstance().getForgeLevel();
            const isPromote = Forge.getInstance().getForgeLevelHasPromote();
            if (isPromote) {
                return false; // 可晋升不显示升级红点
            }

            const data = TBForgeLevel.getInstance().getDataById(lv);
            const infos = Forge.getInstance().getCultivateInfos();
            let flag = false;
            for (const e of data.levelTask) {
                const info = TBForgeCultivateLevel.getInstance().getDataById(e);
                const findIndex = infos.findIndex((k) => k.cultivateLevelId === e); // 找到对应项
                if (
                    info.num !== infos[findIndex].num &&
                    Bag.getInstance().isEnough(
                        info.upgradeCost[0][0],
                        info.upgradeCost[0][1] * info.num - infos[findIndex].num
                    )
                ) {
                    flag = true;
                    break;
                }
            }
            return flag;
        },
    },
    {
        id: RedPointId.ForgePromote,
        name: "锻造台可升阶-进阶中是否有加速机会",
        condition: (id: RedPointId) => {
            const isPromote = Forge.getInstance().getForgeLevelHasPromote();
            if (!isPromote) {
                return false;
            }

            const flag1 = !Forge.getInstance().isPromotionTimeEnd();
            const flag2 = Bag.getInstance().isEnough(ITEM_ID.FORGE_ADD_ITEM, 1);
            const flag3 = Ad.getInstance().hasWatchTimesById(AdConfigId.ForgeAcc);
            const flag4 = Ad.getInstance().isInCd(AdConfigId.ForgeAcc);
            if (flag1 && flag2) {
                return true;
            }

            if (flag1 && !flag2 && flag4) {
                return false;
            }

            if (flag1 && !flag2 && !flag3) {
                return false;
            }

            return true;
        },
    },
    {
        id: RedPointId.CollectionAll,
        name: "藏品系统-总红点",
        deps: [
            RedPointId.CollectionUpGradeAndUpStar,
            RedPointId.CollectionBookInfoReward,
            RedPointId.CollectionWaitExp,
            RedPointId.CollectionDrawAll,
        ],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.COLLECTION);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.CollectionUpGradeAndUpStar,
        name: "藏品系统- 藏品可升级升星",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.COLLECTION);
            if (!result) {
                return false;
            }
            const collectionInfo = Collection.getInstance().getCollectionInfos();
            for (const e of collectionInfo) {
                const isUpgrade = Collection.getInstance().getIsUpgradeById(e.collectionId);
                const isUpStar = Collection.getInstance().isUpStarById(e.collectionId);
                if (isUpgrade) {
                    return true;
                }
                if (isUpStar) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.CollectionUpStar,
        name: "藏品系统- 藏品可升星",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.COLLECTION);
            if (!result) {
                return false;
            }
            const collectionInfo = Collection.getInstance().getCollectionInfos();
            for (const e of collectionInfo) {
                const isUpStar = Collection.getInstance().isUpStarById(e.collectionId);
                if (isUpStar) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.CollectionBookInfoReward,
        name: "藏品系统- 图鉴奖励可领取",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.COLLECTION);
            if (!result) {
                return false;
            }
            const list = TBCollectionHandbook.getInstance()
                .getList()
                .filter((e) => e.level > 0);
            const bookInfo = Collection.getInstance().getBookInfo();
            for (const e of list) {
                const receive = Collection.getInstance().getBookInfoReceivedByLevel(e.level);
                if (e.level <= bookInfo.level && !receive) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.CollectionWaitExp,
        name: "藏品系统- 有未领取经验",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.COLLECTION);
            if (!result) {
                return false;
            }
            return Collection.getInstance().getDataHasExps().length > 0;
        },
    },
    {
        id: RedPointId.CollectionDrawAll,
        name: "藏品抽奖-总红点",
        deps: [RedPointId.CollectionDrawAd, RedPointId.CollectionDrawDayRed],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.COLLECTION);
            if (!result) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.CollectionDrawAd,
        name: "藏品抽奖- 广告红点",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.COLLECTION);
            if (!result) {
                return false;
            }
            const data = TBDraw.getInstance().getDataById(DRAW_CARD_ID.COLLECTION);
            if (Ad.getInstance().hasWatchTimesById(data.advertisementID)) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.CollectionDrawDayRed,
        name: "藏品抽奖-每日免费一抽",
        type: RedPointType.Daily,
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.COLLECTION);
            if (!result) {
                return false;
            }
            return !RedPoint.getInstance().isRecord(id);
        },
    },
    {
        id: RedPointId.MatchGameAll,
        name: "咕嘟药水-总红点",
        deps: [RedPointId.MatchGameReward, RedPointId.MatchGameTip],
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MATCH_GAME);
            if (!result) {
                return false;
            }
            const isValid = Activity.getInstance().isValidById(ACTIVITY_ID.MATCH_GAME);
            if (!isValid) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.MatchGameReward,
        name: "咕嘟药水-有可领奖励",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MATCH_GAME);
            if (!result) {
                return false;
            }
            const isValid = Activity.getInstance().isValidById(ACTIVITY_ID.MATCH_GAME);
            if (!isValid) {
                return false;
            }

            const taskActivityId = TBActivity.getInstance().getItemByIdAndType(
                ACTIVITY_ID.MATCH_GAME,
                EnumActivityItemType.Task
            );
            const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId
            );
            const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
            for (const e of taskGroup.taskID) {
                const taskDetailData = TBTaskDetail.getInstance().getDataById(e);
                const info = Task.getInstance().getTaskInfo(e);

                const { reachValue } = taskDetailData;
                const { progress, isAwarded } = info;
                if (!isAwarded && progress >= reachValue) {
                    return true;
                }
            }

            return false;
        },
    },
    {
        id: RedPointId.MatchGameTip,
        name: "咕嘟药水-登陆提示红点",
        condition: (id: RedPointId) => {
            const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.MATCH_GAME);
            if (!result) {
                return false;
            }
            const isValid = Activity.getInstance().isValidById(ACTIVITY_ID.MATCH_GAME);
            if (!isValid) {
                return false;
            }

            const isRecord = ActivityGame.getInstance().getMatchGameTip();
            const taskActivityId = TBActivity.getInstance().getItemByIdAndType(
                ACTIVITY_ID.MATCH_GAME,
                EnumActivityItemType.Task
            );
            const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId
            );
            const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
            let count = 0;
            for (const e of taskGroup.taskID) {
                const info = Task.getInstance().getTaskInfo(e);
                const { isAwarded } = info;
                if (isAwarded) {
                    count++;
                }
            }
            return count < taskGroup.taskID.length && !isRecord;
        },
    },
    {
        id: RedPointId.BagAll,
        name: "背包-总红点",
        deps: [RedPointId.BagBox, RedPointId.BagExchange],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.BagBox,
        name: "背包-宝箱",
        condition: (id: RedPointId) => {
            const itemsTB = TBItem.getInstance()
                .getDataListByTag(EnumItemTag.Core)
                .filter((v) => v.type === EnumItemType.Box);
            for (const e of itemsTB) {
                if (Bag.getInstance().getItemCountById(e.id)) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.BagExchange,
        name: "背包-可合成",
        condition: (id: RedPointId) => {
            const itemsTB = TBItem.getInstance()
                .getDataListByTag(EnumItemTag.Core)
                .filter((v) => v.effectType === EnumItemEffectType.PetFragment);
            for (const e of itemsTB) {
                const data = TBItemChange.getInstance().getDataById(e.id);
                if (Math.floor(Bag.getInstance().getItemCountById(e.id) / Math.floor(1 / data.change)) >= 1) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.ActivityPersonSprintAll,
        name: "个人竞速-总红点",
        deps: [RedPointId.ActivityPersonSprint, RedPointId.ShopPersonSprint],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.ActivityPersonSprint,
        name: "个人竞速",
        condition: () => {
            for (const e of ACTIVITY_PERSON_SPRINT_ID) {
                const activity = Activity.getInstance().isValidById(e);
                if (!activity) {
                    continue;
                }
                const taskActivityId = TBActivity.getInstance().getItemByIdAndType(e, EnumActivityItemType.Task);
                const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                    EnumTaskSeqModule.ActivityLoopTask,
                    taskActivityId
                );
                const taskInfo = Task.getInstance().getTaskInfoBySeqId(dataSeq.id);
                const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
                for (const e of taskGroup.taskID) {
                    if (Task.getInstance().isTaskCanGetReward(e)) {
                        return true;
                    }
                }
                const shopActivityId = TBActivity.getInstance().getItemByIdAndType(e, EnumActivityItemType.Shop);
                const data = TBShop.getInstance().getDataByActivityId(shopActivityId);
                for (const e of data) {
                    if (e.buyType === EnumShopBuyType.Free && !Shop.getInstance().isSellOut(e.id)) {
                        return true;
                    }
                }

                const activityItemData = TBActivityItem.getInstance().getDataById(taskActivityId);
                let count = 0;
                for (const e of taskGroup.taskID) {
                    const taskDetailData = TBTaskDetail.getInstance().getDataById(e);
                    const info = Task.getInstance().getTaskInfo(e);
                    if (info.progress >= taskDetailData.reachValue) {
                        count++;
                    }
                }
                if (
                    activityItemData.typeValue.length > 0 &&
                    !taskInfo.isReceiveBigReward &&
                    taskInfo.loopCount >= dataSeq.loopCount &&
                    count >= taskGroup.taskID.length
                ) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.ShopPersonSprint,
        name: "商店-个人竞速",
        type: RedPointType.Daily,
        condition: (redId: RedPointId) => {
            return Shop.getInstance().getRedStateByShop(EnumShopType.PersonSprint, redId);
        },
    },
    {
        id: RedPointId.ActivityUnionAll,
        name: "公会活动-总红点",
        deps: [RedPointId.UnionBoss, RedPointId.ActivityUnionDefenseAll],
        condition: (id: RedPointId) => {
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.ActivityUnionDefenseAll,
        name: "公会攻防活动-总红点",
        interval: RedPointIntervalType.Seconds10,
        deps: [
            RedPointId.ActivityUnionDefenseAchieve,
            RedPointId.ActivityUnionDefenseReward,
            RedPointId.ActivityUnionDefenseHasCount,
            RedPointId.ShopActivityUnionDefense,
        ],
        condition: (id: RedPointId) => {
            const status = UnionSiege.getInstance().getStatus(ACTIVITY_ID.UNION_DEFENSE);
            const redShow = UnionSiege.getInstance().isRedPoint(status);
            if (!redShow) {
                return false;
            }
            const isUnionEmpty = UnionSiege.getInstance().isUnionEmpty();
            if (!isUnionEmpty) {
                return false;
            }
            const isSelfEmpty = UnionSiege.getInstance().isSelfEmpty();
            if (!isSelfEmpty) {
                return false;
            }
            return RedPoint.getInstance().checkDepends(id);
        },
    },
    {
        id: RedPointId.ActivityUnionDefenseAchieve,
        name: "公会活动-守卫战-成就可领",
        condition: () => {
            const status = UnionSiege.getInstance().getStatus(ACTIVITY_ID.UNION_DEFENSE);
            const redShow = UnionSiege.getInstance().isRedPoint(status);
            if (!redShow) {
                return false;
            }
            const isUnionEmpty = UnionSiege.getInstance().isUnionEmpty();
            if (!isUnionEmpty) {
                return false;
            }
            const isSelfEmpty = UnionSiege.getInstance().isSelfEmpty();
            if (!isSelfEmpty) {
                return false;
            }

            const taskActivityId = TBActivity.getInstance().getItemByIdAndType(
                ACTIVITY_ID.UNION_DEFENSE,
                EnumActivityItemType.Task
            );
            const dataSeq = TBTaskSeq.getInstance().getDataByModuleActivityId(
                EnumTaskSeqModule.ActivityTask,
                taskActivityId
            );
            const taskGroup = TBTaskGroup.getInstance().getDataById(dataSeq.taskGroup);
            for (const e of taskGroup.taskID) {
                if (Task.getInstance().isTaskCanGetReward(e)) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        id: RedPointId.ActivityUnionDefenseReward,
        name: "公会活动-守卫战-宝库可领",
        interval: RedPointIntervalType.Seconds10,
        condition: () => {
            const status = UnionSiege.getInstance().getStatus(ACTIVITY_ID.UNION_DEFENSE);
            const redShow = UnionSiege.getInstance().isRedPoint(status);
            if (!redShow) {
                return false;
            }
            const isUnionEmpty = UnionSiege.getInstance().isUnionEmpty();
            if (!isUnionEmpty) {
                return false;
            }
            const isSelfEmpty = UnionSiege.getInstance().isSelfEmpty();
            if (!isSelfEmpty) {
                return false;
            }

            const myDetails = UnionSiege.getInstance().getMyUnionSingeDetailObj();
            if (status === EnumUnionSiegeStatus.Status5 && myDetails && !myDetails.isBoxRewarded) {
                return true; // 在领奖期 同时奖励未领取
            }
            return false;
        },
    },
    {
        id: RedPointId.ActivityUnionDefenseHasCount,
        name: "公会活动-守卫战-可挑战",
        interval: RedPointIntervalType.Seconds10,
        condition: () => {
            const status = UnionSiege.getInstance().getStatus(ACTIVITY_ID.UNION_DEFENSE);
            const redShow = UnionSiege.getInstance().isRedPoint(status);
            if (!redShow) {
                return false;
            }
            const isUnionEmpty = UnionSiege.getInstance().isUnionEmpty();
            if (!isUnionEmpty) {
                return false;
            }
            const isSelfEmpty = UnionSiege.getInstance().isSelfEmpty();
            if (!isSelfEmpty) {
                return false;
            }

            const myDetails = UnionSiege.getInstance().getMyUnionSingeDetailObj();
            const unionData = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseBattleNumber);
            if (status === EnumUnionSiegeStatus.Status3 && myDetails && unionData - myDetails.todayChallengeCount > 0) {
                return true;
            }
            return false;
        },
    },
    {
        id: RedPointId.ShopActivityUnionDefense,
        name: "商店-公会攻防战商店",
        type: RedPointType.Daily,
        condition: (redId: RedPointId) => {
            return Shop.getInstance().getRedStateByShop(EnumShopType.UnionDefenseShop, redId);
        },
    },
];

export const RED_POINT_CONFIG: IRedPointConfig[] = RP_CONFIG_ZJR.concat(RP_CONFIG_CX)
    .concat(RP_CONFIG_WYM)
    .concat(RP_CONFIG_FSJ)
    .concat(RP_CONFIG_ZWJ)
    .concat(RP_CONFIG_TTQ);
