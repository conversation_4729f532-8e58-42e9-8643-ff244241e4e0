/*
 * 系统入口定义
 * @Author: linyb
 * @Date: 2022-07-19 11:05:32
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-16 17:02:45
 */

import Channel from "../../../nsn/config/Channel";
import Platform from "../../../nsn/platform/Platform";
import UI from "../../../nsn/ui/UI";
import Time, { MINUTE_TO_SECOND } from "../../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../../nsn/util/TimeFormat";
import Tips from "../../../nsn/util/Tips";
import i18n from "../../config/i18n/I18n";
import { EnumSystemEntryPlace } from "../../data/base/BaseSystemEntry";
import { EnumUniversalPara } from "../../data/base/BaseUniversal";
import DataSystemEntry from "../../data/extend/DataSystemEntry";
import { ACTIVITY_ID, ACTIVITY_PERSON_SPRINT_ID, ACTIVITY_SPRINT_ID } from "../../data/parser/TBActivity";
import TBActivityLogin from "../../data/parser/TBActivityLogin";
import TBGameSwitch from "../../data/parser/TBGameSwitch";
import TBPopup from "../../data/parser/TBPopup";
import TBRechargeGift, { RECHARGE_GIFT_ID } from "../../data/parser/TBRechargeGift";
import { SYSTEM_ENTRY_ID } from "../../data/parser/TBSystemEntry";
import TBUniversal from "../../data/parser/TBUniversal";
import Activity from "../../game/Activity";
import Bulletin from "../../game/Bulletin";
import GameClub, { GameClubScene } from "../../game/GameClub";
import GameSwitch from "../../game/GameSwitch";
import PopUps from "../../game/PopUps";
import Recharge from "../../game/Recharge";
import ActivitySignIn from "../../game/activity/ActivitySignIn";
import CommonPopup from "../commonPopup/CommonPopup";
import { RedPointId } from "../redPoint/RedPointId";

// 返回值接口定义
interface ISystemEntryConfig {
    name: string; // 备注
    id: number; // SystemEntry表id
    redPoint?: RedPointId; // 红点
    param?: (config: DataSystemEntry) => any; // 窗口参数
    condition?: (config: DataSystemEntry) => boolean; // 是否满足显示条件
    click?: (config: DataSystemEntry) => void; // 额外方法。如果设置了当前函数，则不走ui.open
    text?: (text: cc.Label, config: DataSystemEntry) => void; // 文本
    extPfb?: string; // 额外的预制体
}

interface ISystemEntryPlaceConfig {
    name: string; // 名称
    place: EnumSystemEntryPlace; // 位置
    redPointOptimization: boolean; // 是否进行红点优化
    redPoint?: RedPointId; // 红点
}

/**
 * 系统入口枚举
 */
export const SYSTEM_ENTRY_CONFIG: ISystemEntryConfig[] = [
    {
        name: "邮件",
        id: SYSTEM_ENTRY_ID.MAIL,
        redPoint: RedPointId.MailAll,
    },
    {
        name: "玩法预告",
        id: SYSTEM_ENTRY_ID.GAME_SWITCH,
        redPoint: RedPointId.GameSwitch,
        condition: (config: DataSystemEntry) => {
            const allList = TBGameSwitch.getInstance().getList();
            const list = allList.filter((v) => v.preview && !!v.reward.length);
            const allReceive = list.every((data) => {
                const { result } = GameSwitch.getInstance().check(data.id);
                const received = GameSwitch.getInstance().isReceived(data.id);
                if (result && received) {
                    return true;
                }
                return false;
            });
            return !allReceive;
        },
    },
    {
        name: "日常任务",
        id: SYSTEM_ENTRY_ID.DAILY_TASK,
        redPoint: RedPointId.DailyTaskTotal,
    },
    {
        name: "祝福系统",
        id: SYSTEM_ENTRY_ID.BLESS,
        extPfb: "prefab/systemEntry/PrefabSystemEntryBless",
    },
    { name: "充值好礼", id: SYSTEM_ENTRY_ID.RECHARGE_GIFT, redPoint: RedPointId.RechargeGift },
    {
        name: "排行榜",
        id: SYSTEM_ENTRY_ID.RANK,
    },
    {
        name: "开服庆典",
        id: SYSTEM_ENTRY_ID.OPENING_CELEBRATION,
        redPoint: RedPointId.ActivityOpeningCelebrationAll,
    },
    {
        name: "签到",
        id: SYSTEM_ENTRY_ID.SIGN_IN,
        redPoint: RedPointId.SignInAll,
        condition: (config: DataSystemEntry) => {
            const activity = Activity.getInstance().getDataById(ACTIVITY_ID.SIGN_IN);
            if (!activity) {
                return false;
            }
            const isOpen = Activity.getInstance().isOpeningById(ACTIVITY_ID.SIGN_IN);
            if (!isOpen) {
                return false;
            }
            const sign = TBActivityLogin.getInstance().getDataByActivityId(ACTIVITY_ID.SIGN_IN);
            const { takedIds } = ActivitySignIn.getInstance().getDataByActivityId(ACTIVITY_ID.SIGN_IN);
            return takedIds.length < sign.length;
        },
    },
    {
        name: "首充礼包",
        id: SYSTEM_ENTRY_ID.FIRST_RECHARGE,
        redPoint: RedPointId.FirstRechargeAll,
        condition: (config: DataSystemEntry) => {
            let isAllReceived = true;
            const rechargeGiftData = TBRechargeGift.getInstance().getDataById(RECHARGE_GIFT_ID.FIRST_RECHARGE);
            for (const e of rechargeGiftData.giftID) {
                const isReceived = Recharge.getInstance().isRechargeGiftReceived(e);
                if (!isReceived) {
                    isAllReceived = false;
                    break;
                }
            }
            return !isAllReceived;
        },
    },
    {
        name: "限时竞速",
        id: SYSTEM_ENTRY_ID.SPRINT,
        redPoint: RedPointId.ActivitySprintAll,
        condition: () => {
            for (const e of ACTIVITY_SPRINT_ID) {
                const isOpen = Activity.getInstance().isValidById(e);
                if (isOpen) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        name: "狂送x抽",
        id: SYSTEM_ENTRY_ID.CRAZY_DRAW,
        redPoint: RedPointId.CrazyDraw,
    },
    {
        name: "公告",
        id: SYSTEM_ENTRY_ID.BULLETIN,
        redPoint: RedPointId.BulletinUnRead,
        click: () => {
            Bulletin.getInstance().request(() => {
                const bi = Bulletin.getInstance().getBulletinInfo();
                if (!bi.length) {
                    Tips.getInstance().show(i18n.bulletin0001);
                } else {
                    UI.getInstance().open("PopupBulletin");
                }
            });
        },
    },
    {
        name: "通用礼包",
        id: SYSTEM_ENTRY_ID.COMMON_GIFT,
        condition: (config: DataSystemEntry) => {
            const popupTB = TBPopup.getInstance().getDataListByUIName(config.windowName);
            for (const item of popupTB) {
                if (CommonPopup.getInstance().isActiveById(item)) {
                    return true;
                }
            }
            return false;
        },
        text: (text: cc.Label, config: DataSystemEntry) => {
            const residueTimes: number[] = [];
            const popupTB = TBPopup.getInstance().getDataListByUIName(config.windowName);
            for (const item of popupTB) {
                const residueTime = CommonPopup.getInstance().getResidueTime(item.id);
                residueTime && residueTimes.push(residueTime);
            }
            const minTime = Math.min(...residueTimes);
            text.string = TimeFormat.getInstance().getTextByDuration(minTime, TimeDurationFormatType.HH_MM_SS);
        },
    },
    {
        name: "链式礼包",
        id: SYSTEM_ENTRY_ID.LINK_GIFT,
        redPoint: RedPointId.LinkGift,
        condition: (config: DataSystemEntry) => {
            const popupTB = TBPopup.getInstance().getDataListByUIName(config.windowName);
            for (const item of popupTB) {
                if (CommonPopup.getInstance().isActiveById(item)) {
                    return true;
                }
            }
            return false;
        },
        text: (text: cc.Label, config: DataSystemEntry) => {
            const residueTimes: number[] = [];
            const popupTB = TBPopup.getInstance().getDataListByUIName(config.windowName);
            for (const item of popupTB) {
                const residueTime = CommonPopup.getInstance().getResidueTime(item.id);
                residueTime && residueTimes.push(residueTime);
            }
            const minTime = Math.min(...residueTimes);
            text.string = TimeFormat.getInstance().getTextByDuration(minTime, TimeDurationFormatType.HH_MM_SS);
        },
    },
    {
        name: "好友",
        id: SYSTEM_ENTRY_ID.FRIEND,
        redPoint: RedPointId.FriendAll,
    },
    {
        name: "社群福利",
        id: SYSTEM_ENTRY_ID.SOCIAL,
        redPoint: RedPointId.SocialAll,
        condition: (config: DataSystemEntry) => {
            const socials = Channel.getInstance().getConfig().getSocialType();
            return !!socials.length;
        },
    },
    {
        name: "背包",
        id: SYSTEM_ENTRY_ID.BAG,
        redPoint: RedPointId.BagAll,
    },
    {
        name: "时空回溯1",
        id: SYSTEM_ENTRY_ID.TIME_BACK1,
        redPoint: RedPointId.TimeBackAll,
        click: (config: DataSystemEntry) => {
            UI.getInstance().open(config.windowName, config.activityId);
        },
    },
    {
        name: "战车夺宝1",
        id: SYSTEM_ENTRY_ID.TANK_TREASURE1,
        redPoint: RedPointId.TankDrawAll,
        click: (config: DataSystemEntry) => {
            UI.getInstance().open(config.windowName, config.activityId);
        },
    },
    {
        name: "宠物",
        id: SYSTEM_ENTRY_ID.PET,
        redPoint: RedPointId.PetAll,
    },
    {
        name: "战车",
        id: SYSTEM_ENTRY_ID.TANK,
        redPoint: RedPointId.TankAll,
    },
    {
        name: "竞技场",
        id: SYSTEM_ENTRY_ID.ARENA,
        redPoint: RedPointId.ArenaAll,
    },
    {
        name: "累天累充",
        id: SYSTEM_ENTRY_ID.TOTAL_RECHARGE,
        redPoint: RedPointId.TotalRechargeAll,
    },
    {
        name: "特权卡",
        id: SYSTEM_ENTRY_ID.PRIVILEGE,
        redPoint: RedPointId.RechargePrivilege,
    },
    {
        name: "主线副本-章节",
        id: SYSTEM_ENTRY_ID.DUNGEON_CHAPTER_MAIN,
    },
    {
        name: "新手试炼",
        id: SYSTEM_ENTRY_ID.NEWBIE_TRIAL,
        redPoint: RedPointId.ActivityNewbieTrial,
    },
    {
        name: "背饰抽卡",
        id: SYSTEM_ENTRY_ID.WING_DRAW_CARD,
        redPoint: RedPointId.WingDrawCardAll,
        click: (config: DataSystemEntry) => {
            UI.getInstance().open(config.windowName, config.activityId);
        },
    },
    {
        name: "神器抽卡",
        id: SYSTEM_ENTRY_ID.WEAPON_DRAW_CARD,
        redPoint: RedPointId.WeaponDrawCardAll,
        click: (config: DataSystemEntry) => {
            UI.getInstance().open(config.windowName, config.activityId);
        },
    },
    {
        name: "宝箱系统",
        id: SYSTEM_ENTRY_ID.BOX,
        redPoint: RedPointId.BoxAll,
        click: (config: DataSystemEntry) => {
            UI.getInstance().open(config.windowName, config.activityId);
        },
    },
    {
        name: "活动小游戏-战车跑酷",
        id: SYSTEM_ENTRY_ID.TANK_MINI_GAME,
        redPoint: RedPointId.HamsterParkour,
        click: (config: DataSystemEntry) => {
            UI.getInstance().open(config.windowName, config.activityId);
        },
    },
    {
        name: "挂机收益",
        id: SYSTEM_ENTRY_ID.IDLE_INCOME,
        redPoint: RedPointId.IdleReward,
    },
    {
        name: "微信游戏圈",
        id: SYSTEM_ENTRY_ID.GAME_CLUB,
        condition: (config: DataSystemEntry) => {
            return Platform.getInstance().isWechatMinigame();
        },
        click: (config: DataSystemEntry) => {
            GameClub.getInstance().request((res) => {
                GameClub.getInstance().sendMiniUpdateMomentsInfo(res.encryptedData, res.iv, GameClubScene.OpenGameClub);
            });
        },
    },
    {
        name: "个人竞速",
        id: SYSTEM_ENTRY_ID.PERSON_SPRINT,
        redPoint: RedPointId.ActivityPersonSprintAll,
        condition() {
            for (const e of ACTIVITY_PERSON_SPRINT_ID) {
                const isOpen = Activity.getInstance().isValidById(e);
                if (isOpen) {
                    return true;
                }
            }
            return false;
        },
    },
    {
        name: "王权基金",
        id: SYSTEM_ENTRY_ID.POWER_FUND,
        redPoint: RedPointId.PowerFund,
    },
    {
        name: "专属客服",
        id: SYSTEM_ENTRY_ID.VIP_SERVICE,
        condition: () => {
            const data = PopUps.getInstance().getPopUpsInfos();
            const isShowEntry = PopUps.getInstance().isPopUpsEntryShow();
            if (data.length > 0 && isShowEntry) {
                const time = PopUps.getInstance().getPopUpsEntryTime();
                if (time !== 0) {
                    const uniData = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.PushPopupTime);
                    const now = Time.getInstance().now();
                    if (time + uniData * MINUTE_TO_SECOND * 1000 > now) {
                        return true;
                    }
                }
            }
            return false;
        },
        text: (text: cc.Label) => {
            const time = PopUps.getInstance().getPopUpsEntryTime();
            const uniData = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.PushPopupTime);
            const now = Time.getInstance().now();
            text.string = TimeFormat.getInstance().getTextByDuration(
                time + uniData * MINUTE_TO_SECOND * 1000 - now,
                TimeDurationFormatType.HH_MM_SS
            );
        },
    },
];

export const SYSTEM_ENTRY_PLACE_CONFIG: ISystemEntryPlaceConfig[] = [
    {
        name: "主界面-右侧",
        place: EnumSystemEntryPlace.HomeRight,
        redPointOptimization: true,
        redPoint: RedPointId.SystemEntryHome,
    },
    {
        name: "主界面-右侧收纳",
        place: EnumSystemEntryPlace.HomeRightFold,
        redPointOptimization: true,
    },
    {
        name: "主界面-左侧",
        place: EnumSystemEntryPlace.HomeLeft,
        redPointOptimization: true,
    },
    {
        name: "家园",
        place: EnumSystemEntryPlace.HomeLand,
        redPointOptimization: false,
    },
];
