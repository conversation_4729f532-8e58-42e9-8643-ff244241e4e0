/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-07 16:46:47
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-22 09:46:02
 */

import Audio from "../../nsn/audio/Audio";
import Channel from "../../nsn/config/Channel";
import Server from "../../nsn/config/Server";
import ClientConfig from "../../nsn/core/ClientConfig";
import Reporter from "../../nsn/core/Reporter";
import Socket from "../../nsn/core/Socket";
import Etadpu from "../../nsn/etadpu/Etadpu";
import Minigame from "../../nsn/platform/Minigame";
import Platform from "../../nsn/platform/Platform";
import Recycle from "../../nsn/recycle/Recycle";
import UI from "../../nsn/ui/UI";
import Logger from "../../nsn/util/Logger";
import Singleton from "../../nsn/util/Singleton";
import { REPORTER_ID } from "../config/ReporterConfig";
import Activity from "../game/Activity";
import ActivityGame from "../game/ActivityGame";
import Ad from "../game/Ad";
import Archer from "../game/Archer";
import Arena from "../game/Arena";
import Attribute from "../game/Attribute";
import Bag from "../game/Bag";
import Bless from "../game/Bless";
import Box from "../game/Box";
import CdKey from "../game/CdKey";
import Collection from "../game/Collection";
import CollectionGame from "../game/CollectionGame";
import CombatLog from "../game/CombatLog";
import CombatScore from "../game/CombatScore";
import CombatSetting from "../game/CombatSetting";
import Confidant from "../game/Confidant";
import DrawCard from "../game/DrawCard";
import DungeonBoss from "../game/DungeonBoss";
import DungeonBox from "../game/DungeonBox";
import DungeonCloud from "../game/DungeonCloud";
import DungeonEquip from "../game/DungeonEquip";
import DungeonMain from "../game/DungeonMain";
import DungeonThief from "../game/DungeonThief";
import DungeonTower from "../game/DungeonTower";
import Equip from "../game/Equip";
import Forge from "../game/Forge";
import Friend from "../game/Friend";
import GameClub from "../game/GameClub";
import GameLog from "../game/GameLog";
import GameSwitch from "../game/GameSwitch";
import IdleReward from "../game/IdleReward";
import LeadSkin from "../game/LeadSkin";
import Magical from "../game/Magical";
import Mail from "../game/Mail";
import MakeArrow from "../game/MakeArrow";
import Mining from "../game/Mining";
import Park from "../game/Park";
import Personalized from "../game/Personalized";
import Pet from "../game/Pet";
import Player from "../game/Player";
import PopUps from "../game/PopUps";
import Power from "../game/Power";
import PowerPeak from "../game/PowerPeak";
import Privilege from "../game/Privilege";
import Progress from "../game/Progress";
import RandomShop from "../game/RandomShop";
import Rank from "../game/Rank";
import Recharge from "../game/Recharge";
import RedPacket from "../game/RedPacket";
import Report from "../game/Report";
import SelectPack from "../game/SelectPack";
import Setting from "../game/Setting";
import Shop from "../game/Shop";
import Talent from "../game/Talent";
import Tank from "../game/Tank";
import Task from "../game/Task";
import Team from "../game/Team";
import Union from "../game/Union";
import UnionHelp from "../game/UnionHelp";
import UnionSiege from "../game/UnionSiege";
import Weapon from "../game/Weapon";
import Welfare from "../game/Welfare";
import Wing from "../game/Wing";
import CombatDungeonMain from "../game/combat/CombatDungeonMain";
import CombatDungeonPve from "../game/combat/CombatDungeonPve";
import CombatDungeonPvp from "../game/combat/CombatDungeonPvp";
import Bugly from "../sdk/Bugly";
import GameData from "../sdk/GameData";
import SDKReporter from "../sdk/reporter/SDKReporter";
import Chat from "./Chat";
import Guide from "./Guide";
import PlayerData from "./PlayerData";
import Reset from "./Reset";
import CommonPopup from "./commonPopup/CommonPopup";
import RedPoint from "./redPoint/RedPoint";
import GameTimer from "./timer/GameTimer";

export default class Game extends Singleton {
    private leaveGameTime: number = 0; // 离开游戏时长统计

    constructor() {
        super();
        cc.game.on(cc.game.EVENT_SHOW, this.onGameShow, this);
        cc.game.on(cc.game.EVENT_HIDE, this.onGameHide, this);
    }

    /**
     * 初始化
     */
    public init(): void {
        UI.getInstance().init();
        Recycle.getInstance().init();
        RedPoint.getInstance().init();
    }

    /**
     * 清除所有数据
     */
    public clear(): void {
        // game
        Socket.getInstance().clear();
        RedPoint.getInstance().clear();
        Player.getInstance().clear();
        PlayerData.getInstance().clear();
        Reset.getInstance().clear();
        Bag.getInstance().clear();
        RandomShop.getInstance().clear();
        Union.getInstance().clear();
        Shop.getInstance().clear();
        Task.getInstance().clear();
        GameLog.getInstance().clear();
        DrawCard.getInstance().clear();
        GameSwitch.getInstance().clear();
        Guide.getInstance().clear();
        Chat.getInstance().clear();
        Recharge.getInstance().clear();
        Mail.getInstance().clear();
        Privilege.getInstance().clear();
        CommonPopup.getInstance().clear();
        Activity.getInstance().clear();
        ActivityGame.getInstance().clear();
        GameTimer.getInstance().clear();
        Rank.getInstance().clear();
        Progress.getInstance().clear();
        CdKey.getInstance().clear();
        Ad.getInstance().clear();
        Archer.getInstance().clear();
        Team.getInstance().clear();
        MakeArrow.getInstance().clear();
        Pet.getInstance().clear();
        Attribute.getInstance().clear();
        Bless.getInstance().clear();
        IdleReward.getInstance().clear();
        SelectPack.getInstance().clear();
        Magical.getInstance().clear();
        Mining.getInstance().clear();
        Friend.getInstance().clear();
        Welfare.getInstance().clear();
        Personalized.getInstance().clear();
        CombatDungeonMain.getInstance().clear();
        CombatDungeonPve.getInstance().clear();
        CombatDungeonPvp.getInstance().clear();
        CombatLog.getInstance().clear();
        CombatScore.getInstance().clear();
        DungeonMain.getInstance().clear();
        Equip.getInstance().clear();
        Progress.getInstance().clear();
        Park.getInstance().clear();
        DungeonEquip.getInstance().clear();
        Talent.getInstance().clear();
        Power.getInstance().clear();
        Confidant.getInstance().clear();
        LeadSkin.getInstance().clear();
        Tank.getInstance().clear();
        Wing.getInstance().clear();
        Weapon.getInstance().clear();
        DungeonBoss.getInstance().clear();
        DungeonCloud.getInstance().clear();
        DungeonThief.getInstance().clear();
        DungeonTower.getInstance().clear();
        DungeonBox.getInstance().clear();
        Box.getInstance().clear();
        Forge.getInstance().clear();
        Collection.getInstance().clear();
        Arena.getInstance().clear();
        CombatSetting.getInstance().clear();
        CollectionGame.getInstance().clear();
        PowerPeak.getInstance().clear();
        GameClub.getInstance().clear();
        UnionHelp.getInstance().clear();
        RedPacket.getInstance().clear();
        UnionSiege.getInstance().clear();
        Setting.getInstance().clear();
        Report.getInstance().clear();
        PopUps.getInstance().clear();
        ClientConfig.getInstance().clear();
    }

    /**
     * 更新页初始化
     */
    public async initInEtadpuScene(): Promise<void> {
        await Channel.getInstance().init();
        Audio.getInstance().init();
        GameData.getInstance().init();
        Server.getInstance().init();
        Reporter.logSceneEvent(REPORTER_ID.SCENE.ETADPU);
        SDKReporter.getInstance().start();
    }

    /**
     * 登录页初始化
     */
    public async initInLoginScene(): Promise<void> {
        await Channel.getInstance().init();
        Audio.getInstance().init();
        GameData.getInstance().init();
        Server.getInstance().init();
        Bugly.getInstance().init();
        Reporter.logSceneEvent(REPORTER_ID.SCENE.LOGIN);
    }

    /**
     * 游戏内初始化
     */
    public initInGameScene(): void {
        Reporter.logSceneEvent(REPORTER_ID.SCENE.GAME);
    }

    /**
     * 修复游戏
     */
    public fix(): void {
        Logger.info("Game", "修复游戏");
        cc.sys.localStorage.clear();
        if (Platform.getInstance().isMinigame()) {
            Minigame.getInstance().fixGame();
        } else if (Platform.getInstance().isNative()) {
            const storagePath = Etadpu.getInstance().getStoragePath();
            if (jsb.fileUtils.isDirectoryExist(storagePath)) {
                Logger.info("Game", "清除热更文件");
                jsb.fileUtils.removeDirectory(storagePath);
            }
            const tempStoragePath = storagePath + "_temp/";
            if (jsb.fileUtils.isDirectoryExist(tempStoragePath)) {
                Logger.info("Game", "清除热更临时文件");
                jsb.fileUtils.removeDirectory(tempStoragePath);
            }
            cc.game.end();
        }
    }

    /**
     * 结束游戏
     */
    public end(): void {
        cc.game.end();
    }

    /**
     * 游戏进入前台时调用
     * @param event
     */
    private onGameShow(event: Event): void {
        Logger.info("游戏管理", `游戏进入前台，离开时长${((Date.now() - this.leaveGameTime) / 1000).toFixed(2)}秒`);
        this.leaveGameTime = 0;
        Socket.getInstance().ping();
    }

    /**
     * 游戏进入后台时调用
     * @param event
     */
    private onGameHide(event: Event): void {
        Logger.info("游戏管理", "游戏进入后台");
        this.leaveGameTime = Date.now();
        Socket.getInstance().ping();
    }
}
