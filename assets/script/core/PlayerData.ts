/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-09 10:52:19
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-16 16:28:29
 */

import Loading, {
    LoadingErrorTypeEvent,
    LoadingEvent,
    LoadingProgressEvent,
    LoadingType,
} from "../../nsn/core/Loading";
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import ArrayUtils from "../../nsn/util/ArrayUtils";
import Logger from "../../nsn/util/Logger";
import {
    AdsInit,
    ArcherInit,
    ArenaInit,
    ArrowGroupInit,
    BagInit,
    BlessInit,
    BoxInit,
    C2S,
    CollectionGameInit,
    CollectionInit,
    ConfidantInit,
    DrawCardInit,
    DungeonBossInit,
    DungeonBoxInit,
    DungeonCloudInit,
    DungeonThiefInit,
    DungeonTowerInit,
    EquipDungeonInit,
    EquipInit,
    ForgeInit,
    FriendInit,
    GameSwitchInit,
    GuideInit,
    IdleEarningsInit,
    LeadSkinInit,
    MagicalInit,
    MailInit,
    MapBarrierInit,
    MiniInit,
    MiningInit,
    PetInit,
    PlotInit,
    PopUpsInit,
    PowerInit,
    PowerPeakGet,
    PrivilegeInit,
    ProgressInit,
    RandomShopInit,
    RechargeGetInfo,
    RechargeGetPackInfo,
    RedPacketInit,
    SettingInit,
    ShopGet,
    TalentTreeInit,
    TankInit,
    TaskInit,
    UnionActiveRewardGet,
    UnionBossGet,
    UnionDonateGet,
    UnionGetApplyList,
    UnionHelpGet,
    UnionInit,
    UnionSiegeGet,
    UnionTreasureShopGet,
    WeaponInit,
    WelfareInit,
    WingsInit,
} from "../../protobuf/proto";
import { EnumChatType } from "../data/base/BaseChat";
import { EnumPopupType } from "../data/base/BasePopup";
import { EnumShopType } from "../data/base/BaseShop";
import TBChat from "../data/parser/TBChat";
import SceneUtils, { SceneType } from "../utils/SceneUtils";
import Chat from "./Chat";
import CommonPopup from "./commonPopup/CommonPopup";
import Guide from "./Guide";
import Reset, { ResetEvent } from "./Reset";

const BASE_REQUEST = {
    archerInit: ArcherInit.create(),
    petInit: PetInit.create(),
    mapbarrierInit: MapBarrierInit.create(),
    arrowgroupInit: ArrowGroupInit.create(),
    arenaInit: ArenaInit.create(),
    bagInit: BagInit.create(),
    randomshopInit: RandomShopInit.create(),
    shopGet: ShopGet.create({
        types: Object.values(EnumShopType).slice(Object.values(EnumShopType).length / 2) as number[],
    }),
    unionInit: UnionInit.create(),
    taskInit: TaskInit.create(),
    drawcardInit: DrawCardInit.create(),
    gameswitchInit: GameSwitchInit.create(),
    guideInit: GuideInit.create(),
    rechargeGetInfo: RechargeGetInfo.create(),
    rechargeGetPackInfo: RechargeGetPackInfo.create(),
    magicalInit: MagicalInit.create(),
    mailInit: MailInit.create(),
    unionGetApplyList: UnionGetApplyList.create(),
    unionActiveRewardGet: UnionActiveRewardGet.create(),
    unionTreasureShopGet: UnionTreasureShopGet.create(),
    unionBossGet: UnionBossGet.create(),
    unionDonateGet: UnionDonateGet.create(),
    adsInit: AdsInit.create(),
    blessInit: BlessInit.create(),
    privilegeInit: PrivilegeInit.create(),
    idleearningsInit: IdleEarningsInit.create(),
    miningInit: MiningInit.create(),
    friendInit: FriendInit.create(),
    forgeInit: ForgeInit.create(),
    welfareInit: WelfareInit.create(),
    equipInit: EquipInit.create(),
    progressInit: ProgressInit.create(),
    plotInit: PlotInit.create(),
    powerInit: PowerInit.create(),
    equipdungeonInit: EquipDungeonInit.create(),
    talenttreeInit: TalentTreeInit.create(),
    confidantInit: ConfidantInit.create(),
    leadskinInit: LeadSkinInit.create(),
    tankInit: TankInit.create(),
    wingsInit: WingsInit.create(),
    weaponInit: WeaponInit.create(),
    dungeonbossInit: DungeonBossInit.create(),
    dungeoncloudInit: DungeonCloudInit.create(),
    dungeonthiefInit: DungeonThiefInit.create(),
    dungeontowerInit: DungeonTowerInit.create(),
    dungeonboxInit: DungeonBoxInit.create(),
    boxInit: BoxInit.create(),
    collectionInit: CollectionInit.create(),
    collectiongameInit: CollectionGameInit.create(),
    powerpeakGet: PowerPeakGet.create(),
    miniInit: MiniInit.create(),
    unionHelpGet: UnionHelpGet.create(),
    redpacketInit: RedPacketInit.create(),
    unionSiegeGet: UnionSiegeGet.create(),
    settingInit: SettingInit.create(),
    popupsInit: PopUpsInit.create(),
};

export default class PlayerData extends Logic {
    private baseRequestMsg: string[] = [];
    private isRequesting: boolean = false;

    /**
     * 清理数据
     */
    public clear(): void {
        this.baseRequestMsg = [];
        this.isRequesting = false;
    }

    protected registerHandler(): void {
        Socket.getInstance().on(
            Object.values(BASE_REQUEST).map((v) => v.clazzName + "Ret"),
            (data: any) => {
                ArrayUtils.remove(this.baseRequestMsg, (v) => v === data.clazzName);
                if (this.baseRequestMsg.length === 0 && this.isRequesting) {
                    this.isRequesting = false;
                    Logger.info("PlayerData", "请求用户基础数据完成...");
                    const curScene = SceneUtils.getCurrentScene();
                    switch (curScene) {
                        case SceneType.Login:
                            Loading.getInstance().emit(LoadingEvent.PlayerDataInitSuccess);
                            break;
                        case SceneType.Game:
                            // 触发首次引导
                            Guide.getInstance().checkAll();
                            // 触发弹窗
                            CommonPopup.getInstance().triggerByType(EnumPopupType.LoginType);
                            // 重置玩家数据
                            Reset.getInstance().emit(ResetEvent.ResetPlayerData);
                            break;
                        default:
                            break;
                    }
                }
            },
            this
        );
        Loading.getInstance().on(
            LoadingProgressEvent.ProgressError,
            (errorType: LoadingErrorTypeEvent, type: LoadingType, text: string) => {
                // 超时错误
                if (type === LoadingType.Network && this.isRequesting) {
                    Logger.error("PlayerData", "请求用户基础数据失败：" + JSON.stringify(this.baseRequestMsg));
                }
            },
            this
        );
    }

    /**
     * 请求基础数据
     */
    public requestBaseInfo(): void {
        Logger.info("PlayerData", "开始请求用户基础数据...");
        this.isRequesting = true;
        this.baseRequestMsg = Object.values(BASE_REQUEST).map((v) => v.clazzName + "Ret");
        const data = C2S.create(BASE_REQUEST);
        Socket.getInstance().send(data);
    }

    /**
     * 请求额外数据
     */
    public requestExtraInfo(): void {
        Logger.info("PlayerData", "开始请求用户延迟数据...");
        // 请求聊天数据
        this.requestChatData();
    }

    /**
     * 请求聊天数据
     */
    private requestChatData(): void {
        Chat.getInstance().init();
        Chat.getInstance().sendChatGetOfflineMessage();
        const chatList = TBChat.getInstance().getList();
        for (const chat of chatList) {
            let channel = "";
            switch (chat.type) {
                case EnumChatType.WorldChat:
                case EnumChatType.UnionChat:
                case EnumChatType.NewsChat:
                    channel = chat.channelSign;
                    break;
                default:
                    break;
            }
            if (channel) {
                Chat.getInstance().sendChatGetChannelMessages(channel);
            }
        }
    }
}
