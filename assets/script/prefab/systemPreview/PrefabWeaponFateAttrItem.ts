/*
 * @Author: linds
 * @Date: 2023-07-25 13:54:51
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-17 20:49:26
 */

import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import TextUtils from "../../../nsn/util/TextUtils";
import i18n from "../../config/i18n/I18n";
import DataWingStar from "../../data/extend/DataWingStar";
import TBAttribute from "../../data/parser/TBAttribute";
import TBWeaponStar from "../../data/parser/TBWeaponStar";
import ImageUtils from "../../utils/ImageUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabWeaponFateAttrItem extends ListViewItem {
    @property(cc.Node)
    stars: cc.Node = null;
    @property(cc.Node)
    starCount: cc.Node = null;
    @property(cc.Node)
    attr: cc.Node = null;

    public unuse(): void {}
    public reuse(): void {}

    public updateData(data: DataWingStar): void {
        const { quality, star } = data;
        const starData = TBWeaponStar.getInstance().getDataByQualityAndStar(quality, star);
        const fateAttr = TBAttribute.getInstance().getFateAttrs(starData.attribute);
        const { name, value } = TBAttribute.getInstance().formatAttribute(fateAttr);

        ImageUtils.setStarsIcon(this.stars, star, star === 0);
        this.starCount.label(TextUtils.format(i18n.skin0005, star));
        this.attr.label(name + "+" + value);
    }
}
