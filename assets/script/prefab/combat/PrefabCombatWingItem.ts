/*
 * @Author: chenx
 * @Date: 2025-03-18 09:44:48
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 16:10:16
 */
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import MathUtils from "../../../nsn/util/MathUtils";
import { EnumSkillType } from "../../data/base/BaseSkill";
import ArcherClientConfig, { ISpineData } from "../../game/ArcherClientConfig";
import { CombatMemberState } from "../../game/combat/CombatMember";
import { ICombatWingData } from "../../game/combat/CombatMemberPlayer";
import { ICombatMemberSkillData } from "../../game/combat/CombatSkill";
import SpineUtils from "../../utils/SpineUtils";

/**
 * 动画名称
 */
enum AniName {
    Wait = "attackWait", // 待机
    Cast = "skill", // 施法
}

/**
 * 动画事件名称
 */
const ANI_EVENT_NAME = "trigger";

const { ccclass, property } = cc._decorator;

/**
 * 背饰item
 */
@ccclass
export default class PrefabCombatWingItem extends I18nComponent {
    @property(sp.Skeleton)
    spineWing: sp.Skeleton = null; // 背饰

    private data: ICombatWingData = null; // 背饰数据

    private spineData: ISpineData = null; // 骨骼数据
    private waitTriggerState: CombatMemberState = null; // 待触发状态
    private stateTime: number = 0; // 状态时间
    private castCb: () => void = null; // 施法回调
    private castTime: number = 0; // 施法时间
    private gameSpeed: number = 1; // 游戏速度
    private isSkipingCombat: boolean = false; // 是否正在跳过战斗

    /**
     * 初始化数据
     * @param data 背饰数据
     */
    public initData(data: ICombatWingData): void {
        this.data = data;
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatMemberState): void {
        this.data.state = state;

        switch (this.data.state) {
            case CombatMemberState.Init:
                this.waitTriggerState = null;
                this.stateTime = 0;
                this.castCb = null;
                this.castTime = 0;
                this.isSkipingCombat = false;

                this.spineWing.clearTracks();
                break;
            case CombatMemberState.Wait:
                if (!this.isSkipingCombat) {
                    if (this.spineWing.animation !== AniName.Wait) {
                        this.spineWing.setAnimation(0, AniName.Wait, true);
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 更新状态
     * @param dt
     */
    public updateState(dt: number): void {
        switch (this.data.state) {
            case CombatMemberState.Cast:
                if (this.stateTime > 0) {
                    this.stateTime -= dt;
                    this.stateTime = MathUtils.round(this.stateTime, 3);
                    if (this.stateTime <= 0) {
                        this.changeState(this.waitTriggerState);
                        this.waitTriggerState = null;
                    }
                }

                if (this.castTime > 0) {
                    this.castTime -= dt;
                    this.castTime = MathUtils.round(this.castTime, 3);
                    if (this.castTime <= 0) {
                        this.castCb();
                        this.castCb = null;
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 重置状态
     */
    public resetState(): void {
        this.changeState(CombatMemberState.Init);
        this.changeState(CombatMemberState.Wait);
    }

    /**
     * 设置特效
     */
    public setEffect(): void {
        this.changeState(CombatMemberState.Init);

        this.spineWing.node.active = this.data.showId !== -1;
        if (this.data.showId === -1) {
            this.spineWing.node.skeleton(null);
            // @ts-ignore
            const attachUtil = this.spineWing.attachUtil;
            attachUtil.destroyAllAttachedNodes();
        } else {
            SpineUtils.setWingWithoutAniName(this.spineWing, this.data.showInfo.res, () => {
                this.spineData = ArcherClientConfig.getInstance().getSpineData(`efWing${this.data.showInfo.res}`);

                // @ts-ignore
                const attachUtil = this.spineWing.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                this.changeState(CombatMemberState.Wait);
            });
        }
    }

    /**
     * 设置游戏速度
     * @param speed 速度
     */
    public setGameSpeed(speed: number): void {
        this.gameSpeed = speed;

        this.spineWing.timeScale = this.gameSpeed;
    }

    /**
     * 播放施法动画
     * @param memberSkillData 成员技能数据
     * @param castCb 施法回调
     * @param isSkipingCombat 是否正在跳过战斗
     */
    public playCastAni(memberSkillData: ICombatMemberSkillData, castCb: () => void, isSkipingCombat: boolean): void {
        this.isSkipingCombat = isSkipingCombat;

        let aniName = "";
        switch (memberSkillData.info.type) {
            case EnumSkillType.WingASkill:
                aniName = AniName.Cast;
                break;
            default:
                break;
        }
        if (!this.isSkipingCombat) {
            this.spineWing.setAnimation(0, aniName, false);
            this.spineWing.addAnimation(0, AniName.Wait, true);
        }

        this.waitTriggerState = this.data.state;
        this.stateTime = this.spineData[aniName].duration;

        this.castCb = castCb;
        this.castTime = this.spineData[aniName].event[ANI_EVENT_NAME][0];

        this.changeState(CombatMemberState.Cast);
    }
}
