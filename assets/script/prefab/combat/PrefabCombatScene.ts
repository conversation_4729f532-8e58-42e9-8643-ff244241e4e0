/*
 * @Author: chenx
 * @Date: 2024-11-01 14:38:59
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:26:58
 */
import Loader from "../../../nsn/core/Loader";
import Pool from "../../../nsn/core/Pool";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import Platform from "../../../nsn/platform/Platform";
import UI from "../../../nsn/ui/UI";
import { UIType } from "../../../nsn/ui/UIType";
import { AUDIO_EFFECT_PATH } from "../../config/AudioEffectConfig";
import { EnumAttributeType } from "../../data/base/BaseAttribute";
import { EnumSkillShowFlight, EnumSkillShowPreform, EnumSkillShowTargetType } from "../../data/base/BaseSkillShow";
import { DungeonType } from "../../game/Combat";
import { CombatDungeon } from "../../game/combat/CombatDungeon";
import { CombatMemberState } from "../../game/combat/CombatMember";
import CombatMemberMonster from "../../game/combat/CombatMemberMonster";
import CombatMemberPlayer, { CombatPlayerType } from "../../game/combat/CombatMemberPlayer";
import { ICombatMemberSkillData } from "../../game/combat/CombatSkill";
import {
    CombatShowItemState,
    ICombatShowData,
    ICombatShowItemData,
    ICombatSkillData,
    SkillEffectState,
    SkillShowState,
} from "../../game/Skill";
import AudioUtils from "../../utils/AudioUtils";
import PrefabCombatArcherItem from "./PrefabCombatArcherItem";
import PrefabCombatDamageItem, { CombatDamageState, ICombatDamageData } from "./PrefabCombatDamageItem";
import PrefabCombatDialog from "./PrefabCombatDialog";
import PrefabCombatLeadItem from "./PrefabCombatLeadItem";
import PrefabCombatMonsterItem from "./PrefabCombatMonsterItem";
import PrefabCombatPetItem from "./PrefabCombatPetItem";
import { PrefabCombatShowItem } from "./PrefabCombatShowItem";
import PrefabCombatSkillItem1 from "./PrefabCombatSkillItem1";
import PrefabCombatSkillItem2 from "./PrefabCombatSkillItem2";
import PrefabCombatTankItem from "./PrefabCombatTankItem";
import PrefabCombatWeaponItem from "./PrefabCombatWeaponItem";
import PrefabCombatWingItem from "./PrefabCombatWingItem";

/**
 * 怪兽区域格数
 */
export const MONSTER_AREA_GRID_NUM = [4, 4];

/**
 * 怪兽阵型数量
 */
export const MONSTER_FORMATION_NUM = 15;

/**
 * 步行怪兽阵型
 */
export const WALK_MONSTER_FORMATION = [1, 2, 3, 4, 5, 6, 7, 8];

/**
 * 飞行怪兽阵型
 */
export const FLY_MONSTER_FORMATION = [12, 13, 14, 15];

/**
 * 最大伤害数量
 */
const MAX_DAMAGE_NUM = 20;

const { ccclass, property } = cc._decorator;

/**
 * 战斗-场景
 */
@ccclass
export default class PrefabCombatScene extends I18nComponent {
    @property(cc.Node)
    nodeTank: cc.Node = null; // 战车
    @property(cc.Node)
    nodeMonster: cc.Node = null; // 怪兽-自己
    @property(cc.Node)
    nodeMonster2: cc.Node = null; // 怪兽-对手
    @property(cc.Node)
    nodeShowTop: cc.Node = null; // 表现-上层-图片-弓箭
    @property(cc.Node)
    nodeShowTop2: cc.Node = null; // 表现-上层-图片-其他
    @property(cc.Node)
    nodeShowTop3: cc.Node = null; // 表现-上层-特效
    @property(cc.Node)
    nodeShowBottom: cc.Node = null; // 表现-下层
    @property(cc.Node)
    nodeDamage: cc.Node = null; // 伤害

    @property(cc.Node)
    nodeDarkScreenAni: cc.Node = null; // 暗屏动画

    @property(cc.Prefab)
    prefabLeadItem: cc.Prefab = null; // 主角item
    @property(cc.Prefab)
    prefabWeaponItem: cc.Prefab = null; // 武器item
    @property(cc.Prefab)
    prefabWingItem: cc.Prefab = null; // 背饰item
    @property(cc.Prefab)
    prefabTankItem: cc.Prefab = null; // 战车item
    @property(cc.Prefab)
    prefabArcherItem: cc.Prefab = null; // 弓箭手item
    @property(cc.Prefab)
    prefabPetItem: cc.Prefab = null; // 宠物item
    @property(cc.Prefab)
    prefabSkillItem: cc.Prefab = null; // 技能item
    @property(cc.Prefab)
    prefabSkillItem2: cc.Prefab = null; // 技能item

    private dungeonCtl: CombatDungeon = null; // 副本ctl
    private dialogCtl: PrefabCombatDialog = null; // 对话ctl

    private damageData: ICombatDamageData[] = []; // 伤害数据

    private nodeLeadItem: { [uuid: number]: cc.Node } = {}; // 主角item
    private nodeWeaponItem: { [uuid: number]: cc.Node } = {}; // 武器item
    private nodeWingItem: { [uuid: number]: cc.Node } = {}; // 背饰item
    private nodeTankItem: { [uuid: number]: cc.Node } = {}; // 战车item
    private nodeArcherItem: { [uuid: string]: cc.Node } = {}; // 弓箭手item
    private nodePetItem: { [uuid: number]: cc.Node } = {}; // 宠物item
    private nodeMonsterItem: { [uuid: number]: cc.Node } = {}; // 怪物item
    private nodeShowItem: { [uuid: number]: cc.Node } = {}; // 表现item
    private nodeDamageItem: { [uuid: number]: cc.Node } = {}; // 伤害item
    private nodeSkillItem: { [uuid: string]: cc.Node } = {}; // 技能item

    private skillIdByDarkScreenAni: number = -1; // 技能id-暗屏动画
    private isPlayingByDarkScreenAni: boolean = false; // 是否正在播放-暗屏动画

    private poolDamageItem: cc.Node[] = []; // 伤害item

    /**
     * 设置副本ctl
     * @param dungeonCtl 副本ctl
     */
    public setDungeonCtl(dungeonCtl: CombatDungeon): void {
        this.dungeonCtl = dungeonCtl;
    }

    /**
     * 设置对话ctl
     * @param dialogCtl 对话ctl
     */
    public setDialogCtl(dialogCtl: PrefabCombatDialog): void {
        this.dialogCtl = dialogCtl;
    }

    /**
     * 初始化玩家信息
     * @param player 玩家
     */
    public initPlayerInfo(player: CombatMemberPlayer): void {
        const baseData = this.dungeonCtl.getBaseData();
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        // 战车
        const nodeTankItem = Loader.getInstance().instantiate(this.prefabTankItem);
        this.nodeTank.addChild(nodeTankItem);
        this.nodeTankItem[playerBaseData.uuid] = nodeTankItem;
        switch (playerData.type) {
            case CombatPlayerType.Self:
                nodeTankItem.scaleX = 1;
                break;
            case CombatPlayerType.Opponent:
                nodeTankItem.scaleX = -1;
                break;
            default:
                break;
        }

        const compTankItem = nodeTankItem.getComponent(PrefabCombatTankItem);
        compTankItem.initData(playerData.tankData);
        compTankItem.changeState(CombatMemberState.Init);
        compTankItem.setGameSpeed(baseData.gameSpeed);

        // 主角
        const nodeLeadItem = Loader.getInstance().instantiate(this.prefabLeadItem);
        nodeTankItem.child("nodeLead").addChild(nodeLeadItem);
        this.nodeLeadItem[playerBaseData.uuid] = nodeLeadItem;

        const compLeadItem = nodeLeadItem.getComponent(PrefabCombatLeadItem);
        compLeadItem.initData(playerData.leadData, playerData.weaponData);
        compLeadItem.changeState(CombatMemberState.Init);
        compLeadItem.setGameSpeed(baseData.gameSpeed);

        // 武器
        const nodeWeaponItem = Loader.getInstance().instantiate(this.prefabWeaponItem);
        nodeLeadItem.child("nodeWeapon").addChild(nodeWeaponItem);
        this.nodeWeaponItem[playerBaseData.uuid] = nodeWeaponItem;

        const compWeaponItem = nodeWeaponItem.getComponent(PrefabCombatWeaponItem);
        compWeaponItem.initData(playerData.weaponData);
        compWeaponItem.changeState(CombatMemberState.Init);
        compWeaponItem.setGameSpeed(baseData.gameSpeed);

        // 背饰
        const nodeWingItem = Loader.getInstance().instantiate(this.prefabWingItem);
        nodeLeadItem.child("nodeWing").addChild(nodeWingItem);
        this.nodeWingItem[playerBaseData.uuid] = nodeWingItem;

        const compWingItem = nodeWingItem.getComponent(PrefabCombatWingItem);
        compWingItem.initData(playerData.wingData);
        compWingItem.changeState(CombatMemberState.Init);
        compWingItem.setGameSpeed(baseData.gameSpeed);

        // 弓箭手
        playerData.archerData.forEach((e) => {
            const nodeArcherItem = Loader.getInstance().instantiate(this.prefabArcherItem);
            nodeTankItem.child(`nodeArcher${e.gridId}`).addChild(nodeArcherItem);
            this.nodeArcherItem[`${playerBaseData.uuid}#${e.gridId}`] = nodeArcherItem;

            const compArcherItem = nodeArcherItem.getComponent(PrefabCombatArcherItem);
            compArcherItem.initData(e);
            compArcherItem.changeState(CombatMemberState.Init);
            compArcherItem.setGameSpeed(
                baseData.gameSpeed,
                playerBaseData.attr[EnumAttributeType.ArcherAtkHaste].value
            );
        });

        // 宠物
        const nodePetItem = Loader.getInstance().instantiate(this.prefabPetItem);
        nodeTankItem.child("nodePet").addChild(nodePetItem);
        this.nodePetItem[playerBaseData.uuid] = nodePetItem;

        const compPetItem = nodePetItem.getComponent(PrefabCombatPetItem);
        compPetItem.initData(playerData.petData);
        compPetItem.changeState(CombatMemberState.Init);
        compPetItem.setGameSpeed(baseData.gameSpeed);
    }

    /**
     * 更新玩家信息
     * @param player 玩家
     */
    public updatePlayerInfo(player: CombatMemberPlayer): void {
        this.updateTankInfo(player, true);
        this.updateLeadInfo(player);
        this.updateWeaponInfo(player);
        this.updateWingInfo(player);
        this.updateArcherInfo(player);
        this.updatePetInfo(player);
    }

    /**
     * 更新主角信息
     * @param player 玩家
     */
    public updateLeadInfo(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();

        const nodeLeadItem = this.nodeLeadItem[playerBaseData.uuid];
        const compLeadItem = nodeLeadItem.getComponent(PrefabCombatLeadItem);
        compLeadItem.setEffect(playerBaseData.dungeonType);
    }

    /**
     * 更新神器信息
     * @param player 玩家
     */
    public updateWeaponInfo(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();

        const nodeWeaponItem = this.nodeWeaponItem[playerBaseData.uuid];
        const compWeaponItem = nodeWeaponItem.getComponent(PrefabCombatWeaponItem);
        compWeaponItem.setEffect();
    }

    /**
     * 更新背饰信息
     * @param player 玩家
     */
    public updateWingInfo(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();

        const nodeWingItem = this.nodeWingItem[playerBaseData.uuid];
        const compWingItem = nodeWingItem.getComponent(PrefabCombatWingItem);
        compWingItem.setEffect();
    }

    /**
     * 更新战车信息
     * @param player 玩家
     * @param isPlayEnterAni 是否播放入场动画
     */
    public updateTankInfo(player: CombatMemberPlayer, isPlayEnterAni: boolean = false): void {
        const playerBaseData = player.getBaseData();

        const nodeTankItem = this.nodeTankItem[playerBaseData.uuid];
        const compTankItem = nodeTankItem.getComponent(PrefabCombatTankItem);
        compTankItem.setEffect(playerBaseData.dungeonType, isPlayEnterAni);
    }

    /**
     * 更新弓箭手信息
     * @param player 玩家
     */
    public updateArcherInfo(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        playerData.archerData.forEach((e) => {
            const nodeArcherItem = this.nodeArcherItem[`${playerBaseData.uuid}#${e.gridId}`];
            const compArcherItem = nodeArcherItem.getComponent(PrefabCombatArcherItem);
            compArcherItem.setEffect();
        });
    }

    /**
     * 更新宠物信息
     * @param player 玩家
     */
    public updatePetInfo(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();

        const nodePetItem = this.nodePetItem[playerBaseData.uuid];
        const compPetItem = nodePetItem.getComponent(PrefabCombatPetItem);
        compPetItem.setEffect();
    }

    /**
     * 重置玩家信息
     * @param player 玩家
     */
    public resetPlayerInfo(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        const compLeadItem = this.getLeadItem(playerBaseData.uuid);
        compLeadItem.resetState();
        const compWeaponItem = this.getWeaponItem(playerBaseData.uuid);
        compWeaponItem.resetState();
        const compWingItem = this.getWingItem(playerBaseData.uuid);
        compWingItem.resetState();
        const compTankItem = this.getTankItem(playerBaseData.uuid);
        compTankItem.resetState();
        playerData.archerData.forEach((e) => {
            const compArcherItem = this.getArcherItem(`${playerBaseData.uuid}#${e.gridId}`);
            compArcherItem.resetState();
        });
        const compPetItem = this.getPetItem(playerBaseData.uuid);
        compPetItem.resetState();
    }

    /**
     * 更新游戏速度-玩家
     * @param player 玩家
     */
    public updateGameSpeedByPlayer(player: CombatMemberPlayer): void {
        const baseData = this.dungeonCtl.getBaseData();
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        const compLeadItem = this.getLeadItem(playerBaseData.uuid);
        compLeadItem.setGameSpeed(baseData.gameSpeed);
        const compWeaponItem = this.getWeaponItem(playerBaseData.uuid);
        compWeaponItem.setGameSpeed(baseData.gameSpeed);
        const compWingItem = this.getWingItem(playerBaseData.uuid);
        compWingItem.setGameSpeed(baseData.gameSpeed);
        const compTankItem = this.getTankItem(playerBaseData.uuid);
        compTankItem.setGameSpeed(baseData.gameSpeed);
        playerData.archerData.forEach((e) => {
            const compArcherItem = this.getArcherItem(`${playerBaseData.uuid}#${e.gridId}`);
            compArcherItem.setGameSpeed(
                baseData.gameSpeed,
                playerBaseData.attr[EnumAttributeType.ArcherAtkHaste].value
            );
        });
        const compPetItem = this.getPetItem(playerBaseData.uuid);
        compPetItem.setGameSpeed(baseData.gameSpeed);
        const showData: ICombatShowData[] = [];
        playerBaseData.skillData.forEach((e) => {
            showData.push(
                ...e.showData.filter((e2) => [SkillShowState.Show, SkillShowState.Finish].includes(e2.state))
            );
            const effectData = e.effectData.filter((e2) =>
                [SkillEffectState.Show, SkillEffectState.Effect, SkillEffectState.Finish].includes(e2.state)
            );
            effectData.forEach((e2) => {
                showData.push(
                    ...e2.showData.filter((e3) => [SkillShowState.Show, SkillShowState.Finish].includes(e3.state))
                );
            });
        });
        playerBaseData.buffData.forEach((e) => {
            showData.push(
                ...e.showData.filter((e2) => [SkillShowState.Show, SkillShowState.Finish].includes(e2.state))
            );
        });
        showData.forEach((e) => {
            e.itemData.forEach((e2) => {
                switch (e2.state) {
                    case CombatShowItemState.Init:
                    case CombatShowItemState.Show:
                    case CombatShowItemState.Finish:
                    case CombatShowItemState.WaitClear:
                        const compShowItem = this.getShowItem(e2.uuid);
                        compShowItem && compShowItem.setGameSpeed(baseData.gameSpeed);
                        break;
                    default:
                        break;
                }
            });
        });
    }

    /**
     * 更新游戏速度-弓箭手
     * @param player 玩家
     */
    public updateGameSpeedByArcher(player: CombatMemberPlayer): void {
        const baseData = this.dungeonCtl.getBaseData();
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        playerData.archerData.forEach((e) => {
            const compArcherItem = this.getArcherItem(`${playerBaseData.uuid}#${e.gridId}`);
            compArcherItem.setGameSpeed(
                baseData.gameSpeed,
                playerBaseData.attr[EnumAttributeType.ArcherAtkHaste].value
            );
        });
    }

    /**
     * 更新游戏速度-怪兽
     * @param monster 怪兽
     */
    public updateGameSpeedByMonster(monster: CombatMemberMonster): void {
        const baseData = this.dungeonCtl.getBaseData();
        const monsterBaseData = monster.getBaseData();

        const compMonsterItem = this.getMonsterItem(monsterBaseData.uuid);
        compMonsterItem.setGameSpeed(baseData.gameSpeed);
        const showData: ICombatShowData[] = [];
        monsterBaseData.skillData.forEach((e) => {
            showData.push(
                ...e.showData.filter((e2) => [SkillShowState.Show, SkillShowState.Finish].includes(e2.state))
            );
            const effectData = e.effectData.filter((e2) =>
                [SkillEffectState.Show, SkillEffectState.Effect, SkillEffectState.Finish].includes(e2.state)
            );
            effectData.forEach((e2) => {
                showData.push(
                    ...e2.showData.filter((e3) => [SkillShowState.Show, SkillShowState.Finish].includes(e3.state))
                );
            });
        });
        monsterBaseData.buffData.forEach((e) => {
            showData.push(
                ...e.showData.filter((e2) => [SkillShowState.Show, SkillShowState.Finish].includes(e2.state))
            );
        });
        showData.forEach((e) => {
            e.itemData.forEach((e2) => {
                switch (e2.state) {
                    case CombatShowItemState.Init:
                    case CombatShowItemState.Show:
                    case CombatShowItemState.Finish:
                    case CombatShowItemState.WaitClear:
                        const compShowItem = this.getShowItem(e2.uuid);
                        compShowItem && compShowItem.setGameSpeed(baseData.gameSpeed);
                        break;
                    default:
                        break;
                }
            });
        });
    }

    /**
     * 获取主角item
     * @param playerUuid 玩家uuid
     * @returns
     */
    public getLeadItem(playerUuid: number): PrefabCombatLeadItem {
        return this.nodeLeadItem[playerUuid].getComponent(PrefabCombatLeadItem);
    }

    /**
     * 获取武器item
     * @param playerUuid 玩家uuid
     * @returns
     */
    public getWeaponItem(playerUuid: number): PrefabCombatWeaponItem {
        return this.nodeWeaponItem[playerUuid].getComponent(PrefabCombatWeaponItem);
    }

    /**
     * 获取背饰item
     * @param playerUuid 玩家uuid
     * @returns
     */
    public getWingItem(playerUuid: number): PrefabCombatWingItem {
        return this.nodeWingItem[playerUuid].getComponent(PrefabCombatWingItem);
    }

    /**
     * 获取战车item
     * @param playerUuid 玩家uuid
     * @returns
     */
    public getTankItem(playerUuid: number): PrefabCombatTankItem {
        return this.nodeTankItem[playerUuid].getComponent(PrefabCombatTankItem);
    }

    /**
     * 获取弓箭手item
     * @param playerUuid 玩家uuid
     * @returns
     */
    public getArcherItem(playerUuid: string): PrefabCombatArcherItem {
        return this.nodeArcherItem[playerUuid].getComponent(PrefabCombatArcherItem);
    }

    /**
     * 获取宠物item
     * @param playerUuid 玩家uuid
     * @returns
     */
    public getPetItem(playerUuid: number): PrefabCombatPetItem {
        return this.nodePetItem[playerUuid].getComponent(PrefabCombatPetItem);
    }

    /**
     * 初始化怪兽item
     * @param playerType 玩家类型
     * @param monster 怪兽
     */
    public initMonsterItem(playerType: CombatPlayerType, monster: CombatMemberMonster): void {
        const baseData = this.dungeonCtl.getBaseData();
        const monsterBaseData = monster.getBaseData();

        const loadCb: (nodeItem: cc.Node) => void = (nodeItem) => {
            this.nodeMonsterItem[monsterBaseData.uuid] = nodeItem;

            const compItem = nodeItem.getComponent(PrefabCombatMonsterItem);
            compItem.initData(monster);
            compItem.changeState(CombatMemberState.Init);
            compItem.setGameSpeed(baseData.gameSpeed);

            this.dungeonCtl.setMonster(monster);
        };

        if (Pool.getInstance().size("PrefabCombatMonsterItem") > 0) {
            const nodeItem = Pool.getInstance().get("PrefabCombatMonsterItem");
            const nodeMonster = this.getMonster(playerType);
            nodeMonster.addChild(nodeItem);
            loadCb(nodeItem);
        } else {
            Loader.getInstance().loadPrefab("prefab/combat/PrefabCombatMonsterItem", (prefab) => {
                if (cc.isValid(this.node)) {
                    const nodeItem = Loader.getInstance().instantiate(prefab);
                    const nodeMonster = this.getMonster(playerType);
                    nodeMonster.addChild(nodeItem);
                    loadCb(nodeItem);
                }
            });
        }
    }

    /**
     * 获取怪兽item
     * @param monsterUuid 怪兽uuid
     * @returns
     */
    public getMonsterItem(monsterUuid: number): PrefabCombatMonsterItem {
        return this.nodeMonsterItem[monsterUuid].getComponent(PrefabCombatMonsterItem);
    }

    /**
     * 清理怪兽item
     * @param monsterUuid 怪兽uuid
     */
    public clearMonsterItem(monsterUuid: number): void {
        Pool.getInstance().put(this.nodeMonsterItem[monsterUuid]);

        this.nodeMonsterItem[monsterUuid] = null;
    }

    /**
     * 播放表现动画
     * @param showItemData 表现item数据
     * @param showData 表现数据
     * @param skillData 技能数据
     */
    public playShowAni(
        showItemData: ICombatShowItemData,
        showData: ICombatShowData,
        skillData: ICombatSkillData
    ): void {
        const baseData = this.dungeonCtl.getBaseData();
        const releaseMemberBaseData = skillData.releaseMember.getBaseData();

        const loadCb: (nodeItem: cc.Node) => void = (nodeItem) => {
            switch (showItemData.targetType) {
                case EnumSkillShowTargetType.TargetType4:
                    this.nodeShowTop.addChild(nodeItem);
                    break;
                case EnumSkillShowTargetType.TargetType1:
                    this.nodeShowTop2.addChild(nodeItem);
                    break;
                case EnumSkillShowTargetType.TargetType2:
                case EnumSkillShowTargetType.TargetType13:
                case EnumSkillShowTargetType.TargetType14:
                    this.nodeShowTop3.addChild(nodeItem);
                    break;
                case EnumSkillShowTargetType.TargetType3:
                    this.nodeShowBottom.addChild(nodeItem);
                    break;
                case EnumSkillShowTargetType.TargetType8:
                    {
                        const [attachedName] = showItemData.info.targetPara;

                        const compTankItem = this.getTankItem(releaseMemberBaseData.uuid);
                        const nodeAttached = compTankItem.getAttached(attachedName);
                        if (nodeAttached) {
                            nodeAttached.addChild(nodeItem);
                        } else {
                            compTankItem.node.addChild(nodeItem);
                        }
                    }
                    break;
                case EnumSkillShowTargetType.TargetType9:
                    {
                        const [attachedName] = showItemData.info.targetPara;

                        const compLeadItem = this.getLeadItem(releaseMemberBaseData.uuid);
                        const nodeAttached = compLeadItem.getAttached(attachedName);
                        if (nodeAttached) {
                            nodeAttached.addChild(nodeItem);
                        } else {
                            compLeadItem.node.addChild(nodeItem);
                        }
                    }
                    break;
                default:
                    break;
            }
            this.nodeShowItem[showItemData.uuid] = nodeItem;

            switch (showItemData.info.flight) {
                case EnumSkillShowFlight.Not:
                    switch (showItemData.info.targetType) {
                        case EnumSkillShowTargetType.TargetType2:
                        case EnumSkillShowTargetType.TargetType3:
                        case EnumSkillShowTargetType.TargetType13:
                            showItemData.initPos = nodeItem.parent.convertToNodeSpaceAR(showItemData.initPos);
                            showItemData.targetPos = nodeItem.parent.convertToNodeSpaceAR(showItemData.targetPos);
                            break;
                        case EnumSkillShowTargetType.TargetType8:
                        case EnumSkillShowTargetType.TargetType9:
                        case EnumSkillShowTargetType.TargetType14:
                            break;
                        default:
                            break;
                    }
                    break;
                case EnumSkillShowFlight.NotFlight:
                case EnumSkillShowFlight.NotFlight2:
                case EnumSkillShowFlight.Straight:
                case EnumSkillShowFlight.Parabolic:
                    showItemData.initPos = nodeItem.parent.convertToNodeSpaceAR(showItemData.initPos);
                    showItemData.targetPos = nodeItem.parent.convertToNodeSpaceAR(showItemData.targetPos);
                    break;
                default:
                    break;
            }

            const compItem = nodeItem.getComponent(PrefabCombatShowItem);
            compItem.initData(showItemData, showData, skillData);
            compItem.changeState(CombatShowItemState.Init);
            compItem.setGameSpeed(baseData.gameSpeed);

            switch (showItemData.info.flight) {
                case EnumSkillShowFlight.Not:
                case EnumSkillShowFlight.NotFlight:
                case EnumSkillShowFlight.NotFlight2:
                    this.playShowAudio(
                        showItemData,
                        releaseMemberBaseData.skill.find((e) => e.getData().id === skillData.id).getData()
                    );
                    break;
                default:
                    break;
            }

            showData.itemData.push(showItemData);
        };

        switch (showItemData.prefabType) {
            case EnumSkillShowPreform.Picture1:
                if (Pool.getInstance().size("PrefabCombatShowItem1") > 0) {
                    const nodeItem = Pool.getInstance().get("PrefabCombatShowItem1");
                    loadCb(nodeItem);
                } else {
                    Loader.getInstance().loadPrefab("prefab/combat/PrefabCombatShowItem1", (prefab) => {
                        if (cc.isValid(this.node)) {
                            const nodeItem = Loader.getInstance().instantiate(prefab);
                            loadCb(nodeItem);
                        }
                    });
                }
                break;
            case EnumSkillShowPreform.Spine:
                if (Pool.getInstance().size("PrefabCombatShowItem2") > 0) {
                    const nodeItem = Pool.getInstance().get("PrefabCombatShowItem2");
                    loadCb(nodeItem);
                } else {
                    Loader.getInstance().loadPrefab("prefab/combat/PrefabCombatShowItem2", (prefab) => {
                        if (cc.isValid(this.node)) {
                            const nodeItem = Loader.getInstance().instantiate(prefab);
                            loadCb(nodeItem);
                        }
                    });
                }
                break;
            default:
                break;
        }
    }

    /**
     * 清理表现item
     * @param showUuid 表现uuid
     */
    public clearShowItem(showUuid: number): void {
        const nodeItem = this.nodeShowItem[showUuid];
        if (!nodeItem) {
            return;
        }

        cc.isValid(nodeItem) && Pool.getInstance().put(nodeItem);

        this.nodeShowItem[showUuid] = null;
    }

    /**
     * 获取表现item
     * @param showUuid 表现uuid
     * @returns
     */
    public getShowItem(showUuid: number): PrefabCombatShowItem {
        const nodeItem = this.nodeShowItem[showUuid];
        if (cc.isValid(nodeItem)) {
            return nodeItem.getComponent(PrefabCombatShowItem);
        }
    }

    /**
     * 怪兽行为-层级
     */
    public actionMonsterByZIndex(): void {
        const nodeItem: cc.Node[] = [];
        this.nodeMonster.children.forEach((e) => nodeItem.push(e));
        nodeItem.sort((a, b) => b.y - a.y);
        nodeItem.forEach((e, i) => {
            e.zIndex = i;
        });

        const nodeItem2: cc.Node[] = [];
        this.nodeMonster2.children.forEach((e) => nodeItem2.push(e));
        nodeItem2.sort((a, b) => b.y - a.y);
        nodeItem2.forEach((e, i) => {
            e.zIndex = i;
        });
    }

    /**
     * 获取怪兽区域
     * @param type 玩家类型
     * @returns
     */
    public getMonster(type: CombatPlayerType): cc.Node {
        return type === CombatPlayerType.Opponent ? this.nodeMonster2 : this.nodeMonster;
    }

    /**
     * 获取怪兽区域尺寸
     * @param type 玩家类型
     * @returns
     */
    public getMonsterSize(type: CombatPlayerType): { width: number; height: number } {
        const nodeMonster = this.getMonster(type);
        const scaleX = this.getSkillEffectScaleX(type);
        return {
            width: nodeMonster.width * scaleX,
            height: nodeMonster.height,
        };
    }

    /**
     * 获取世界pos-怪兽区域
     * @param type 玩家类型
     * @param pos 结点pos
     * @returns
     */
    public getWorldPosByMonsterArea(type: CombatPlayerType, pos: cc.Vec2): cc.Vec2 {
        return this.getMonster(type).convertToWorldSpaceAR(pos);
    }

    /**
     * 获取结点pos-怪兽区域
     * @param type 玩家类型
     * @param pos 世界pos
     * @returns
     */
    public getNodePosByMonsterArea(type: CombatPlayerType, pos: cc.Vec2): cc.Vec2 {
        return this.getMonster(type).convertToNodeSpaceAR(pos);
    }

    /**
     * 获取技能特效的scaleX
     * @param type 玩家类型
     */
    public getSkillEffectScaleX(type: CombatPlayerType): number {
        return type === CombatPlayerType.Opponent ? -1 : 1;
    }

    /**
     * 伤害行为
     */
    public actionDamage(): void {
        for (let i = this.damageData.length - 1; i >= 0; i--) {
            const damageData = this.damageData[i];

            if (damageData.state !== CombatDamageState.Put) {
                continue;
            }

            this.clearDamageItem(damageData.uuid);
            this.damageData.splice(i, 1);
        }
    }

    /**
     * 播放伤害动画
     * @param damageData 伤害数据
     * @param beAttackedPoint 被攻击点
     */
    public playDamageAni(damageData: ICombatDamageData, beAttackedPoint: cc.Vec2): void {
        const baseData = this.dungeonCtl.getBaseData();
        if (baseData.type === DungeonType.Main && UI.getInstance().checkHaveUIType(UIType.UI)) {
            return;
        }
        if (Platform.getInstance().isMinigame() && this.damageData.length >= MAX_DAMAGE_NUM) {
            return;
        }

        damageData.uuid = baseData.damageUuid++;
        damageData.initPos = this.nodeDamage.convertToNodeSpaceAR(cc.v2(beAttackedPoint.x, beAttackedPoint.y + 50));

        const loadCb: (nodeItem: cc.Node) => void = (nodeItem) => {
            this.nodeDamageItem[damageData.uuid] = nodeItem;

            const compItem = nodeItem.getComponent(PrefabCombatDamageItem);
            compItem.initData(damageData);
            compItem.changeState(CombatDamageState.PlayAni);

            this.damageData.push(damageData);
        };

        if (this.poolDamageItem.length > 0) {
            const nodeItem = this.poolDamageItem.shift();
            loadCb(nodeItem);
        } else if (Pool.getInstance().size("PrefabCombatDamageItem") > 0) {
            const nodeItem = Pool.getInstance().get("PrefabCombatDamageItem");
            this.nodeDamage.addChild(nodeItem);
            loadCb(nodeItem);
        } else {
            Loader.getInstance().loadPrefab("prefab/combat/PrefabCombatDamageItem", (prefab) => {
                if (cc.isValid(this.node)) {
                    const nodeItem = Loader.getInstance().instantiate(prefab);
                    this.nodeDamage.addChild(nodeItem);
                    loadCb(nodeItem);
                }
            });
        }
    }

    /**
     * 清理全部伤害item
     */
    public clearAllDamageItem(): void {
        this.poolDamageItem.forEach((e) => {
            Pool.getInstance().put(e);
        });
        this.poolDamageItem = [];
    }

    /**
     * 清理伤害item
     * @param damageUuid 伤害uuid
     */
    private clearDamageItem(damageUuid: number): void {
        const nodeItem = this.nodeDamageItem[damageUuid];
        const compItem = nodeItem.getComponent(PrefabCombatDamageItem);
        compItem.unuse();

        this.poolDamageItem.push(nodeItem);

        this.nodeDamageItem[damageUuid] = null;
    }

    /**
     * 初始化技能item
     * @param skillUuid 技能uuid
     * @param nodeSkill 技能
     * @returns
     */
    public initSkillItem(skillUuid: string, nodeSkill: cc.Node): cc.Node {
        const nodeItem = Loader.getInstance().instantiate(this.prefabSkillItem);
        nodeSkill.addChild(nodeItem);
        this.nodeSkillItem[skillUuid] = nodeItem;

        return nodeItem;
    }

    /**
     * 初始化技能item
     * @param skillUuid 技能uuid
     * @param nodeSkill 技能
     * @returns
     */
    public initSkillItem2(skillUuid: string, nodeSkill: cc.Node): cc.Node {
        const nodeItem = Loader.getInstance().instantiate(this.prefabSkillItem2);
        nodeSkill.addChild(nodeItem);
        this.nodeSkillItem[skillUuid] = nodeItem;

        return nodeItem;
    }

    /**
     * 清理技能item
     * @param skillUuid 技能uuid
     */
    public clearSkillItem(skillUuid: string): void {
        this.nodeSkillItem[skillUuid].destroy();
        this.nodeSkillItem[skillUuid] = null;
    }

    /**
     * 获取技能item
     * @param skillUuid 技能uuid
     * @returns
     */
    public getSkillItem(skillUuid: string): PrefabCombatSkillItem1 | PrefabCombatSkillItem2 {
        return (
            this.nodeSkillItem[skillUuid] &&
            (this.nodeSkillItem[skillUuid].getComponent(PrefabCombatSkillItem1) ||
                this.nodeSkillItem[skillUuid].getComponent(PrefabCombatSkillItem2))
        );
    }

    /**
     * 播放暗屏动画
     * @param skillId 技能id
     */
    public playDarkScreenAni(skillId: number): void {
        if (this.isPlayingByDarkScreenAni) {
            return;
        }

        this.skillIdByDarkScreenAni = skillId;
        this.isPlayingByDarkScreenAni = true;

        this.nodeDarkScreenAni.active = true;
        this.nodeDarkScreenAni.opacity = 130;
    }

    /**
     * 停止暗屏动画
     * @param skillId 技能id
     */
    public stopDarkScreenAni(skillId: number): void {
        if (!this.isPlayingByDarkScreenAni) {
            return;
        }
        if (this.skillIdByDarkScreenAni !== skillId) {
            return;
        }

        cc.tween(this.nodeDarkScreenAni)
            .to(0.1, { opacity: 0 })
            .call(() => {
                this.skillIdByDarkScreenAni = -1;
                this.isPlayingByDarkScreenAni = false;

                this.nodeDarkScreenAni.active = false;
            })
            .start();
    }

    /**
     * 播放表现音效
     * @param showItemData 表现item数据
     * @param skillData 技能数据
     */
    public playShowAudio(showItemData: ICombatShowItemData, skillData: ICombatMemberSkillData = null): void {
        if (showItemData.audioRes === "") {
            return;
        }

        switch (showItemData.info.flight) {
            case EnumSkillShowFlight.Not:
            case EnumSkillShowFlight.NotFlight:
            case EnumSkillShowFlight.NotFlight2:
                AudioUtils.playCombatEffect(
                    `skill${showItemData.audioRes}`,
                    AUDIO_EFFECT_PATH.COMBAT,
                    showItemData.dungeonType,
                    (audioId) => {
                        if (cc.isValid(this.node)) {
                            skillData.audioId.push(audioId);
                        }
                    },
                    (audioId) => {
                        if (cc.isValid(this.node)) {
                            const index = skillData.audioId.findIndex((e) => e === audioId);
                            index !== -1 && skillData.audioId.splice(index, 1);
                        }
                    }
                );
                break;
            case EnumSkillShowFlight.Straight:
            case EnumSkillShowFlight.Parabolic:
                AudioUtils.playCombatEffect(
                    `skill${showItemData.audioRes}`,
                    AUDIO_EFFECT_PATH.COMBAT,
                    showItemData.dungeonType
                );
                break;
            default:
                break;
        }
    }
}
