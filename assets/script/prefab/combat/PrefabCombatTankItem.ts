/*
 * @Author: chenx
 * @Date: 2024-02-27 15:59:10
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 16:09:38
 */
import Audio from "../../../nsn/audio/Audio";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import MathUtils from "../../../nsn/util/MathUtils";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../../config/AudioEffectConfig";
import { EnumSkillType } from "../../data/base/BaseSkill";
import ArcherClientConfig, { ISpineData } from "../../game/ArcherClientConfig";
import Combat, { CombatType, DungeonType } from "../../game/Combat";
import { CombatMemberState } from "../../game/combat/CombatMember";
import { CombatPlayerType, ICombatTankData } from "../../game/combat/CombatMemberPlayer";
import { ICombatMemberSkillData } from "../../game/combat/CombatSkill";
import AudioUtils from "../../utils/AudioUtils";
import SpineUtils from "../../utils/SpineUtils";

/**
 * 战车spine动画名称
 */
enum AniName {
    Wait = "wait", // 待机
    Move = "move", // 移动
    Cast = "skill", // 施法
    CastMove = "skill_move", // 施法-移动
    BeAttacked = "blow", // 被攻击
    BeAttacked2 = "blow2", // 被攻击
}

/**
 * 动画事件名称
 */
const ANI_EVENT_NAME = "trigger";

/**
 * 入场移速
 */
const ENTER_MS = 6;

const { ccclass, property } = cc._decorator;

/**
 * 战斗-战车item
 */
@ccclass
export default class PrefabCombatTankItem extends I18nComponent {
    @property(sp.Skeleton)
    spineTank: sp.Skeleton = null; // 战车
    @property(cc.Node)
    nodeShowBottom: cc.Node = null; // 表现-下层

    private data: ICombatTankData = null; // 战车数据

    private spineData: ISpineData = null; // 骨骼数据
    private waitTriggerState: CombatMemberState = null; // 待触发状态
    private stateTime: number = 0; // 状态时间
    private castCb: () => void = null; // 施法回调
    private castTime: number = 0; // 施法时间
    private beAttackedWorldPos: cc.Vec2 = cc.v2(); // 被攻击世界位置
    private gameSpeed: number = 1; // 游戏速度
    private enterDelay: number = 0; // 入场延迟
    private isSkipingCombat: boolean = false; // 是否正在跳过战斗

    /**
     * 初始化数据
     * @param tankData 站车数据
     */
    public initData(tankData: ICombatTankData): void {
        this.data = tankData;
    }

    /**
     * 设置状态
     * @param state 状态
     */
    public setState(state: CombatMemberState): void {
        this.data.state = state;

        switch (state) {
            case CombatMemberState.Wait:
                this.node.x = (this.node.parent.width / 2) * -this.node.scaleX;

                this.spineTank.setCompleteListener(null);
                this.spineTank.clearTracks();
                this.spineTank.setAnimation(0, AniName.Wait, true);
                break;
            default:
                break;
        }
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatMemberState): void {
        this.data.state = state;

        switch (this.data.state) {
            case CombatMemberState.Init:
                this.waitTriggerState = null;
                this.stateTime = 0;
                this.castCb = null;
                this.castTime = 0;
                this.isSkipingCombat = false;

                this.spineTank.setCompleteListener(null);
                this.spineTank.clearTracks();
                break;
            case CombatMemberState.WaitEnter:
                if (this.enterDelay > 0) {
                    this.node.x = cc.winSize.width * -this.node.scaleX;
                } else {
                    this.node.x = (cc.winSize.width / 5) * 3 * -this.node.scaleX;

                    this.changeState(CombatMemberState.Enter);
                }
                break;
            case CombatMemberState.Enter:
                if (this.spineTank.animation !== AniName.Move) {
                    this.spineTank.setAnimation(0, AniName.Move, true);
                }
                break;
            case CombatMemberState.Wait:
                if (!this.isSkipingCombat) {
                    switch (this.data.dungeonType) {
                        case DungeonType.Thief:
                            if (this.spineTank.animation !== AniName.Move) {
                                this.spineTank.setAnimation(0, AniName.Move, true);
                            }
                            break;
                        default:
                            if (this.spineTank.animation !== AniName.Wait) {
                                this.spineTank.setAnimation(0, AniName.Wait, true);
                            }
                            break;
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 更新状态
     * @param dt
     * @param playerType 玩家类型
     */
    public updateState(dt: number, playerType: CombatPlayerType): void {
        switch (this.data.state) {
            case CombatMemberState.WaitEnter:
                if (this.enterDelay > 0) {
                    this.enterDelay -= dt;
                    if (this.enterDelay <= 0) {
                        this.changeState(CombatMemberState.Enter);
                    }
                }
                break;
            case CombatMemberState.Enter:
                const targetPosX = (this.node.parent.width / 2) * -this.node.scaleX;
                switch (playerType) {
                    case CombatPlayerType.Self:
                        this.node.x = Math.min(
                            this.node.x + MathUtils.floor(ENTER_MS * 60 * dt, 2) * this.node.scaleX,
                            targetPosX
                        );
                        if (this.node.x >= targetPosX) {
                            cc.tween(this.node).by(0.1, { x: 6 }).by(0.1, { x: -6 }).start();

                            this.changeState(CombatMemberState.Wait);
                        }
                        break;
                    case CombatPlayerType.Opponent:
                        this.node.x = Math.max(
                            this.node.x + MathUtils.floor(ENTER_MS * 60 * dt, 2) * this.node.scaleX,
                            targetPosX
                        );
                        if (this.node.x <= targetPosX) {
                            cc.tween(this.node).by(0.1, { x: -6 }).by(0.1, { x: 6 }).start();

                            this.changeState(CombatMemberState.Wait);
                        }
                        break;
                    default:
                        break;
                }
                break;
            case CombatMemberState.Cast:
                if (this.stateTime > 0) {
                    this.stateTime -= dt;
                    this.stateTime = MathUtils.round(this.stateTime, 3);
                    if (this.stateTime <= 0) {
                        this.changeState(this.waitTriggerState);
                        this.waitTriggerState = null;
                    }
                }

                if (this.castTime > 0) {
                    this.castTime -= dt;
                    this.castTime = MathUtils.round(this.castTime, 3);
                    if (this.castTime <= 0) {
                        this.castCb();
                        this.castCb = null;
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 重置状态
     */
    public resetState(): void {
        this.changeState(CombatMemberState.Init);
        this.changeState(CombatMemberState.Wait);
    }

    /**
     * 设置特效
     * @param dungeonType 副本类型
     * @param isPlayEnterAni 是否播放入场动画
     */
    public setEffect(dungeonType: DungeonType, isPlayEnterAni: boolean): void {
        this.changeState(CombatMemberState.Init);

        SpineUtils.setTankWithoutAniName(this.spineTank, this.data.info.res, () => {
            this.spineData = ArcherClientConfig.getInstance().getSpineData(`efTank${this.data.info.res}`);

            const scale = this.spineTank.node.scale;
            this.node.width = Math.floor(this.spineTank.node.width * scale);
            this.node.height = Math.floor(this.spineTank.node.height * scale);

            // @ts-ignore
            const attachUtil = this.spineTank.attachUtil;
            attachUtil.destroyAllAttachedNodes();
            attachUtil.generateAllAttachedNodes();

            if (isPlayEnterAni) {
                this.setWaitEnterState(dungeonType);
            } else {
                this.changeState(CombatMemberState.Wait);
            }
        });

        this.changeState(CombatMemberState.Load);
    }

    /**
     * 获取挂点
     * @param name 名称
     * @returns
     */
    public getAttached(name: string): cc.Node {
        if (name === "bottom") {
            return this.nodeShowBottom;
        }

        // @ts-ignore
        const attachUtil = this.spineTank.attachUtil;
        const nodeAttached: cc.Node[] = attachUtil.getAttachedNodes(name);

        return nodeAttached[0];
    }

    /**
     * 更新特效
     */
    private updateEffect(): void {
        const nodeContent = this.getAttachedNode("buff");
        this.beAttackedWorldPos = nodeContent.convertToWorldSpaceAR(cc.v2());
    }

    /**
     * 被攻击
     * @param isPlayAni 是否播放动画
     * @param isPlayAni2 是否播放动画
     * @param dungeonType 副本类型
     * @param isSkipingCombat 是否正在跳过战斗
     */
    public beAttacked(
        isPlayAni: boolean,
        isPlayAni2: boolean,
        dungeonType: DungeonType,
        isSkipingCombat: boolean
    ): void {
        if (isSkipingCombat) {
            return;
        }
        if (this.data.state !== CombatMemberState.Wait) {
            return;
        }

        const combatType = Combat.getInstance().getCombatType(dungeonType);
        switch (combatType) {
            case CombatType.Main:
            case CombatType.Pve:
                if (isPlayAni) {
                    this.spineTank.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                        if (trackEntry.animation.name === AniName.BeAttacked) {
                            this.spineTank.setCompleteListener(null);

                            this.changeState(CombatMemberState.Wait);
                        }
                    });
                    this.spineTank.setAnimation(0, AniName.BeAttacked, false);
                } else if (isPlayAni2) {
                    this.spineTank.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                        if (trackEntry.animation.name === AniName.BeAttacked2) {
                            this.spineTank.setCompleteListener(null);

                            this.changeState(CombatMemberState.Wait);
                        }
                    });
                    this.spineTank.setAnimation(0, AniName.BeAttacked2, false);
                }

                if (isPlayAni || isPlayAni2) {
                    AudioUtils.playCombatEffect(
                        AUDIO_EFFECT_TYPE.DUNGEON_TANK_BE_ATTACKED,
                        AUDIO_EFFECT_PATH.DUNGEON,
                        dungeonType
                    );

                    this.changeState(CombatMemberState.BeAttacked);
                }
                break;
            case CombatType.Pvp:
                if (isPlayAni2) {
                    this.spineTank.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                        if (trackEntry.animation.name === AniName.BeAttacked) {
                            this.spineTank.setCompleteListener(null);

                            this.changeState(CombatMemberState.Wait);
                        }
                    });
                    this.spineTank.setAnimation(0, AniName.BeAttacked, false);

                    Audio.getInstance().playEffect(
                        AUDIO_EFFECT_TYPE.DUNGEON_TANK_BE_ATTACKED,
                        AUDIO_EFFECT_PATH.DUNGEON
                    );

                    this.changeState(CombatMemberState.BeAttacked);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 获取挂点
     * @param name 骨骼名称
     * @returns
     */
    public getAttachedNode(name: string): cc.Node {
        // @ts-ignore
        const attachUtil = this.spineTank.attachUtil;
        const attachedNode: cc.Node[] = attachUtil.getAttachedNodes(name);

        return attachedNode.length !== 0 ? attachedNode[0] : null;
    }

    /**
     * 获取被攻击点
     * @returns
     */
    public getBeAttackedPoint(): cc.Vec2 {
        return this.node.convertToWorldSpaceAR(this.beAttackedWorldPos);
    }

    /**
     * 设置游戏速度
     * @param speed 速度
     */
    public setGameSpeed(speed: number): void {
        this.gameSpeed = speed;

        this.spineTank.timeScale = this.gameSpeed;
    }

    /**
     * 设置待入场状态
     * @param dungeonType 副本类型
     */
    public setWaitEnterState(dungeonType: DungeonType): void {
        switch (dungeonType) {
            case DungeonType.Thief:
                this.enterDelay = 1;
                break;
            default:
                this.enterDelay = 0;
                break;
        }

        this.changeState(CombatMemberState.WaitEnter);
    }

    /**
     * 播放施法动画
     * @param memberSkillData 成员技能数据
     * @param castCb 施法回调
     * @param isSkipingCombat 是否正在跳过战斗
     */
    public playCastAni(memberSkillData: ICombatMemberSkillData, castCb: () => void, isSkipingCombat: boolean): void {
        this.isSkipingCombat = isSkipingCombat;

        let aniName = "";
        switch (memberSkillData.info.type) {
            case EnumSkillType.TankASkill:
                switch (this.data.dungeonType) {
                    case DungeonType.Thief:
                        aniName = AniName.CastMove;
                        break;
                    default:
                        aniName = AniName.Cast;
                        break;
                }
                break;
            default:
                break;
        }
        if (!this.isSkipingCombat) {
            this.spineTank.setAnimation(0, aniName, false);
            switch (this.data.dungeonType) {
                case DungeonType.Thief:
                    this.spineTank.addAnimation(0, AniName.Move, true);
                    break;
                default:
                    this.spineTank.addAnimation(0, AniName.Wait, true);
                    break;
            }
        }

        this.waitTriggerState = this.data.state;
        this.stateTime = this.spineData[aniName].duration;

        this.castCb = castCb;
        this.castTime = this.spineData[aniName].event[ANI_EVENT_NAME][0];

        this.changeState(CombatMemberState.Cast);
    }
}
