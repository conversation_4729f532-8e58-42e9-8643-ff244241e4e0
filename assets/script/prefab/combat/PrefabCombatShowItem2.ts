/*
 * @Author: chenx
 * @Date: 2025-01-07 16:38:54
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-11 15:56:38
 */
import MathUtils from "../../../nsn/util/MathUtils";
import { EnumSkillShowFlight, EnumSkillShowLoop } from "../../data/base/BaseSkillShow";
import Combat, { CombatEvent } from "../../game/Combat";
import { CombatShowItemState } from "../../game/Skill";
import SpineUtils from "../../utils/SpineUtils";
import { PrefabCombatShowItem } from "./PrefabCombatShowItem";

/**
 * 重力
 */
const GRAVITY = cc.v2(0, -320);

/**
 * 重力缩放
 */
const GRAVITY_SCALE = 10;

/**
 * 动画事件名称-表现
 */
const ANI_EVENT_NAME = "shake";

const { ccclass, property } = cc._decorator;

/**
 * 表现item
 */
@ccclass
export default class PrefabCombatShowItem2 extends PrefabCombatShowItem {
    @property(sp.Skeleton)
    spineShow: sp.Skeleton = null; // 表现

    private time: number = 0; // 时间
    private posByPreFrame: cc.Vec2 = null; // 位置-上一帧
    private linearVelocity: cc.Vec2 = null; // 线速度

    public unuse(): void {
        super.unuse();

        this.time = 0;
        this.posByPreFrame = null;
        this.linearVelocity = null;

        this.node.opacity = 0;
        this.node.zIndex = 0;
        this.node.angle = 0;
        this.node.scale = 1;

        this.spineShow.node.active = false;
        this.spineShow.setEventListener(null);
        this.spineShow.setCompleteListener(null);
        this.spineShow.node.skeleton(null);
    }

    /**
     * 切换状态-初始化
     */
    public changeStateByInit(): void {
        this.node.setPosition(this.data.initPos);
        this.data.zIndex !== -1 && (this.node.zIndex = this.data.zIndex);
        this.node.opacity = 0;
        switch (this.data.info.flight) {
            case EnumSkillShowFlight.Not:
            case EnumSkillShowFlight.NotFlight:
            case EnumSkillShowFlight.NotFlight2:
                this.node.scaleX = this.data.info.scalingFactor * this.data.scaleX;
                this.node.scaleY = this.data.info.scalingFactor;
                this.node.angle = this.data.angle;

                this.spineShow.node.active = true;
                SpineUtils.setSkillEffect(
                    this.spineShow,
                    this.data.resFolder,
                    this.data.resPre,
                    this.data.resSuf,
                    () => {
                        if (!this.data) {
                            this.spineShow.node.skeleton(null);
                            return;
                        }

                        this.node.opacity = 255;
                        if (this.data.info.loop === EnumSkillShowLoop.Loop2) {
                            this.spineShow.setEventListener(
                                (trackEntry: sp.spine.TrackEntry, event: sp.spine.Event) => {
                                    if (event.data.name === ANI_EVENT_NAME) {
                                        this.spineShow.setEventListener(null);

                                        Combat.getInstance().emit(
                                            CombatEvent.PlayShakeScreenAni,
                                            this.skillData.releaseMember.getBaseData().dungeonType,
                                            this.data.info.shake,
                                            this.skillData.id
                                        );
                                    }
                                }
                            );
                            this.spineShow.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                                if (trackEntry.animation.name === "skill") {
                                    this.spineShow.setCompleteListener(null);

                                    this.changeState(CombatShowItemState.WaitClear);
                                }
                            });
                            this.spineShow.setAnimation(0, "skill", false);

                            if (this.data.info.delayTime !== 0) {
                                this.changeState(CombatShowItemState.Show);
                            } else {
                                this.changeState(CombatShowItemState.Finish);
                            }
                        } else {
                            this.spineShow.setAnimation(0, "skill", false);
                            this.spineShow.addAnimation(0, "skill2", true);

                            this.changeState(CombatShowItemState.Show);
                        }
                    }
                );
                break;
            case EnumSkillShowFlight.Straight:
                {
                    this.node.scale = this.data.info.scalingFactor;
                    const radian = Math.atan2(
                        this.data.targetPos.y - this.data.initPos.y,
                        this.data.targetPos.x - this.data.initPos.x
                    );
                    this.data.angle = MathUtils.a2d(radian);
                    this.node.angle = this.data.angle;

                    const velocity = this.data.info.preformPara[0];
                    this.linearVelocity = cc.v2(
                        velocity * Math.cos(MathUtils.d2a(this.data.angle)),
                        velocity * Math.sin(MathUtils.d2a(this.data.angle))
                    );

                    this.spineShow.node.active = true;
                    SpineUtils.setSkillEffect(
                        this.spineShow,
                        this.data.resFolder,
                        this.data.resPre,
                        this.data.resSuf,
                        () => {
                            if (!this.data) {
                                this.spineShow.node.skeleton(null);
                                return;
                            }

                            this.node.opacity = 255;
                            this.spineShow.setAnimation(0, "skill", true);
                        }
                    );

                    this.changeState(CombatShowItemState.Show);
                }
                break;
            case EnumSkillShowFlight.Parabolic:
                {
                    this.node.scale = this.data.info.scalingFactor;
                    this.data.angle = 60;
                    this.node.angle = this.data.angle;

                    this.posByPreFrame = this.data.initPos;
                    const dx = this.data.targetPos.x - this.data.initPos.x;
                    const dy = this.data.targetPos.y - this.data.initPos.y;
                    const tanAngle = Math.tan(MathUtils.d2a(this.data.angle));
                    const tempVelocity =
                        (GRAVITY.y * GRAVITY_SCALE * Math.pow(dx, 2)) /
                        ((dy - dx * tanAngle) / (1 + Math.pow(tanAngle, 2))) /
                        2;
                    const velocity = Math.sqrt(tempVelocity);
                    this.linearVelocity = cc.v2(
                        velocity * Math.cos(MathUtils.d2a(this.data.angle)),
                        velocity * Math.sin(MathUtils.d2a(this.data.angle))
                    );

                    this.spineShow.node.active = true;
                    SpineUtils.setSkillEffect(
                        this.spineShow,
                        this.data.resFolder,
                        this.data.resPre,
                        this.data.resSuf,
                        () => {
                            if (!this.data) {
                                this.spineShow.node.skeleton(null);
                                return;
                            }

                            this.node.opacity = 255;
                            this.spineShow.setAnimation(0, "skill", true);
                        }
                    );

                    this.changeState(CombatShowItemState.Show);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 更新状态-表现
     * @param dt
     */
    public updateStateByShow(dt: number): void {
        if (dt === 0) {
            return;
        }

        this.time += dt;

        let isFinish = false;
        switch (this.data.info.flight) {
            case EnumSkillShowFlight.Not:
            case EnumSkillShowFlight.NotFlight:
            case EnumSkillShowFlight.NotFlight2:
                this.data.info.loop === EnumSkillShowLoop.Loop2 &&
                    this.time >= this.data.info.delayTime &&
                    this.changeState(CombatShowItemState.Finish);
                break;
            case EnumSkillShowFlight.Straight:
                this.node.x = this.data.initPos.x + this.linearVelocity.x * this.time;
                this.node.y = this.data.initPos.y + this.linearVelocity.y * this.time;

                if (this.data.targetPos.x >= this.data.initPos.x) {
                    isFinish = this.node.x >= this.data.targetPos.x;
                } else {
                    isFinish = this.node.x <= this.data.targetPos.x;
                }
                break;
            case EnumSkillShowFlight.Parabolic:
                this.node.x = this.data.initPos.x + this.linearVelocity.x * this.time;
                this.node.y =
                    this.data.initPos.y +
                    this.linearVelocity.y * this.time +
                    0.5 * GRAVITY.y * GRAVITY_SCALE * Math.pow(this.time, 2);
                this.node.angle = -MathUtils.a2d(
                    cc.v2(this.node.x - this.posByPreFrame.x, this.node.y - this.posByPreFrame.y).signAngle(cc.v2(1, 0))
                );
                this.posByPreFrame = this.node.getPosition().clone();

                if (this.data.targetPos.x >= this.data.initPos.x) {
                    isFinish = this.node.x >= this.data.targetPos.x;
                } else {
                    isFinish = this.node.x <= this.data.targetPos.x;
                }
                break;
            default:
                break;
        }
        isFinish && this.changeState(CombatShowItemState.WaitClear);
    }

    /**
     * 播放动画
     * @param name 名称
     * @param name2 名称
     * @param isLoop 是否循环
     * @param playCb 播放cb
     */
    public playAni(name: string, name2: string, isLoop: boolean, playCb: () => void): void {
        if (playCb) {
            this.spineShow.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                if (trackEntry.animation.name === name2) {
                    this.spineShow.setCompleteListener(null);

                    playCb();
                }
            });
        }
        this.spineShow.setMix(name, name2, 0.1);
        this.spineShow.setAnimation(0, name2, isLoop);
    }

    /**
     * 设置游戏速度
     * @param speed 速度
     */
    public setGameSpeed(speed: number): void {
        super.setGameSpeed(speed);

        this.spineShow.timeScale = this.gameSpeed;
    }
}
