/*
 * @Author: chenx
 * @Date: 2024-06-03 14:36:32
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 15:01:13
 */
import Loader from "../../../nsn/core/Loader";
import UI from "../../../nsn/ui/UI";
import ArrayUtils from "../../../nsn/util/ArrayUtils";
import CocosExt from "../../../nsn/util/CocosExt";
import MathUtils from "../../../nsn/util/MathUtils";
import { HOUR_TO_SECOND } from "../../../nsn/util/Time";
import Tips from "../../../nsn/util/Tips";
import Utils from "../../../nsn/util/Utils";
import {
    ArcherBattleRet,
    ArrowGroupSyncRet,
    BagUpdateRet,
    EquipCreateNoticeRet,
    IEquipInfo,
    LeadSkinDressRet,
    MagicalBatchBattleRet,
    MagicalBattleRet,
    MapBarrierPropDrop,
    MapBarrierSync,
    MapBarrierSyncRet,
    PetBattleRet,
    TankBattleRet,
    WeaponBattleRet,
    WeaponShowRet,
    WingsCreateRet,
    WingsLineupRet,
    WingsShowRet,
} from "../../../protobuf/proto";
import { SCENE_BG_MIN_NUM } from "../../comp/SceneBgMoveComp";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../../config/AudioEffectConfig";
import Guide from "../../core/Guide";
import RedPoint from "../../core/redPoint/RedPoint";
import { RedPointId } from "../../core/redPoint/RedPointId";
import { EnumCombatDialogueGroupTriggerType } from "../../data/base/BaseCombatDialogueGroup";
import { EnumIdleEarningsTotalPara } from "../../data/base/BaseIdleEarningsTotal";
import { EnumMonsterBaseMoveType, EnumMonsterBaseType } from "../../data/base/BaseMonsterBase";
import { EnumPrivilegeConfigType } from "../../data/base/BasePrivilegeConfig";
import { EnumSkillEffectEffectType } from "../../data/base/BaseSkillEffect";
import { EnumTaskDetailType } from "../../data/base/BaseTaskDetail";
import { EnumUniversalPara } from "../../data/base/BaseUniversal";
import { GAME_SWITCH_ID } from "../../data/parser/TBGameSwitch";
import TBIdleEarningsTotal from "../../data/parser/TBIdleEarningsTotal";
import TBMainBarrier from "../../data/parser/TBMainBarrier";
import TBMainChapter from "../../data/parser/TBMainChapter";
import TBMainMap from "../../data/parser/TBMainMap";
import TBMainRewardGroup from "../../data/parser/TBMainRewardGroup";
import TBMonsterBase from "../../data/parser/TBMonsterBase";
import TBMonsterCallFormation from "../../data/parser/TBMonsterCallFormation";
import TBMonsterFormation from "../../data/parser/TBMonsterFormation";
import TBMonsterGroup from "../../data/parser/TBMonsterGroup";
import TBPrivilegeConfig from "../../data/parser/TBPrivilegeConfig";
import { EnumSkillParamLevelType } from "../../data/parser/TBSkill";
import TBUniversal from "../../data/parser/TBUniversal";
import Archer from "../../game/Archer";
import Attribute, { AttributeEvent } from "../../game/Attribute";
import Bag from "../../game/Bag";
import Combat, { CombatEvent, DungeonType } from "../../game/Combat";
import CombatLog, { CombatLogId } from "../../game/CombatLog";
import CombatScore from "../../game/CombatScore";
import DungeonMain, { DungeonMainEvent } from "../../game/DungeonMain";
import Equip from "../../game/Equip";
import GameSwitch from "../../game/GameSwitch";
import LeadSkin from "../../game/LeadSkin";
import Magical from "../../game/Magical";
import MakeArrow from "../../game/MakeArrow";
import Pet from "../../game/Pet";
import Privilege from "../../game/Privilege";
import Setting, { SettingId } from "../../game/Setting";
import Skill, { SkillEvent } from "../../game/Skill";
import Tank from "../../game/Tank";
import Task from "../../game/Task";
import Weapon from "../../game/Weapon";
import Wing from "../../game/Wing";
import { CombatDungeonState } from "../../game/combat/CombatDungeon";
import CombatDungeonMain from "../../game/combat/CombatDungeonMain";
import { CombatMemberState } from "../../game/combat/CombatMember";
import CombatMemberMonster from "../../game/combat/CombatMemberMonster";
import CombatMemberPlayer, { CombatPlayerType } from "../../game/combat/CombatMemberPlayer";
import AudioUtils from "../../utils/AudioUtils";
import ImageUtils from "../../utils/ImageUtils";
import NumberUtils from "../../utils/NumberUtils";
import { PrefabCombatDungeon } from "./PrefabCombatDungeon";
import { ICombatItemDropData } from "./PrefabCombatItemDrop";
import { FLY_MONSTER_FORMATION, MONSTER_FORMATION_NUM, WALK_MONSTER_FORMATION } from "./PrefabCombatScene";

/**
 * 低血量参数
 */
const LOW_HP_PARA = 0.2;

/**
 * 跳关动画速度
 */
const SKIP_LEVEL_ANI_SPEED = 2;

/**
 * 过关动画速度
 */
const PASS_LEVEL_ANI_SPEED = 1.3;

const { ccclass, property } = cc._decorator;

/**
 * 主线副本
 */
@ccclass
export default class PrefabCombatDungeonMain extends PrefabCombatDungeon {
    @property(cc.Node)
    nodeLevel: cc.Node = null; // 关卡
    @property(cc.Label)
    lbtLevelName: cc.Label = null; // 关卡名称
    @property(cc.Node)
    nodeWave: cc.Node = null; // 波数
    @property(cc.Node)
    nodeWave2: cc.Node = null; // 波数
    @property(cc.Label)
    lbtMonsterName: cc.Label = null; // 怪兽名称
    @property(cc.ProgressBar)
    prgMonsterHp: cc.ProgressBar = null; // 怪兽血量
    @property(cc.ProgressBar)
    prgMonsterHp2: cc.ProgressBar = null; // 怪兽血量-过渡
    @property(cc.ProgressBar)
    prgMonsterHp3: cc.ProgressBar = null; // 怪兽血量-护盾
    @property(cc.Label)
    lbtMonsterHp: cc.Label = null; // 怪兽血量
    @property(cc.ProgressBar)
    prgLevelTime: cc.ProgressBar = null; // 关卡时间
    @property(cc.Node)
    nodeLevelTimeTips: cc.Node = null; // 关卡时间提示
    @property(cc.Node)
    nodeLevelTimeTipsItem: cc.Node = null; // 关卡时间提示item
    @property(cc.Label)
    lbtLevelTime: cc.Label = null; // 关卡时间
    @property(cc.Node)
    nodeJumpBoss: cc.Node = null; // 跳转boss按钮
    @property(cc.Node)
    nodeJumpBossBg: cc.Node = null; // 跳转boss按钮bg
    @property(cc.Node)
    nodeAuto: cc.Node = null; // 自动按钮-释放技能

    // 通关动画
    @property(cc.Node)
    nodePassAni: cc.Node = null;
    @property(cc.Node)
    nodePassAniBg: cc.Node = null; // bg
    @property(cc.Label)
    lbtPassAniLevelName: cc.Label = null; // 关卡名称
    @property(cc.Node)
    nodePassAniIncomeItem: cc.Node = null; // 收益item
    @property([cc.Node])
    nodePassAniTips: cc.Node[] = []; // tips
    @property(cc.Sprite)
    spPassAniTitle: cc.Sprite = null; // 标题
    @property(cc.Node)
    nodePassAniStar: cc.Node = null; // 星星
    @property(cc.Node)
    nodePassAniStar2: cc.Node = null; // 星星
    @property(sp.Skeleton)
    spinePassAniBoom: sp.Skeleton = null; // 爆炸
    @property(sp.Skeleton)
    spineWind: sp.Skeleton = null; // 风

    // 跳关动画
    @property(cc.Node)
    nodeSkipLevelAni: cc.Node = null; // 跳关动画
    @property(sp.Skeleton)
    spineSkipLevelAniLight: sp.Skeleton = null; // 光

    // 怪兽来袭动画
    @property(cc.Node)
    nodeMonsterAni: cc.Node = null;
    @property(cc.Node)
    nodeMonsterAniMaskBg: cc.Node = null; // maskBg
    @property(cc.Node)
    nodeMonsterAniBg: cc.Node = null; // bg
    @property(cc.Sprite)
    spMonsterAniIcon: cc.Sprite = null; // icon
    @property(cc.Sprite)
    spMonsterAniIcon2: cc.Sprite = null; // icon
    @property(cc.Sprite)
    spMonsterAniTitle: cc.Sprite = null; // 标题

    // 玩家低血量动画
    @property(cc.Node)
    nodeLowHpAni: cc.Node = null;

    // 关卡重置动画
    @property(cc.Node)
    nodeResetAni: cc.Node = null;
    @property(cc.Node)
    nodeResetAniBlockEvent: cc.Node = null; // 拦截事件

    private dungeonMainCtl: CombatDungeonMain = CombatDungeonMain.getInstance(); // 主线副本ctl
    private isPlayingAni: boolean = false; // 是否正在播放动画-低血量动画
    private isPlayingPassWaveAni: boolean = false; // 是否正在播放动画-过波动画

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spPassAniTitle,
                url: "texture/syncUI/combat/spMQResultTitle",
            },
            {
                sprite: this.spMonsterAniTitle,
                url: "texture/syncUI/combat/spMQBossTxt",
            },
        ];
    }

    protected onLoad(): void {
        this.nodeLevelTimeTipsItem.parent = null;

        this.registerHandler();
        this.setDungeonCtl(this.dungeonMainCtl);
        this.loadScene();
    }

    protected onDestroy(): void {
        super.onDestroy();

        this.nodeLevelTimeTipsItem.destroy();
    }

    protected registerHandler(): void {
        super.registerHandler();

        // 主线副本-同步数据
        DungeonMain.getInstance().on(
            MapBarrierSyncRet.prototype.clazzName,
            () => {
                const baseData = this.dungeonMainCtl.getBaseData();
                const mainData = this.dungeonMainCtl.getData();
                switch (baseData.state) {
                    case CombatDungeonState.Failure:
                        baseData.resetTime = 0;

                        baseData.isPlayEnterAni = true;
                        this.changeState(CombatDungeonState.Reset);
                        break;
                    default:
                        if (baseData.resetTime <= 0) {
                            if (DungeonMain.getInstance().getLevelId() > mainData.levelId) {
                                baseData.isPlayEnterAni = true;
                                this.changeState(CombatDungeonState.Reset);
                            }
                        } else {
                            baseData.resetTime = 0;
                        }
                        break;
                }
            },
            this
        );
        // 主线副本-播放地图动画cb
        DungeonMain.getInstance().on(
            DungeonMainEvent.PlayMapAniCb,
            () => {
                this.dungeonMainCtl.getBaseData().isPlayEnterAni = true;
                this.changeState(CombatDungeonState.Reset);
            },
            this
        );
        // 战斗-击杀怪兽
        Combat.getInstance().on(
            CombatEvent.KillMonster,
            (dungeonType: DungeonType, monsterUuid: number, worldPosByDrop: cc.Vec2, heightByDrop: number) => {
                const baseData = this.dungeonMainCtl.getBaseData();
                if (baseData.type !== dungeonType) {
                    return;
                }

                baseData.monsterKillNum++;
                baseData.monsterDropData.push([monsterUuid, worldPosByDrop, heightByDrop]);

                DungeonMain.getInstance().dropItem();

                const mainData = this.dungeonMainCtl.getData();
                if (mainData.wave === mainData.totalWave) {
                    if (baseData.monsterKillNum === baseData.monsterTotalNum) {
                        baseData.resetTime = 10;
                        const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.DUNGEON_MAIN_SKIP_LEVEL);
                        const privilegeInfo = TBPrivilegeConfig.getInstance().getDataListByType(
                            EnumPrivilegeConfigType.MainJump
                        );
                        const isEffect =
                            privilegeInfo.findIndex((e) => Privilege.getInstance().hasPrivilege(e.id)) !== -1;
                        const isSkipLevel =
                            (result || isEffect) &&
                            Setting.getInstance().getSwitchState(SettingId.DungeonMainSkipLevel);
                        DungeonMain.getInstance().sendSyncData(
                            MathUtils.ceil(mainData.mapInfo.time - mainData.limitTime, 1),
                            true,
                            isSkipLevel
                        );
                    }
                    return;
                }

                const para: number[][] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.EquipmentDrops);
                if (
                    para.findIndex(
                        ([levelId, wave]) =>
                            levelId > mainData.levelId || (levelId === mainData.levelId && wave >= mainData.wave)
                    ) !== -1
                ) {
                    if (
                        para.findIndex(
                            ([levelId, wave, killNum]) =>
                                levelId === mainData.levelId &&
                                wave === mainData.wave &&
                                killNum === baseData.monsterKillNum
                        ) !== -1
                    ) {
                        DungeonMain.getInstance().sendDropEquip(
                            mainData.levelId,
                            mainData.wave,
                            baseData.monsterKillNum,
                            true
                        );
                    }
                } else {
                    const fatigueData = Equip.getInstance().getFatigueData();
                    const fatigue = mainData.levelInfo.equipLimit - fatigueData.times;
                    if (fatigue > 0) {
                        const prob: number[] = Array.from(
                            mainData.mapInfo.dropequipmentPro.map(([tempProb]) => tempProb)
                        );
                        const weight: number[] = Array.from(
                            mainData.mapInfo.dropequipmentPro.map(([, tempWeight]) => tempWeight)
                        );
                        if (ArrayUtils.weightedSample(prob, weight) === 1) {
                            DungeonMain.getInstance().sendDropEquip(
                                mainData.levelId,
                                mainData.wave,
                                baseData.monsterKillNum,
                                false
                            );
                        }
                    }
                }
            },
            this
        );
        // 背包-更新
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                const baseData = this.dungeonMainCtl.getBaseData();
                const mainData = this.dungeonMainCtl.getData();

                const itemData: number[][] = [];
                switch (data.srcReq) {
                    case MapBarrierSync.prototype.clazzName:
                        const rewardInfo = TBMainRewardGroup.getInstance().getDataById(
                            mainData.levelInfo.rewardGroupId
                        );
                        rewardInfo.reward.forEach(([itemId, num]) => {
                            itemData.push([itemId, num]);
                        });
                        break;
                    case MapBarrierPropDrop.prototype.clazzName:
                        data.gotItem.forEach((e) => {
                            itemData.push([e.itemInfoId, e.num]);
                        });
                        break;
                    default:
                        break;
                }
                const itemDropData: ICombatItemDropData[] = [];
                itemData.forEach(([itemId, num]) => {
                    const itemDropUuid = mainData.itemDropUuid++;
                    if (baseData.monsterDropData.length !== 0) {
                        const index = MathUtils.getRandomInt(0, baseData.monsterDropData.length);
                        const [, worldPos, height] = baseData.monsterDropData[index];
                        itemDropData.push({
                            uuid: itemDropUuid,
                            itemData: { itemInfoId: itemId, num },
                            worldPos,
                            height,
                            nodeItem: null,
                        });
                    } else {
                        const { width, height } = this.sceneCtl.getMonsterSize(CombatPlayerType.Self);
                        const worldPos = this.sceneCtl.getWorldPosByMonsterArea(
                            CombatPlayerType.Self,
                            cc.v2(width / 2, height / 2)
                        );
                        itemDropData.push({
                            uuid: itemDropUuid,
                            itemData: { itemInfoId: itemId, num },
                            worldPos,
                            height: height / 4,
                            nodeItem: null,
                        });
                    }

                    mainData.itemDropUuid2.push(itemDropUuid);
                });
                itemDropData.length !== 0 && DungeonMain.getInstance().emit(DungeonMainEvent.ItemDrop, itemDropData);
            },
            this
        );
        // 装备-新增通知
        Equip.getInstance().on(
            EquipCreateNoticeRet.prototype.clazzName,
            (equipData: IEquipInfo[]) => {
                const mainData = this.dungeonMainCtl.getData();
                if (!mainData || mainData.isCheckEquipDrop) {
                    return;
                }

                const itemDropData: ICombatItemDropData[] = [];
                const mainBaseData = this.dungeonMainCtl.getBaseData();
                equipData.forEach((e) => {
                    const itemDropUuid = mainData.itemDropUuid++;
                    if (mainBaseData.monsterDropData.length !== 0) {
                        const index = MathUtils.getRandomInt(0, mainBaseData.monsterDropData.length);
                        const [, worldPos, height] = mainBaseData.monsterDropData[index];
                        itemDropData.push({ uuid: itemDropUuid, equipData: e, worldPos, height, nodeItem: null });
                    } else {
                        const { width, height } = this.sceneCtl.getMonsterSize(CombatPlayerType.Self);
                        const worldPos = this.sceneCtl.getWorldPosByMonsterArea(
                            CombatPlayerType.Self,
                            cc.v2(width / 2, height / 2)
                        );
                        itemDropData.push({
                            uuid: itemDropUuid,
                            equipData: e,
                            worldPos,
                            height: height / 4,
                            nodeItem: null,
                        });
                    }

                    mainData.itemDropUuid2.push(itemDropUuid);
                });
                itemDropData.length !== 0 && DungeonMain.getInstance().emit(DungeonMainEvent.ItemDrop, itemDropData);
            },
            this
        );
        // 主线副本-清理道具掉落
        DungeonMain.getInstance().on(
            DungeonMainEvent.ClearItemDrop,
            (itemDropUuid: number) => {
                const mainData = this.dungeonMainCtl.getData();
                const index = mainData.itemDropUuid2.findIndex((e) => e === itemDropUuid);
                mainData.itemDropUuid2.splice(index, 1);

                mainData.itemDropUuid2.length === 0 && this.changeState(CombatDungeonState.Combat);
            },
            this
        );
        // 主线副本-暂停
        DungeonMain.getInstance().on(
            DungeonMainEvent.Pause,
            (isPause: boolean) => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();
                if (isPause) {
                    mainBaseData.state === CombatDungeonState.Combat && this.changeState(CombatDungeonState.Pause);
                } else {
                    mainBaseData.state === CombatDungeonState.Pause && this.changeState(CombatDungeonState.Combat);
                }
            },
            this
        );
        // 主角-上阵
        LeadSkin.getInstance().on(
            LeadSkinDressRet.prototype.clazzName,
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();

                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updateLeadData(LeadSkin.getInstance().getId());
                    if (!Weapon.getInstance().isShowWeapon()) {
                        player.updateWeaponData(-1, false);
                    }
                    player.updateAttrByCul();
                    player.updateAttrBySkill();
                    player.updateSkillCd();
                    player.initSkillCd();
                    this.sceneCtl.updateLeadInfo(player);
                    this.sceneCtl.updateWeaponInfo(player);
                    this.sceneCtl.updateGameSpeedByArcher(player);
                    this.updatePlayerSkillInfo(player);
                    CombatScore.getInstance().updateScore();
                }
            },
            this
        );
        // 神器-上阵
        Weapon.getInstance().on(
            WeaponBattleRet.prototype.clazzName,
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();

                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updateWeaponData(Weapon.getInstance().getTeamId(), Weapon.getInstance().isShowWeapon());
                    player.updateAttrByCul();
                    player.updateAttrBySkill();
                    player.updateSkillCd();
                    player.initSkillCd();
                    this.sceneCtl.updateWeaponInfo(player);
                    this.sceneCtl.updateGameSpeedByArcher(player);
                    this.updatePlayerSkillInfo(player);
                    CombatScore.getInstance().updateScore();
                }
            },
            this
        );
        // 神器-展示
        Weapon.getInstance().on(
            WeaponShowRet.prototype.clazzName,
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();

                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updateWeaponData(-1, Weapon.getInstance().isShowWeapon());
                    this.sceneCtl.updateWeaponInfo(player);
                }
            },
            this
        );
        // 背饰-获得
        Wing.getInstance().on(
            WingsCreateRet.prototype.clazzName,
            (data: WingsCreateRet) => {
                if (data.lineupWingId === 0) {
                    return;
                }

                const mainBaseData = this.dungeonMainCtl.getBaseData();
                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updateWingData(Wing.getInstance().getTeamId(), Wing.getInstance().isShowWing());
                    player.updateAttrByCul();
                    player.updateAttrBySkill();
                    player.updateSkillCd();
                    player.initSkillCd();
                    this.sceneCtl.updateWingInfo(player);
                    this.sceneCtl.updateGameSpeedByArcher(player);
                    this.updatePlayerSkillInfo(player);
                    CombatScore.getInstance().updateScore();
                }
            },
            this
        );
        // 背饰-上阵
        Wing.getInstance().on(
            WingsLineupRet.prototype.clazzName,
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();
                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updateWingData(Wing.getInstance().getTeamId(), Wing.getInstance().isShowWing());
                    player.updateAttrByCul();
                    player.updateAttrBySkill();
                    player.updateSkillCd();
                    player.initSkillCd();
                    this.sceneCtl.updateWingInfo(player);
                    this.sceneCtl.updateGameSpeedByArcher(player);
                    this.updatePlayerSkillInfo(player);
                    CombatScore.getInstance().updateScore();
                }
            },
            this
        );
        // 背饰-展示
        Wing.getInstance().on(
            WingsShowRet.prototype.clazzName,
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();

                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updateWingData(-1, Wing.getInstance().isShowWing());
                    this.sceneCtl.updateWingInfo(player);
                }
            },
            this
        );
        // 战车-上阵
        Tank.getInstance().on(
            TankBattleRet.prototype.clazzName,
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();

                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updateTankData(Tank.getInstance().getTeamId());
                    player.updateAttrByCul();
                    player.updateAttrBySkill();
                    player.updateSkillCd();
                    player.initSkillCd();
                    this.sceneCtl.updateTankInfo(player);
                    this.sceneCtl.updateGameSpeedByArcher(player);
                    this.updatePlayerSkillInfo(player);
                    CombatScore.getInstance().updateScore();
                }
            },
            this
        );
        // 弓箭手-上阵
        Archer.getInstance().on(
            ArcherBattleRet.prototype.clazzName,
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();

                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updateArcherData(Archer.getInstance().getTeam());
                    player.updateAttrByCul();
                    player.updateAttrBySkill();
                    player.updateSkillCd();
                    player.initSkillCd();
                    this.sceneCtl.updateArcherInfo(player);
                    this.sceneCtl.updateGameSpeedByArcher(player);
                    this.updatePlayerSkillInfo(player);
                    CombatScore.getInstance().updateScore();
                }
            },
            this
        );
        // 制作-同步数据
        MakeArrow.getInstance().on(
            ArrowGroupSyncRet.prototype.clazzName,
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();

                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    const isUpdate = player.updateArrowData(MakeArrow.getInstance().getArcherGridDataArr());
                    if (isUpdate) {
                        player.updateAttrByCul();
                        player.updateAttrBySkill();
                        player.updateSkillCd();
                        this.sceneCtl.updateGameSpeedByArcher(player);
                        CombatScore.getInstance().updateScore();
                        Skill.getInstance().emit(
                            SkillEvent.ResetEffectPara,
                            EnumSkillParamLevelType.ArrowLevel,
                            Archer.getInstance().getTeamIds()
                        );
                    }
                }
            },
            this
        );
        // 宠物-上阵
        Pet.getInstance().on(
            PetBattleRet.prototype.clazzName,
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();

                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updatePetData(Pet.getInstance().getBattleId());
                    player.updateAttrByCul();
                    player.updateAttrBySkill();
                    player.updateSkillCd();
                    player.initSkillCd();
                    this.sceneCtl.updatePetInfo(player);
                    this.sceneCtl.updateGameSpeedByArcher(player);
                    this.updatePlayerSkillInfo(player);
                    CombatScore.getInstance().updateScore();
                }
            },
            this
        );
        // 魔法-上阵/批量上阵
        Magical.getInstance().on(
            [MagicalBattleRet.prototype.clazzName, MagicalBatchBattleRet.prototype.clazzName],
            () => {
                const mainBaseData = this.dungeonMainCtl.getBaseData();

                if (mainBaseData && mainBaseData.state !== CombatDungeonState.Init) {
                    const player = this.dungeonMainCtl
                        .getAllPlayer()
                        .find((e) => e.getData().type === CombatPlayerType.Self);

                    player.updateMagicData(Magical.getInstance().getSkillBattleInfo());
                    player.updateAttrByCul();
                    player.updateAttrBySkill();
                    player.updateSkillCd();
                    player.initSkillCd();
                    this.sceneCtl.updateGameSpeedByArcher(player);
                    this.updatePlayerSkillInfo(player);
                    CombatScore.getInstance().updateScore();
                }
            },
            this
        );
        // 属性-更新
        Attribute.getInstance().on(
            AttributeEvent.Update,
            () => {
                const baseData = this.dungeonMainCtl.getBaseData();
                if (baseData.state === CombatDungeonState.Init) {
                    return;
                }

                const player = this.dungeonMainCtl
                    .getAllPlayer()
                    .find((e) => e.getData().type === CombatPlayerType.Self);
                player.updateAttrByCul();
                player.updateAttrBySkill();
                player.updateSkillCd();
                this.sceneCtl.updateGameSpeedByArcher(player);
                CombatScore.getInstance().updateScore();
            },
            this
        );
        // 技能-重置效果参数
        Skill.getInstance().on(
            SkillEvent.ResetEffectPara,
            (paraType: EnumSkillParamLevelType, memberId: number[]) => {
                const player = this.dungeonMainCtl
                    .getAllPlayer()
                    .find((e) => e.getData().type === CombatPlayerType.Self);
                Skill.getInstance().resetEffectPara(player, paraType, memberId);
            },
            this
        );
        // 战斗-跳转boss
        Combat.getInstance().on(
            CombatEvent.JumpBoss,
            (dungeonType: DungeonType) => {
                if (this.dungeonMainCtl.getBaseData().type === dungeonType) {
                    const mainData = this.dungeonMainCtl.getData();
                    mainData.wave = mainData.totalWave - 1;
                    mainData.isCombated = false;

                    const allPlayer = this.dungeonMainCtl.getAllPlayer();
                    const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
                    this.dungeonMainCtl.resetCombat();
                    this.sceneCtl.resetPlayerInfo(player);
                    this.resetPlayerSkillInfo(player);
                    this.updatePlayerHpState(player, true);

                    this.changeState(CombatDungeonState.Ready);
                }
            },
            this
        );
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatDungeonState): void {
        super.changeState(state);

        const baseData = this.dungeonMainCtl.getBaseData();
        const mainData = this.dungeonMainCtl.getData();
        const allPlayer = this.dungeonMainCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);

        switch (baseData.state) {
            case CombatDungeonState.Init:
                this.updateLevelInfo();
                this.sceneCtl.initPlayerInfo(player);
                this.sceneCtl.updatePlayerInfo(player);
                this.updatePlayerSkillInfo(player);
                this.updatePlayerHpState(player, true);
                this.updatePlayerDamageState();
                this.updateAutoState();
                this.updateGameSpeedState(true);

                player.updateAttrByCul();
                player.updateAttrBySkill();
                player.updateSkillCd();
                this.sceneCtl.updateGameSpeedByArcher(player);
                CombatScore.getInstance().updateScore();

                baseData.isPlayEnterAni = true;
                mainData.isCheckEquipDrop = true;
                this.changeState(CombatDungeonState.Ready);
                break;
            case CombatDungeonState.Ready:
                mainData.wave++;

                if (mainData.wave > mainData.totalWave) {
                    this.changeState(CombatDungeonState.Success);
                } else {
                    if (mainData.wave === mainData.totalWave) {
                        if (mainData.isCombated) {
                            this.changeState(CombatDungeonState.Reset);
                            return;
                        }
                    }

                    this.updateWaveInfo();
                    this.initMonsterInfo();

                    if (mainData.wave === mainData.totalWave) {
                        baseData.isPlayEnterAni = true;
                        this.playMonsterComingAni();
                    }

                    this.changeState(CombatDungeonState.Load);
                }
                break;
            case CombatDungeonState.Success:
                this.playPassAni();

                this.dialogCtl.trigger(EnumCombatDialogueGroupTriggerType.ContinuousBarrier, 1);
                DungeonMain.getInstance().emit(DungeonMainEvent.UpdateLevel, DungeonMain.getInstance().getLevelId());
                break;
            case CombatDungeonState.Failure:
                mainData.refreshTime = 0;

                if (mainData.wave >= mainData.totalWave) {
                    baseData.resetTime = 10;
                    DungeonMain.getInstance().sendSyncData(0, false, false);

                    this.dialogCtl.trigger(EnumCombatDialogueGroupTriggerType.ContinuousBarrier, -1);
                } else {
                    this.dialogCtl.trigger(EnumCombatDialogueGroupTriggerType.ContinuousBarrier, -1);

                    this.changeState(CombatDungeonState.Reset);
                }
                break;
            case CombatDungeonState.Reset:
                mainData.refreshTime = 0;

                this.playResetAni();
                break;
            default:
                break;
        }

        if (state === CombatDungeonState.Success || state === CombatDungeonState.Failure) {
            if (
                CombatLog.getInstance().getShowState(CombatLogId.CombatResultData) &&
                CombatLog.getInstance().getShowStateByDungeonType(baseData.type)
            ) {
                allPlayer.forEach((e) => {
                    const tempPlayerData = e.getData();

                    CombatLog.getInstance().logTitle(CombatLogId.CombatResultData, e);

                    const logData = {
                        combatTime: baseData.combatTime,
                        skillRecord: Utils.clone(tempPlayerData.skillRecord),
                    };
                    cc.log(logData);
                });
            }
        }
    }

    /**
     * 更新状态
     * @param dt
     */
    public updateState(dt: number): void {
        const baseData = this.dungeonMainCtl.getBaseData();
        const mainData = this.dungeonMainCtl.getData();
        const allPlayer = this.dungeonMainCtl.getAllPlayer();
        const allMonster = this.dungeonMainCtl.getAllMonster();

        switch (baseData.state) {
            case CombatDungeonState.Load:
                this.updataStateByLoad(dt);
                break;
            case CombatDungeonState.Enter:
                allPlayer.forEach((e) => {
                    const playerData = e.getData();

                    if (
                        playerData.tankData.state === CombatMemberState.WaitEnter ||
                        playerData.tankData.state === CombatMemberState.Enter
                    ) {
                        const compTankItem = this.sceneCtl.getTankItem(e.getBaseData().uuid);
                        compTankItem.updateState(dt, playerData.type);
                    }
                });

                if (
                    allPlayer.findIndex((e) => {
                        const playerData = e.getData();
                        return (
                            playerData.tankData.state === CombatMemberState.Load ||
                            playerData.tankData.state === CombatMemberState.WaitEnter ||
                            playerData.tankData.state === CombatMemberState.Enter
                        );
                    }) === -1 &&
                    allMonster.findIndex((e) => {
                        const monsterData = e.getData();
                        return (
                            monsterData.info.type === EnumMonsterBaseType.BossMonster &&
                            (monsterData.state === CombatMemberState.Load ||
                                monsterData.state === CombatMemberState.WaitEnter ||
                                monsterData.state === CombatMemberState.Enter)
                        );
                    }) === -1
                ) {
                    if (mainData.isCheckEquipDrop) {
                        mainData.isCheckEquipDrop = false;
                        this.checkEquipDrop();
                        if (mainData.itemDropUuid2.length > 0) {
                            this.changeState(CombatDungeonState.Pause);
                        } else {
                            this.changeState(CombatDungeonState.Combat);
                        }
                    } else {
                        this.changeState(CombatDungeonState.Combat);
                    }
                }
                break;
            case CombatDungeonState.Combat:
                this.actionSkillCd(allPlayer, allMonster, dt);
                this.actionSkillRelease(allPlayer, allMonster, dt);

                this.actionSkillShow([...allPlayer, ...allMonster], dt);
                this.actionSkillEffect(allPlayer, allMonster, dt);
                this.actionSkill([...allPlayer, ...allMonster], dt);

                this.actionBuff([...allPlayer, ...allMonster], dt);

                this.actionPlayer(dt);

                this.actionMonster(dt);
                this.sceneCtl.actionMonsterByZIndex();

                this.judgeResult(dt);
                break;
            case CombatDungeonState.Success:
            case CombatDungeonState.Failure:
                this.actionSkillShow([...allPlayer, ...allMonster], dt);
                break;
            default:
                break;
        }

        this.dialogCtl.actionDialog(allPlayer, allMonster);

        this.sceneCtl.actionDamage();

        if (baseData.resetTime > 0) {
            baseData.resetTime -= dt;
            if (baseData.resetTime <= 0) {
                baseData.isPlayEnterAni = true;
                this.changeState(CombatDungeonState.Reset);
            }
        }
    }

    /**
     * 更新关卡信息
     */
    public updateLevelInfo(): void {
        const mainData = this.dungeonMainCtl.getData();
        mainData.totalWave = 5;
        mainData.wave = 0;

        const levelData = DungeonMain.getInstance().getLevelData();
        if (mainData.levelId !== levelData.mapBarrierId) {
            mainData.levelId = levelData.mapBarrierId;
            mainData.levelInfo = TBMainBarrier.getInstance().getDataById(mainData.levelId);

            if (mainData.mapId !== mainData.levelInfo.map) {
                mainData.mapId = mainData.levelInfo.map;
                mainData.mapInfo = TBMainMap.getInstance().getDataById(mainData.mapId);

                this.compSceneBgMove.clearBg();
                if (mainData.mapInfo.res[1] && mainData.mapInfo.res[1].length !== 0) {
                    for (let i = 0; i < Math.ceil(SCENE_BG_MIN_NUM / mainData.mapInfo.res[1].length); i++) {
                        mainData.mapInfo.res[1].forEach((e, j) => {
                            const nodeItem = this.compSceneBgMove.getFarItem();
                            nodeItem.x = nodeItem.width * nodeItem.scale * (mainData.mapInfo.res[1].length * i + j);
                            nodeItem.spriteAsync(
                                `texture/dungeonSceneBg/main/spMIMap${mainData.mapInfo.res[0][0]}Far${e}`
                            );

                            this.compSceneBgMove.nodeFar.push(nodeItem);
                        });
                    }
                }
                if (mainData.mapInfo.res[2] && mainData.mapInfo.res[2].length !== 0) {
                    for (let i = 0; i < Math.ceil(SCENE_BG_MIN_NUM / mainData.mapInfo.res[2].length); i++) {
                        mainData.mapInfo.res[2].forEach((e, j) => {
                            const nodeItem = this.compSceneBgMove.getMidItem();
                            nodeItem.x = nodeItem.width * nodeItem.scale * (mainData.mapInfo.res[2].length * i + j);
                            nodeItem.spriteAsync(
                                `texture/dungeonSceneBg/main/spMIMap${mainData.mapInfo.res[0][0]}Mid${e}`
                            );

                            this.compSceneBgMove.nodeMid.push(nodeItem);
                        });
                    }
                }
                if (mainData.mapInfo.res[3] && mainData.mapInfo.res[3].length !== 0) {
                    for (let i = 0; i < Math.ceil(SCENE_BG_MIN_NUM / mainData.mapInfo.res[3].length); i++) {
                        mainData.mapInfo.res[3].forEach((e, j) => {
                            const nodeItem = this.compSceneBgMove.getNearItem();
                            nodeItem.x = nodeItem.width * nodeItem.scale * (mainData.mapInfo.res[3].length * i + j);
                            nodeItem.spriteAsync(
                                `texture/dungeonSceneBg/main/spMIMap${mainData.mapInfo.res[0][0]}Near${e}`
                            );

                            this.compSceneBgMove.nodeNear.push(nodeItem);
                        });
                    }
                }

                const para: number[][] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.PassesXLevels);
                if (this.nodeLevelTimeTips.childrenCount === 0) {
                    para.forEach(() => {
                        const nodeItem = Loader.getInstance().instantiate(this.nodeLevelTimeTipsItem);
                        this.nodeLevelTimeTips.addChild(nodeItem);
                    });
                }
                para.forEach(([, time], i) => {
                    const nodeItem = this.nodeLevelTimeTips.children[i];
                    nodeItem.x = MathUtils.floor(
                        this.nodeLevelTimeTips.width * ((mainData.mapInfo.time - time) / mainData.mapInfo.time),
                        3
                    );
                });

                if (mainData.chapterId !== mainData.mapInfo.mapTag) {
                    mainData.chapterId = mainData.mapInfo.mapTag;
                    mainData.chapterInfo = TBMainChapter.getInstance().getDataById(mainData.chapterId);
                }
            }
        }

        mainData.isCombated = levelData.bossStatus === 1;
    }

    /**
     * 更新波数信息
     */
    public updateWaveInfo(): void {
        const mainData = this.dungeonMainCtl.getData();
        const isFinalWave = mainData.wave === mainData.totalWave;
        this.lbtLevelName.node.active = !isFinalWave;
        this.nodeWave.active = !isFinalWave && !mainData.isCombated;
        this.nodeWave2.active = !isFinalWave && !mainData.isCombated;
        this.nodeJumpBoss.active = !isFinalWave && mainData.isCombated;
        this.lbtMonsterName.node.active = isFinalWave;
        this.prgMonsterHp.node.active = isFinalWave;
        this.prgLevelTime.node.active = isFinalWave;
        if (!isFinalWave) {
            this.lbtLevelName.string = `${mainData.mapInfo.name}${mainData.mapInfo.reveal}-${mainData.levelInfo.reveal}`;
            if (!mainData.isCombated) {
                this.nodeWave.children.forEach((e, i) => {
                    const nodePassedTag = e.child("spPassedTag");
                    nodePassedTag && (nodePassedTag.active = i + 1 < mainData.wave);
                });
                this.nodeWave2.children.forEach((e, i) => {
                    const nodePassedTag = e.child("spPassedTag");
                    nodePassedTag && (nodePassedTag.active = i + 1 < mainData.wave);
                });
            } else {
                cc.Tween.stopAllByTarget(this.nodeJumpBossBg);
                this.nodeJumpBossBg.angle = 0;
                cc.tween(this.nodeJumpBossBg)
                    .repeatForever(
                        cc
                            .tween()
                            .to(4, { angle: -360 })
                            .call(() => {
                                this.nodeJumpBossBg.angle = 0;
                            })
                    )
                    .start();
            }
        }
        isFinalWave && this.updateLimitTimeState(true);
    }

    /**
     * 更新限制时间状态
     * @param isInit 是否为初始化调用
     */
    public updateLimitTimeState(isInit: boolean = false): void {
        const baseData = this.dungeonMainCtl.getBaseData();
        const mainData = this.dungeonMainCtl.getData();

        if (isInit) {
            baseData.combatTime = 0;
            mainData.limitTime = mainData.mapInfo.time;

            const progressTime = MathUtils.floor(mainData.limitTime / mainData.mapInfo.time, 3);
            this.prgLevelTime.progress = progressTime;
            this.scheduleOnce(() => {
                this.prgLevelTime.barSprite.node.opacity = 255;
            });
        } else {
            const progressTime = MathUtils.floor(mainData.limitTime / mainData.mapInfo.time, 3);
            cc.Tween.stopAllByTarget(this.prgLevelTime);
            cc.tween(this.prgLevelTime)
                .to(0.8, { progress: progressTime })
                .call(() => {
                    this.prgLevelTime.barSprite.node.opacity = progressTime !== 0 ? 255 : 0;
                })
                .start();
        }
        this.lbtLevelTime.string = `${Math.max(MathUtils.ceil(mainData.limitTime, 1), 0)}s`;

        mainData.refreshTime = mainData.refreshDuration;
    }

    /**
     * 更新玩家血量状态
     * @param player 玩家
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public updatePlayerHpState(player: CombatMemberPlayer, isInit: boolean = false, isPlayAni: boolean = false): void {
        const playerBaseData = player.getBaseData();

        if (isInit) {
            const compLeadItem = this.sceneCtl.getLeadItem(playerBaseData.uuid);
            compLeadItem.updateHpState(player, isInit);
        }

        const isPlayLowHpAni = MathUtils.floor(playerBaseData.hp / playerBaseData.totalHp, 3) <= LOW_HP_PARA;
        if (this.isPlayingAni !== isPlayLowHpAni) {
            this.isPlayingAni = isPlayLowHpAni;

            this.playLowHpAni();
        }
    }

    /**
     * 更新玩家伤害状态
     */
    public updatePlayerDamageState(): void {}

    /**
     * 初始化怪兽信息
     */
    public initMonsterInfo(): void {
        const mainBaseData = this.dungeonMainCtl.getBaseData();
        const mainData = this.dungeonMainCtl.getData();

        const allMonster = this.dungeonMainCtl.initAllMonster(mainBaseData.type, mainData.levelId);
        const callInfo = TBMonsterCallFormation.getInstance().getDataById(mainData.levelInfo.formationId);
        const formationInfo = TBMonsterFormation.getInstance().getDataById(callInfo.formation[mainData.wave - 1]);
        const formationData: number[][] = [];
        formationInfo.formationPara.forEach(([enterDelay, ...formation]) => {
            formation.forEach((e) => formationData.push([e, enterDelay]));
        });
        ArrayUtils.shuffle(formationData, true);
        const { width, height } = this.sceneCtl.getMonsterSize(CombatPlayerType.Self);
        const scaleX = this.sceneCtl.getSkillEffectScaleX(CombatPlayerType.Self);
        allMonster.forEach((e) => {
            const monsterData = e.getData();

            const index = formationData.findIndex(([formation]) => {
                switch (monsterData.info.moveType) {
                    case EnumMonsterBaseMoveType.Walk:
                        return WALK_MONSTER_FORMATION.includes(formation);
                    case EnumMonsterBaseMoveType.Flight:
                        return FLY_MONSTER_FORMATION.includes(formation);
                    default:
                        return false;
                }
            });
            const [formation, enterDelay] = formationData[index];
            formationData.splice(index, 1);

            monsterData.initPos = cc.v2(width, (height / MONSTER_FORMATION_NUM) * (formation - 1 + 0.5));
            monsterData.targetPos = cc.v2();
            switch (monsterData.info.moveType) {
                case EnumMonsterBaseMoveType.Walk:
                    monsterData.targetPos.y = (height / MONSTER_FORMATION_NUM) * (WALK_MONSTER_FORMATION.length / 4);
                    monsterData.targetPos.y += (height / 2 / MONSTER_FORMATION_NUM) * (formation - 1 + 0.5);
                    break;
                case EnumMonsterBaseMoveType.Flight:
                    monsterData.targetPos.x = width / 2;
                    monsterData.targetPos.y = monsterData.initPos.y;
                    break;
                default:
                    break;
            }
            monsterData.scaleX = scaleX;
            const radian = Math.atan2(
                monsterData.targetPos.y - monsterData.initPos.y,
                monsterData.targetPos.x - monsterData.initPos.x
            );
            monsterData.angle = MathUtils.a2d(radian);
            monsterData.enterDelay = enterDelay;

            this.sceneCtl.initMonsterItem(CombatPlayerType.Self, e);

            this.updateMonsterHpState(e, true);
        });
    }

    /**
     * 更新怪兽血量状态
     * @param monster 怪兽
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public updateMonsterHpState(
        monster: CombatMemberMonster,
        isInit: boolean = false,
        isPlayAni: boolean = false
    ): void {
        const mainData = this.dungeonMainCtl.getData();
        if (mainData.wave !== mainData.totalWave) {
            return;
        }
        const monsterData = monster.getData();
        if (monsterData.info.type !== EnumMonsterBaseType.BossMonster) {
            return;
        }

        const monsterBaseData = monster.getBaseData();

        let shield = 0;
        monsterBaseData.buffData.forEach((e) => {
            if (e.info.effectType === EnumSkillEffectEffectType.Buff703) {
                e.layerData.forEach(([, , tempShield]) => {
                    shield += tempShield;
                });
            }
        });
        const progressHp = MathUtils.floor(monsterBaseData.hp / (monsterBaseData.totalHp + shield), 3);
        const progressShield = MathUtils.floor((monsterBaseData.hp + shield) / (monsterBaseData.totalHp + shield), 3);
        if (isInit) {
            this.lbtMonsterName.string = monsterData.info.name;
            this.prgMonsterHp.progress = progressHp;
            this.prgMonsterHp2.progress = progressHp;
            this.prgMonsterHp3.progress = progressShield;
            this.scheduleOnce(() => {
                this.prgMonsterHp.barSprite.node.opacity = 255;
                this.prgMonsterHp2.barSprite.node.opacity = 255;
                this.prgMonsterHp3.barSprite.node.opacity = 255;
            });
        } else {
            this.prgMonsterHp.progress = progressHp;
            this.prgMonsterHp.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
            this.prgMonsterHp3.progress = progressShield;
            this.prgMonsterHp3.barSprite.node.opacity = progressShield !== 0 ? 255 : 0;
            if (!isPlayAni) {
                this.prgMonsterHp2.progress = progressHp;
                this.prgMonsterHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
            } else {
                cc.tween(this.prgMonsterHp2)
                    .to(0.8, { progress: progressHp })
                    .call(() => {
                        this.prgMonsterHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                    })
                    .start();
            }

            if (monsterData.state === CombatMemberState.Die) {
                mainData.refreshTime = 0;
            }
        }
        this.lbtMonsterHp.string = `${MathUtils.ceil((monsterBaseData.hp / monsterBaseData.totalHp) * 100, 2)}%`;
    }

    /**
     * 更新开关状态-自动释放技能
     */
    public updateAutoState(): void {
        const allPlayer = this.dungeonMainCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerBaseData = player.getBaseData();

        const nodeIcon = this.nodeAuto.child("spIcon");
        const nodeIcon2 = this.nodeAuto.child("spIcon2");
        cc.Tween.stopAllByTarget(nodeIcon);
        cc.Tween.stopAllByTarget(nodeIcon2);
        nodeIcon.angle = 30;
        nodeIcon2.angle = 0;
        if (playerBaseData.isAuto) {
            cc.tween(nodeIcon)
                .repeatForever(
                    cc
                        .tween()
                        .to(2, { angle: 390 })
                        .call(() => {
                            nodeIcon.angle = 30;
                        })
                )
                .start();
            cc.tween(nodeIcon2)
                .repeatForever(
                    cc
                        .tween()
                        .to(4, { angle: 360 })
                        .call(() => {
                            nodeIcon2.angle = 0;
                        })
                )
                .start();
        }
    }

    /**
     * 检测装备掉落
     */
    private checkEquipDrop(): void {
        const itemDropData: ICombatItemDropData[] = [];
        const equipData = Equip.getInstance().getList();
        const dressedEquipUuid = Equip.getInstance().getEquippedList();
        const mainData = this.dungeonMainCtl.getData();
        equipData.forEach((e) => {
            if (!dressedEquipUuid.includes(e.uuid)) {
                const itemDropUuid = mainData.itemDropUuid++;
                const { width, height } = this.sceneCtl.getMonsterSize(CombatPlayerType.Self);
                const worldPos = this.sceneCtl.getWorldPosByMonsterArea(
                    CombatPlayerType.Self,
                    cc.v2(width / 2, height / 2)
                );
                itemDropData.push({ uuid: itemDropUuid, equipData: e, worldPos, height: height / 4, nodeItem: null });

                mainData.itemDropUuid2.push(itemDropUuid);
            }
        });
        itemDropData.length !== 0 && DungeonMain.getInstance().emit(DungeonMainEvent.ItemDrop, itemDropData);
    }

    /**
     * 判断结果
     * @param dt
     */
    public judgeResult(dt: number): void {
        if (this.isPlayingPassWaveAni) {
            return;
        }

        const baseData = this.dungeonMainCtl.getBaseData();
        const mainData = this.dungeonMainCtl.getData();
        const allPlayer = this.dungeonMainCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const allMonster = this.dungeonMainCtl.getAllMonster();

        if (player.getBaseData().hp <= 0) {
            this.changeState(CombatDungeonState.Failure);
            return;
        }

        if (allMonster.length === 0) {
            if (mainData.wave === mainData.totalWave) {
                if (mainData.itemDropUuid2.length === 0 && baseData.resetTime <= 0) {
                    this.changeState(CombatDungeonState.Ready);
                }
            } else {
                if (mainData.itemDropUuid2.length === 0) {
                    if (mainData.wave + 1 === mainData.totalWave) {
                        this.dungeonMainCtl.resetCombat();
                        this.sceneCtl.resetPlayerInfo(player);
                        this.resetPlayerSkillInfo(player);
                        this.updatePlayerHpState(player, true);
                    }

                    if (CombatLog.getInstance().getShowState(CombatLogId.PerWave)) {
                        this.playPassWaveAni();
                    } else {
                        this.changeState(CombatDungeonState.Ready);
                    }
                }
            }
            return;
        }

        if (mainData.refreshTime > 0) {
            baseData.combatTime += dt;
            mainData.limitTime -= dt;
            mainData.refreshTime -= dt;
            if (mainData.refreshTime <= 0 || mainData.limitTime <= 0) {
                this.updateLimitTimeState();

                if (mainData.limitTime <= 0) {
                    this.changeState(CombatDungeonState.Failure);
                    return;
                }
            }
        }
    }

    /**
     * 播放过波动画
     */
    private playPassWaveAni(): void {
        const baseData = this.dungeonMainCtl.getBaseData();
        const allPlayer = this.dungeonMainCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerBaseData = player.getBaseData();

        this.isPlayingPassWaveAni = true;
        this.compSceneBgMove.setGameSpeed(baseData.gameSpeed);
        this.compSceneBgMove.setMoveState(true);
        const compTankItem = this.sceneCtl.getTankItem(playerBaseData.uuid);
        compTankItem.changeState(CombatMemberState.Enter);
        cc.tween(compTankItem.node)
            .by(2 / baseData.gameSpeed, { x: 150 })
            .call(() => {
                compTankItem.changeState(CombatMemberState.Wait);
            })
            .by(0.5 / baseData.gameSpeed, { x: -150 })
            .call(() => {
                this.compSceneBgMove.setMoveState(false);
                this.isPlayingPassWaveAni = false;

                this.changeState(CombatDungeonState.Ready);
            })
            .start();
    }

    /**
     * 播放通关动画
     */
    private playPassAni(): void {
        const baseData = this.dungeonMainCtl.getBaseData();
        const mainData = this.dungeonMainCtl.getData();
        const allPlayer = this.dungeonMainCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerBaseData = player.getBaseData();
        const levelId = DungeonMain.getInstance().getLevelId();
        const nextLevelInfo = TBMainBarrier.getInstance().getDataById(levelId);
        const isJumpMap = nextLevelInfo && mainData.levelInfo.map !== nextLevelInfo.map;

        this.lbtPassAniLevelName.string = `${mainData.mapInfo.name}${mainData.mapInfo.reveal}-${mainData.levelInfo.reveal}`;
        ImageUtils.setItemIcon(
            this.nodePassAniIncomeItem.child("spIcon"),
            mainData.levelInfo.idlePrestigeEarnings[0][0]
        );
        const idleDuration = TBIdleEarningsTotal.getInstance().getValueByPara(EnumIdleEarningsTotalPara.ProfitTime);
        this.nodePassAniIncomeItem
            .child("lbtText")
            .label(
                `${NumberUtils.format(
                    (mainData.levelInfo.idlePrestigeEarnings[0][1] / idleDuration) * HOUR_TO_SECOND
                )}/h`
            );
        this.nodePassAniIncomeItem
            .child("lbtText2")
            .label(
                `${NumberUtils.format((nextLevelInfo.idlePrestigeEarnings[0][1] / idleDuration) * HOUR_TO_SECOND)}/h`
            );

        cc.Tween.stopAllByTarget(this.nodeLevel);
        cc.tween(this.nodeLevel).to(0.2, { opacity: 0 }).start();

        cc.Tween.stopAllByTarget(this.nodePassAni);
        this.nodePassAni.active = true;
        this.nodePassAni.opacity = 0;
        this.spinePassAniBoom.node.opacity = 0;
        this.spinePassAniBoom.clearTracks();
        cc.tween(this.nodePassAni)
            .delay(0.2)
            .call(() => {
                this.nodePassAni.opacity = 255;
                this.spinePassAniBoom.setAnimation(0, "wait", false);
                this.spinePassAniBoom.node.opacity = 255;
            })
            .delay(3)
            .to(0.2, { opacity: 0 })
            .call(() => {
                this.nodePassAni.active = false;

                const passLevelNum = DungeonMain.getInstance().getPassLevelNum();
                if (passLevelNum > 1) {
                    this.playSkipLevelAni(passLevelNum);
                    return;
                }

                if (isJumpMap) {
                    if (!Guide.getInstance().isGuideShowing() && UI.getInstance().isEmpty()) {
                        UI.getInstance().open("FloatDungeonChapterMain", true);
                    } else {
                        baseData.isPlayEnterAni = true;
                        this.changeState(CombatDungeonState.Reset);
                    }
                    return;
                }

                if (!CombatLog.getInstance().getShowState(CombatLogId.PerLevel)) {
                    this.dungeonMainCtl.resetCombat();
                    this.sceneCtl.resetPlayerInfo(player);
                    this.resetPlayerSkillInfo(player);
                    this.updatePlayerHpState(player, true);

                    this.isPlayingPassWaveAni = true;
                    this.compSceneBgMove.setGameSpeed(baseData.gameSpeed * PASS_LEVEL_ANI_SPEED);
                    this.compSceneBgMove.setMoveState(true);
                    mainData.gameSpeedRate = PASS_LEVEL_ANI_SPEED;
                    Combat.getInstance().emit(CombatEvent.UpdateGameSpeed, baseData.type);
                    const compTankItem = this.sceneCtl.getTankItem(playerBaseData.uuid);
                    compTankItem.changeState(CombatMemberState.Enter);
                    this.spineWind.node.active = true;
                    cc.tween(compTankItem.node)
                        .by(2 / baseData.gameSpeed / PASS_LEVEL_ANI_SPEED, { x: 150 })
                        .call(() => {
                            mainData.gameSpeedRate = 1;
                            Combat.getInstance().emit(CombatEvent.UpdateGameSpeed, baseData.type);
                            compTankItem.changeState(CombatMemberState.Wait);
                            this.spineWind.node.active = false;
                        })
                        .by(0.5 / baseData.gameSpeed / PASS_LEVEL_ANI_SPEED, { x: -150 })
                        .call(() => {
                            this.compSceneBgMove.setMoveState(false);
                            this.isPlayingPassWaveAni = false;

                            this.updateLevelInfo();
                            cc.Tween.stopAllByTarget(this.nodeLevel);
                            cc.tween(this.nodeLevel).to(0.4, { opacity: 255 }).start();
                            const nextLevelInfo2 = TBMainBarrier.getInstance().getDataById(mainData.levelId + 1);
                            const isJumpMap2 = !nextLevelInfo2 || mainData.levelInfo.map !== nextLevelInfo2.map;
                            isJumpMap2 && this.dialogCtl.trigger(EnumCombatDialogueGroupTriggerType.MapEnding);

                            this.changeState(CombatDungeonState.Ready);
                        })
                        .start();
                } else {
                    baseData.isPlayEnterAni = true;
                    this.changeState(CombatDungeonState.Reset);
                }
            })
            .start();

        cc.Tween.stopAllByTarget(this.nodePassAniBg);
        this.nodePassAniBg.scale = 2;
        this.nodePassAniBg.opacity = 0;
        cc.tween(this.nodePassAniBg)
            .delay(1)
            .call(() => {
                this.nodePassAniBg.opacity = 255;
            })
            .to(0.33, { scale: 1 })
            .start();

        this.nodePassAniTips.forEach((e, i) => {
            cc.Tween.stopAllByTarget(e);
            e.opacity = 0;
            cc.tween(e)
                .delay(1.33 + 0.166 * i)
                .to(0.5, { opacity: 255 })
                .start();
        });

        cc.Tween.stopAllByTarget(this.spPassAniTitle.node);
        this.spPassAniTitle.node.y = 0;
        this.spPassAniTitle.node.scale = 0.5;
        this.spPassAniTitle.node.opacity = 0;
        this.nodePassAniStar.opacity = 0;
        this.nodePassAniStar2.opacity = 0;
        cc.tween(this.spPassAniTitle.node)
            .delay(0.2)
            .to(0.5, { scale: 1, opacity: 255 })
            .delay(0.5)
            .to(0.33, { y: 134 + 10 })
            .to(0.17, { y: 134 })
            .call(() => {
                this.nodePassAniStar.opacity = 255;
                this.nodePassAniStar2.opacity = 255;
            })
            .start();
    }

    /**
     * 播放跳关动画
     * @param levelNum 关卡数
     */
    private playSkipLevelAni(levelNum: number): void {
        const baseData = this.dungeonMainCtl.getBaseData();
        const mainData = this.dungeonMainCtl.getData();
        const allPlayer = this.dungeonMainCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerBaseData = player.getBaseData();
        const levelId = DungeonMain.getInstance().getLevelId();
        const nextLevelInfo = TBMainBarrier.getInstance().getDataById(levelId);
        const isJumpMap = nextLevelInfo && mainData.levelInfo.map !== nextLevelInfo.map;

        this.dungeonMainCtl.resetCombat();
        this.sceneCtl.resetPlayerInfo(player);
        this.resetPlayerSkillInfo(player);
        this.updatePlayerHpState(player, true);

        const para: number[][] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.PassesXLevels);
        const index = para.findIndex(([, , tempLevelNum]) => tempLevelNum === levelNum);
        switch (index) {
            case 1:
                this.nodeSkipLevelAni.active = true;
                this.spineSkipLevelAniLight.setCompleteListener(() => {
                    this.spineSkipLevelAniLight.setCompleteListener(null);

                    this.spineSkipLevelAniLight.clearTracks();
                    this.nodeSkipLevelAni.active = false;

                    if (!Guide.getInstance().isGuideShowing() && UI.getInstance().isEmpty()) {
                        if (isJumpMap) {
                            UI.getInstance().open("FloatDungeonChapterMain", true);
                            if (!RedPoint.getInstance().isRecord(RedPointId.DungeonMainSkipLevel)) {
                                UI.getInstance().pushBuffer("FloatDungeonSkipLevelMain", {
                                    levelId: mainData.levelId + 1,
                                    levelId2: nextLevelInfo.id - 1,
                                });
                            }
                            return;
                        } else {
                            if (!RedPoint.getInstance().isRecord(RedPointId.DungeonMainSkipLevel)) {
                                UI.getInstance().open("FloatDungeonSkipLevelMain", {
                                    levelId: mainData.levelId + 1,
                                    levelId2: nextLevelInfo.id - 1,
                                });
                            }
                        }
                    } else {
                        if (isJumpMap) {
                            baseData.isPlayEnterAni = true;
                            this.changeState(CombatDungeonState.Reset);
                            return;
                        }
                    }

                    this.updateLevelInfo();
                    cc.Tween.stopAllByTarget(this.nodeLevel);
                    cc.tween(this.nodeLevel).to(0.4, { opacity: 255 }).start();
                    const nextLevelInfo2 = TBMainBarrier.getInstance().getDataById(mainData.levelId + 1);
                    const isJumpMap2 = !nextLevelInfo2 || mainData.levelInfo.map !== nextLevelInfo2.map;
                    isJumpMap2 && this.dialogCtl.trigger(EnumCombatDialogueGroupTriggerType.MapEnding);

                    this.changeState(CombatDungeonState.Ready);
                });
                this.spineSkipLevelAniLight.setAnimation(0, "wait", false);
                break;
            case 2:
                this.nodeSkipLevelAni.active = true;
                this.spineSkipLevelAniLight.setCompleteListener(() => {
                    this.spineSkipLevelAniLight.setCompleteListener(null);

                    this.spineSkipLevelAniLight.clearTracks();
                    this.nodeSkipLevelAni.active = false;

                    if (!Guide.getInstance().isGuideShowing() && UI.getInstance().isEmpty()) {
                        if (isJumpMap) {
                            UI.getInstance().open("FloatDungeonChapterMain", true);
                            if (!RedPoint.getInstance().isRecord(RedPointId.DungeonMainSkipLevel)) {
                                UI.getInstance().pushBuffer("FloatDungeonSkipLevelMain", {
                                    levelId: mainData.levelId + 1,
                                    levelId2: nextLevelInfo.id - 1,
                                });
                            }
                            return;
                        } else {
                            if (!RedPoint.getInstance().isRecord(RedPointId.DungeonMainSkipLevel)) {
                                UI.getInstance().open("FloatDungeonSkipLevelMain", {
                                    levelId: mainData.levelId + 1,
                                    levelId2: nextLevelInfo.id - 1,
                                });
                            }
                        }
                    } else {
                        if (isJumpMap) {
                            baseData.isPlayEnterAni = true;
                            this.changeState(CombatDungeonState.Reset);
                            return;
                        }
                    }

                    this.updateLevelInfo();
                    cc.Tween.stopAllByTarget(this.nodeLevel);
                    cc.tween(this.nodeLevel).to(0.4, { opacity: 255 }).start();
                    const nextLevelInfo2 = TBMainBarrier.getInstance().getDataById(mainData.levelId + 1);
                    const isJumpMap2 = !nextLevelInfo2 || mainData.levelInfo.map !== nextLevelInfo2.map;
                    isJumpMap2 && this.dialogCtl.trigger(EnumCombatDialogueGroupTriggerType.MapEnding);

                    this.changeState(CombatDungeonState.Ready);
                });
                this.spineSkipLevelAniLight.setAnimation(0, "wait2", false);
                break;
            default:
                this.isPlayingPassWaveAni = true;
                this.compSceneBgMove.setGameSpeed(baseData.gameSpeed * SKIP_LEVEL_ANI_SPEED);
                this.compSceneBgMove.setMoveState(true);
                mainData.gameSpeedRate = SKIP_LEVEL_ANI_SPEED;
                Combat.getInstance().emit(CombatEvent.UpdateGameSpeed, baseData.type);
                const compTankItem = this.sceneCtl.getTankItem(playerBaseData.uuid);
                compTankItem.changeState(CombatMemberState.Enter);
                this.spineWind.node.active = true;
                cc.tween(compTankItem.node)
                    .by(2 / baseData.gameSpeed / SKIP_LEVEL_ANI_SPEED, { x: 150 })
                    .delay(1 / baseData.gameSpeed / SKIP_LEVEL_ANI_SPEED)
                    .call(() => {
                        mainData.gameSpeedRate = 1;
                        Combat.getInstance().emit(CombatEvent.UpdateGameSpeed, baseData.type);
                        compTankItem.changeState(CombatMemberState.Wait);
                        this.spineWind.node.active = false;
                    })
                    .by(0.5 / baseData.gameSpeed / SKIP_LEVEL_ANI_SPEED, { x: -150 })
                    .call(() => {
                        this.compSceneBgMove.setMoveState(false);
                        this.isPlayingPassWaveAni = false;

                        if (!Guide.getInstance().isGuideShowing() && UI.getInstance().isEmpty()) {
                            if (isJumpMap) {
                                UI.getInstance().open("FloatDungeonChapterMain", true);
                                if (!RedPoint.getInstance().isRecord(RedPointId.DungeonMainSkipLevel)) {
                                    UI.getInstance().open("FloatDungeonSkipLevelMain", {
                                        levelId: mainData.levelId + 1,
                                        levelId2: nextLevelInfo.id - 1,
                                    });
                                }
                                return;
                            } else {
                                if (!RedPoint.getInstance().isRecord(RedPointId.DungeonMainSkipLevel)) {
                                    UI.getInstance().open("FloatDungeonSkipLevelMain", {
                                        levelId: mainData.levelId + 1,
                                        levelId2: nextLevelInfo.id - 1,
                                    });
                                }
                            }
                        } else {
                            if (isJumpMap) {
                                baseData.isPlayEnterAni = true;
                                this.changeState(CombatDungeonState.Reset);
                                return;
                            }
                        }

                        this.updateLevelInfo();
                        cc.Tween.stopAllByTarget(this.nodeLevel);
                        cc.tween(this.nodeLevel).to(0.4, { opacity: 255 }).start();
                        const nextLevelInfo2 = TBMainBarrier.getInstance().getDataById(mainData.levelId + 1);
                        const isJumpMap2 = !nextLevelInfo2 || mainData.levelInfo.map !== nextLevelInfo2.map;
                        isJumpMap2 && this.dialogCtl.trigger(EnumCombatDialogueGroupTriggerType.MapEnding);

                        this.changeState(CombatDungeonState.Ready);
                    })
                    .start();
                break;
        }
    }

    /**
     * 播放怪兽来袭动画
     */
    private playMonsterComingAni(): void {
        const mainData = this.dungeonMainCtl.getData();
        const groupInfo = TBMonsterGroup.getInstance().getDataById(mainData.levelInfo.monsterGrouopId);
        const monsterInfo = TBMonsterBase.getInstance().getDataById(groupInfo.monster[mainData.totalWave - 1][0][0]);
        const path = `texture/monster/iconMQBoss${monsterInfo.res}`;
        CocosExt.setSpriteFrameAsync(this.spMonsterAniIcon, path);
        CocosExt.setSpriteFrameAsync(this.spMonsterAniIcon2, path);

        cc.Tween.stopAllByTarget(this.nodeMonsterAni);
        this.nodeMonsterAni.active = true;
        this.nodeMonsterAni.opacity = 255;
        cc.tween(this.nodeMonsterAni)
            .delay(1)
            .to(0.5, { opacity: 0 })
            .call(() => {
                this.nodeMonsterAni.active = false;
            })
            .start();

        cc.Tween.stopAllByTarget(this.nodeMonsterAniMaskBg);
        this.nodeMonsterAniMaskBg.x = 604;
        this.nodeMonsterAniMaskBg.y = 278;
        cc.tween(this.nodeMonsterAniMaskBg).to(0.66, { x: 92, y: 150 }).start();

        cc.Tween.stopAllByTarget(this.nodeMonsterAniBg);
        this.nodeMonsterAniBg.x = 0;
        cc.tween(this.nodeMonsterAniBg).to(0.66, { x: 528 }).start();

        cc.Tween.stopAllByTarget(this.spMonsterAniIcon.node);
        this.spMonsterAniIcon.node.x = 861;
        this.spMonsterAniIcon.node.y = 222;
        this.spMonsterAniIcon.node.scale = 1;
        this.spMonsterAniIcon.node.opacity = 255;
        cc.tween(this.spMonsterAniIcon.node)
            .to(0.56, { x: 510, y: 137 }, { easing: cc.easing.sineOut })
            .to(0.1, { x: 540, y: 144 }, { easing: cc.easing.sineIn })
            .delay(0.34)
            .to(0.5, { scale: 1.2, opacity: 0 })
            .start();

        cc.Tween.stopAllByTarget(this.spMonsterAniIcon2.node);
        this.spMonsterAniIcon2.node.scale = 1;
        this.spMonsterAniIcon2.node.opacity = 0;
        cc.tween(this.spMonsterAniIcon2.node)
            .delay(0.66)
            .to(0.17, { opacity: 255 })
            .to(0.5, { scale: 1.2, opacity: 0 })
            .start();

        cc.Tween.stopAllByTarget(this.spMonsterAniTitle.node);
        this.spMonsterAniTitle.node.scale = 4;
        this.spMonsterAniTitle.node.opacity = 0;
        cc.tween(this.spMonsterAniTitle.node)
            .delay(0.33)
            .to(0.33, { scale: 1, opacity: 255 }, { easing: cc.easing.sineIn })
            .start();

        AudioUtils.playCombatEffect(AUDIO_EFFECT_TYPE.DUNGEON_MAIN_MONSTER_COMING, AUDIO_EFFECT_PATH.DUNGEON);
    }

    /**
     * 播放玩家低血量动画
     */
    private playLowHpAni(): void {
        if (!this.isPlayingAni) {
            cc.Tween.stopAllByTarget(this.nodeLowHpAni);
            this.nodeLowHpAni.active = false;
            this.nodeLowHpAni.opacity = 0;
            return;
        }

        this.nodeLowHpAni.active = true;
        cc.tween(this.nodeLowHpAni)
            .repeatForever(
                cc
                    .tween()
                    .to(1, { opacity: 255 }, { easing: cc.easing.quadOut })
                    .to(1, { opacity: 100 }, { easing: cc.easing.quadIn })
            )
            .start();
    }

    /**
     * 播放关卡重置动画
     */
    private playResetAni(): void {
        cc.Tween.stopAllByTarget(this.nodeResetAni);
        this.nodeResetAni.active = true;
        this.nodeResetAni.opacity = 0;
        this.nodeResetAniBlockEvent.active = true;
        cc.tween(this.nodeResetAni)
            .to(1, { opacity: 255 })
            .call(() => {
                this.updateLevelInfo();
                cc.Tween.stopAllByTarget(this.nodeLevel);
                cc.tween(this.nodeLevel).to(0.4, { opacity: 255 }).start();

                const allPlayer = this.dungeonMainCtl.getAllPlayer();
                const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
                this.dungeonMainCtl.resetCombat();
                this.sceneCtl.resetPlayerInfo(player);
                this.resetPlayerSkillInfo(player);
                this.updatePlayerHpState(player, true);
                this.dialogCtl.clearAllDialog();
                if (this.dungeonMainCtl.getBaseData().isPlayEnterAni) {
                    const playerBaseData = player.getBaseData();
                    const compTankItem = this.sceneCtl.getTankItem(playerBaseData.uuid);
                    compTankItem.setWaitEnterState(playerBaseData.dungeonType);
                }

                this.changeState(CombatDungeonState.Ready);
            })
            .delay(0.2)
            .to(0.4, { opacity: 0 })
            .call(() => {
                this.nodeResetAni.active = false;
                this.nodeResetAniBlockEvent.active = false;
            })
            .start();
    }

    /**
     * 关卡奖励
     */
    protected onClickLevelReward(): void {
        const mainData = this.dungeonMainCtl.getData();
        if (mainData.wave === mainData.totalWave || mainData.isCombated) {
            return;
        }

        UI.getInstance().open("PopupDungeonRewardMain", { levelId: mainData.levelId, isJumpBoss: false });
    }

    /**
     * 清理装备
     */
    protected onClickClearEquip(): void {
        const mainData = this.dungeonMainCtl.getData();
        if (mainData.wave === mainData.totalWave) {
            return;
        }

        UI.getInstance().open("UILead");
    }

    /**
     * 跳转boss
     */
    protected onClickJumpBoss(): void {
        const mainData = this.dungeonMainCtl.getData();
        if (mainData.wave === mainData.totalWave || !mainData.isCombated) {
            return;
        }

        UI.getInstance().open("PopupDungeonRewardMain", { levelId: mainData.levelId, isJumpBoss: true });
    }

    /**
     * 自动-释放技能
     */
    protected onClickAuto(): void {
        const mainBaseData = this.dungeonMainCtl.getBaseData();
        if (!mainBaseData || mainBaseData.state !== CombatDungeonState.Combat) {
            return;
        }
        const { result, msg } = GameSwitch.getInstance().check(GAME_SWITCH_ID.COMBAT_AUTO_RELEASE_SKILL);
        if (!result) {
            Tips.getInstance().info(msg);
            return;
        }

        const playerBaseData = this.dungeonMainCtl
            .getAllPlayer()
            .find((e) => e.getData().type === CombatPlayerType.Self)
            .getBaseData();
        playerBaseData.isAuto = !playerBaseData.isAuto;
        Setting.getInstance().setSwitchState(SettingId.DungeonMainAutoReleaseSkill);
        this.updateAutoState();
        Task.getInstance().setTaskProgress(EnumTaskDetailType.ClickMagicAutoTask, 1);
    }
}
