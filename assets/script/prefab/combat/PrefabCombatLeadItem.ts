/*
 * @Author: chenx
 * @Date: 2024-05-27 16:41:14
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 16:10:10
 */
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import MathUtils from "../../../nsn/util/MathUtils";
import Time from "../../../nsn/util/Time";
import Utils from "../../../nsn/util/Utils";
import { EnumCombatDialogueGroupTriggerType } from "../../data/base/BaseCombatDialogueGroup";
import { EnumSkillType } from "../../data/base/BaseSkill";
import { EnumSkillEffectEffectType } from "../../data/base/BaseSkillEffect";
import { EnumWeaponAttackType } from "../../data/base/BaseWeapon";
import ArcherClientConfig, { ISpineData } from "../../game/ArcherClientConfig";
import Combat, { CombatEvent, CombatType, DungeonType } from "../../game/Combat";
import { CombatMemberState } from "../../game/combat/CombatMember";
import CombatMemberPlayer, { ICombatLeadData, ICombatWeaponData } from "../../game/combat/CombatMemberPlayer";
import { ICombatMemberSkillData } from "../../game/combat/CombatSkill";
import CombatLog, { CombatLogId } from "../../game/CombatLog";
import SpineUtils from "../../utils/SpineUtils";

/**
 * 动画名称
 */
enum AniName {
    Wait = "attackWait", // 待机
    Attack = "closeAttack", // 攻击
    Attack2 = "farAttack", // 攻击
    Cast = "skill1", // 施法
    Cast2 = "skill2", // 施法
}

/**
 * 动画事件名称
 */
const ANI_EVENT_NAME = "trigger";

const { ccclass, property } = cc._decorator;

/**
 * 战斗-主角item
 */
@ccclass
export default class PrefabCombatLeadItem extends I18nComponent {
    @property(sp.Skeleton)
    spineLead: sp.Skeleton = null; // 主角

    @property(cc.Node)
    nodeWeapon: cc.Node = null; // 神器
    @property(cc.Node)
    nodeShowBottom: cc.Node = null; // 表现-下层

    @property(cc.ProgressBar)
    prgHp: cc.ProgressBar = null; // 血量
    @property(cc.ProgressBar)
    prgHp2: cc.ProgressBar = null; // 血量-过渡
    @property(cc.ProgressBar)
    prgHp3: cc.ProgressBar = null; // 血量-护盾

    private data: ICombatLeadData = null; // 主角数据
    private weaponData: ICombatWeaponData = null; // 神器数据

    private spineData: ISpineData = null; // 骨骼数据
    private waitTriggerState: CombatMemberState = null; // 待触发状态
    private stateTime: number = 0; // 状态时间
    private castCb: () => void = null; // 施法回调
    private castTime: number = 0; // 施法时间
    private gameSpeed: number = 1; // 游戏速度
    private isSkipingCombat: boolean = false; // 是否正在跳过战斗

    /**
     * 初始化数据
     * @param data 主角数据
     * @param weaponData 神器数据
     */
    public initData(data: ICombatLeadData, weaponData: ICombatWeaponData): void {
        this.data = data;
        this.weaponData = weaponData;
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatMemberState): void {
        this.data.state = state;

        switch (this.data.state) {
            case CombatMemberState.Init:
                this.waitTriggerState = null;
                this.stateTime = 0;
                this.castCb = null;
                this.castTime = 0;
                this.isSkipingCombat = false;

                this.spineLead.clearTracks();

                cc.Tween.stopAllByTarget(this.nodeWeapon);
                this.nodeWeapon.opacity = 255;
                break;
            case CombatMemberState.Wait:
                if (!this.isSkipingCombat) {
                    if (this.spineLead.animation !== AniName.Wait) {
                        this.spineLead.setAnimation(0, AniName.Wait, true);
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 更新状态
     * @param dt
     */
    public updateState(dt: number): void {
        switch (this.data.state) {
            case CombatMemberState.Cast:
                if (this.stateTime > 0) {
                    this.stateTime -= dt;
                    this.stateTime = MathUtils.round(this.stateTime, 3);
                    if (this.stateTime <= 0) {
                        this.changeState(this.waitTriggerState);
                        this.waitTriggerState = null;
                    }
                }

                if (this.castTime > 0) {
                    this.castTime -= dt;
                    this.castTime = MathUtils.round(this.castTime, 3);
                    if (this.castTime <= 0) {
                        this.castCb();
                        this.castCb = null;
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 重置状态
     */
    public resetState(): void {
        this.changeState(CombatMemberState.Init);
        this.changeState(CombatMemberState.Wait);
    }

    /**
     * 设置特效
     * @param dungeonType 副本类型
     */
    public setEffect(dungeonType: DungeonType): void {
        this.changeState(CombatMemberState.Init);

        SpineUtils.setLeadWithoutAniName(this.spineLead, this.data.info.res, () => {
            this.spineData = ArcherClientConfig.getInstance().getSpineData(`efLead${this.data.info.res}`);

            const scale = this.spineLead.node.scale;
            this.node.width = Math.floor(this.spineLead.node.width * scale);
            this.node.height = Math.floor(this.spineLead.node.height * scale);

            this.prgHp.node.y = this.node.height + 20;
            const combatType = Combat.getInstance().getCombatType(dungeonType);
            switch (combatType) {
                case CombatType.Main:
                case CombatType.Pve:
                    this.prgHp.node.opacity = 255;
                    break;
                case CombatType.Pvp:
                    break;
                default:
                    break;
            }

            // @ts-ignore
            const attachUtil = this.spineLead.attachUtil;
            attachUtil.destroyAllAttachedNodes();
            attachUtil.generateAllAttachedNodes();

            this.changeState(CombatMemberState.Wait);
        });
    }

    /**
     * 获取挂点
     * @param name 名称
     * @returns
     */
    public getAttached(name: string): cc.Node {
        if (name === "bottom") {
            return this.nodeShowBottom;
        }

        // @ts-ignore
        const attachUtil = this.spineLead.attachUtil;
        const nodeAttached: cc.Node[] = attachUtil.getAttachedNodes(name);

        return nodeAttached[0];
    }

    /**
     * 获取伤害点
     * @returns
     */
    public getDamagePoint(): cc.Vec2 {
        return this.node.convertToWorldSpaceAR(cc.v2(0, this.node.height));
    }

    /**
     * 更新血量状态
     * @param player 玩家
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public updateHpState(player: CombatMemberPlayer, isInit: boolean = false, isPlayAni: boolean = false): void {
        const playerBaseData = player.getBaseData();

        let shield = 0;
        playerBaseData.buffData.forEach((e) => {
            if (e.info.effectType === EnumSkillEffectEffectType.Buff703) {
                e.layerData.forEach(([, , tempShield]) => {
                    shield += tempShield;
                });
            }
        });
        const progressHp = MathUtils.floor(playerBaseData.hp / (playerBaseData.totalHp + shield), 3);
        const progressShield = MathUtils.floor((playerBaseData.hp + shield) / (playerBaseData.totalHp + shield), 3);
        if (isInit) {
            this.prgHp.progress = progressHp;
            this.prgHp2.progress = progressHp;
            this.prgHp3.progress = progressShield;
            this.scheduleOnce(() => {
                this.prgHp.barSprite.node.opacity = 255;
                this.prgHp2.barSprite.node.opacity = 255;
                this.prgHp3.barSprite.node.opacity = 255;
            });
        } else {
            this.prgHp.progress = progressHp;
            this.prgHp.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
            this.prgHp3.progress = progressShield;
            this.prgHp3.barSprite.node.opacity = progressShield !== 0 ? 255 : 0;
            if (!isPlayAni) {
                this.prgHp2.progress = progressHp;
                this.prgHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
            } else {
                cc.tween(this.prgHp2)
                    .to(0.8, { progress: progressHp })
                    .call(() => {
                        this.prgHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                    })
                    .start();
            }
        }
    }

    /**
     * 被攻击
     * @param damage 伤害
     * @param player 玩家
     * @param isSkipingCombat 是否正在跳过战斗
     */
    public beAttacked(damage: number, player: CombatMemberPlayer, isSkipingCombat: boolean): void {
        const playerBaseData = player.getBaseData();

        const buffData703 = playerBaseData.buffData.filter(
            (e) => e.info.effectType === EnumSkillEffectEffectType.Buff703 && e.layerData.length !== 0
        );
        for (const e of buffData703) {
            const oldLayerData = Utils.clone(e.layerData);
            for (let i = e.layerData.length - 1; i >= 0; i--) {
                if (damage >= e.layerData[i][2]) {
                    damage -= e.layerData[i][2];
                    e.layerData.splice(i, 1);
                } else {
                    e.layerData[i][2] -= damage;
                    damage = 0;
                }

                if (damage <= 0) {
                    break;
                }
            }

            if (CombatLog.getInstance().getShowState(CombatLogId.BuffClear)) {
                if (CombatLog.getInstance().getShowStateByDungeonType(playerBaseData.dungeonType)) {
                    CombatLog.getInstance().logTitle(CombatLogId.BuffClear, player);

                    cc.log({
                        buffId: e.id,
                        clearAdd: oldLayerData.length - e.layerData.length,
                        oldLayerData,
                        layerData: Utils.clone(e.layerData),
                        maxLayerData: Utils.clone(e.maxLayerData),
                        addMaxLayerData: Utils.clone(e.addMaxLayerData),
                        time: Time.getInstance().now(),
                    });
                }
            }

            if (damage <= 0) {
                break;
            }
        }

        if (damage > 0) {
            const progressHp = MathUtils.floor(playerBaseData.hp / playerBaseData.totalHp, 2);
            playerBaseData.hp = Math.max(playerBaseData.hp - damage, 0);
            const progressHp2 = MathUtils.floor(playerBaseData.hp / playerBaseData.totalHp, 2);

            if (!isSkipingCombat) {
                Combat.getInstance().emit(
                    CombatEvent.TriggerDialog,
                    this.data.dungeonType,
                    EnumCombatDialogueGroupTriggerType.UnderHp,
                    progressHp,
                    progressHp2
                );
            }
        }

        !isSkipingCombat && this.updateHpState(player, false, buffData703.length <= 0);
    }

    /**
     * 设置游戏速度
     * @param speed 速度
     */
    public setGameSpeed(speed: number): void {
        this.gameSpeed = speed;

        this.spineLead.timeScale = this.gameSpeed;
    }

    /**
     * 播放施法动画
     * @param memberSkillData 成员技能数据
     * @param castCb 施法回调
     * @param isSkipingCombat 是否正在跳过战斗
     */
    public playCastAni(memberSkillData: ICombatMemberSkillData, castCb: () => void, isSkipingCombat: boolean): void {
        this.isSkipingCombat = isSkipingCombat;

        let aniName = "";
        let isHideWeapon = false;
        switch (memberSkillData.info.type) {
            case EnumSkillType.LeadAtk:
                switch (this.weaponData.showInfo.attackType) {
                    case EnumWeaponAttackType.CloseAttack:
                        aniName = AniName.Attack;
                        break;
                    case EnumWeaponAttackType.FarAttack:
                        aniName = AniName.Attack2;
                        break;
                    default:
                        break;
                }
                break;
            case EnumSkillType.LeadASkill:
                const index = this.data.info.skillId.findIndex((e) => e === memberSkillData.id);
                switch (index) {
                    case 0:
                        aniName = AniName.Cast;
                        break;
                    case 1:
                        aniName = AniName.Cast2;
                        break;
                    default:
                        break;
                }
                isHideWeapon = true;
                break;
            default:
                break;
        }
        if (!this.isSkipingCombat) {
            this.spineLead.setAnimation(0, aniName, false);
            this.spineLead.addAnimation(0, AniName.Wait, true);

            if (isHideWeapon) {
                cc.Tween.stopAllByTarget(this.nodeWeapon);
                cc.tween(this.nodeWeapon)
                    .to(0.1, { opacity: 0 })
                    .delay(this.spineData[aniName].duration - 0.2)
                    .to(0.1, { opacity: 255 })
                    .start();
            }
        }

        this.waitTriggerState = this.data.state;
        this.stateTime = this.spineData[aniName].duration;

        this.castCb = castCb;
        this.castTime = this.spineData[aniName].event[ANI_EVENT_NAME][0];

        this.changeState(CombatMemberState.Cast);
    }
}
