/*
 * @Author: chenx
 * @Date: 2025-01-07 16:38:49
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-11 15:56:57
 */
import MathUtils from "../../../nsn/util/MathUtils";
import { EnumSkillType } from "../../data/base/BaseSkill";
import { EnumSkillShowFlight } from "../../data/base/BaseSkillShow";
import { CombatShowItemState } from "../../game/Skill";
import ImageUtils from "../../utils/ImageUtils";
import { PrefabCombatShowItem } from "./PrefabCombatShowItem";

/**
 * 重力
 */
const GRAVITY = cc.v2(0, -320);

/**
 * 重力缩放
 */
const GRAVITY_SCALE = 10;

const { ccclass, property } = cc._decorator;

/**
 * 表现item
 */
@ccclass
export default class PrefabCombatShowItem1 extends PrefabCombatShowItem {
    @property(cc.Sprite)
    spShow: cc.Sprite = null; // 表现

    private time: number = 0; // 时间
    private posByPreFrame: cc.Vec2 = null; // 位置-上一帧
    private linearVelocity: cc.Vec2 = null; // 线速度

    public unuse(): void {
        super.unuse();

        this.time = 0;
        this.posByPreFrame = null;
        this.linearVelocity = null;

        this.node.opacity = 0;
        this.node.zIndex = 0;
        this.node.angle = 0;
        this.node.scale = 1;
    }

    /**
     * 切换状态-初始化
     */
    public changeStateByInit(): void {
        this.node.setPosition(this.data.initPos);
        this.data.zIndex !== -1 && (this.node.zIndex = this.data.zIndex);
        this.node.opacity = 0;
        switch (this.data.info.flight) {
            case EnumSkillShowFlight.Straight:
                {
                    const radian = Math.atan2(
                        this.data.targetPos.y - this.data.initPos.y,
                        this.data.targetPos.x - this.data.initPos.x
                    );
                    this.data.angle = MathUtils.a2d(radian);
                    this.node.angle = this.data.angle;

                    const velocity = this.data.info.preformPara[0];
                    this.linearVelocity = cc.v2(
                        velocity * Math.cos(MathUtils.d2a(this.data.angle)),
                        velocity * Math.sin(MathUtils.d2a(this.data.angle))
                    );
                }
                break;
            case EnumSkillShowFlight.Parabolic:
                {
                    this.data.angle = this.data.targetPos.x >= this.data.initPos.x ? 60 : 120;
                    this.node.angle = this.data.angle;

                    this.posByPreFrame = this.data.initPos;
                    const dx = this.data.targetPos.x - this.data.initPos.x;
                    const dy = this.data.targetPos.y - this.data.initPos.y;
                    const tanAngle = Math.tan(MathUtils.d2a(this.data.angle));
                    const tempVelocity =
                        (GRAVITY.y * GRAVITY_SCALE * Math.pow(dx, 2)) /
                        ((dy - dx * tanAngle) / (1 + Math.pow(tanAngle, 2))) /
                        2;
                    const velocity = Math.sqrt(tempVelocity);
                    this.linearVelocity = cc.v2(
                        velocity * Math.cos(MathUtils.d2a(this.data.angle)),
                        velocity * Math.sin(MathUtils.d2a(this.data.angle))
                    );
                }
                break;
            default:
                break;
        }

        if (
            (this.data.info.flight === EnumSkillShowFlight.Straight ||
                this.data.info.flight === EnumSkillShowFlight.Parabolic) &&
            this.skillData.info.type === EnumSkillType.ArcherAtk
        ) {
            ImageUtils.setArrowIcon(this.spShow, this.data.resPre, () => {
                if (!this.data) {
                    return;
                }

                this.node.opacity = 255;
            });
        } else {
            ImageUtils.setSkillEffect(this.spShow, this.data.resPre, () => {
                if (!this.data) {
                    return;
                }

                this.node.opacity = 255;
            });
        }

        this.changeState(CombatShowItemState.Show);
    }

    /**
     * 更新状态-表现
     * @param dt
     */
    public updateStateByShow(dt: number): void {
        if (dt === 0) {
            return;
        }

        this.time += dt;

        let isFinish = false;
        switch (this.data.info.flight) {
            case EnumSkillShowFlight.Straight:
                this.node.x = this.data.initPos.x + this.linearVelocity.x * this.time;
                this.node.y = this.data.initPos.y + this.linearVelocity.y * this.time;

                if (this.data.targetPos.x >= this.data.initPos.x) {
                    isFinish = this.node.x >= this.data.targetPos.x;
                } else {
                    isFinish = this.node.x <= this.data.targetPos.x;
                }
                break;
            case EnumSkillShowFlight.Parabolic:
                this.node.x = this.data.initPos.x + this.linearVelocity.x * this.time;
                this.node.y =
                    this.data.initPos.y +
                    this.linearVelocity.y * this.time +
                    0.5 * GRAVITY.y * GRAVITY_SCALE * Math.pow(this.time, 2);
                this.node.angle = -MathUtils.a2d(
                    cc.v2(this.node.x - this.posByPreFrame.x, this.node.y - this.posByPreFrame.y).signAngle(cc.v2(1, 0))
                );
                this.posByPreFrame = this.node.getPosition().clone();

                if (this.data.targetPos.x >= this.data.initPos.x) {
                    isFinish = this.node.x >= this.data.targetPos.x;
                } else {
                    isFinish = this.node.x <= this.data.targetPos.x;
                }
                break;
            default:
                break;
        }
        isFinish && this.changeState(CombatShowItemState.WaitClear);
    }
}
