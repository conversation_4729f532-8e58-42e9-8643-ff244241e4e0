/*
 * @Author: chenx
 * @Date: 2024-02-23 17:25:24
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-29 10:08:18
 */
import Loader from "../../../nsn/core/Loader";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import Logger from "../../../nsn/util/Logger";
import MathUtils from "../../../nsn/util/MathUtils";
import Time from "../../../nsn/util/Time";
import Utils from "../../../nsn/util/Utils";
import SceneBgMoveComp from "../../comp/SceneBgMoveComp";
import { AUDIO_EFFECT_PATH } from "../../config/AudioEffectConfig";
import { EnumCombatDialogueGroupTriggerType } from "../../data/base/BaseCombatDialogueGroup";
import { EnumMonsterBaseType } from "../../data/base/BaseMonsterBase";
import { EnumSkillType } from "../../data/base/BaseSkill";
import {
    EnumSkillEffectCondition,
    EnumSkillEffectEffectType,
    EnumSkillEffectScope,
    EnumSkillEffectTarget,
} from "../../data/base/BaseSkillEffect";
import {
    EnumSkillShowFlight,
    EnumSkillShowFolder,
    EnumSkillShowPreform,
    EnumSkillShowShake,
    EnumSkillShowTargetType,
} from "../../data/base/BaseSkillShow";
import { EnumTaskDetailType } from "../../data/base/BaseTaskDetail";
import { EnumWeaponResAttacType } from "../../data/base/BaseWeapon";
import ArcherClientConfig from "../../game/ArcherClientConfig";
import Combat, { CombatEvent, CombatType, DungeonType } from "../../game/Combat";
import { CombatDungeon, CombatDungeonState } from "../../game/combat/CombatDungeon";
import { CombatMember, CombatMemberState, CombatMemberType } from "../../game/combat/CombatMember";
import CombatMemberMonster from "../../game/combat/CombatMemberMonster";
import CombatMemberPlayer, { CombatPlayerType } from "../../game/combat/CombatMemberPlayer";
import CombatSkill, { CombatSkillState } from "../../game/combat/CombatSkill";
import CombatLog, { CombatLogId } from "../../game/CombatLog";
import Damage from "../../game/Damage";
import Setting from "../../game/Setting";
import Skill, {
    CombatShowItemState,
    ICombatBuffData,
    ICombatEffectData,
    ICombatShowData,
    ICombatShowItemData,
    ICombatSkillData,
    SkillEffectState,
    SkillEvent,
    SkillShowState,
    SkillState,
} from "../../game/Skill";
import Task from "../../game/Task";
import AudioUtils from "../../utils/AudioUtils";
import PrefabCombatDialog from "./PrefabCombatDialog";
import PrefabCombatItemDrop from "./PrefabCombatItemDrop";
import PrefabCombatScene, { MONSTER_FORMATION_NUM } from "./PrefabCombatScene";
import PrefabCombatShowItem2 from "./PrefabCombatShowItem2";

/**
 * 重力
 */
const GRAVITY = cc.v2(0, -320);

/**
 * 重力缩放
 */
const GRAVITY_SCALE = 10;

const { ccclass, property } = cc._decorator;

/**
 * 副本
 */
@ccclass
export abstract class PrefabCombatDungeon extends I18nComponent {
    @property(cc.Node)
    nodeSceneBg: cc.Node = null; // 场景bg
    @property(SceneBgMoveComp)
    compSceneBgMove: SceneBgMoveComp = null; // 场景bg移动
    @property(cc.Node)
    nodeScene: cc.Node = null; // 场景
    @property(cc.Node)
    nodeSkill: cc.Node = null; // 技能
    @property(cc.Node)
    nodeSkillByDebug: cc.Node = null; // 技能
    @property(cc.Node)
    nodeSkillByDebug2: cc.Node = null; // 技能

    private dungeonCtl: CombatDungeon = null; // 副本ctl
    protected sceneCtl: PrefabCombatScene = null; // 场景ctl
    protected dialogCtl: PrefabCombatDialog = null; // 对话ctl
    protected itemDropCtl: PrefabCombatItemDrop = null; // 道具掉落ctl

    private skillIdByShakeScreenAni: number = -1; // 技能id-震屏动画
    private posSceneBg: cc.Vec2 = null; // 场景bg
    private posScene: cc.Vec2 = null; // 场景
    private isPlayingByShakeScreenAni: boolean = false; // 是否正在播放-震屏动画

    protected onPreDestroy(): void {
        this.sceneCtl && this.sceneCtl.clearAllDamageItem();
    }

    protected onDestroy(): void {
        this.dungeonCtl && this.dungeonCtl.clearCombat();
    }

    protected update(dt: number): void {
        const baseData = this.dungeonCtl.getBaseData();
        if (!baseData || baseData.state === CombatDungeonState.Init) {
            return;
        }

        dt *= baseData.gameSpeed;
        this.updateState(dt);
    }

    protected registerHandler(): void {
        // 技能-释放
        Skill.getInstance().on(
            SkillEvent.Release,
            (dungeonType: DungeonType, memberUuid: number, skillId: number) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (baseData.type !== dungeonType) {
                    return;
                }
                if (baseData.state !== CombatDungeonState.Combat) {
                    return;
                }

                const allPlayer = this.dungeonCtl.getAllPlayer();
                const player = allPlayer.find((e) => e.getBaseData().uuid === memberUuid);
                const playerBaseData = player.getBaseData();
                const playerData = player.getData();
                const monster = this.dungeonCtl.getAllMonster();
                const skill = playerBaseData.skill.find((e) => e.getData().id === skillId);
                const skillData = skill.getData();

                if (!skill.isRelease(true)) {
                    return;
                }
                if (skillData.totalCd === -1) {
                    this.releaseSkill(skill, player, null, null, null, null);
                    return;
                }

                const combatType = Combat.getInstance().getCombatType(playerBaseData.dungeonType);
                switch (combatType) {
                    case CombatType.Main:
                    case CombatType.Pve:
                        const tempMonster = monster.filter((e) => e.isAttack());
                        if (tempMonster.length > 0) {
                            const index = MathUtils.getRandomInt(0, tempMonster.length - 1);
                            const compMonsterItem = this.sceneCtl.getMonsterItem(tempMonster[index].getBaseData().uuid);
                            this.releaseSkill(
                                skill,
                                player,
                                null,
                                tempMonster[index],
                                compMonsterItem.getBeAttackedPoint(),
                                compMonsterItem.getBeAttackedPoint2()
                            );
                        }
                        break;
                    case CombatType.Pvp:
                        let tempPlayer: CombatMemberPlayer = null;
                        switch (playerData.type) {
                            case CombatPlayerType.Self:
                                tempPlayer = allPlayer.find((e) => e.getData().type === CombatPlayerType.Opponent);
                                break;
                            case CombatPlayerType.Opponent:
                                tempPlayer = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
                                break;
                            default:
                                break;
                        }
                        const compTankItem = this.sceneCtl.getTankItem(tempPlayer.getBaseData().uuid);
                        this.releaseSkill(
                            skill,
                            player,
                            null,
                            tempPlayer,
                            compTankItem.getBeAttackedPoint(),
                            compTankItem.getBeAttackedPoint()
                        );
                        break;
                    default:
                        break;
                }
            },
            this
        );
        // 技能-更新增益703
        Skill.getInstance().on(
            SkillEvent.UpdateBuff703,
            (dungeonType: DungeonType, memberType: CombatMemberType, memberUuid: number) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (baseData.type !== dungeonType) {
                    return;
                }

                this.updateBuff703(memberType, memberUuid);
            },
            this
        );
        // 技能-清理表现
        Skill.getInstance().on(
            SkillEvent.ClearShow,
            (dungeonType: DungeonType, skillData: ICombatSkillData[], buffData: ICombatBuffData[]) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (baseData.type !== dungeonType) {
                    return;
                }

                skillData.forEach((e) => {
                    let showData = e.showData.filter((e2) =>
                        [SkillShowState.Show, SkillShowState.Finish].includes(e2.state)
                    );
                    const effectData = e.effectData.filter((e2) =>
                        [SkillEffectState.Show, SkillEffectState.Effect, SkillEffectState.Finish].includes(e2.state)
                    );
                    effectData.forEach((e2) => {
                        showData = showData.concat(
                            e2.showData.filter((e3) => [SkillShowState.Show, SkillShowState.Finish].includes(e3.state))
                        );
                    });

                    showData.forEach((e2) => {
                        e2.itemData.forEach((e3) => {
                            this.sceneCtl.clearShowItem(e3.uuid);
                        });
                    });

                    this.sceneCtl.stopDarkScreenAni(e.id);
                    this.stopShakeScreenAni(e.id);
                });

                buffData.forEach((e) => {
                    const showData = e.showData.filter((e2) =>
                        [SkillShowState.Show, SkillShowState.Finish].includes(e2.state)
                    );

                    showData.forEach((e2) => {
                        e2.itemData.forEach((e3) => {
                            this.sceneCtl.clearShowItem(e3.uuid);
                        });
                    });
                });
            },
            this
        );
        // 技能-清理技能
        Skill.getInstance().on(
            SkillEvent.ClearSkill,
            (dungeonType: DungeonType, skillUuid: string[]) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (baseData.type !== dungeonType) {
                    return;
                }

                skillUuid.forEach((e) => this.sceneCtl.clearSkillItem(e));
            },
            this
        );
        // 战斗-重置怪兽
        Combat.getInstance().on(
            CombatEvent.ResetMonster,
            (dungeonType: DungeonType, monsterUuid: number[]) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (baseData.type !== dungeonType) {
                    return;
                }

                monsterUuid.forEach((e) => this.sceneCtl.clearMonsterItem(e));
            },
            this
        );
        // 战斗-播放震屏动画
        Combat.getInstance().on(
            CombatEvent.PlayShakeScreenAni,
            (dungeonType: DungeonType, type: EnumSkillShowShake, skillId: number) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (baseData.type !== dungeonType) {
                    return;
                }

                this.playShakeScreenAni(type, skillId);
            },
            this
        );
        // 战斗-更新游戏速度
        Combat.getInstance().on(
            CombatEvent.UpdateGameSpeed,
            (dungeonType: DungeonType) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (!baseData || baseData.type !== dungeonType) {
                    return;
                }

                this.dungeonCtl.updateGameSpeed();
                this.compSceneBgMove && this.compSceneBgMove.setGameSpeed(baseData.gameSpeed);
                const allPlayer = this.dungeonCtl.getAllPlayer();
                allPlayer.forEach((e) => this.sceneCtl.updateGameSpeedByPlayer(e));
                const allMonster = this.dungeonCtl.getAllMonster();
                allMonster.forEach((e) => this.sceneCtl.updateGameSpeedByMonster(e));
            },
            this
        );
        // 战斗-场景bg移动
        Combat.getInstance().on(
            CombatEvent.SceneBgMove,
            (dungeonType: DungeonType, isMove: boolean, moveSpeed: number) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (baseData.type !== dungeonType) {
                    return;
                }

                if (this.compSceneBgMove) {
                    this.compSceneBgMove.speedFar = moveSpeed / 4;
                    this.compSceneBgMove.speedMid = moveSpeed / 2;
                    this.compSceneBgMove.speedNear = moveSpeed;
                    this.compSceneBgMove.setGameSpeed(baseData.gameSpeed);
                    this.compSceneBgMove.setMoveState(isMove);
                }
            },
            this
        );
        // 战斗-触发对话
        Combat.getInstance().on(
            CombatEvent.TriggerDialog,
            (dungeonType: DungeonType, type: EnumCombatDialogueGroupTriggerType, ...extraPara: number[]) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (baseData.type !== dungeonType) {
                    return;
                }

                this.dialogCtl.trigger(type, ...extraPara);
            },
            this
        );
        // 战斗-关闭副本
        Combat.getInstance().on(
            CombatEvent.CloseDungeon,
            (type: DungeonType) => {
                const baseData = this.dungeonCtl.getBaseData();
                if (baseData.type !== type) {
                    return;
                }

                baseData.resetTime = 0;
                UI.getInstance().closeToWindow(this.node.name);
                UI.getInstance().close();
                switch (baseData.type) {
                    case DungeonType.Union:
                        UI.getInstance().open("PopupUnionBoss");
                        break;
                    default:
                        break;
                }
            },
            this
        );
    }

    /**
     * 设置副本ctl
     * @param dungeonCtl 副本ctl
     */
    public setDungeonCtl(dungeonCtl: CombatDungeon): void {
        this.dungeonCtl = dungeonCtl;
    }

    /**
     * 加载场景
     */
    public loadScene(): void {
        Loader.getInstance().loadPrefab("prefab/combat/PrefabCombatScene", (prefab) => {
            const node = Loader.getInstance().instantiate(prefab);
            this.nodeScene.addChild(node);

            this.sceneCtl = node.getComponent(PrefabCombatScene);
            this.dialogCtl = node.getComponent(PrefabCombatDialog);
            this.itemDropCtl = node.getComponent(PrefabCombatItemDrop);

            this.sceneCtl.setDungeonCtl(this.dungeonCtl);
            this.sceneCtl.setDialogCtl(this.dialogCtl);

            this.dialogCtl.setDungeonCtl(this.dungeonCtl);
            this.dialogCtl.setSceneCtl(this.sceneCtl);

            this.itemDropCtl.setDungeonCtl(this.dungeonCtl);

            this.changeState(CombatDungeonState.Init);
        });
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatDungeonState): void {
        state !== CombatDungeonState.Init && (this.dungeonCtl.getBaseData().state = state);
    }

    /**
     * 更新状态
     * @param dt
     */
    public abstract updateState(dt: number): void;

    /**
     * 更新状态-加载
     * @param dt
     */
    public updataStateByLoad(dt: number): void {
        const baseData = this.dungeonCtl.getBaseData();
        const allMonster = this.dungeonCtl.getAllMonster();

        if (allMonster.length === baseData.monsterTotalNum) {
            const monsterType: EnumMonsterBaseType[] = [];
            allMonster.forEach((e) => {
                const tempMonsterType = e.getData().info.type;
                !monsterType.includes(tempMonsterType) && monsterType.push(tempMonsterType);
            });
            monsterType.length !== 0 &&
                this.dialogCtl.trigger(EnumCombatDialogueGroupTriggerType.MonsterType, ...monsterType);

            if (baseData.isPlayEnterAni) {
                baseData.isPlayEnterAni = false;
                this.changeState(CombatDungeonState.Enter);
            } else {
                this.changeState(CombatDungeonState.Combat);
            }
        }
    }

    /**
     * 更新关卡信息
     */
    public abstract updateLevelInfo(): void;

    /**
     * 更新波数信息
     */
    public abstract updateWaveInfo(): void;

    /**
     * 更新限制时间状态
     * @param isInit 是否为初始化调用
     */
    public abstract updateLimitTimeState(isInit: boolean): void;

    /**
     * 更新玩家技能信息
     * @param player 玩家
     */
    public updatePlayerSkillInfo(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        for (const e of playerBaseData.skill) {
            const skillData = e.getData();

            if (!skillData.isShow) {
                continue;
            }
            if (this.sceneCtl.getSkillItem(`${playerBaseData.uuid}#${skillData.id}`)) {
                continue;
            }

            const combatType = Combat.getInstance().getCombatType(playerBaseData.dungeonType);
            switch (playerData.type) {
                case CombatPlayerType.Self:
                    switch (combatType) {
                        case CombatType.Main:
                            this.sceneCtl.initSkillItem(
                                `${playerBaseData.uuid}#${skillData.id}`,
                                CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow)
                                    ? this.nodeSkillByDebug
                                    : this.nodeSkill
                            );
                            break;
                        case CombatType.Pve:
                        case CombatType.Pvp:
                            this.sceneCtl.initSkillItem2(
                                `${playerBaseData.uuid}#${skillData.id}`,
                                CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow)
                                    ? this.nodeSkillByDebug
                                    : this.nodeSkill
                            );
                            break;
                        default:
                            break;
                    }
                    break;
                case CombatPlayerType.Opponent:
                    switch (combatType) {
                        case CombatType.Pvp:
                            this.sceneCtl.initSkillItem2(
                                `${playerBaseData.uuid}#${skillData.id}`,
                                this.nodeSkillByDebug2
                            );
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            const compItem = this.sceneCtl.getSkillItem(`${playerBaseData.uuid}#${skillData.id}`);
            compItem.initData(skillData);
        }
    }

    /**
     * 重置玩家技能信息
     * @param player 玩家
     */
    public resetPlayerSkillInfo(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();

        for (const e of playerBaseData.skill) {
            const skillData = e.getData();

            if (!skillData.isShow) {
                continue;
            }

            const compItem = this.sceneCtl.getSkillItem(`${playerBaseData.uuid}#${skillData.id}`);
            compItem.updateCdState();
        }
    }

    /**
     * 更新玩家血量状态
     * @param player 玩家
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public abstract updatePlayerHpState(player: CombatMemberPlayer, isInit: boolean, isPlayAni: boolean): void;

    /**
     * 更新玩家伤害状态
     */
    public abstract updatePlayerDamageState(): void;

    /**
     * 初始化怪兽信息
     */
    public abstract initMonsterInfo(): void;

    /**
     * 更新怪兽血量状态
     * @param monster 怪兽
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public abstract updateMonsterHpState(monster: CombatMemberMonster, isInit: boolean, isPlayAni: boolean): void;

    /**
     * 技能行为
     * @param member 成员
     * @param dt
     */
    public actionSkill(member: CombatMember[], dt: number): void {
        const baseData = this.dungeonCtl.getBaseData();

        member.forEach((tempMember) => {
            const memberBaseData = tempMember.getBaseData();

            const initSkillData = memberBaseData.skillData.filter((e) => e.state === SkillState.Init);
            initSkillData.forEach((e) => {
                e.state = SkillState.Show;
            });

            const showSkillData = memberBaseData.skillData.filter((e) => e.state === SkillState.Show);
            for (const e of showSkillData) {
                if (
                    e.showData.findIndex((e2) => ![SkillShowState.Finish, SkillShowState.Clear].includes(e2.state)) ===
                    -1
                ) {
                    e.state = SkillState.Effect;
                    continue;
                }

                const initShowData = e.showData.filter((e2) => e2.state === SkillShowState.Init);
                initShowData.forEach((e2) => {
                    const lastShowData = e.showData.find((e3) => e3.info.nextShow.includes(e2.id));
                    if (!lastShowData || [SkillShowState.Finish, SkillShowState.Clear].includes(lastShowData.state)) {
                        e2.state = SkillShowState.Show;

                        this.triggerShow(e2, e);

                        !baseData.isSkipingCombat &&
                            e2.info.darkSwitch.includes(1) &&
                            this.sceneCtl.playDarkScreenAni(e.id);
                    }
                });
            }

            const effectSkillData = memberBaseData.skillData.filter((e) => e.state === SkillState.Effect);
            effectSkillData.forEach((e) => {
                const initEffectData = e.effectData.filter((e2) => e2.state === SkillEffectState.Init);
                initEffectData.forEach((e2) => {
                    const lastEffectData = e.effectData.filter((e3) => e3.nextEffectId === e2.id);
                    if (
                        lastEffectData.length === 0 ||
                        lastEffectData.findIndex(
                            (e3) => ![SkillEffectState.Finish, SkillEffectState.Clear].includes(e3.state)
                        ) === -1
                    ) {
                        this.triggerEffect(tempMember, e2, e);
                    }
                });

                const delayEffectData = e.effectData.filter((e2) => e2.state === SkillEffectState.Delay);
                delayEffectData.forEach((e2) => {
                    e2.delay -= dt;
                    e2.delay <= 0 && (e2.state = SkillEffectState.Show);
                });

                const showEffectData = e.effectData.filter((e2) => e2.state === SkillEffectState.Show);
                for (const e2 of showEffectData) {
                    if (
                        e2.showData.findIndex(
                            (e3) => ![SkillShowState.Finish, SkillShowState.Clear].includes(e3.state)
                        ) === -1
                    ) {
                        e2.state = SkillEffectState.Effect;
                        continue;
                    }

                    const initShowData = e2.showData.filter((e3) => e3.state === SkillShowState.Init);
                    initShowData.forEach((e3) => {
                        const lastShowData = e2.showData.find((e4) => e4.info.nextShow.includes(e3.id));
                        if (
                            !lastShowData ||
                            [SkillShowState.Finish, SkillShowState.Clear].includes(lastShowData.state)
                        ) {
                            e3.state = SkillShowState.Show;

                            this.triggerShow(e3, e);

                            !baseData.isSkipingCombat &&
                                e3.info.darkSwitch.includes(1) &&
                                this.sceneCtl.playDarkScreenAni(e.id);
                        }
                    });
                }

                const finishEffectData = e.effectData.filter((e2) => e2.state === SkillEffectState.Finish);
                finishEffectData.forEach((e2) => {
                    if (e2.showData.findIndex((e3) => e3.state !== SkillShowState.Clear) === -1) {
                        e2.state = SkillEffectState.Clear;

                        if (e.info.cd === -1) {
                            switch (e2.info.condition) {
                                case EnumSkillEffectCondition.Condition1:
                                case EnumSkillEffectCondition.Condition12:
                                case EnumSkillEffectCondition.Condition13:
                                case EnumSkillEffectCondition.Condition14:
                                case EnumSkillEffectCondition.Condition15:
                                    break;
                                default:
                                    Skill.getInstance().resetEffect(e2);
                                    break;
                            }
                        }
                    }
                });

                if (e.effectData.findIndex((e2) => e2.state !== SkillEffectState.Clear) === -1) {
                    e.state = SkillState.Finish;
                }
            });

            const finishSkillData = memberBaseData.skillData.filter((e) => e.state === SkillState.Finish);
            finishSkillData.forEach((e) => {
                if (e.showData.findIndex((e2) => e2.state !== SkillShowState.Clear) === -1) {
                    e.state = SkillState.Clear;
                }
            });

            for (let i = memberBaseData.skillData.length - 1; i >= 0; i--) {
                if (memberBaseData.skillData[i].state === SkillState.Clear) {
                    memberBaseData.skillData.splice(i, 1);
                }
            }
        });
    }

    /**
     * 技能冷却行为
     * @param player 玩家
     * @param monster 怪兽
     * @param dt
     */
    public actionSkillCd(player: CombatMemberPlayer[], monster: CombatMemberMonster[], dt: number): void {
        const baseData = this.dungeonCtl.getBaseData();

        player.forEach((e) => {
            const playerBaseData = e.getBaseData();

            playerBaseData.skill.forEach((e2) => {
                const skillData = e2.getData();

                switch (skillData.state) {
                    case CombatSkillState.Cd:
                        e2.updateState(dt);

                        if (skillData.cd !== -1 && skillData.isShow && !baseData.isSkipingCombat) {
                            const compItem = this.sceneCtl.getSkillItem(`${playerBaseData.uuid}#${skillData.id}`);
                            compItem.updateCdState();
                        }
                        break;
                    default:
                        e2.updateState(dt);
                        break;
                }
            });
        });

        monster.forEach((e) => {
            const monsterBaseData = e.getBaseData();
            const monsterData = e.getData();

            monsterBaseData.skill.forEach((e2) => {
                let isUpdate = true;
                switch (e2.getData().info.type) {
                    case EnumSkillType.MonsterASkill:
                        isUpdate = [
                            CombatMemberState.Move,
                            CombatMemberState.Wait,
                            CombatMemberState.Cast,
                            CombatMemberState.BeAttacked,
                        ].includes(monsterData.state);
                        break;
                    case EnumSkillType.MonsterAtk:
                        isUpdate = [
                            CombatMemberState.Wait,
                            CombatMemberState.Cast,
                            CombatMemberState.BeAttacked,
                        ].includes(monsterData.state);
                        break;
                    default:
                        break;
                }
                isUpdate && e2.updateState(dt);
            });
        });
    }

    /**
     * 技能释放行为
     * @param player 玩家
     * @param monster 怪兽
     * @param dt
     */
    public actionSkillRelease(player: CombatMemberPlayer[], monster: CombatMemberMonster[], dt: number): void {
        if (!CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillManualRelease)) {
            for (const e of player) {
                const playerBaseData = e.getBaseData();
                const playerData = e.getData();

                playerBaseData.skill.sort((a, b) => {
                    const skillDataA = a.getData();
                    const skillDataB = b.getData();
                    if (skillDataA.priority === -1 && skillDataB.priority !== -1) {
                        return 1;
                    }
                    if (skillDataA.priority !== -1 && skillDataB.priority === -1) {
                        return -1;
                    }
                    return skillDataA.priority - skillDataB.priority;
                });
                for (const e2 of playerBaseData.skill) {
                    if (!e2.isRelease(playerBaseData.isAuto)) {
                        continue;
                    }
                    if (e2.getData().totalCd === -1) {
                        this.releaseSkill(e2, e, null, null, null, null);
                        continue;
                    }

                    const combatType = Combat.getInstance().getCombatType(playerBaseData.dungeonType);
                    switch (combatType) {
                        case CombatType.Main:
                        case CombatType.Pve:
                            const tempMonster = monster.filter((e3) => e3.isAttack());
                            if (tempMonster.length > 0) {
                                const index = MathUtils.getRandomInt(0, tempMonster.length - 1);
                                const compMonsterItem = this.sceneCtl.getMonsterItem(
                                    tempMonster[index].getBaseData().uuid
                                );
                                this.releaseSkill(
                                    e2,
                                    e,
                                    null,
                                    tempMonster[index],
                                    compMonsterItem.getBeAttackedPoint(),
                                    compMonsterItem.getBeAttackedPoint2()
                                );
                            }
                            break;
                        case CombatType.Pvp:
                            let tempPlayer: CombatMemberPlayer = null;
                            switch (playerData.type) {
                                case CombatPlayerType.Self:
                                    tempPlayer = player.find((e3) => e3.getData().type === CombatPlayerType.Opponent);
                                    break;
                                case CombatPlayerType.Opponent:
                                    tempPlayer = player.find((e3) => e3.getData().type === CombatPlayerType.Self);
                                    break;
                                default:
                                    break;
                            }
                            const compTankItem = this.sceneCtl.getTankItem(tempPlayer.getBaseData().uuid);
                            this.releaseSkill(
                                e2,
                                e,
                                null,
                                tempPlayer,
                                compTankItem.getBeAttackedPoint(),
                                compTankItem.getBeAttackedPoint()
                            );
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        if (!CombatLog.getInstance().getShowState(CombatLogId.MonsterSkillManualRelease)) {
            for (const e of monster) {
                const monsterBaseData = e.getBaseData();

                monsterBaseData.skill.sort((a, b) => {
                    const skillDataA = a.getData();
                    const skillDataB = b.getData();
                    if (skillDataA.priority === -1 && skillDataB.priority !== -1) {
                        return 1;
                    }
                    if (skillDataA.priority !== -1 && skillDataB.priority === -1) {
                        return -1;
                    }
                    return skillDataA.priority - skillDataB.priority;
                });
                for (const e2 of monsterBaseData.skill) {
                    if (!e2.isRelease(monsterBaseData.isAuto)) {
                        continue;
                    }
                    if (e2.getData().totalCd === -1) {
                        this.releaseSkill(e2, null, e, null, null, null);
                        continue;
                    }

                    const tempPlayer = player.find((e3) => e3.getData().type === CombatPlayerType.Self);
                    const compTankItem = this.sceneCtl.getTankItem(tempPlayer.getBaseData().uuid);
                    this.releaseSkill(
                        e2,
                        null,
                        e,
                        tempPlayer,
                        compTankItem.getBeAttackedPoint(),
                        compTankItem.getBeAttackedPoint()
                    );
                }
            }
        }
    }

    /**
     * 技能表现行为
     * @param allMember 成员
     * @param dt
     */
    public actionSkillShow(allMember: CombatMember[], dt: number): void {
        allMember.forEach((member) => {
            const memberBaseData = member.getBaseData();

            const showData: [ICombatSkillData, ICombatShowData][] = [];
            const skillData = memberBaseData.skillData.filter((e) =>
                [SkillState.Show, SkillState.Effect, SkillState.Finish].includes(e.state)
            );
            skillData.forEach((e) => {
                e.showData.forEach((e2) => {
                    showData.push([e, e2]);
                });

                const effectData = e.effectData.filter((e2) =>
                    [SkillEffectState.Show, SkillEffectState.Effect, SkillEffectState.Finish].includes(e2.state)
                );
                effectData.forEach((e2) => {
                    e2.showData.forEach((e3) => {
                        showData.push([e, e3]);
                    });
                });
            });
            showData.forEach(([skillData, e]) => {
                switch (e.state) {
                    case SkillShowState.Show:
                    case SkillShowState.Finish:
                        e.itemData.forEach((e2) => {
                            switch (e2.state) {
                                case CombatShowItemState.Show:
                                case CombatShowItemState.Finish:
                                    const compShowItem = this.sceneCtl.getShowItem(e2.uuid);
                                    if (compShowItem) {
                                        compShowItem.updateState(dt);
                                    } else {
                                        e2.state = CombatShowItemState.WaitClear;
                                    }
                                    break;
                                case CombatShowItemState.WaitClear:
                                    this.sceneCtl.clearShowItem(e2.uuid);

                                    e2.state = CombatShowItemState.Clear;

                                    switch (e2.info.flight) {
                                        case EnumSkillShowFlight.Straight:
                                        case EnumSkillShowFlight.Parabolic:
                                            this.sceneCtl.playShowAudio(e2);
                                            break;
                                        default:
                                            break;
                                    }
                                    break;
                                case CombatShowItemState.Skip:
                                    e2.skipTime > 0 && (e2.skipTime -= dt);
                                    e2.skipTime <= 0 && (e2.state = CombatShowItemState.Clear);
                                    break;
                                default:
                                    break;
                            }
                        });

                        if (
                            e.state === SkillShowState.Show &&
                            e.itemData.findIndex((e2) =>
                                [CombatShowItemState.Init, CombatShowItemState.Show, CombatShowItemState.Skip].includes(
                                    e2.state
                                )
                            ) === -1
                        ) {
                            e.state = SkillShowState.Finish;
                        }

                        if (e.itemData.findIndex((e2) => e2.state !== CombatShowItemState.Clear) === -1) {
                            e.state = SkillShowState.Clear;

                            e.info.darkSwitch.includes(2) && this.sceneCtl.stopDarkScreenAni(skillData.id);
                        }
                        break;
                    default:
                        break;
                }
            });
        });
    }

    /**
     * 技能效果行为
     * @param player 玩家
     * @param monster 怪兽
     * @param dt
     */
    public actionSkillEffect(player: CombatMemberPlayer[], monster: CombatMemberMonster[], dt: number): void {
        [...player, ...monster].forEach((member) => {
            const memberBaseData = member.getBaseData();

            Skill.getInstance().updateEffectTriggerPara(
                member,
                memberBaseData.skillData,
                EnumSkillEffectCondition.Condition5,
                dt
            );

            const effectSkillData = memberBaseData.skillData.filter((e) => e.state === SkillState.Effect);
            effectSkillData.forEach((e) => {
                const effectEffectData = e.effectData.filter((e2) => e2.state === SkillEffectState.Effect);
                effectEffectData.forEach((e2) => {
                    this.effectEffect(e2, e, player, monster);

                    Skill.getInstance().updateEffectTriggerPara(
                        member,
                        memberBaseData.skillData,
                        EnumSkillEffectCondition.Condition2,
                        e2.id
                    );
                });
            });
        });
    }

    /**
     * 增益行为
     * @param allMember 成员
     * @param dt
     */
    public actionBuff(allMember: CombatMember[], dt: number): void {
        allMember.forEach((member) => {
            const memberBaseData = member.getBaseData();

            for (let i = memberBaseData.buffData.length - 1; i >= 0; i--) {
                const buffData = memberBaseData.buffData[i];

                let clearAdd = 0;
                let clearMax = 0;
                let clearAddMax = 0;
                for (let j = buffData.layerData.length - 1; j >= 0; j--) {
                    const layerData = buffData.layerData[j];

                    if (layerData[0] > 0) {
                        layerData[0] -= dt;
                        if (layerData[0] <= 0) {
                            buffData.layerData.splice(j, 1);

                            clearAdd++;
                        }
                    }
                }
                for (let j = buffData.maxLayerData.length - 1; j >= 0; j--) {
                    const maxLayerData = buffData.maxLayerData[j];

                    if (maxLayerData[0] > 0) {
                        maxLayerData[0] -= dt;
                        if (maxLayerData[0] <= 0) {
                            buffData.maxLayerData.splice(j, 1);

                            clearMax++;
                        }
                    }
                }
                for (let j = buffData.addMaxLayerData.length - 1; j >= 0; j--) {
                    const addMaxLayerData = buffData.addMaxLayerData[j];

                    if (addMaxLayerData[0] > 0) {
                        addMaxLayerData[0] -= dt;
                        if (addMaxLayerData[0] <= 0) {
                            buffData.addMaxLayerData.splice(j, 1);

                            clearAddMax++;
                        }
                    }
                }

                if (
                    (clearAdd !== 0 || clearMax !== 0 || clearAddMax !== 0) &&
                    CombatLog.getInstance().getShowState(CombatLogId.BuffClear)
                ) {
                    if (CombatLog.getInstance().getShowStateByDungeonType(memberBaseData.dungeonType)) {
                        CombatLog.getInstance().logTitle(CombatLogId.BuffClear, member);

                        cc.log({
                            buffId: buffData.id,
                            clearAdd,
                            clearMax,
                            clearAddMax,
                            layerData: Utils.clone(buffData.layerData),
                            maxLayerData: Utils.clone(buffData.maxLayerData),
                            addMaxLayerData: Utils.clone(buffData.addMaxLayerData),
                            time: Time.getInstance().now(),
                        });
                    }
                }

                for (let j = buffData.showData.length - 1; j >= 0; j--) {
                    const showData = buffData.showData[j];

                    switch (showData.state) {
                        case SkillShowState.Init:
                            showData.state = SkillShowState.Show;

                            this.triggerShow(showData, buffData.skillData);
                            break;
                        case SkillShowState.Show:
                            if (buffData.layerData.length !== 0 || buffData.addMaxLayerData.length !== 0) {
                                continue;
                            }

                            showData.itemData.forEach((e) => {
                                switch (e.state) {
                                    case CombatShowItemState.Show:
                                        const compShowItem = this.sceneCtl.getShowItem(e.uuid) as PrefabCombatShowItem2;
                                        if (compShowItem) {
                                            compShowItem.playAni("skill2", "skill3", false, () => {
                                                compShowItem.changeState(CombatShowItemState.WaitClear);
                                            });

                                            compShowItem.changeState(CombatShowItemState.Finish);
                                        } else {
                                            e.state = CombatShowItemState.WaitClear;
                                        }
                                        break;
                                    case CombatShowItemState.Skip:
                                        e.state = CombatShowItemState.Clear;
                                        break;
                                    default:
                                        break;
                                }
                            });

                            showData.state = SkillShowState.Finish;
                            break;
                        case SkillShowState.Finish:
                            showData.itemData.forEach((e) => {
                                switch (e.state) {
                                    case CombatShowItemState.WaitClear:
                                        this.sceneCtl.clearShowItem(e.uuid);

                                        e.state = CombatShowItemState.Clear;

                                        switch (e.info.flight) {
                                            case EnumSkillShowFlight.Straight:
                                            case EnumSkillShowFlight.Parabolic:
                                                this.sceneCtl.playShowAudio(e);
                                                break;
                                            default:
                                                break;
                                        }
                                        break;
                                    default:
                                        break;
                                }
                            });

                            if (showData.itemData.findIndex((e) => e.state !== CombatShowItemState.Clear) === -1) {
                                showData.state = SkillShowState.Clear;

                                buffData.showData.splice(j, 1);
                            }
                            break;
                        default:
                            break;
                    }
                }

                if (
                    buffData.layerData.length === 0 &&
                    buffData.addMaxLayerData.length === 0 &&
                    buffData.showData.length === 0
                ) {
                    if (buffData.targetMember) {
                        const targetMemberBaseData = buffData.targetMember.getBaseData();

                        switch (buffData.info.effectType) {
                            case EnumSkillEffectEffectType.Buff201:
                                buffData.targetMember.updateAttrBySkill();
                                break;
                            case EnumSkillEffectEffectType.Buff703:
                                this.updateBuff703(targetMemberBaseData.type, targetMemberBaseData.uuid);
                                break;
                            default:
                                break;
                        }
                    }

                    Skill.getInstance().updateEffectTriggerPara(
                        buffData.skillData.releaseMember,
                        buffData.skillData.releaseMember.getBaseData().skillData,
                        EnumSkillEffectCondition.Condition10,
                        buffData.id
                    );

                    memberBaseData.buffData.splice(i, 1);
                }
            }

            memberBaseData.buffData.forEach((e) => this.effectSkillBuffByTime(e, dt));
        });
    }

    /**
     * 触发表现
     * @param showData 表现数据
     * @param skillData 技能数据
     */
    private triggerShow(showData: ICombatShowData, skillData: ICombatSkillData): void {
        const baseData = this.dungeonCtl.getBaseData();
        const releaseMemberBaseData = skillData.releaseMember.getBaseData();
        const releasePlayer =
            releaseMemberBaseData.type === CombatMemberType.Player
                ? (skillData.releaseMember as CombatMemberPlayer)
                : null;
        const releasePlayerData = releasePlayer && releasePlayer.getData();
        const targetMemberBaseData = skillData.targetMember ? skillData.targetMember.getBaseData() : null;

        const showItemData: ICombatShowItemData = {
            state: CombatShowItemState.Init,

            uuid: baseData.showUuid++,
            info: showData.info,
            dungeonType: baseData.type,

            targetType: showData.info.targetType,
            prefabType: showData.info.preform,
            resFolder: showData.info.folder,
            resPre: showData.info.resEffectPre,
            resSuf: showData.info.resEffectSuffix,
            initPos: cc.v2(),
            targetPos: cc.v2(),
            zIndex: -1,
            angle: 0,
            scaleX: 1,

            audioRes: showData.info.resSound,

            skipTime: 0,
        };
        switch (showItemData.info.flight) {
            case EnumSkillShowFlight.Straight:
            case EnumSkillShowFlight.Parabolic:
                switch (skillData.info.type) {
                    case EnumSkillType.LeadAtk:
                        switch (releasePlayerData.weaponData.showInfo.resAttacType) {
                            case EnumWeaponResAttacType.PictureRes:
                                showItemData.targetType = EnumSkillShowTargetType.TargetType1;
                                showItemData.prefabType = EnumSkillShowPreform.Picture1;
                                showItemData.resFolder = EnumSkillShowFolder.Not;
                                showItemData.resPre = releasePlayerData.weaponData.showInfo.resAttac;
                                showItemData.resSuf = "";
                                break;
                            case EnumWeaponResAttacType.SpineRes:
                                showItemData.targetType = EnumSkillShowTargetType.TargetType2;
                                showItemData.prefabType = EnumSkillShowPreform.Spine;
                                showItemData.resFolder = EnumSkillShowFolder.Weapon;
                                showItemData.resPre = releasePlayerData.weaponData.showInfo.resAttac;
                                showItemData.resSuf = "";
                                break;
                            default:
                                break;
                        }
                        showItemData.audioRes = releasePlayerData.weaponData.showInfo.soundAtk;
                        break;
                    case EnumSkillType.ArcherAtk:
                        const archerData = releasePlayerData.archerData.find(
                            (e) => e.id !== -1 && e.info.attackId === skillData.id
                        );
                        showItemData.resFolder = EnumSkillShowFolder.Not;
                        showItemData.resPre = archerData.arrowInfo.res;
                        showItemData.resSuf = "";
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        switch (showItemData.info.flight) {
            case EnumSkillShowFlight.Not:
                switch (showItemData.info.targetType) {
                    case EnumSkillShowTargetType.TargetType2:
                    case EnumSkillShowTargetType.TargetType3:
                        {
                            const [zIndex] = showItemData.info.targetPara[0];
                            const [y, y2, x, x2] = showItemData.info.targetPara[1];

                            showItemData.zIndex = zIndex;
                            const nodeMonster = this.sceneCtl.getMonster(releasePlayerData.type);
                            const monsterScaleX = this.sceneCtl.getSkillEffectScaleX(releasePlayerData.type);
                            showItemData.initPos = cc.v2(
                                ((x + x2) / 2) * monsterScaleX,
                                ((y - 1 + y2) * (nodeMonster.height / MONSTER_FORMATION_NUM)) / 2
                            );
                            showItemData.initPos = nodeMonster.convertToWorldSpaceAR(showItemData.initPos);
                            showItemData.targetPos = showItemData.initPos.clone();
                        }
                        break;
                    case EnumSkillShowTargetType.TargetType8:
                        {
                            const [, x, y] = showItemData.info.targetPara;

                            showItemData.initPos = cc.v2(x, y);
                            showItemData.targetPos = showItemData.initPos.clone();
                        }
                        break;
                    case EnumSkillShowTargetType.TargetType9:
                        {
                            const [, x, y] = showItemData.info.targetPara;

                            showItemData.initPos = cc.v2(x, y);
                            showItemData.targetPos = showItemData.initPos.clone();
                        }
                        break;
                    case EnumSkillShowTargetType.TargetType13:
                        {
                            const [zIndex] = showItemData.info.targetPara[0];
                            const [y, y2, x, x2] = showItemData.info.targetPara[1];
                            const [x3, y3] = showItemData.info.targetPara[2];

                            showItemData.zIndex = zIndex;
                            const nodeMonster = this.sceneCtl.getMonster(releasePlayerData.type);
                            const monsterScaleX = this.sceneCtl.getSkillEffectScaleX(releasePlayerData.type);
                            showItemData.initPos = cc.v2(
                                ((x + x2) / 2) * monsterScaleX,
                                ((y - 1 + y2) * (nodeMonster.height / MONSTER_FORMATION_NUM)) / 2
                            );
                            showItemData.initPos = nodeMonster.convertToWorldSpaceAR(showItemData.initPos);
                            showItemData.initPos.x += x3;
                            showItemData.initPos.y += y3;
                            showItemData.targetPos = showItemData.initPos.clone();
                        }
                        break;
                    case EnumSkillShowTargetType.TargetType14:
                        {
                            const [zIndex] = showItemData.info.targetPara[0];
                            const [x, y] = showItemData.info.targetPara[1];

                            showItemData.zIndex = zIndex;
                            showItemData.initPos = cc.v2(x, y);
                            showItemData.targetPos = showItemData.initPos.clone();
                        }
                        break;
                    default:
                        break;
                }
                break;
            case EnumSkillShowFlight.NotFlight:
                if (targetMemberBaseData.uuid === -1) {
                    showItemData.initPos = skillData.targetPoint;
                    showItemData.targetPos = showItemData.initPos.clone();
                } else {
                    switch (targetMemberBaseData.type) {
                        case CombatMemberType.Player:
                            const compTankItem = this.sceneCtl.getTankItem(targetMemberBaseData.uuid);
                            showItemData.initPos = compTankItem.getBeAttackedPoint();
                            showItemData.targetPos = showItemData.initPos.clone();
                            break;
                        case CombatMemberType.Monster:
                            const compMonsterItem = this.sceneCtl.getMonsterItem(targetMemberBaseData.uuid);
                            showItemData.initPos = compMonsterItem.getBeAttackedPoint();
                            showItemData.targetPos = showItemData.initPos.clone();
                            break;
                        default:
                            break;
                    }
                }
                break;
            case EnumSkillShowFlight.NotFlight2:
                if (targetMemberBaseData.uuid === -1) {
                    showItemData.initPos = skillData.targetPoint2;
                    showItemData.targetPos = showItemData.initPos.clone();
                } else {
                    switch (targetMemberBaseData.type) {
                        case CombatMemberType.Player:
                            const compTankItem = this.sceneCtl.getTankItem(targetMemberBaseData.uuid);
                            showItemData.initPos = compTankItem.getBeAttackedPoint();
                            showItemData.targetPos = showItemData.initPos.clone();
                            break;
                        case CombatMemberType.Monster:
                            const compMonsterItem = this.sceneCtl.getMonsterItem(targetMemberBaseData.uuid);
                            showItemData.initPos = compMonsterItem.getBeAttackedPoint2();
                            showItemData.targetPos = showItemData.initPos.clone();
                            break;
                        default:
                            break;
                    }
                }
                break;
            case EnumSkillShowFlight.Straight:
            case EnumSkillShowFlight.Parabolic:
                switch (skillData.info.type) {
                    case EnumSkillType.LeadASkill:
                    case EnumSkillType.LeadAtk:
                    case EnumSkillType.WeaponASkill:
                    case EnumSkillType.WeaponPSkill:
                        const compWeaponItem = this.sceneCtl.getWeaponItem(releaseMemberBaseData.uuid);
                        showItemData.initPos = compWeaponItem.getAttackPoint();
                        break;
                    case EnumSkillType.ArcherASkill:
                    case EnumSkillType.ArcherPSkill:
                    case EnumSkillType.ArcherAtk:
                        const archerData = releasePlayerData.archerData.find(
                            (e) =>
                                e.id !== -1 &&
                                (e.info.skillId.includes(skillData.id) || e.info.attackId === skillData.id)
                        );
                        const compArcherItem = this.sceneCtl.getArcherItem(
                            `${releaseMemberBaseData.uuid}#${archerData.gridId}`
                        );
                        showItemData.initPos = compArcherItem.node.convertToWorldSpaceAR(cc.v2());
                        break;
                    case EnumSkillType.PetASkill:
                    case EnumSkillType.PetPSkill:
                        const compPetItem = this.sceneCtl.getPetItem(releaseMemberBaseData.uuid);
                        showItemData.initPos = compPetItem.getAttackPoint();
                        break;
                    case EnumSkillType.MonsterASkill:
                    case EnumSkillType.MonsterAtk:
                        const compMonsterItem = this.sceneCtl.getMonsterItem(releaseMemberBaseData.uuid);
                        showItemData.initPos = compMonsterItem.node.convertToWorldSpaceAR(cc.v2());
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        switch (showItemData.info.flight) {
            case EnumSkillShowFlight.Not:
            case EnumSkillShowFlight.NotFlight:
            case EnumSkillShowFlight.NotFlight2:
                break;
            case EnumSkillShowFlight.Straight:
            case EnumSkillShowFlight.Parabolic:
                if (targetMemberBaseData.uuid === -1) {
                    showItemData.targetPos = skillData.targetPoint2;
                } else {
                    switch (targetMemberBaseData.type) {
                        case CombatMemberType.Player:
                            const compTankItem = this.sceneCtl.getTankItem(targetMemberBaseData.uuid);
                            showItemData.targetPos = compTankItem.getBeAttackedPoint();
                            break;
                        case CombatMemberType.Monster:
                            const compMonsterItem = this.sceneCtl.getMonsterItem(targetMemberBaseData.uuid);
                            showItemData.targetPos = compMonsterItem.getBeAttackedPoint2();
                            break;
                        default:
                            break;
                    }
                }
                break;
            default:
                break;
        }
        switch (releasePlayerData.type) {
            case CombatPlayerType.Self:
                showItemData.scaleX = 1;
                break;
            case CombatPlayerType.Opponent:
                showItemData.scaleX = -1;
                break;
            default:
                break;
        }

        if (baseData.isSkipingCombat) {
            showItemData.state = CombatShowItemState.Skip;

            switch (showItemData.info.flight) {
                case EnumSkillShowFlight.Not:
                case EnumSkillShowFlight.NotFlight:
                case EnumSkillShowFlight.NotFlight2:
                    const spineData = ArcherClientConfig.getInstance().getSpineData(
                        `ef${showItemData.resPre}Skill${showItemData.resSuf}`
                    );
                    if (spineData.skill.event.effect) {
                        showItemData.skipTime = spineData.skill.event.effect[0];
                    } else {
                        showItemData.skipTime = showItemData.info.delayTime;
                    }
                    break;
                case EnumSkillShowFlight.Straight:
                    {
                        const radian = Math.atan2(
                            showItemData.targetPos.y - showItemData.initPos.y,
                            showItemData.targetPos.x - showItemData.initPos.x
                        );
                        const angle = MathUtils.a2d(radian);
                        const velocity = showItemData.info.preformPara[0];
                        const linearVelocityX = velocity * Math.cos(MathUtils.d2a(angle));
                        showItemData.skipTime = MathUtils.round(
                            (showItemData.targetPos.x - showItemData.initPos.x) / linearVelocityX,
                            3
                        );
                    }
                    break;
                case EnumSkillShowFlight.Parabolic:
                    {
                        const dx = showItemData.targetPos.x - showItemData.initPos.x;
                        const dy = showItemData.targetPos.y - showItemData.initPos.y;
                        const angle = 60;
                        const tanAngle = Math.tan(MathUtils.d2a(angle));
                        const tempVelocity =
                            (GRAVITY.y * GRAVITY_SCALE * Math.pow(dx, 2)) /
                            ((dy - dx * tanAngle) / (1 + Math.pow(tanAngle, 2))) /
                            2;
                        const velocity = Math.sqrt(tempVelocity);
                        const linearVelocityX = velocity * Math.cos(MathUtils.d2a(angle));
                        showItemData.skipTime = MathUtils.round(
                            (showItemData.targetPos.x - showItemData.initPos.x) / linearVelocityX,
                            3
                        );
                    }
                    break;
                default:
                    break;
            }

            showData.itemData.push(showItemData);
        } else {
            this.sceneCtl.playShowAni(showItemData, showData, skillData);
        }
    }

    /**
     * 触发效果
     * @param member 成员
     * @param effectData 效果数据
     * @param skillData 技能数据
     */
    private triggerEffect(member: CombatMember, effectData: ICombatEffectData, skillData: ICombatSkillData): void {
        let isTrigger = false;
        let isLog = false;
        switch (effectData.info.condition) {
            case EnumSkillEffectCondition.Condition1:
            case EnumSkillEffectCondition.Condition12:
            case EnumSkillEffectCondition.Condition13:
            case EnumSkillEffectCondition.Condition14:
            case EnumSkillEffectCondition.Condition15:
                isTrigger = true;
                break;
            case EnumSkillEffectCondition.Condition2:
            case EnumSkillEffectCondition.Condition3:
            case EnumSkillEffectCondition.Condition4:
                {
                    const index = effectData.triggerPara.findIndex(([, base, cur]) => cur >= base);
                    isTrigger = index !== -1;
                    isTrigger && (effectData.triggerPara[index][2] -= effectData.triggerPara[index][1]);

                    isLog = true;
                }
                break;
            case EnumSkillEffectCondition.Condition5:
            case EnumSkillEffectCondition.Condition9:
                isTrigger = effectData.triggerPara[0][1] >= effectData.triggerPara[0][0];
                isTrigger && (effectData.triggerPara[0][1] -= effectData.triggerPara[0][0]);

                isLog = true;
                break;
            case EnumSkillEffectCondition.Condition6:
            case EnumSkillEffectCondition.Condition10:
                {
                    const index = effectData.triggerPara.findIndex(([, cur]) => cur === 1);
                    isTrigger = index !== -1;
                    isTrigger && (effectData.triggerPara[index][1] = 0);

                    isLog = true;
                }
                break;
            case EnumSkillEffectCondition.Condition11:
                isTrigger = false;
                break;
            default:
                break;
        }

        if (isTrigger && isLog && CombatLog.getInstance().getShowState(CombatLogId.EffectTriggerTypeTrigger)) {
            if (CombatLog.getInstance().getShowStateByDungeonType(member.getBaseData().dungeonType)) {
                CombatLog.getInstance().logTitle(CombatLogId.EffectTriggerTypeTrigger, member);

                cc.log({
                    triggerType: effectData.info.condition,
                    skillId: skillData.id,
                    effectId: effectData.id,
                    triggerPara: Utils.clone(effectData.triggerPara),
                });
            }
        }

        if (isTrigger && effectData.info.probability < 1) {
            let prob = effectData.info.probability;
            const buffData = skillData.releaseMember.getBaseData().buffData.filter((e) => {
                if (e.info.effectType === EnumSkillEffectEffectType.Buff701 && e.layerData.length !== 0) {
                    const [, , , , , , ...effectId] = e.para;

                    return effectId.includes(effectData.id);
                }

                return false;
            });
            buffData.forEach((e) => {
                const [init, max] = e.para;
                prob += Math.min(init * e.layerData.length, max);

                Skill.getInstance().clearBuffLayer(skillData.releaseMember, e, e.layerData.length, true);
            });
            const randomProb = MathUtils.getRandomValue(0, 1);
            isTrigger = randomProb <= prob;

            if (CombatLog.getInstance().getShowState(CombatLogId.EffectTriggerTypeProb)) {
                const memberBaseData = member.getBaseData();
                if (CombatLog.getInstance().getShowStateByDungeonType(memberBaseData.dungeonType)) {
                    CombatLog.getInstance().logTitle(CombatLogId.EffectTriggerTypeProb, member);

                    const increaseProbBuffData: { init: number; max: number; layer: number }[] = [];
                    buffData.forEach((e) => {
                        const [init, max] = e.para;
                        increaseProbBuffData.push({ init, max, layer: e.layerData.length });
                    });
                    cc.log({
                        skillId: skillData.id,
                        effectId: effectData.id,
                        prob,
                        baseProb: effectData.info.probability,
                        increaseProbBuffData,
                        randomProb,
                        isTrigger,
                    });
                }
            }
        }

        switch (effectData.info.condition) {
            case EnumSkillEffectCondition.Condition1:
            case EnumSkillEffectCondition.Condition12:
            case EnumSkillEffectCondition.Condition13:
            case EnumSkillEffectCondition.Condition14:
            case EnumSkillEffectCondition.Condition15:
                if (isTrigger) {
                    effectData.state = SkillEffectState.Delay;
                } else {
                    effectData.state = SkillEffectState.Finish;
                }
                break;
            case EnumSkillEffectCondition.Condition2:
            case EnumSkillEffectCondition.Condition3:
            case EnumSkillEffectCondition.Condition4:
            case EnumSkillEffectCondition.Condition5:
            case EnumSkillEffectCondition.Condition6:
            case EnumSkillEffectCondition.Condition9:
            case EnumSkillEffectCondition.Condition10:
                isTrigger && (effectData.state = SkillEffectState.Delay);
                break;
            case EnumSkillEffectCondition.Condition11:
                break;
            default:
                break;
        }
    }

    /**
     * 触发增益
     * @param member 成员
     * @param buffData 增益数据
     */
    private triggerBuff(member: CombatMember, buffData: ICombatBuffData[]): void {
        for (const e of buffData) {
            if (e.isTrigger) {
                continue;
            }

            let isLog = false;
            switch (e.info.condition) {
                case EnumSkillEffectCondition.Condition12:
                case EnumSkillEffectCondition.Condition13:
                case EnumSkillEffectCondition.Condition14:
                    {
                        const index = e.triggerPara.findIndex(([, base, cur]) => cur >= base);
                        e.isTrigger = index !== -1;
                        e.isTrigger && (e.triggerPara[index][2] -= e.triggerPara[index][1]);

                        isLog = true;
                    }
                    break;
                case EnumSkillEffectCondition.Condition15:
                    {
                        const index = e.triggerPara.findIndex(([, cur]) => cur === 1);
                        e.isTrigger = index !== -1;
                        e.isTrigger && (e.triggerPara[index][1] = 0);

                        isLog = true;
                    }
                    break;
                default:
                    break;
            }

            if (e.isTrigger && isLog && CombatLog.getInstance().getShowState(CombatLogId.BuffTriggerTypeTrigger)) {
                if (CombatLog.getInstance().getShowStateByDungeonType(member.getBaseData().dungeonType)) {
                    CombatLog.getInstance().logTitle(CombatLogId.BuffTriggerTypeTrigger, member);

                    cc.log({
                        triggerType: e.info.condition,
                        buffId: e.id,
                        triggerPara: Utils.clone(e.triggerPara),
                    });
                }
            }
        }
    }

    /**
     * 生效效果
     * @param effectData 效果数据
     * @param skillData 技能数据
     * @param player 玩家
     * @param monster 怪兽
     */
    private effectEffect(
        effectData: ICombatEffectData,
        skillData: ICombatSkillData,
        player: CombatMemberPlayer[],
        monster: CombatMemberMonster[]
    ): void {
        const releaseMemberBaseData = skillData.releaseMember.getBaseData();

        this.updateEffectTarget(effectData, skillData, player, monster);
        Skill.getInstance().updateEffectPara(effectData, skillData);
        switch (effectData.info.effectType) {
            case EnumSkillEffectEffectType.Buff101:
            case EnumSkillEffectEffectType.Buff103:
                this.effectEffectBy101Or103(effectData, skillData);
                break;
            case EnumSkillEffectEffectType.Buff201:
            case EnumSkillEffectEffectType.Buff302:
            case EnumSkillEffectEffectType.Buff701:
            case EnumSkillEffectEffectType.Buff703:
            case EnumSkillEffectEffectType.Buff704:
                effectData.targetMember.forEach(
                    (e) => e.isAttack() && Skill.getInstance().triggerBuff(effectData, skillData, e)
                );

                effectData.state = SkillEffectState.Finish;
                break;
            case EnumSkillEffectEffectType.Buff601:
                {
                    const [, , , effectId] = effectData.para;

                    const tempEffectData = skillData.effectData.find((e) => e.id === effectId);
                    Skill.getInstance().updateEffectPara(tempEffectData, skillData);
                    effectData.targetMember.forEach(
                        (e) => e.isAttack() && Skill.getInstance().triggerBuff(effectData, skillData, e)
                    );

                    effectData.state = SkillEffectState.Finish;
                    tempEffectData.state = SkillEffectState.Finish;
                }
                break;
            case EnumSkillEffectEffectType.Buff602:
                {
                    const [clearEffectId, clearNum, layerEffectType, effectNum] = effectData.para;
                    let allEffectNum = 0;
                    const effectMember: [CombatMember, number][] = [];
                    effectData.targetMember.forEach((e) => {
                        const targetMemberBaseData = e.getBaseData();

                        const buffData = targetMemberBaseData.buffData.find((e2) => e2.id === clearEffectId);
                        if (buffData) {
                            const times = Math.floor(buffData.layerData.length / clearNum);
                            if (times !== 0) {
                                Skill.getInstance().clearBuffLayer(e, buffData, clearNum * times);
                                allEffectNum += effectNum * times;
                                effectMember.push([e, effectNum * times]);
                            }
                        }
                    });

                    effectData.state = SkillEffectState.Finish;

                    const effectEffectData = skillData.effectData.filter((e) => e.id === effectData.nextEffectId);
                    effectEffectData.forEach((e) => {
                        switch (layerEffectType) {
                            case 1:
                                switch (e.info.effectType) {
                                    case EnumSkillEffectEffectType.Buff302:
                                        e.para[2] *= allEffectNum;
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 2:
                                if (e.info.scope === EnumSkillEffectScope.IgnoreTarget) {
                                    effectMember.forEach(([e2, num]) => {
                                        for (let i = 0; i < num; i++) {
                                            e.targetMember.push(e2);
                                        }
                                    });
                                }
                                break;
                            default:
                                break;
                        }
                    });

                    if (CombatLog.getInstance().getShowState(CombatLogId.Buff602)) {
                        if (CombatLog.getInstance().getShowStateByDungeonType(releaseMemberBaseData.dungeonType)) {
                            CombatLog.getInstance().logTitle(CombatLogId.Buff602, skillData.releaseMember);

                            const logData = {
                                skillId: skillData.id,
                                effectId: effectData.id,
                                para: Utils.clone(effectData.para),
                                allEffectNum,
                                effectMember,
                                effectEffectData: [],
                            };
                            effectEffectData.forEach((e) => {
                                const tempEffectEffectData = { para: Utils.clone(e.para), targetMemberUuid: [] };
                                e.targetMember.forEach((e2) =>
                                    tempEffectEffectData.targetMemberUuid.push(e2.getBaseData().uuid)
                                );

                                logData.effectEffectData.push(tempEffectEffectData);
                            });
                            cc.log(logData);
                        }
                    }
                }
                break;
            case EnumSkillEffectEffectType.Buff702:
                {
                    const [effectNum] = effectData.para;

                    effectData.state = SkillEffectState.Finish;

                    const effectEffectData = skillData.effectData.filter((e) => e.id === effectData.nextEffectId);
                    effectEffectData.forEach((e) => {
                        switch (e.info.effectType) {
                            case EnumSkillEffectEffectType.Buff101:
                            case EnumSkillEffectEffectType.Buff103:
                                if (e.info.scope === EnumSkillEffectScope.IgnoreTarget) {
                                    effectData.targetMember.forEach((e2) => {
                                        for (let i = 0; i < effectNum; i++) {
                                            e.targetMember.push(e2);
                                        }
                                    });
                                }
                                break;
                            default:
                                break;
                        }
                    });

                    if (CombatLog.getInstance().getShowState(CombatLogId.Buff702)) {
                        if (CombatLog.getInstance().getShowStateByDungeonType(releaseMemberBaseData.dungeonType)) {
                            CombatLog.getInstance().logTitle(CombatLogId.Buff702, skillData.releaseMember);

                            const logData = {
                                skillId: skillData.id,
                                effectId: effectData.id,
                                para: effectData.para,
                                effectData: Utils.clone(effectData),
                                effectEffectData: [],
                            };
                            effectEffectData.forEach((e) => {
                                const tempEffectEffectData = { targetMemberUuid: [] };
                                e.targetMember.forEach((e2) =>
                                    tempEffectEffectData.targetMemberUuid.push(e2.getBaseData().uuid)
                                );

                                logData.effectEffectData.push(tempEffectEffectData);
                            });
                            cc.log(logData);
                        }
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 生效效果-101/103
     * @param effectData 效果数据
     * @param skillData 技能数据
     */
    private effectEffectBy101Or103(effectData: ICombatEffectData, skillData: ICombatSkillData): void {
        const baseData = this.dungeonCtl.getBaseData();
        const releaseMemberBaseData = skillData.releaseMember.getBaseData();

        switch (effectData.info.target) {
            case EnumSkillEffectTarget.Oneself:
                effectData.targetMember.forEach(
                    (e) => e.isAttack() && Skill.getInstance().triggerBuff(effectData, skillData, e)
                );
                break;
            case EnumSkillEffectTarget.Enemy:
                for (const e of effectData.targetMember) {
                    const targetMemberBaseData = e.getBaseData();

                    const buffData704 = targetMemberBaseData.buffData.filter(
                        (e2) => e2.info.effectType === EnumSkillEffectEffectType.Buff704 && e2.layerData.length !== 0
                    );
                    if (buffData704.length > 0) {
                        Skill.getInstance().clearBuffLayer(e, buffData704[0], 1);
                        continue;
                    }

                    const damageData = Damage.getInstance().getDamageData(
                        effectData.info,
                        effectData.para,
                        skillData,
                        skillData.releaseMember,
                        e
                    );
                    const buffData703 = targetMemberBaseData.buffData.filter(
                        (e2) => e2.info.effectType === EnumSkillEffectEffectType.Buff703 && e2.layerData.length !== 0
                    );
                    switch (targetMemberBaseData.type) {
                        case CombatMemberType.Player:
                            const compLeadItem = this.sceneCtl.getLeadItem(targetMemberBaseData.uuid);
                            compLeadItem.beAttacked(
                                damageData.damage,
                                e as CombatMemberPlayer,
                                baseData.isSkipingCombat
                            );
                            const compTankItem = this.sceneCtl.getTankItem(targetMemberBaseData.uuid);
                            compTankItem.beAttacked(
                                skillData.info.triggerBlow === 0,
                                skillData.info.triggerBlow === 1,
                                releaseMemberBaseData.dungeonType,
                                baseData.isSkipingCombat
                            );

                            this.updatePlayerHpState(e as CombatMemberPlayer, false, buffData703.length <= 0);
                            this.sceneCtl.playDamageAni(damageData, compLeadItem.getDamagePoint());
                            break;
                        case CombatMemberType.Monster:
                            const compMonsterItem = this.sceneCtl.getMonsterItem(targetMemberBaseData.uuid);
                            compMonsterItem.beAttacked(
                                damageData.damage,
                                skillData.info.triggerBlow === 1,
                                baseData.isSkipingCombat
                            );

                            this.updateMonsterHpState(e as CombatMemberMonster, false, buffData703.length <= 0);
                            this.sceneCtl.playDamageAni(damageData, compMonsterItem.getDamagePoint());
                            break;
                        default:
                            break;
                    }
                    if (releaseMemberBaseData.type === CombatMemberType.Player) {
                        const tempPlayer = skillData.releaseMember as CombatMemberPlayer;
                        const tempPlayerData = tempPlayer.getData();
                        tempPlayerData.damage += damageData.damage;
                        tempPlayerData.skillDamage[skillData.id] =
                            (tempPlayerData.skillDamage[skillData.id] || 0) + damageData.damage;
                    }

                    Skill.getInstance().updateEffectTriggerPara(
                        skillData.releaseMember,
                        releaseMemberBaseData.skillData,
                        EnumSkillEffectCondition.Condition6,
                        damageData.sourceType
                    );
                    Skill.getInstance().updateEffectTriggerPara(
                        e,
                        targetMemberBaseData.skillData,
                        EnumSkillEffectCondition.Condition9,
                        1
                    );

                    Skill.getInstance().updateBuffTriggerPara(
                        skillData.releaseMember,
                        releaseMemberBaseData.buffData,
                        EnumSkillEffectCondition.Condition12,
                        effectData.id
                    );
                    Skill.getInstance().updateBuffTriggerPara(
                        skillData.releaseMember,
                        releaseMemberBaseData.buffData,
                        EnumSkillEffectCondition.Condition13,
                        skillData.info.type
                    );
                    Skill.getInstance().updateBuffTriggerPara(
                        skillData.releaseMember,
                        releaseMemberBaseData.buffData,
                        EnumSkillEffectCondition.Condition14,
                        skillData.id
                    );
                    Skill.getInstance().updateBuffTriggerPara(
                        skillData.releaseMember,
                        releaseMemberBaseData.buffData,
                        EnumSkillEffectCondition.Condition15,
                        damageData.sourceType
                    );
                    this.triggerBuff(skillData.releaseMember, releaseMemberBaseData.buffData);
                    for (const e2 of releaseMemberBaseData.buffData) {
                        if (!e2.isTrigger) {
                            continue;
                        }
                        if (
                            ![EnumSkillEffectEffectType.Buff101, EnumSkillEffectEffectType.Buff103].includes(
                                e2.info.effectType
                            )
                        ) {
                            continue;
                        }

                        e2.layerData.forEach(() => {
                            const tempDamageData = Damage.getInstance().getDamageData(
                                e2.info,
                                e2.para,
                                e2.skillData,
                                e2.skillData.releaseMember,
                                e
                            );
                            const tempBuffData703 = targetMemberBaseData.buffData.filter(
                                (e3) =>
                                    e3.info.effectType === EnumSkillEffectEffectType.Buff703 &&
                                    e3.layerData.length !== 0
                            );
                            switch (targetMemberBaseData.type) {
                                case CombatMemberType.Player:
                                    const compLeadItem = this.sceneCtl.getLeadItem(targetMemberBaseData.uuid);
                                    compLeadItem.beAttacked(
                                        tempDamageData.damage,
                                        e as CombatMemberPlayer,
                                        baseData.isSkipingCombat
                                    );
                                    const compTankItem = this.sceneCtl.getTankItem(targetMemberBaseData.uuid);
                                    compTankItem.beAttacked(
                                        false,
                                        false,
                                        releaseMemberBaseData.dungeonType,
                                        baseData.isSkipingCombat
                                    );

                                    this.updatePlayerHpState(
                                        e as CombatMemberPlayer,
                                        false,
                                        tempBuffData703.length <= 0
                                    );
                                    this.sceneCtl.playDamageAni(tempDamageData, compLeadItem.getDamagePoint());
                                    break;
                                case CombatMemberType.Monster:
                                    const compMonsterItem = this.sceneCtl.getMonsterItem(targetMemberBaseData.uuid);
                                    compMonsterItem.beAttacked(tempDamageData.damage, false, baseData.isSkipingCombat);

                                    this.updateMonsterHpState(
                                        e as CombatMemberMonster,
                                        false,
                                        tempBuffData703.length <= 0
                                    );
                                    this.sceneCtl.playDamageAni(tempDamageData, compMonsterItem.getDamagePoint());
                                    break;
                                default:
                                    break;
                            }
                            if (releaseMemberBaseData.type === CombatMemberType.Player) {
                                const tempPlayer = skillData.releaseMember as CombatMemberPlayer;
                                const tempPlayerData = tempPlayer.getData();
                                tempPlayerData.damage += tempDamageData.damage;
                                tempPlayerData.skillDamage[e2.skillData.id] =
                                    (tempPlayerData.skillDamage[e2.skillData.id] || 0) + tempDamageData.damage;
                            }
                        });

                        e2.isTrigger = false;
                    }
                }
                break;
            default:
                break;
        }

        effectData.state = SkillEffectState.Finish;
    }

    /**
     * 更新效果目标
     * @param effectData 效果数据
     * @param skillData 技能数据
     * @param player 玩家
     * @param monster 怪兽
     */
    private updateEffectTarget(
        effectData: ICombatEffectData,
        skillData: ICombatSkillData,
        player: CombatMemberPlayer[],
        monster: CombatMemberMonster[]
    ): void {
        const releaseMemberBaseData = skillData.releaseMember.getBaseData();
        const releasePlayer =
            releaseMemberBaseData.type === CombatMemberType.Player
                ? (skillData.releaseMember as CombatMemberPlayer)
                : null;
        const releasePlayerData = releasePlayer && releasePlayer.getData();
        const combatType = Combat.getInstance().getCombatType(releaseMemberBaseData.dungeonType);

        switch (effectData.info.scope) {
            case EnumSkillEffectScope.IgnoreTarget:
                break;
            case EnumSkillEffectScope.Follow:
                {
                    const tempEffectData = skillData.effectData.find((e) => e.nextEffectId === effectData.id);
                    tempEffectData.targetMember.forEach((e) => {
                        if (e.isAttack()) {
                            effectData.targetMember.push(e);
                        }
                    });
                }
                break;
            case EnumSkillEffectScope.FollowEffect:
                {
                    const tempEffectData = skillData.effectData.find((e) => e.id === effectData.info.scopePara[0]);
                    tempEffectData.targetMember.forEach((e) => {
                        if (e.isAttack()) {
                            effectData.targetMember.push(e);
                        }
                    });
                }
                break;
            case EnumSkillEffectScope.FrontMonomer:
                switch (effectData.info.target) {
                    case EnumSkillEffectTarget.Oneself:
                        if (skillData.releaseMember.isAttack()) {
                            effectData.targetMember.push(skillData.releaseMember);
                        }
                        break;
                    case EnumSkillEffectTarget.Enemy:
                        if (skillData.targetMember) {
                            if (skillData.targetMember.isAttack()) {
                                effectData.targetMember.push(skillData.targetMember);
                            }
                        } else {
                            switch (releaseMemberBaseData.type) {
                                case CombatMemberType.Player:
                                    switch (combatType) {
                                        case CombatType.Main:
                                        case CombatType.Pve:
                                            const tempMonster = monster.find((e) => e.isAttack());
                                            tempMonster && effectData.targetMember.push(tempMonster);
                                            break;
                                        case CombatType.Pvp:
                                            let tempPlayer: CombatMemberPlayer = null;
                                            switch (releasePlayerData.type) {
                                                case CombatPlayerType.Self:
                                                    tempPlayer = player.find(
                                                        (e) => e.getData().type === CombatPlayerType.Opponent
                                                    );
                                                    break;
                                                case CombatPlayerType.Opponent:
                                                    tempPlayer = player.find(
                                                        (e) => e.getData().type === CombatPlayerType.Self
                                                    );
                                                    break;
                                                default:
                                                    break;
                                            }
                                            effectData.targetMember.push(tempPlayer);
                                            break;
                                        default:
                                            break;
                                    }
                                    break;
                                case CombatMemberType.Monster:
                                    const tempPlayer = player.find((e) => e.getData().type === CombatPlayerType.Self);
                                    effectData.targetMember.push(tempPlayer);
                                    break;
                                default:
                                    break;
                            }
                        }
                        break;
                    default:
                        break;
                }
                break;
            case EnumSkillEffectScope.Whole:
                switch (effectData.info.target) {
                    case EnumSkillEffectTarget.Oneself:
                        switch (releaseMemberBaseData.type) {
                            case CombatMemberType.Player:
                                switch (combatType) {
                                    case CombatType.Main:
                                    case CombatType.Pve:
                                        {
                                            const tempPlayer = player.find(
                                                (e) => e.getData().type === CombatPlayerType.Self
                                            );
                                            effectData.targetMember.push(tempPlayer);
                                        }
                                        break;
                                    case CombatType.Pvp:
                                        {
                                            let tempPlayer: CombatMemberPlayer = null;
                                            switch (releasePlayerData.type) {
                                                case CombatPlayerType.Self:
                                                    tempPlayer = player.find(
                                                        (e) => e.getData().type === CombatPlayerType.Self
                                                    );
                                                    break;
                                                case CombatPlayerType.Opponent:
                                                    tempPlayer = player.find(
                                                        (e) => e.getData().type === CombatPlayerType.Opponent
                                                    );
                                                    break;
                                                default:
                                                    break;
                                            }
                                            effectData.targetMember.push(tempPlayer);
                                        }
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case CombatMemberType.Monster:
                                monster.forEach((e) => e.isAttack() && effectData.targetMember.push(e));
                                break;
                            default:
                                break;
                        }
                        break;
                    case EnumSkillEffectTarget.Enemy:
                        switch (releaseMemberBaseData.type) {
                            case CombatMemberType.Player:
                                switch (combatType) {
                                    case CombatType.Main:
                                    case CombatType.Pve:
                                        monster.forEach((e) => e.isAttack() && effectData.targetMember.push(e));
                                        break;
                                    case CombatType.Pvp:
                                        let tempPlayer: CombatMemberPlayer = null;
                                        switch (releasePlayerData.type) {
                                            case CombatPlayerType.Self:
                                                tempPlayer = player.find(
                                                    (e) => e.getData().type === CombatPlayerType.Opponent
                                                );
                                                break;
                                            case CombatPlayerType.Opponent:
                                                tempPlayer = player.find(
                                                    (e) => e.getData().type === CombatPlayerType.Self
                                                );
                                                break;
                                            default:
                                                break;
                                        }
                                        effectData.targetMember.push(tempPlayer);
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case CombatMemberType.Monster:
                                const tempPlayer = player.find((e) => e.getData().type === CombatPlayerType.Self);
                                effectData.targetMember.push(tempPlayer);
                                break;
                            default:
                                break;
                        }
                        break;
                    default:
                        break;
                }
                break;
            case EnumSkillEffectScope.ExtentRectangle:
                if (effectData.info.target !== EnumSkillEffectTarget.Enemy) {
                    return;
                }
                if (releaseMemberBaseData.type !== CombatMemberType.Player) {
                    return;
                }

                switch (combatType) {
                    case CombatType.Main:
                    case CombatType.Pve:
                        {
                            if (skillData.targetMember.isAttack()) {
                                const targetMemberBaseData = skillData.targetMember.getBaseData();
                                const compMonsterItem = this.sceneCtl.getMonsterItem(targetMemberBaseData.uuid);
                                skillData.targetPoint = compMonsterItem.getBeAttackedPoint();
                                skillData.targetPoint2 = compMonsterItem.getBeAttackedPoint2();
                            }

                            let targetRect: cc.Rect = null;
                            monster.forEach((e) => {
                                if (e.isAttack()) {
                                    const monsterBaseData = e.getBaseData();

                                    const compMonsterItem = this.sceneCtl.getMonsterItem(monsterBaseData.uuid);
                                    if (!targetRect) {
                                        const targetPoint = compMonsterItem.node.parent.convertToNodeSpaceAR(
                                            skillData.targetPoint
                                        );
                                        targetRect = cc.rect(
                                            targetPoint.x - effectData.info.scopePara[0] / 2,
                                            targetPoint.y - effectData.info.scopePara[1] / 2,
                                            effectData.info.scopePara[0],
                                            effectData.info.scopePara[1]
                                        );
                                    }
                                    const monsterRect = cc.rect(
                                        compMonsterItem.node.x - (compMonsterItem.node.width * 0.9) / 2,
                                        compMonsterItem.node.y - (compMonsterItem.node.width * 0.2) / 2,
                                        compMonsterItem.node.width * 0.9,
                                        compMonsterItem.node.width * 0.2
                                    );
                                    if (targetRect.intersects(monsterRect)) {
                                        effectData.targetMember.push(e);
                                    }
                                }
                            });
                        }
                        break;
                    case CombatType.Pvp:
                        switch (releasePlayerData.type) {
                            case CombatPlayerType.Self:
                                {
                                    const tempPlayer = player.find(
                                        (e) => e.getData().type === CombatPlayerType.Opponent
                                    );
                                    effectData.targetMember.push(tempPlayer);
                                }
                                break;
                            case CombatPlayerType.Opponent:
                                {
                                    const tempPlayer = player.find((e) => e.getData().type === CombatPlayerType.Self);
                                    effectData.targetMember.push(tempPlayer);
                                }
                                break;
                            default:
                                break;
                        }
                        break;
                    default:
                        break;
                }
                break;
            case EnumSkillEffectScope.FrontRectangle:
                if (effectData.info.target !== EnumSkillEffectTarget.Enemy) {
                    return;
                }
                if (releaseMemberBaseData.type !== CombatMemberType.Player) {
                    return;
                }

                switch (combatType) {
                    case CombatType.Main:
                    case CombatType.Pve:
                        {
                            let targetRect: cc.Rect[] = null;
                            monster.forEach((e) => {
                                if (e.isAttack()) {
                                    const monsterBaseData = e.getBaseData();
                                    const monsterData = e.getData();

                                    const compMonsterItem = this.sceneCtl.getMonsterItem(monsterBaseData.uuid);
                                    if (!targetRect) {
                                        targetRect = [];
                                        effectData.info.scopePara[1].forEach(([y, y2, x, x2]) => {
                                            targetRect.push(
                                                cc.rect(
                                                    x * monsterData.scaleX,
                                                    (y - 1) *
                                                        (compMonsterItem.node.parent.height / MONSTER_FORMATION_NUM),
                                                    x2 - x,
                                                    (y2 - (y - 1)) *
                                                        (compMonsterItem.node.parent.height / MONSTER_FORMATION_NUM)
                                                )
                                            );
                                        });
                                    }
                                    const monsterRect = cc.rect(
                                        compMonsterItem.node.x - (compMonsterItem.node.width * 0.9) / 2,
                                        compMonsterItem.node.y - (compMonsterItem.node.width * 0.2) / 2,
                                        compMonsterItem.node.width * 0.9,
                                        compMonsterItem.node.width * 0.2
                                    );
                                    if (targetRect.findIndex((e2) => e2.intersects(monsterRect)) !== -1) {
                                        effectData.targetMember.push(e);
                                    }
                                }
                            });
                            if (effectData.info.scopePara[0][0] !== -1) {
                                while (effectData.targetMember.length > effectData.info.scopePara[0][0]) {
                                    effectData.targetMember.splice(
                                        MathUtils.getRandomInt(0, effectData.targetMember.length),
                                        1
                                    );
                                }
                            }
                        }
                        break;
                    case CombatType.Pvp:
                        switch (releasePlayerData.type) {
                            case CombatPlayerType.Self:
                                {
                                    const tempPlayer = player.find(
                                        (e) => e.getData().type === CombatPlayerType.Opponent
                                    );
                                    effectData.targetMember.push(tempPlayer);
                                }
                                break;
                            case CombatPlayerType.Opponent:
                                {
                                    const tempPlayer = player.find((e) => e.getData().type === CombatPlayerType.Self);
                                    effectData.targetMember.push(tempPlayer);
                                }
                                break;
                            default:
                                break;
                        }
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }

    /**
     * 生效技能增益-时间
     * @param buffData 增益数据
     * @param dt
     */
    private effectSkillBuffByTime(buffData: ICombatBuffData, dt: number): void {
        const baseData = this.dungeonCtl.getBaseData();

        if (buffData.layerData.length === 0) {
            return;
        }
        buffData.effectTime !== -1 && (buffData.effectTime -= dt);
        if (buffData.effectTime > 0) {
            return;
        }
        if (!buffData.targetMember.isAttack()) {
            return;
        }

        switch (buffData.info.effectType) {
            case EnumSkillEffectEffectType.Buff102:
                {
                    const [, , , effectDuration] = buffData.para;

                    const targetMemberBaseData = buffData.targetMember.getBaseData();

                    const buffData704 = targetMemberBaseData.buffData.filter(
                        (e) => e.info.effectType === EnumSkillEffectEffectType.Buff704 && e.layerData.length !== 0
                    );
                    if (buffData704.length > 0) {
                        Skill.getInstance().clearBuffLayer(buffData.targetMember, buffData704[0], 1);
                        return;
                    }

                    const releaseMemberBaseData = buffData.skillData.releaseMember.getBaseData();

                    const damageData = Damage.getInstance().getDamageData(
                        buffData.info,
                        buffData.para,
                        buffData.skillData,
                        buffData.skillData.releaseMember,
                        buffData.targetMember
                    );
                    const buffData703 = targetMemberBaseData.buffData.filter(
                        (e) => e.info.effectType === EnumSkillEffectEffectType.Buff703 && e.layerData.length !== 0
                    );
                    switch (targetMemberBaseData.type) {
                        case CombatMemberType.Player:
                            const compLeadItem = this.sceneCtl.getLeadItem(targetMemberBaseData.uuid);
                            compLeadItem.beAttacked(
                                damageData.damage,
                                buffData.targetMember as CombatMemberPlayer,
                                baseData.isSkipingCombat
                            );
                            const compTankItem = this.sceneCtl.getTankItem(targetMemberBaseData.uuid);
                            compTankItem.beAttacked(
                                buffData.skillData.info.triggerBlow === 0,
                                buffData.skillData.info.triggerBlow === 1,
                                releaseMemberBaseData.dungeonType,
                                baseData.isSkipingCombat
                            );

                            this.updatePlayerHpState(
                                buffData.targetMember as CombatMemberPlayer,
                                false,
                                buffData703.length <= 0
                            );
                            this.sceneCtl.playDamageAni(damageData, compLeadItem.getDamagePoint());
                            break;
                        case CombatMemberType.Monster:
                            const compMonsterItem = this.sceneCtl.getMonsterItem(targetMemberBaseData.uuid);
                            compMonsterItem.beAttacked(
                                damageData.damage,
                                buffData.skillData.info.triggerBlow === 1,
                                baseData.isSkipingCombat
                            );

                            this.updateMonsterHpState(
                                buffData.targetMember as CombatMemberMonster,
                                false,
                                buffData703.length <= 0
                            );
                            this.sceneCtl.playDamageAni(damageData, compMonsterItem.getDamagePoint());
                            break;
                        default:
                            break;
                    }
                    if (releaseMemberBaseData.type === CombatMemberType.Player) {
                        const tempPlayer = buffData.skillData.releaseMember as CombatMemberPlayer;
                        const tempPlayerData = tempPlayer.getData();
                        tempPlayerData.damage += damageData.damage;
                        tempPlayerData.skillDamage[buffData.skillData.id] =
                            (tempPlayerData.skillDamage[buffData.skillData.id] || 0) + damageData.damage;
                    }

                    Skill.getInstance().updateEffectTriggerPara(
                        buffData.targetMember,
                        targetMemberBaseData.skillData,
                        EnumSkillEffectCondition.Condition9,
                        1
                    );

                    buffData.effectTime = effectDuration;
                }
                break;
            default:
                break;
        }
    }

    /**
     * 玩家行为
     * @param dt
     */
    public actionPlayer(dt: number): void {
        const allPlayer = this.dungeonCtl.getAllPlayer();

        allPlayer.forEach((e) => {
            const playerBaseData = e.getBaseData();
            const playerData = e.getData();

            const compLeadItem = this.sceneCtl.getLeadItem(playerBaseData.uuid);
            compLeadItem.updateState(dt);

            const compWeaponItem = this.sceneCtl.getWeaponItem(playerBaseData.uuid);
            compWeaponItem.updateState(dt);

            const compWingItem = this.sceneCtl.getWingItem(playerBaseData.uuid);
            compWingItem.updateState(dt);

            const compTankItem = this.sceneCtl.getTankItem(playerBaseData.uuid);
            compTankItem.updateState(dt, playerData.type);

            playerData.archerData.forEach((e2) => {
                const compArcherItem = this.sceneCtl.getArcherItem(`${playerBaseData.uuid}#${e2.gridId}`);
                compArcherItem.updateState(dt);
            });

            const compPetItem = this.sceneCtl.getPetItem(playerBaseData.uuid);
            compPetItem.updateState(dt);
        });
    }

    /**
     * 怪兽行为
     * @param dt 时间变化量
     */
    public actionMonster(dt: number): void {
        const baseData = this.dungeonCtl.getBaseData();
        const allMonster = this.dungeonCtl.getAllMonster();

        for (let i = allMonster.length - 1; i >= 0; i--) {
            const monsterBaseData = allMonster[i].getBaseData();
            const monsterData = allMonster[i].getData();

            if (monsterData.state === CombatMemberState.Put) {
                const index = baseData.monsterDropData.findIndex(
                    ([monsterUuid]) => monsterUuid === monsterBaseData.uuid
                );
                index !== -1 && baseData.monsterDropData.splice(index, 1);
                this.sceneCtl.clearMonsterItem(monsterBaseData.uuid);
                Skill.getInstance().emit(
                    SkillEvent.ClearShow,
                    monsterBaseData.dungeonType,
                    monsterBaseData.skillData,
                    monsterBaseData.buffData
                );
                this.dungeonCtl.clearMonster(allMonster[i]);
                allMonster.splice(i, 1);
            }
        }

        allMonster.forEach((e) => {
            const compMonsterItem = this.sceneCtl.getMonsterItem(e.getBaseData().uuid);
            compMonsterItem.updateState(dt);
        });
    }

    /**
     * 判断结果
     * @param dt
     */
    public abstract judgeResult(dt: number): void;

    /**
     * 释放技能
     * @param memberSkill 成员技能
     * @param player 玩家
     * @param monster 怪兽
     * @param targetMember 目标成员
     * @param targetPoint 目标点
     * @param targetPoint2 目标点
     */
    private releaseSkill(
        memberSkill: CombatSkill,
        player: CombatMemberPlayer,
        monster: CombatMemberMonster,
        targetMember: CombatMember,
        targetPoint: cc.Vec2,
        targetPoint2: cc.Vec2
    ): void {
        const baseData = this.dungeonCtl.getBaseData();
        const memberSkillData = memberSkill.getData();
        const playerBaseData = player ? player.getBaseData() : null;
        const playerData = player ? player.getData() : null;
        const monsterBaseData = monster ? monster.getBaseData() : null;

        const isRelease =
            (player && player.isReleaseSkill(memberSkillData.info.type, memberSkillData.memberId)) ||
            (monster && monster.isReleaseSkill(memberSkillData.info.type));
        if (
            [
                EnumSkillType.LeadASkill,
                EnumSkillType.WeaponASkill,
                EnumSkillType.WingASkill,
                EnumSkillType.TankASkill,
                EnumSkillType.ArcherASkill,
                EnumSkillType.PetASkill,
                EnumSkillType.MonsterASkill,
            ].includes(memberSkillData.info.type)
        ) {
            memberSkillData.isWaitRelease = !isRelease;
        }
        if (isRelease) {
            if (player) {
                playerData.skillCastTime[memberSkillData.id] = playerData.skillCastTime[memberSkillData.id] || [];
                playerData.skillCastTime[memberSkillData.id].push(MathUtils.round(baseData.combatTime, 3));
            }

            const memberSkillId = memberSkillData.id;
            const castCb: () => void = () => {
                if (memberSkillData.id !== memberSkillId) {
                    return;
                }

                Skill.getInstance().release(
                    memberSkillData.id,
                    memberSkillData.memberId,
                    player || monster,
                    targetMember,
                    targetPoint,
                    targetPoint2
                );

                Skill.getInstance().updateEffectTriggerPara(
                    player ? player : monster,
                    playerBaseData ? playerBaseData.skillData : monsterBaseData.skillData,
                    EnumSkillEffectCondition.Condition3,
                    memberSkillData.info.type
                );
                Skill.getInstance().updateEffectTriggerPara(
                    player ? player : monster,
                    playerBaseData ? playerBaseData.skillData : monsterBaseData.skillData,
                    EnumSkillEffectCondition.Condition4,
                    memberSkillData.id
                );

                if (player) {
                    playerData.skillReleaseTime[memberSkillData.id] =
                        playerData.skillReleaseTime[memberSkillData.id] || [];
                    playerData.skillReleaseTime[memberSkillData.id].push(MathUtils.round(baseData.combatTime, 3));
                }
            };
            switch (memberSkillData.info.type) {
                case EnumSkillType.LeadAtk:
                case EnumSkillType.LeadASkill:
                    const compLeadItem = this.sceneCtl.getLeadItem(playerBaseData.uuid);
                    compLeadItem.playCastAni(memberSkillData, castCb, baseData.isSkipingCombat);
                    break;
                case EnumSkillType.WeaponASkill:
                    if (playerData.weaponData.showId !== playerData.weaponData.id) {
                        castCb();
                    } else {
                        const compWeaponItem = this.sceneCtl.getWeaponItem(playerBaseData.uuid);
                        compWeaponItem.playCastAni(memberSkillData, castCb, baseData.isSkipingCombat);
                    }
                    break;
                case EnumSkillType.WingASkill:
                    if (playerData.wingData.showId !== playerData.wingData.id) {
                        castCb();
                    } else {
                        const compWingItem = this.sceneCtl.getWingItem(playerBaseData.uuid);
                        compWingItem.playCastAni(memberSkillData, castCb, baseData.isSkipingCombat);
                    }
                    break;
                case EnumSkillType.TankASkill:
                    const compTankItem = this.sceneCtl.getTankItem(playerBaseData.uuid);
                    compTankItem.playCastAni(memberSkillData, castCb, baseData.isSkipingCombat);
                    break;
                case EnumSkillType.ArcherAtk:
                case EnumSkillType.ArcherASkill:
                    const archerData = playerData.archerData.find((e) => e.id === memberSkillData.memberId);
                    const compArcherItem = this.sceneCtl.getArcherItem(`${playerBaseData.uuid}#${archerData.gridId}`);
                    compArcherItem.playCastAni(memberSkillData, castCb, baseData.isSkipingCombat);
                    break;
                case EnumSkillType.PetASkill:
                    const compPetItem = this.sceneCtl.getPetItem(playerBaseData.uuid);
                    compPetItem.playCastAni(memberSkillData, castCb, baseData.isSkipingCombat);
                    break;
                case EnumSkillType.MonsterAtk:
                case EnumSkillType.MonsterASkill:
                    const compMonsterItem = this.sceneCtl.getMonsterItem(monsterBaseData.uuid);
                    compMonsterItem.playCastAni(memberSkillData, castCb, baseData.isSkipingCombat);
                    break;
                default:
                    castCb();
                    break;
            }

            if (!baseData.isSkipingCombat && memberSkillData.info.resSound !== "") {
                AudioUtils.playCombatEffect(
                    `skill${memberSkillData.info.resSound}`,
                    AUDIO_EFFECT_PATH.COMBAT,
                    memberSkillData.dungeonType,
                    (audioId) => {
                        if (cc.isValid(this.node)) {
                            memberSkillData.audioId.push(audioId);
                        }
                    },
                    (audioId) => {
                        if (cc.isValid(this.node)) {
                            const index = memberSkillData.audioId.findIndex((e) => e === audioId);
                            index !== -1 && memberSkillData.audioId.splice(index, 1);
                        }
                    }
                );
            }
        }

        if (!isRelease && !memberSkillData.isWaitRelease) {
            return;
        }
        if (memberSkillData.state === CombatSkillState.Cd) {
            return;
        }

        memberSkill.changeState(CombatSkillState.Cd);
        if (player && memberSkillData.isShow && !baseData.isSkipingCombat) {
            const compSkillItem = this.sceneCtl.getSkillItem(`${playerBaseData.uuid}#${memberSkillData.id}`);
            compSkillItem.updateCdState();

            Task.getInstance().setTaskProgress(EnumTaskDetailType.ClickSkillTask, 1);
        }
    }

    /**
     * 更新增益703
     * @param memberType 成员类型
     * @param memberUuid 成员uuid
     */
    private updateBuff703(memberType: CombatMemberType, memberUuid: number): void {
        switch (memberType) {
            case CombatMemberType.Player:
                const allPlayer = this.dungeonCtl.getAllPlayer();
                const player = allPlayer.find((e) => e.getBaseData().uuid === memberUuid);
                if (player) {
                    const compLeadItem = this.sceneCtl.getLeadItem(memberUuid);
                    compLeadItem.updateHpState(player);

                    this.updatePlayerHpState(player, false, false);
                }
                break;
            case CombatMemberType.Monster:
                const allMonster = this.dungeonCtl.getAllMonster();
                const monster = allMonster.find((e) => e.getBaseData().uuid === memberUuid);
                if (monster) {
                    const compMonsterItem = this.sceneCtl.getMonsterItem(memberUuid);
                    compMonsterItem.updateHpState();

                    this.updateMonsterHpState(monster, false, false);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 更新伤害状态
     */
    public updateDamageState(): void {}

    /**
     * 更新自动按钮状态-释放技能
     */
    public abstract updateAutoState(): void;

    /**
     * 更新游戏速度状态
     * @param isInit 是否为初始化调用
     */
    public updateGameSpeedState(isInit: boolean = false): void {}

    /**
     * 切换游戏速度
     */
    protected switchGameSpeed(): void {
        const baseData = this.dungeonCtl.getBaseData();
        const settingId = Combat.getInstance().getGameSpeedSettingId(baseData.type);
        Setting.getInstance().setSwitchState(settingId);
        this.updateGameSpeedState();
        Combat.getInstance().emit(CombatEvent.UpdateGameSpeed, baseData.type);
    }

    /**
     * 跳过战斗
     */
    protected skipCombat(): void {
        const baseData = this.dungeonCtl.getBaseData();
        if (!baseData.isSkipCombat) {
            return;
        }
        const allPlayer = this.dungeonCtl.getAllPlayer();
        const allMonster = this.dungeonCtl.getAllMonster();
        switch (baseData.state) {
            case CombatDungeonState.Enter:
                if (
                    allPlayer.findIndex((e) => {
                        const playerData = e.getData();
                        return playerData.tankData.state === CombatMemberState.Load;
                    }) !== -1 ||
                    allMonster.findIndex((e) => {
                        const monsterData = e.getData();
                        return monsterData.state === CombatMemberState.Load;
                    }) !== -1
                ) {
                    return;
                }
                break;
            case CombatDungeonState.Combat:
                break;
            default:
                return;
        }

        baseData.isSkipingCombat = true;

        switch (baseData.state) {
            case CombatDungeonState.Enter:
                allPlayer.forEach((e) => {
                    const playerBaseData = e.getBaseData();
                    const playerData = e.getData();
                    if (
                        playerData.tankData.state === CombatMemberState.WaitEnter ||
                        playerData.tankData.state === CombatMemberState.Enter
                    ) {
                        const compTankItem = this.sceneCtl.getTankItem(playerBaseData.uuid);
                        compTankItem.setState(CombatMemberState.Wait);
                    }
                });
                allMonster.forEach((e) => {
                    const monsterBaseData = e.getBaseData();
                    const monsterData = e.getData();
                    if (
                        monsterData.state === CombatMemberState.WaitEnter ||
                        monsterData.state === CombatMemberState.Enter
                    ) {
                        const compMonsterItem = this.sceneCtl.getMonsterItem(monsterBaseData.uuid);
                        compMonsterItem.setState(CombatMemberState.Wait);
                    }
                });

                this.changeState(CombatDungeonState.Combat);
                break;
            case CombatDungeonState.Combat:
                allMonster.forEach((e) => {
                    const monsterBaseData = e.getBaseData();
                    const monsterData = e.getData();
                    if (monsterData.state === CombatMemberState.Die) {
                        const compMonsterItem = this.sceneCtl.getMonsterItem(monsterBaseData.uuid);
                        compMonsterItem.changeState(CombatMemberState.Put);
                    }
                });
                break;
            default:
                break;
        }

        const time = new Date().getTime();
        while (baseData.state === CombatDungeonState.Combat) {
            this.updateState(baseData.skipCombatTime);
        }
        const time2 = new Date().getTime();
        Logger.log("跳过战斗耗时", `${time2 - time}ms`);
    }

    /**
     * 播放震屏动画
     * @param type 类型
     * @param skillId 技能id
     */
    public playShakeScreenAni(type: EnumSkillShowShake, skillId: number): void {
        if (this.isPlayingByShakeScreenAni) {
            return;
        }
        if (type === EnumSkillShowShake.Shake1) {
            return;
        }

        this.skillIdByShakeScreenAni = skillId;
        this.isPlayingByShakeScreenAni = true;

        !this.posSceneBg && (this.posSceneBg = this.nodeSceneBg.getPosition());
        !this.posScene && (this.posScene = this.nodeScene.getPosition());
        switch (type) {
            case EnumSkillShowShake.Shake2:
                cc.tween(this.nodeSceneBg)
                    .bezierTo(
                        0.1,
                        cc.v2(this.posSceneBg.x, this.posSceneBg.y - 60),
                        cc.v2(this.posSceneBg.x + 10, this.posSceneBg.y - 60),
                        cc.v2(this.posSceneBg.x + 10, this.posSceneBg.y)
                    )
                    .bezierTo(
                        0.1,
                        cc.v2(this.posSceneBg.x + 10, this.posSceneBg.y + 60),
                        cc.v2(this.posSceneBg.x, 60),
                        this.posSceneBg
                    )
                    .start();
                cc.tween(this.nodeScene)
                    .bezierTo(
                        0.1,
                        cc.v2(this.posScene.x, this.posScene.y - 60),
                        cc.v2(this.posScene.x + 10, this.posScene.y - 60),
                        cc.v2(this.posScene.x + 10, this.posScene.y)
                    )
                    .bezierTo(
                        0.1,
                        cc.v2(this.posScene.x + 10, this.posScene.y + 60),
                        cc.v2(this.posScene.x, this.posScene.y + 60),
                        this.posScene
                    )
                    .call(() => {
                        this.skillIdByShakeScreenAni = -1;
                        this.isPlayingByShakeScreenAni = false;
                    })
                    .start();
                break;
            case EnumSkillShowShake.Shake3:
                cc.tween(this.nodeSceneBg)
                    .bezierTo(
                        0.1,
                        cc.v2(this.posSceneBg.x - 60, this.posSceneBg.y),
                        cc.v2(this.posSceneBg.x - 60, this.posSceneBg.y - 10),
                        cc.v2(this.posSceneBg.x, this.posSceneBg.y - 10)
                    )
                    .bezierTo(
                        0.1,
                        cc.v2(this.posSceneBg.x + 60, this.posSceneBg.y - 10),
                        cc.v2(this.posSceneBg.x + 60, this.posSceneBg.y),
                        this.posSceneBg
                    )
                    .start();
                cc.tween(this.nodeScene)
                    .bezierTo(
                        0.1,
                        cc.v2(this.posScene.x - 60, this.posScene.y),
                        cc.v2(this.posScene.x - 60, this.posScene.y - 10),
                        cc.v2(this.posScene.x, this.posScene.y - 10)
                    )
                    .bezierTo(
                        0.1,
                        cc.v2(this.posScene.x + 60, this.posScene.y - 10),
                        cc.v2(this.posScene.x + 60, this.posScene.y),
                        this.posScene
                    )
                    .call(() => {
                        this.skillIdByShakeScreenAni = -1;
                        this.isPlayingByShakeScreenAni = false;
                    })
                    .start();
                break;
            case EnumSkillShowShake.Shake4:
                cc.tween(this.nodeSceneBg)
                    .to(0.15, { scale: 1.05 })
                    .to(0.05, { scale: 0.98 })
                    .to(0.05, { scale: 1 })
                    .start();
                cc.tween(this.nodeScene)
                    .to(0.15, { scale: 1.05 })
                    .to(0.05, { scale: 0.98 })
                    .to(0.05, { scale: 1 })
                    .call(() => {
                        this.skillIdByShakeScreenAni = -1;
                        this.isPlayingByShakeScreenAni = false;
                    })
                    .start();
                break;
            default:
                break;
        }
    }

    /**
     * 停止震屏动画
     * @param skillId 技能id
     */
    public stopShakeScreenAni(skillId: number): void {
        if (!this.isPlayingByShakeScreenAni) {
            return;
        }
        if (this.skillIdByShakeScreenAni !== skillId) {
            return;
        }

        cc.Tween.stopAllByTarget(this.nodeSceneBg);
        cc.tween(this.nodeSceneBg).to(0.1, { position: this.posSceneBg, scale: 1 }).start();
        cc.Tween.stopAllByTarget(this.nodeScene);
        cc.tween(this.nodeScene)
            .to(0.1, { position: this.posScene, scale: 1 })
            .call(() => {
                this.skillIdByShakeScreenAni = -1;
                this.isPlayingByShakeScreenAni = false;
            })
            .start();
    }

    /**
     * 切换游戏速度
     */
    protected onClickSwitchGameSpeed(): void {}
}
