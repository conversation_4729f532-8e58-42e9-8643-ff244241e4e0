/*
 * @Author: chenx
 * @Date: 2025-02-05 11:45:34
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 15:01:07
 */
import UI from "../../../nsn/ui/UI";
import MathUtils from "../../../nsn/util/MathUtils";
import Utils from "../../../nsn/util/Utils";
import i18n from "../../config/i18n/I18n";
import { EnumArenaPara } from "../../data/base/BaseArena";
import { EnumSkillEffectEffectType } from "../../data/base/BaseSkillEffect";
import TBArena from "../../data/parser/TBArena";
import TBDungeonCombat from "../../data/parser/TBDungeonCombat";
import Combat, { DungeonType } from "../../game/Combat";
import { CombatDungeonState } from "../../game/combat/CombatDungeon";
import CombatDungeonPvp from "../../game/combat/CombatDungeonPvp";
import { CombatMemberState } from "../../game/combat/CombatMember";
import CombatMemberMonster from "../../game/combat/CombatMemberMonster";
import CombatMemberPlayer, { CombatPlayerType } from "../../game/combat/CombatMemberPlayer";
import CombatLog, { CombatLogId } from "../../game/CombatLog";
import Setting from "../../game/Setting";
import PlayerInfoUtils from "../../utils/PlayerInfoUtils";
import { PrefabCombatDungeon } from "./PrefabCombatDungeon";

/**
 * 界面参数
 */
interface IPrefabCombatDungeonPvpArgs {
    type: DungeonType; // 类型
    resultCb: (isWin: boolean) => void; // 结果cb
}

const { ccclass, property } = cc._decorator;

/**
 * pvp副本
 */
@ccclass
export default class PrefabCombatDungeonPvp extends PrefabCombatDungeon {
    @property(cc.Node)
    nodeHead: cc.Node = null; // 头像
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.ProgressBar)
    prgHp: cc.ProgressBar = null; // 血量
    @property(cc.ProgressBar)
    prgHp2: cc.ProgressBar = null; // 血量-过渡
    @property(cc.ProgressBar)
    prgHp3: cc.ProgressBar = null; // 血量-护盾
    @property(cc.Node)
    nodeHead2: cc.Node = null; // 头像
    @property(cc.Label)
    lbtName2: cc.Label = null; // 名称
    @property(cc.ProgressBar)
    prgHp4: cc.ProgressBar = null; // 血量
    @property(cc.ProgressBar)
    prgHp5: cc.ProgressBar = null; // 血量-过渡
    @property(cc.ProgressBar)
    prgHp6: cc.ProgressBar = null; // 血量-护盾
    @property(cc.Label)
    lbtTime: cc.Label = null; // 时间
    @property(cc.Node)
    nodeAutoTag: cc.Node = null; // 自动tag-释放技能
    @property(cc.Node)
    nodeGameSpeed: cc.Node = null; // 游戏速度按钮
    @property(cc.Node)
    nodeSkipCombat: cc.Node = null; // 跳过战斗按钮

    private initData: IPrefabCombatDungeonPvpArgs = null; // 初始化数据
    protected dungeonPvpCtl: CombatDungeonPvp = CombatDungeonPvp.getInstance(); // pvp副本ctl
    private skipCombatTime: number = 0; // 跳过战斗倒计时

    protected onLoad(): void {
        this.initData = this.args;
        this.dungeonPvpCtl.initCombat(this.initData.type);
        this.setDungeonCtl(this.dungeonPvpCtl);
        this.loadScene();
    }

    protected start(): void {
        const pvpBaseData = this.dungeonPvpCtl.getBaseData();

        if (pvpBaseData && pvpBaseData.state !== CombatDungeonState.Init) {
            const allPlayer = this.dungeonPvpCtl.getAllPlayer();
            const playerOpponent = allPlayer.find((e) => e.getData().type === CombatPlayerType.Opponent);
            const playerOpponentBaseData = playerOpponent.getBaseData();

            const playerCombatData = Combat.getInstance().getPlayerCombatData(
                playerOpponentBaseData.dungeonType,
                playerOpponentBaseData.uuid
            );
            UI.getInstance().open("FloatArenaEntry", playerCombatData);
        }
    }

    protected registerHandler(): void {
        super.registerHandler();
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatDungeonState): void {
        super.changeState(state);

        const baseData = this.dungeonPvpCtl.getBaseData();
        const pvpData = this.dungeonPvpCtl.getData();
        const allPlayer = this.dungeonPvpCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerOpponent = allPlayer.find((e) => e.getData().type === CombatPlayerType.Opponent);

        switch (baseData.state) {
            case CombatDungeonState.Init:
                this.updateLevelInfo(true);
                this.sceneCtl.initPlayerInfo(player);
                this.sceneCtl.updatePlayerInfo(player);
                this.updatePlayerHpState(player, true);
                this.sceneCtl.initPlayerInfo(playerOpponent);
                this.sceneCtl.updatePlayerInfo(playerOpponent);
                this.updatePlayerHpState(playerOpponent, true);
                this.updatePlayerSkillInfo(player);
                this.updatePlayerSkillInfo(playerOpponent);
                this.updatePlayerDamageState();
                this.updateAutoState();
                this.updateGameSpeedState(true);

                baseData.isPlayEnterAni = true;
                this.changeState(CombatDungeonState.Ready);
                break;
            case CombatDungeonState.Ready:
                this.scheduleOnce(() => {
                    this.changeState(CombatDungeonState.Load);
                }, 2);
                break;
            case CombatDungeonState.Success:
                baseData.resetTime = 10;
                this.initData.resultCb(true);
                break;
            case CombatDungeonState.Failure:
                pvpData.refreshTime = 0;

                baseData.resetTime = 10;
                this.initData.resultCb(false);
                break;
            default:
                break;
        }

        if (state === CombatDungeonState.Success || state === CombatDungeonState.Failure) {
            if (
                CombatLog.getInstance().getShowState(CombatLogId.CombatResultData) &&
                CombatLog.getInstance().getShowStateByDungeonType(baseData.type)
            ) {
                allPlayer.forEach((e) => {
                    const tempPlayerData = e.getData();

                    CombatLog.getInstance().logTitle(CombatLogId.CombatResultData, e);

                    const logData = {
                        combatTime: baseData.combatTime,
                        skillRecord: Utils.clone(tempPlayerData.skillRecord),
                    };
                    cc.log(logData);
                });
            }
        }
    }

    /**
     * 更新状态
     * @param dt
     */
    public updateState(dt: number): void {
        const baseData = this.dungeonPvpCtl.getBaseData();
        const allPlayer = this.dungeonPvpCtl.getAllPlayer();

        switch (baseData.state) {
            case CombatDungeonState.Load:
                this.updataStateByLoad(dt);
                break;
            case CombatDungeonState.Enter:
                allPlayer.forEach((e) => {
                    const playerData = e.getData();

                    if (
                        playerData.tankData.state === CombatMemberState.WaitEnter ||
                        playerData.tankData.state === CombatMemberState.Enter
                    ) {
                        const compTankItem = this.sceneCtl.getTankItem(e.getBaseData().uuid);
                        compTankItem.updateState(dt, playerData.type);
                    }
                });

                if (
                    allPlayer.findIndex((e) => {
                        const playerData = e.getData();
                        return (
                            playerData.tankData.state === CombatMemberState.Load ||
                            playerData.tankData.state === CombatMemberState.WaitEnter ||
                            playerData.tankData.state === CombatMemberState.Enter
                        );
                    }) === -1
                ) {
                    this.changeState(CombatDungeonState.Combat);
                }
                break;
            case CombatDungeonState.Combat:
                this.actionSkillCd(allPlayer, [], dt);
                this.actionSkillRelease(allPlayer, [], dt);

                this.actionSkillShow([...allPlayer], dt);
                this.actionSkillEffect(allPlayer, [], dt);
                this.actionSkill([...allPlayer], dt);

                this.actionBuff([...allPlayer], dt);

                this.actionPlayer(dt);

                this.judgeResult(dt);
                break;
            case CombatDungeonState.Success:
            case CombatDungeonState.Failure:
                this.actionSkillShow([...allPlayer], dt);
                break;
            default:
                break;
        }

        this.dialogCtl.actionDialog(allPlayer, []);

        this.sceneCtl.actionDamage();

        if (baseData.resetTime > 0) {
            baseData.resetTime -= dt;
            if (baseData.resetTime <= 0) {
                UI.getInstance().closeToWindow(this.node.name);
                UI.getInstance().close();
            }
        }

        if (this.skipCombatTime > 0) {
            const tempSkipCombatTime = Math.floor(this.skipCombatTime);
            this.skipCombatTime -= dt;
            if (Math.floor(this.skipCombatTime) !== tempSkipCombatTime || this.skipCombatTime === 0) {
                if (this.skipCombatTime <= 0) {
                    baseData.isSkipCombat = true;

                    this.nodeSkipCombat.child("lbtText").label(i18n.common0079);
                } else {
                    this.nodeSkipCombat.child("lbtText").label(`${MathUtils.ceil(this.skipCombatTime, 1)}s`);
                }
            }
        }
    }

    /**
     * 更新关卡信息
     * @param isInit 是否为初始化调用
     */
    public updateLevelInfo(isInit: boolean = false): void {
        const baseData = this.dungeonPvpCtl.getBaseData();
        const pvpData = this.dungeonPvpCtl.getData();
        const allPlayer = this.dungeonPvpCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerData = player.getData();
        const playerOpponent = allPlayer.find((e) => e.getData().type === CombatPlayerType.Opponent);
        const playerOpponentData = playerOpponent.getData();

        switch (baseData.type) {
            case DungeonType.Arena:
            case DungeonType.Park:
            case DungeonType.UnionDefense:
                pvpData.totalLimitTime = TBArena.getInstance().getValueByPara(EnumArenaPara.PkTime);

                PlayerInfoUtils.updateHead(this.nodeHead, playerData.roleData);
                this.lbtName.string = playerData.roleData.name;
                PlayerInfoUtils.updateHead(this.nodeHead2, playerOpponentData.roleData);
                this.lbtName2.string = playerOpponentData.roleData.name;
                break;
            default:
                break;
        }

        const combatInfo = TBDungeonCombat.getInstance().getDataByType(baseData.type);
        const isShow = !!combatInfo.skipSwitch;
        isInit && (this.nodeSkipCombat.active = isShow);
        if (isShow) {
            this.skipCombatTime = combatInfo.skipUnlock[0];
            baseData.isSkipCombat = this.skipCombatTime <= 0;
            baseData.isSkipingCombat = false;
            this.nodeSkipCombat
                .child("lbtText")
                .label(this.skipCombatTime > 0 ? `${MathUtils.ceil(this.skipCombatTime, 1)}s` : i18n.common0079);
        }

        this.updateLimitTimeState(true);
    }

    /**
     * 更新波数信息
     */
    public updateWaveInfo(): void {}

    /**
     * 更新限制时间状态
     * @param isInit 是否为初始化调用
     */
    public updateLimitTimeState(isInit: boolean = false): void {
        const baseData = this.dungeonPvpCtl.getBaseData();
        const pvpData = this.dungeonPvpCtl.getData();

        if (isInit) {
            baseData.combatTime = 0;
            pvpData.limitTime = pvpData.totalLimitTime;
        }

        !baseData.isSkipingCombat && (this.lbtTime.string = `${Math.max(MathUtils.ceil(pvpData.limitTime, 1), 0)}s`);

        pvpData.refreshTime = pvpData.refreshDuration;
    }

    /**
     * 更新玩家血量状态
     * @param player 玩家
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public updatePlayerHpState(player: CombatMemberPlayer, isInit: boolean = false, isPlayAni: boolean = false): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        if (isInit) {
            const compLeadItem = this.sceneCtl.getLeadItem(playerBaseData.uuid);
            compLeadItem.updateHpState(player, isInit);
        }

        let shield = 0;
        playerBaseData.buffData.forEach((e) => {
            if (e.info.effectType === EnumSkillEffectEffectType.Buff703) {
                e.layerData.forEach(([, , tempShield]) => {
                    shield += tempShield;
                });
            }
        });
        const progressHp = MathUtils.floor(playerBaseData.hp / (playerBaseData.totalHp + shield), 3);
        const progressShield = MathUtils.floor((playerBaseData.hp + shield) / (playerBaseData.totalHp + shield), 3);
        switch (playerData.type) {
            case CombatPlayerType.Self:
                if (isInit) {
                    this.prgHp.progress = progressHp;
                    this.prgHp2.progress = progressHp;
                    this.prgHp3.progress = progressShield;
                    this.scheduleOnce(() => {
                        this.prgHp.barSprite.node.opacity = 255;
                        this.prgHp2.barSprite.node.opacity = 255;
                        this.prgHp3.barSprite.node.opacity = 255;
                    });
                } else {
                    this.prgHp.progress = progressHp;
                    this.prgHp.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                    this.prgHp3.progress = progressShield;
                    this.prgHp3.barSprite.node.opacity = progressShield !== 0 ? 255 : 0;
                    if (!isPlayAni) {
                        this.prgHp2.progress = progressHp;
                        this.prgHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                    } else {
                        cc.tween(this.prgHp2)
                            .to(0.8, { progress: progressHp })
                            .call(() => {
                                this.prgHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                            })
                            .start();
                    }
                }
                break;
            case CombatPlayerType.Opponent:
                if (isInit) {
                    this.prgHp4.progress = progressHp;
                    this.prgHp5.progress = progressHp;
                    this.prgHp6.progress = progressShield;
                    this.scheduleOnce(() => {
                        this.prgHp4.barSprite.node.opacity = 255;
                        this.prgHp5.barSprite.node.opacity = 255;
                        this.prgHp6.barSprite.node.opacity = 255;
                    });
                } else {
                    this.prgHp4.progress = progressHp;
                    this.prgHp4.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                    this.prgHp6.progress = progressShield;
                    this.prgHp6.barSprite.node.opacity = progressShield !== 0 ? 255 : 0;
                    if (!isPlayAni) {
                        this.prgHp5.progress = progressHp;
                        this.prgHp5.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                    } else {
                        cc.tween(this.prgHp5)
                            .to(0.8, { progress: progressHp })
                            .call(() => {
                                this.prgHp5.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                            })
                            .start();
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 更新玩家伤害状态
     */
    public updatePlayerDamageState(): void {}

    /**
     * 初始化怪兽信息
     */
    public initMonsterInfo(): void {}

    /**
     * 更新怪兽血量状态
     * @param monster 怪兽
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public updateMonsterHpState(
        monster: CombatMemberMonster,
        isInit: boolean = false,
        isPlayAni: boolean = false
    ): void {}

    /**
     * 更新开关状态-自动释放技能
     */
    public updateAutoState(): void {
        const allPlayer = this.dungeonPvpCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerBaseData = player.getBaseData();

        this.nodeAutoTag.active = playerBaseData.isAuto;
    }

    /**
     * 更新游戏速度状态
     * @param isInit 是否为初始化调用
     */
    public updateGameSpeedState(isInit: boolean = false): void {
        const baseData = this.dungeonPvpCtl.getBaseData();
        const combatInfo = TBDungeonCombat.getInstance().getDataByType(baseData.type);
        const isShow = !!combatInfo.accelerationSwitch;
        isInit && (this.nodeGameSpeed.active = isShow);
        if (isShow) {
            let gameSpeed = 1;
            const settingId = Combat.getInstance().getGameSpeedSettingId(baseData.type);
            if (Setting.getInstance().getSwitchState(settingId)) {
                gameSpeed = combatInfo.battleAcceleration[1];
            }
            this.nodeGameSpeed.child("lbtSpeed").label(`x${gameSpeed}`);
        }
    }

    /**
     * 判断结果
     * @param dt
     */
    public judgeResult(dt: number): void {
        const baseData = this.dungeonPvpCtl.getBaseData();
        const pvpData = this.dungeonPvpCtl.getData();
        const allPlayer = this.dungeonPvpCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerBaseData = player.getBaseData();
        const playerOpponent = allPlayer.find((e) => e.getData().type === CombatPlayerType.Opponent);
        const playerOpponentBaseData = playerOpponent.getBaseData();

        if (playerOpponentBaseData.hp <= 0) {
            this.changeState(CombatDungeonState.Success);
            return;
        }

        if (playerBaseData.hp <= 0) {
            this.changeState(CombatDungeonState.Failure);
            return;
        }

        if (pvpData.refreshTime > 0) {
            baseData.combatTime += dt;
            pvpData.limitTime -= dt;
            pvpData.refreshTime -= dt;
            if (pvpData.refreshTime <= 0 || pvpData.limitTime <= 0) {
                this.updateLimitTimeState();

                if (pvpData.limitTime <= 0) {
                    const progressHp = MathUtils.floor(playerBaseData.hp / playerBaseData.totalHp, 3);
                    const progressHp2 = MathUtils.floor(playerOpponentBaseData.hp / playerOpponentBaseData.totalHp, 3);
                    if (progressHp >= progressHp2) {
                        this.changeState(CombatDungeonState.Success);
                    } else {
                        this.changeState(CombatDungeonState.Failure);
                    }
                    return;
                }
            }
        }
    }

    /**
     * 切换游戏速度
     */
    protected onClickSwitchGameSpeed(): void {
        const baseData = this.dungeonPvpCtl.getBaseData();
        if (!baseData || baseData.state === CombatDungeonState.Init) {
            return;
        }
        const combatInfo = TBDungeonCombat.getInstance().getDataByType(baseData.type);
        if (!combatInfo.accelerationSwitch) {
            return;
        }

        this.switchGameSpeed();
    }

    /**
     * 跳过战斗
     */
    protected onClickSkipCombat(): void {
        const baseData = this.dungeonPvpCtl.getBaseData();
        if (!baseData || baseData.state === CombatDungeonState.Init) {
            return;
        }

        this.skipCombat();
    }
}
