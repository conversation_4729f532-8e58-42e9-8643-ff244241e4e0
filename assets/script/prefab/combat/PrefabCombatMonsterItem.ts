/*
 * @Author: chenx
 * @Date: 2024-02-23 16:45:09
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 16:09:47
 */
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import MathUtils from "../../../nsn/util/MathUtils";
import Time from "../../../nsn/util/Time";
import Utils from "../../../nsn/util/Utils";
import { EnumMonsterBaseType } from "../../data/base/BaseMonsterBase";
import { EnumSkillType } from "../../data/base/BaseSkill";
import { EnumSkillEffectEffectType } from "../../data/base/BaseSkillEffect";
import { EnumTaskDetailType } from "../../data/base/BaseTaskDetail";
import { EnumUnionPara } from "../../data/base/BaseUnion";
import TBUnion from "../../data/parser/TBUnion";
import TBUnionBoss from "../../data/parser/TBUnionBoss";
import ArcherClientConfig, { ISpineData } from "../../game/ArcherClientConfig";
import Combat, { CombatEvent, DungeonType } from "../../game/Combat";
import { CombatMemberState, ICombatMemberData } from "../../game/combat/CombatMember";
import CombatMemberMonster, { ICombatMonsterData } from "../../game/combat/CombatMemberMonster";
import { ICombatMemberSkillData } from "../../game/combat/CombatSkill";
import CombatLog, { CombatLogId } from "../../game/CombatLog";
import Task from "../../game/Task";
import SpineUtils from "../../utils/SpineUtils";

/**
 * 动画名称-怪兽
 */
enum AniName {
    Show = "born", // 出现
    Wait = "wait", // 待机
    Move = "move", // 移动
    Attack = "attack", // 攻击
    Cast = "skill", // 施法
    BeAttacked = "blow", // 被攻击
    Die = "die", // 死亡
    Change = "change", // 切换
}

/**
 * 动画事件名称-怪兽
 */
const ANI_EVENT_NAME = "trigger";

/**
 * 被攻击动画总cd
 */
const BE_ATTACKED_ANI_TOTAL_CD = 3;

/**
 * 挂点名称
 */
enum AttachedName {
    BeAttacked = "beAttacked",
}

/**
 * 血量状态参数
 */
const HP_STATE_PARA = [0.75, 0.5, 0];

const { ccclass, property } = cc._decorator;

/**
 * 战斗-怪兽item
 */
@ccclass
export default class PrefabCombatMonsterItem extends I18nComponent {
    @property(sp.Skeleton)
    spineMonster: sp.Skeleton = null; // 怪兽
    @property(cc.ProgressBar)
    prgHp: cc.ProgressBar = null; // 血量
    @property(cc.ProgressBar)
    prgHp2: cc.ProgressBar = null; // 血量-过渡
    @property(cc.ProgressBar)
    prgHp3: cc.ProgressBar = null; // 血量-护盾

    private monster: CombatMemberMonster = null; // 怪兽
    private baseData: ICombatMemberData = null; // 怪兽基础数据
    private data: ICombatMonsterData = null; // 怪兽数据

    private spineData: ISpineData = null; // 骨骼数据
    private waitTriggerState: CombatMemberState = null; // 待触发状态
    private stateTime: number = 0; // 状态时间
    private castCb: () => void = null; // 施法回调
    private castTime: number = 0; // 施法时间
    private beAttackedAniCd: number = 0; // 被攻击动画cd
    private beAttackPoint: cc.Vec2 = cc.v2(); // 被攻击点
    private waitRecoverState: CombatMemberState = null; // 待恢复状态
    private gameSpeed: number = 1; // 游戏速度
    private isSkipingCombat: boolean = false; // 是否正在跳过战斗

    protected reuse(): void {}

    protected unuse(): void {
        this.monster = null;
        this.baseData = null;
        this.data = null;

        this.waitTriggerState = null;
        this.stateTime = 0;
        this.castCb = null;
        this.castTime = 0;
        this.stateTime = 0;
        this.castTime = 0;
        this.beAttackedAniCd = 0;
        this.beAttackPoint = cc.v2();
        this.waitRecoverState = null;
        this.gameSpeed = 1;
        this.isSkipingCombat = false;

        this.node.opacity = 0;

        this.spineMonster.node.active = false;
        cc.Tween.stopAllByTarget(this.spineMonster.node);
        this.spineMonster.node.color = cc.color(255, 255, 255);
        this.spineMonster.setCompleteListener(null);
        this.spineMonster.clearTracks();
        this.spineMonster.node.skeleton(null);
        // @ts-ignore
        const attachUtil = this.spineMonster.attachUtil;
        attachUtil.destroyAllAttachedNodes();

        this.prgHp.node.opacity = 0;
        cc.Tween.stopAllByTarget(this.prgHp2);
    }

    /**
     * 初始化数据
     * @param monster 怪兽
     */
    public initData(monster: CombatMemberMonster): void {
        this.monster = monster;
        this.baseData = this.monster.getBaseData();
        this.data = this.monster.getData();
    }

    /**
     * 设置状态
     * @param state 状态
     */
    public setState(state: CombatMemberState): void {
        this.data.state = state;

        switch (state) {
            case CombatMemberState.Wait:
                this.node.opacity = 255;

                this.spineMonster.setCompleteListener(null);
                this.spineMonster.clearTracks();
                switch (this.baseData.dungeonType) {
                    case DungeonType.Thief:
                        const progressHp = MathUtils.floor(this.baseData.hp / this.baseData.totalHp, 3);
                        const index = HP_STATE_PARA.findIndex((e) => progressHp >= e);
                        this.spineMonster.setAnimation(0, `${AniName.Move}${index + 1}`, true);
                        break;
                    default:
                        this.spineMonster.setAnimation(0, AniName.Wait, true);
                        break;
                }
                break;
            default:
                break;
        }
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatMemberState): void {
        this.data.state = state;

        switch (this.data.state) {
            case CombatMemberState.Init:
                this.node.opacity = 0;

                this.spineMonster.node.active = true;

                this.setEffect();
                this.updateHpState(true);
                break;
            case CombatMemberState.WaitEnter:
                this.data.enterDelay <= 0 && this.changeState(CombatMemberState.Enter);
                break;
            case CombatMemberState.Enter:
                this.node.opacity = 255;
                switch (this.data.info.type) {
                    case EnumMonsterBaseType.OrdinaryMonster:
                        this.spineMonster.timeScale = 2;
                        this.spineMonster.setAnimation(0, AniName.Move, true);
                        break;
                    case EnumMonsterBaseType.BossMonster:
                        this.spineMonster.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                            if (trackEntry.animation.name === AniName.Show) {
                                this.spineMonster.setCompleteListener(null);

                                this.changeState(CombatMemberState.Wait);

                                switch (this.baseData.dungeonType) {
                                    case DungeonType.Thief:
                                        Combat.getInstance().emit(
                                            CombatEvent.SceneBgMove,
                                            this.baseData.dungeonType,
                                            true,
                                            300
                                        );
                                        break;
                                    default:
                                        break;
                                }
                            }
                        });
                        this.spineMonster.setAnimation(0, AniName.Show, false);
                        break;
                    default:
                        break;
                }
                break;
            case CombatMemberState.Move:
                if (!this.isSkipingCombat) {
                    if (this.spineMonster.animation !== AniName.Move) {
                        this.spineMonster.setAnimation(0, AniName.Move, true);
                    }
                }
                break;
            case CombatMemberState.Wait:
                if (!this.isSkipingCombat) {
                    switch (this.baseData.dungeonType) {
                        case DungeonType.Thief:
                            const progressHp = MathUtils.floor(this.baseData.hp / this.baseData.totalHp, 3);
                            const index = HP_STATE_PARA.findIndex((e) => progressHp >= e);
                            if (this.spineMonster.animation !== `${AniName.Move}${index + 1}`) {
                                this.spineMonster.setAnimation(0, `${AniName.Move}${index + 1}`, true);
                            }
                            break;
                        default:
                            if (this.spineMonster.animation !== AniName.Wait) {
                                this.spineMonster.setAnimation(0, AniName.Wait, true);
                            }
                            break;
                    }
                }
                break;
            case CombatMemberState.Die:
                Task.getInstance().setTaskProgress(EnumTaskDetailType.HuntMonsterTask, 1);
                break;
            default:
                break;
        }
    }

    /**
     * 更新状态
     * @param dt
     */
    public updateState(dt: number): void {
        switch (this.data.state) {
            case CombatMemberState.WaitEnter:
                if (this.data.enterDelay > 0) {
                    this.data.enterDelay -= dt;
                    if (this.data.enterDelay <= 0) {
                        this.changeState(CombatMemberState.Enter);
                    }
                }
                break;
            case CombatMemberState.Enter:
                switch (this.data.info.type) {
                    case EnumMonsterBaseType.OrdinaryMonster:
                        const velocity = this.data.info.speed * 2 * dt;
                        const linearVelocity = cc.v2(
                            velocity * Math.cos(MathUtils.d2a(this.data.angle)),
                            velocity * Math.sin(MathUtils.d2a(this.data.angle))
                        );
                        this.node.x += linearVelocity.x;
                        this.node.y += linearVelocity.y;

                        if (this.data.targetPos.x >= this.data.initPos.x) {
                            if (this.node.x >= this.data.initPos.x + this.node.width / 2) {
                                this.spineMonster.timeScale = 1;

                                this.changeState(CombatMemberState.Move);
                            }
                        } else {
                            if (this.node.x <= this.data.initPos.x - this.node.width / 2) {
                                this.spineMonster.timeScale = 1;

                                this.changeState(CombatMemberState.Move);
                            }
                        }
                        break;
                    case EnumMonsterBaseType.BossMonster:
                        break;
                    default:
                        break;
                }
                break;
            case CombatMemberState.Move:
                const velocity2 = this.data.info.speed * dt;
                const linearVelocity2 = cc.v2(
                    velocity2 * Math.cos(MathUtils.d2a(this.data.angle)),
                    velocity2 * Math.sin(MathUtils.d2a(this.data.angle))
                );
                this.node.x += linearVelocity2.x;
                this.node.y += linearVelocity2.y;

                if (this.data.targetPos.x >= this.data.initPos.x) {
                    if (this.node.x + this.node.width / 2 >= this.data.targetPos.x) {
                        this.changeState(CombatMemberState.Wait);
                    }
                } else {
                    if (this.node.x - this.node.width / 2 <= this.data.targetPos.x) {
                        this.changeState(CombatMemberState.Wait);
                    }
                }
                break;
            case CombatMemberState.Cast:
                if (this.stateTime > 0) {
                    this.stateTime -= dt;
                    this.stateTime = MathUtils.round(this.stateTime, 3);
                    if (this.stateTime <= 0) {
                        this.changeState(this.waitTriggerState);
                        this.waitTriggerState = null;
                    }
                }

                if (this.castTime > 0) {
                    this.castTime -= dt;
                    this.castTime = MathUtils.round(this.castTime, 3);
                    if (this.castTime <= 0) {
                        this.castCb();
                        this.castCb = null;
                    }
                }
                break;
            default:
                break;
        }

        this.beAttackedAniCd > 0 && (this.beAttackedAniCd -= dt);
    }

    /**
     * 设置特效-怪兽
     */
    private setEffect(): void {
        SpineUtils.setMonsterWithoutAniName(this.spineMonster, this.data.info.res, () => {
            if (!this.data) {
                this.spineMonster.node.skeleton(null);
                return;
            }

            this.spineData = ArcherClientConfig.getInstance().getSpineData(`efMonster${this.data.info.res}`);

            this.node.width = Math.floor(this.spineMonster.node.width);
            this.node.height = Math.floor(this.spineMonster.node.height);
            switch (this.data.info.type) {
                case EnumMonsterBaseType.OrdinaryMonster:
                    this.node.setPosition(
                        this.data.initPos.x + (this.node.width / 2) * this.data.scaleX,
                        this.data.initPos.y
                    );
                    break;
                case EnumMonsterBaseType.BossMonster:
                    this.node.setPosition(
                        this.data.targetPos.x + (this.node.width / 2) * this.data.scaleX,
                        this.data.targetPos.y
                    );
                    break;
                default:
                    break;
            }

            this.prgHp.node.y = this.node.height + 20;
            this.prgHp.node.opacity = this.data.info.type === EnumMonsterBaseType.OrdinaryMonster ? 255 : 0;

            // @ts-ignore
            const attachUtil = this.spineMonster.attachUtil;
            attachUtil.destroyAllAttachedNodes();
            attachUtil.generateAllAttachedNodes();

            this.changeState(CombatMemberState.WaitEnter);
        });

        this.changeState(CombatMemberState.Load);
    }

    /**
     * 获取挂点
     * @param name 名称
     * @returns
     */
    private getAttached(name: string): cc.Node {
        // @ts-ignore
        const attachUtil = this.spineMonster.attachUtil;
        const nodeAttached: cc.Node[] = attachUtil.getAttachedNodes(name);

        return nodeAttached[0];
    }

    /**
     * 获取被攻击点
     * @returns
     */
    public getBeAttackedPoint(): cc.Vec2 {
        return this.node.convertToWorldSpaceAR(cc.v2());
    }

    /**
     * 获取被攻击点
     * @returns
     */
    public getBeAttackedPoint2(): cc.Vec2 {
        switch (this.baseData.dungeonType) {
            case DungeonType.Thief:
                const progressHp = MathUtils.floor(this.baseData.hp / this.baseData.totalHp, 3);
                const index = HP_STATE_PARA.findIndex((e) => progressHp >= e);
                const nodeAttached = this.getAttached(`${AttachedName.BeAttacked}${index + 1}`);
                const point = nodeAttached.convertToWorldSpaceAR(cc.v2());
                (point.x !== 0 || point.y !== 0) && (this.beAttackPoint = point);

                return this.beAttackPoint;
            default:
                return this.node.convertToWorldSpaceAR(cc.v2(0, this.node.height / 2));
        }
    }

    /**
     * 获取伤害点
     * @returns
     */
    public getDamagePoint(): cc.Vec2 {
        switch (this.data.info.type) {
            case EnumMonsterBaseType.BossMonster:
                return this.node.convertToWorldSpaceAR(cc.v2(0, this.node.height / 2));
            default:
                return this.node.convertToWorldSpaceAR(cc.v2(0, this.node.height));
        }
    }

    /**
     * 获取高度
     * @returns
     */
    private getHeight(): number {
        switch (this.data.info.type) {
            case EnumMonsterBaseType.BossMonster:
                return this.node.height / 2;
            default:
                return this.node.height;
        }
    }

    /**
     * 更新血量状态
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public updateHpState(isInit: boolean = false, isPlayAni: boolean = false): void {
        let shield = 0;
        this.baseData.buffData.forEach((e) => {
            if (e.info.effectType === EnumSkillEffectEffectType.Buff703) {
                e.layerData.forEach(([, , tempShield]) => {
                    shield += tempShield;
                });
            }
        });
        const progressHp = MathUtils.floor(this.baseData.hp / (this.baseData.totalHp + shield), 3);
        const progressShield = MathUtils.floor((this.baseData.hp + shield) / (this.baseData.totalHp + shield), 3);
        if (isInit) {
            this.prgHp.progress = progressHp;
            this.prgHp2.progress = progressHp;
            this.prgHp3.progress = progressShield;
            this.scheduleOnce(() => {
                this.prgHp.barSprite.node.opacity = 255;
                this.prgHp2.barSprite.node.opacity = 255;
                this.prgHp3.barSprite.node.opacity = 255;
            });
        } else {
            this.prgHp.progress = progressHp;
            this.prgHp.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
            this.prgHp3.progress = progressShield;
            this.prgHp3.barSprite.node.opacity = progressShield !== 0 ? 255 : 0;
            if (!isPlayAni) {
                this.prgHp2.progress = progressHp;
                this.prgHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
            } else {
                cc.tween(this.prgHp2)
                    .to(0.8, { progress: progressHp })
                    .call(() => {
                        this.prgHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                    })
                    .start();
            }
        }
    }

    /**
     * 被攻击
     * @param damage 伤害
     * @param isPlayAni 是否播放被攻击动画
     * @param isSkipingCombat 是否正在跳过战斗
     */
    public beAttacked(damage: number, isPlayAni: boolean, isSkipingCombat: boolean): void {
        const buffData703 = this.baseData.buffData.filter(
            (e) => e.info.effectType === EnumSkillEffectEffectType.Buff703 && e.layerData.length !== 0
        );
        for (const e of buffData703) {
            const oldLayerData = Utils.clone(e.layerData);
            for (let i = e.layerData.length - 1; i >= 0; i--) {
                if (damage >= e.layerData[i][2]) {
                    damage -= e.layerData[i][2];
                    e.layerData.splice(i, 1);
                } else {
                    e.layerData[i][2] -= damage;
                    damage = 0;
                }

                if (damage <= 0) {
                    break;
                }
            }

            if (CombatLog.getInstance().getShowState(CombatLogId.BuffClear)) {
                if (CombatLog.getInstance().getShowStateByDungeonType(this.baseData.dungeonType)) {
                    CombatLog.getInstance().logTitle(CombatLogId.BuffClear, this.monster);

                    cc.log({
                        buffId: e.id,
                        clearAdd: oldLayerData.length - e.layerData.length,
                        oldLayerData,
                        layerData: Utils.clone(e.layerData),
                        maxLayerData: Utils.clone(e.maxLayerData),
                        addMaxLayerData: Utils.clone(e.addMaxLayerData),
                        time: Time.getInstance().now(),
                    });
                }
            }

            if (damage <= 0) {
                break;
            }
        }

        let index = -1;
        let index2 = -1;
        switch (this.baseData.dungeonType) {
            case DungeonType.Thief:
                const progressHp = MathUtils.floor(this.baseData.hp / this.baseData.totalHp, 3);
                index = HP_STATE_PARA.findIndex((e) => progressHp >= e);
                damage > 0 && (this.baseData.hp = Math.max(this.baseData.hp - damage, 0));
                const progressHp2 = MathUtils.floor(this.baseData.hp / this.baseData.totalHp, 3);
                index2 = HP_STATE_PARA.findIndex((e) => progressHp2 >= e);
                break;
            case DungeonType.Union:
                const monsterHpInfo = TBUnionBoss.getInstance().getList();
                const totalDamage = this.baseData.totalHp - this.baseData.hp;
                index = monsterHpInfo.findIndex((e) => totalDamage < e.addHp);
                index === -1 && (index = monsterHpInfo.length - 1);
                damage > 0 && (this.baseData.hp = Math.max(this.baseData.hp - damage, 0));
                const totalDamage2 = this.baseData.totalHp - this.baseData.hp;
                index2 = monsterHpInfo.findIndex((e) => totalDamage2 < e.addHp);
                index2 === -1 && (index2 = monsterHpInfo.length - 1);
                break;
            default:
                damage > 0 && (this.baseData.hp = Math.max(this.baseData.hp - damage, 0));
                break;
        }

        !isSkipingCombat && this.updateHpState(false, buffData703.length <= 0);

        if (this.baseData.hp <= 0) {
            this.changeState(CombatMemberState.Die);

            switch (this.baseData.dungeonType) {
                case DungeonType.Thief:
                    break;
                default:
                    this.playDieAni(isSkipingCombat);
                    break;
            }

            Combat.getInstance().emit(
                CombatEvent.KillMonster,
                this.baseData.dungeonType,
                this.baseData.uuid,
                this.getDamagePoint(),
                this.getHeight()
            );
            return;
        }
        if (isSkipingCombat) {
            return;
        }
        if (
            this.data.state !== CombatMemberState.Wait &&
            this.data.state !== CombatMemberState.Move &&
            this.data.state !== CombatMemberState.BeAttacked
        ) {
            return;
        }

        let tempIsPlayAni = isPlayAni && this.data.state !== CombatMemberState.BeAttacked && this.beAttackedAniCd <= 0;
        let aniName: string = AniName.BeAttacked;
        switch (this.baseData.dungeonType) {
            case DungeonType.Thief:
                if (index !== index2) {
                    tempIsPlayAni = true;
                    aniName = `${AniName.Change}${index + 1}`;

                    Combat.getInstance().emit(
                        CombatEvent.SceneBgMove,
                        this.baseData.dungeonType,
                        true,
                        300 * (index === 0 ? 1.4 : 1.8)
                    );
                } else {
                    aniName = `${AniName.BeAttacked}${index + 1}`;
                }
                break;
            case DungeonType.Union:
                if (index !== index2) {
                    const para: number[][] = TBUnion.getInstance().getValueByPara(EnumUnionPara.BlowTrigger);
                    const tempIndex = para.findIndex((e) => index2 % e[0] === 0);
                    if (tempIndex !== -1) {
                        tempIsPlayAni = true;
                        aniName = `${AniName.BeAttacked}${para[tempIndex][1]}`;
                    }
                }
                break;
            default:
                break;
        }
        if (tempIsPlayAni) {
            !this.waitRecoverState && (this.waitRecoverState = this.data.state);
            this.spineMonster.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                if (trackEntry.animation.name === aniName) {
                    this.spineMonster.setCompleteListener(null);

                    this.beAttackedAniCd = BE_ATTACKED_ANI_TOTAL_CD;

                    this.changeState(this.waitRecoverState);
                    this.waitRecoverState = null;
                }
            });
            this.spineMonster.setAnimation(0, aniName, false);

            cc.Tween.stopAllByTarget(this.spineMonster.node);
            this.spineMonster.node.color = cc.color(255, 130, 130);
            cc.tween(this.spineMonster.node)
                .delay(0.5)
                .call(() => {
                    this.spineMonster.node.color = cc.color(255, 255, 255);
                })
                .start();

            this.changeState(CombatMemberState.BeAttacked);
        }
    }

    /**
     * 设置游戏速度
     * @param speed 速度
     */
    public setGameSpeed(speed: number): void {
        this.gameSpeed = speed;

        this.spineMonster.timeScale = this.gameSpeed;
    }

    /**
     * 播放施法动画
     * @param memberSkillData 成员技能数据
     * @param castCb 施法回调
     * @param isSkipingCombat 是否正在跳过战斗
     */
    public playCastAni(memberSkillData: ICombatMemberSkillData, castCb: () => void, isSkipingCombat: boolean): void {
        this.isSkipingCombat = isSkipingCombat;

        let aniName = "";
        switch (memberSkillData.info.type) {
            case EnumSkillType.MonsterAtk:
                aniName = AniName.Attack;
                break;
            case EnumSkillType.MonsterASkill:
                aniName = AniName.Cast;
                break;
            default:
                break;
        }
        if (!this.isSkipingCombat) {
            this.spineMonster.setAnimation(0, aniName, false);
            switch (this.data.state) {
                case CombatMemberState.Move:
                    this.spineMonster.addAnimation(0, AniName.Move, true);
                    break;
                case CombatMemberState.Wait:
                    switch (this.baseData.dungeonType) {
                        case DungeonType.Thief:
                            const progressHp = MathUtils.floor(this.baseData.hp / this.baseData.totalHp, 3);
                            const index = HP_STATE_PARA.findIndex((e) => progressHp >= e);
                            this.spineMonster.addAnimation(0, `${AniName.Move}${index + 1}`, true);
                            break;
                        default:
                            this.spineMonster.addAnimation(0, AniName.Wait, true);
                            break;
                    }
                    break;
                default:
                    break;
            }
        }

        this.waitTriggerState = this.data.state;
        this.stateTime = this.spineData[aniName].duration;

        this.castCb = castCb;
        this.castTime = this.spineData[aniName].event[ANI_EVENT_NAME][0];

        this.changeState(CombatMemberState.Cast);
    }

    /**
     * 播放死亡动画
     * @param isSkipingCombat 是否正在跳过战斗
     */
    public playDieAni(isSkipingCombat: boolean): void {
        if (isSkipingCombat) {
            this.changeState(CombatMemberState.Put);
            return;
        }

        switch (this.baseData.dungeonType) {
            case DungeonType.Thief:
                const tempAniName = `${AniName.Change}3`;
                this.spineMonster.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                    const aniName = trackEntry.animation ? trackEntry.animation.name : "";
                    if (aniName === tempAniName) {
                        this.spineMonster.setCompleteListener(null);

                        this.changeState(CombatMemberState.Put);
                    }
                });
                this.spineMonster.setAnimation(0, tempAniName, false);
                break;
            default:
                this.spineMonster.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                    const aniName = trackEntry.animation ? trackEntry.animation.name : "";
                    if (aniName === AniName.Die) {
                        this.spineMonster.setCompleteListener(null);

                        this.changeState(CombatMemberState.Put);
                    }
                });
                this.spineMonster.setAnimation(0, AniName.Die, false);
                break;
        }
    }
}
