/*
 * @Author: chenx
 * @Date: 2024-02-23 17:13:45
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 16:09:14
 */
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import MathUtils from "../../../nsn/util/MathUtils";
import { EnumSkillType } from "../../data/base/BaseSkill";
import ArcherClientConfig, { ISpineData } from "../../game/ArcherClientConfig";
import { CombatMemberState } from "../../game/combat/CombatMember";
import { ICombatArcherData } from "../../game/combat/CombatMemberPlayer";
import { ICombatMemberSkillData } from "../../game/combat/CombatSkill";
import SpineUtils from "../../utils/SpineUtils";

/**
 * 动画名称-弓箭手
 */
enum AniName {
    Wait = "attackWait", // 待机
    Attack = "attack", // 普攻
    Skill = "skill1", // 技能
}

/**
 * 动画事件名称-弓箭手
 */
const ANI_EVENT_NAME = "trigger";

const { ccclass, property } = cc._decorator;

/**
 * 战斗-弓箭手item
 */
@ccclass
export default class PrefabCombatArcherItem extends I18nComponent {
    @property(sp.Skeleton)
    spineArcher: sp.Skeleton = null; // 弓箭手

    private data: ICombatArcherData = null; // 弓箭手数据

    private spineData: ISpineData = null; // 骨骼数据
    private waitTriggerState: CombatMemberState = null; // 待触发状态
    private stateTime: number = 0; // 状态时间
    private castCb: () => void = null; // 施法回调
    private castTime: number = 0; // 施法时间
    private archerIdByEffect: number = -1; // 弓箭手id-特效
    private gameSpeed: number = 1; // 游戏速度
    private gameSpeed2: number = 1; // 游戏速度-普攻
    private isSkipingCombat: boolean = false; // 是否正在跳过战斗

    /**
     * 初始化数据
     * @param archerData 弓箭手数据
     */
    public initData(archerData: ICombatArcherData): void {
        this.data = archerData;
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatMemberState): void {
        this.data.state = state;

        switch (this.data.state) {
            case CombatMemberState.Init:
                this.waitTriggerState = null;
                this.stateTime = 0;
                this.castCb = null;
                this.castTime = 0;
                this.isSkipingCombat = false;

                this.spineArcher.setCompleteListener(null);
                this.spineArcher.timeScale = this.gameSpeed;
                this.spineArcher.clearTracks();
                break;
            case CombatMemberState.Wait:
                if (!this.isSkipingCombat) {
                    if (this.spineArcher.animation !== AniName.Wait) {
                        this.spineArcher.setCompleteListener(null);
                        this.spineArcher.timeScale = this.gameSpeed;
                        this.spineArcher.setAnimation(0, AniName.Wait, true);
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 更新状态
     * @param dt
     */
    public updateState(dt: number): void {
        switch (this.data.state) {
            case CombatMemberState.Attack:
                if (this.stateTime > 0) {
                    this.stateTime -= dt * this.gameSpeed2;
                    this.stateTime = MathUtils.round(this.stateTime, 3);
                    if (this.stateTime <= 0) {
                        this.changeState(this.waitTriggerState);
                        this.waitTriggerState = null;
                    }
                }

                if (this.castTime > 0) {
                    this.castTime -= dt * this.gameSpeed2;
                    this.castTime = MathUtils.round(this.castTime, 3);
                    if (this.castTime <= 0) {
                        this.castCb();
                        this.castCb = null;
                    }
                }
                break;
            case CombatMemberState.Skill:
                if (this.stateTime > 0) {
                    this.stateTime -= dt;
                    this.stateTime = MathUtils.round(this.stateTime, 3);
                    if (this.stateTime <= 0) {
                        this.changeState(this.waitTriggerState);
                        this.waitTriggerState = null;
                    }
                }

                if (this.castTime > 0) {
                    this.castTime -= dt;
                    this.castTime = MathUtils.round(this.castTime, 3);
                    if (this.castTime <= 0) {
                        this.castCb();
                        this.castCb = null;
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 重置状态
     */
    public resetState(): void {
        this.changeState(CombatMemberState.Init);
        this.changeState(CombatMemberState.Wait);
    }

    /**
     * 设置特效
     */
    public setEffect(): void {
        if (this.archerIdByEffect === this.data.id) {
            return;
        }

        this.archerIdByEffect = this.data.id;

        this.changeState(CombatMemberState.Init);

        this.spineArcher.node.active = this.archerIdByEffect !== -1;
        if (this.archerIdByEffect === -1) {
            this.spineArcher.node.skeleton(null);
            // @ts-ignore
            const attachUtil = this.spineArcher.attachUtil;
            attachUtil.destroyAllAttachedNodes();
        } else {
            SpineUtils.setArcherWithoutAniName(this.spineArcher, this.data.info.res, () => {
                this.spineData = ArcherClientConfig.getInstance().getSpineData(`efArcher${this.data.info.res}`);

                const scale = this.spineArcher.node.scale;
                this.node.width = Math.floor(this.spineArcher.node.width * scale);
                this.node.height = Math.floor(this.spineArcher.node.height * scale);

                // @ts-ignore
                const attachUtil = this.spineArcher.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                this.changeState(CombatMemberState.Wait);
            });
        }
    }

    /**
     * 获取挂点
     * @param name 名称
     * @returns
     */
    public getAttachedNode(name: string): cc.Node {
        // @ts-ignore
        const attachUtil = this.spineArcher.attachUtil;
        const attachedNode: cc.Node[] = attachUtil.getAttachedNodes(name);

        return attachedNode.length !== 0 ? attachedNode[0] : null;
    }

    /**
     * 设置游戏速度
     * @param speed 速度
     * @param attackHaste 普攻急速
     */
    public setGameSpeed(speed: number, attackHaste: number): void {
        this.gameSpeed = speed;
        this.gameSpeed2 = this.gameSpeed / (1 - attackHaste);

        switch (this.data.state) {
            case CombatMemberState.Attack:
                this.spineArcher.timeScale = this.gameSpeed2;
                break;
            default:
                this.spineArcher.timeScale = this.gameSpeed;
                break;
        }
    }

    /**
     * 播放施法动画
     * @param memberSkillData 成员技能数据
     * @param castCb 施法回调
     * @param isSkipingCombat 是否正在跳过战斗
     */
    public playCastAni(memberSkillData: ICombatMemberSkillData, castCb: () => void, isSkipingCombat: boolean): void {
        this.isSkipingCombat = isSkipingCombat;

        let aniName = "";
        let gameSpeed = 1;
        let changeState: CombatMemberState = null;
        switch (memberSkillData.info.type) {
            case EnumSkillType.ArcherAtk:
                aniName = AniName.Attack;
                gameSpeed = this.gameSpeed2;
                changeState = CombatMemberState.Attack;
                break;
            case EnumSkillType.ArcherASkill:
                aniName = AniName.Skill;
                gameSpeed = this.gameSpeed;
                changeState = CombatMemberState.Skill;
                break;
            default:
                break;
        }
        if (!this.isSkipingCombat) {
            this.spineArcher.timeScale = gameSpeed;
            this.spineArcher.setCompleteListener(() => {
                this.spineArcher.setCompleteListener(null);

                this.spineArcher.timeScale = this.gameSpeed;
            });
            this.spineArcher.setAnimation(0, aniName, false);
            this.spineArcher.addAnimation(0, AniName.Wait, true);
        }

        this.waitTriggerState = this.data.state;
        this.stateTime = this.spineData[aniName].duration;

        this.castCb = castCb;
        this.castTime = this.spineData[aniName].event[ANI_EVENT_NAME][0];

        this.changeState(changeState);
    }
}
