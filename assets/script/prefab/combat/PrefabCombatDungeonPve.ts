/*
 * @Author: chenx
 * @Date: 2025-01-16 11:19:54
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 15:01:07
 */
import UI from "../../../nsn/ui/UI";
import ArrayUtils from "../../../nsn/util/ArrayUtils";
import MathUtils from "../../../nsn/util/MathUtils";
import TextUtils from "../../../nsn/util/TextUtils";
import Tips from "../../../nsn/util/Tips";
import Utils from "../../../nsn/util/Utils";
import i18n from "../../config/i18n/I18n";
import { EnumDungeonType } from "../../data/base/BaseDungeon";
import { EnumMonsterBaseMoveType, EnumMonsterBaseType } from "../../data/base/BaseMonsterBase";
import { EnumSkillEffectEffectType } from "../../data/base/BaseSkillEffect";
import { EnumUnionPara } from "../../data/base/BaseUnion";
import TBDungeon from "../../data/parser/TBDungeon";
import TBDungeonBoss from "../../data/parser/TBDungeonBoss";
import TBDungeonCloud from "../../data/parser/TBDungeonCloud";
import TBDungeonCombat from "../../data/parser/TBDungeonCombat";
import TBDungeonThief from "../../data/parser/TBDungeonThief";
import TBDungeonTower from "../../data/parser/TBDungeonTower";
import TBMonsterBase from "../../data/parser/TBMonsterBase";
import TBMonsterFormation from "../../data/parser/TBMonsterFormation";
import TBThiefBloodReward from "../../data/parser/TBThiefBloodReward";
import TBTrialBoss from "../../data/parser/TBTrialBoss";
import TBUnion from "../../data/parser/TBUnion";
import TBUnionBoss from "../../data/parser/TBUnionBoss";
import Combat, { CombatEvent, DungeonType } from "../../game/Combat";
import CombatLog, { CombatLogId } from "../../game/CombatLog";
import CombatSetting, { CombatSettingId } from "../../game/CombatSetting";
import DungeonBoss from "../../game/DungeonBoss";
import DungeonCloud from "../../game/DungeonCloud";
import DungeonThief from "../../game/DungeonThief";
import DungeonTower from "../../game/DungeonTower";
import Setting from "../../game/Setting";
import Union from "../../game/Union";
import ActivityNewbieTrial from "../../game/activity/ActivityNewbieTrial";
import { CombatDungeonState } from "../../game/combat/CombatDungeon";
import CombatDungeonPve from "../../game/combat/CombatDungeonPve";
import { CombatMemberState } from "../../game/combat/CombatMember";
import CombatMemberMonster from "../../game/combat/CombatMemberMonster";
import CombatMemberPlayer, { CombatPlayerType } from "../../game/combat/CombatMemberPlayer";
import { PrefabCombatDungeon } from "./PrefabCombatDungeon";
import { FLY_MONSTER_FORMATION, MONSTER_FORMATION_NUM, WALK_MONSTER_FORMATION } from "./PrefabCombatScene";

/**
 * 界面参数
 */
interface IPrefabCombatDungeonPveArgs {
    type: DungeonType; // 类型
    levelId: number; // 关卡id
}

const { ccclass, property } = cc._decorator;

/**
 * pve副本
 */
@ccclass
export default class PrefabCombatDungeonPve extends PrefabCombatDungeon {
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    nodeWave: cc.Node = null; // 波数
    @property(cc.Node)
    nodeWave2: cc.Node = null; // 波数
    @property(cc.ProgressBar)
    prgMonsterHp: cc.ProgressBar = null; // 怪兽血量
    @property(cc.ProgressBar)
    prgMonsterHp2: cc.ProgressBar = null; // 怪兽血量-过渡
    @property(cc.ProgressBar)
    prgMonsterHp3: cc.ProgressBar = null; // 怪兽血量-护盾
    @property(cc.Sprite)
    spMonsterBaseHp: cc.Sprite = null; // 基础血条
    @property(cc.Label)
    lbtMonsterHp: cc.Label = null; // 怪兽血量
    @property(cc.ProgressBar)
    prgTime: cc.ProgressBar = null; // 时间
    @property(cc.Label)
    lbtTime: cc.Label = null; // 时间
    @property(cc.Node)
    nodeAuto: cc.Node = null; // 自动按钮-释放技能
    @property(cc.Node)
    nodeAutoTag: cc.Node = null; // 自动tag-释放技能
    @property(cc.Node)
    nodeGameSpeed: cc.Node = null; // 游戏速度按钮
    @property(cc.Node)
    nodeSkipCombat: cc.Node = null; // 跳过战斗按钮

    @property([cc.SpriteFrame])
    sfMonsterHp: cc.SpriteFrame[] = []; // 怪兽血量
    @property([cc.SpriteFrame])
    sfMonsterHp2: cc.SpriteFrame[] = []; // 怪兽血量

    private initData: IPrefabCombatDungeonPveArgs = null; // 初始化数据
    protected dungeonPveCtl: CombatDungeonPve = CombatDungeonPve.getInstance(); // pve副本ctl
    private skipCombatTime: number = 0; // 跳过战斗倒计时
    private isUpdateMultipleHpState: boolean = true; // 是否更新多血条状态
    private isPlayingPassWaveAni: boolean = false; // 是否正在播放动画-过波动画

    protected onLoad(): void {
        this.initData = this.args;
        this.dungeonPveCtl.initCombat(this.initData.type, this.initData.levelId);
        this.setDungeonCtl(this.dungeonPveCtl);
        this.loadScene();
    }

    protected registerHandler(): void {
        super.registerHandler();

        // 战斗-重置
        Combat.getInstance().on(
            CombatEvent.Reset,
            () => {
                const pveBaseData = this.dungeonPveCtl.getBaseData();
                if (pveBaseData && pveBaseData.state === CombatDungeonState.Pause) {
                    this.changeState(CombatDungeonState.Reset);
                }
            },
            this
        );
        // 战斗-取消暂停
        Combat.getInstance().on(
            CombatEvent.CancelPause,
            () => {
                const pveBaseData = this.dungeonPveCtl.getBaseData();
                if (pveBaseData && pveBaseData.state === CombatDungeonState.Pause) {
                    this.changeState(CombatDungeonState.Combat);
                }
            },
            this
        );
        // 战斗-下一关
        Combat.getInstance().on(
            CombatEvent.NextLevel,
            (nextLevelId: number) => {
                const pveBaseData = this.dungeonPveCtl.getBaseData();
                if (pveBaseData && pveBaseData.state === CombatDungeonState.Success) {
                    const pveData = this.dungeonPveCtl.getData();
                    pveData.levelId = nextLevelId;

                    this.changeState(CombatDungeonState.Reset);
                }
            },
            this
        );
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatDungeonState): void {
        super.changeState(state);

        const baseData = this.dungeonPveCtl.getBaseData();
        const pveData = this.dungeonPveCtl.getData();
        const allPlayer = this.dungeonPveCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerData = player.getData();

        switch (baseData.state) {
            case CombatDungeonState.Init:
                this.updateLevelInfo(true);
                this.sceneCtl.initPlayerInfo(player);
                this.sceneCtl.updatePlayerInfo(player);
                this.updatePlayerSkillInfo(player);
                this.updatePlayerHpState(player, true);
                this.updatePlayerDamageState();
                this.updateAutoState();
                this.updateGameSpeedState(true);

                baseData.isPlayEnterAni = true;
                this.changeState(CombatDungeonState.Ready);
                break;
            case CombatDungeonState.Ready:
                pveData.wave++;

                if (pveData.wave > pveData.totalWave) {
                    this.changeState(CombatDungeonState.Success);
                } else {
                    this.updateWaveInfo();
                    this.initMonsterInfo();

                    if (pveData.wave === pveData.totalWave) {
                        baseData.isPlayEnterAni = true;
                    }

                    this.changeState(CombatDungeonState.Load);
                }
                break;
            case CombatDungeonState.Pause:
                UI.getInstance().open("FloatDungeonPause", baseData.type);
                break;
            case CombatDungeonState.Success:
                baseData.resetTime = 10;
                switch (baseData.type) {
                    case DungeonType.Boss:
                        DungeonBoss.getInstance().sendDungeonBossReward();
                        break;
                    case DungeonType.Cloud:
                        DungeonCloud.getInstance().sendDungeonCloudReward();
                        break;
                    case DungeonType.Thief:
                        DungeonThief.getInstance().sendDungeonThiefReward(
                            Math.min(
                                Math.floor(playerData.damage),
                                pveData.monsterHpData[pveData.monsterHpData.length - 1][1]
                            )
                        );
                        break;
                    case DungeonType.Tower:
                        DungeonTower.getInstance().sendDungeonTowerReward();
                        break;
                    case DungeonType.Union:
                        Union.getInstance().sendUnionBossAttack(
                            Math.min(
                                Math.floor(playerData.damage),
                                pveData.monsterHpData[pveData.monsterHpData.length - 1][1]
                            )
                        );
                        break;
                    case DungeonType.Trial:
                        ActivityNewbieTrial.getInstance().sendActivityNewTrialReward();
                        break;
                    default:
                        break;
                }
                break;
            case CombatDungeonState.Failure:
                pveData.refreshTime = 0;

                switch (baseData.type) {
                    case DungeonType.Boss:
                    case DungeonType.Cloud:
                    case DungeonType.Trial:
                        UI.getInstance().open("FloatDungeonFailure", baseData.type);
                        break;
                    case DungeonType.Thief:
                        baseData.resetTime = 10;
                        DungeonThief.getInstance().sendDungeonThiefReward(
                            Math.min(
                                Math.floor(playerData.damage),
                                pveData.monsterHpData[pveData.monsterHpData.length - 1][1]
                            )
                        );
                        break;
                    case DungeonType.Tower:
                        DungeonTower.getInstance().sendCombatFailure();
                        break;
                    case DungeonType.Union:
                        baseData.resetTime = 10;
                        Union.getInstance().sendUnionBossAttack(
                            Math.min(
                                Math.floor(playerData.damage),
                                pveData.monsterHpData[pveData.monsterHpData.length - 1][1]
                            )
                        );
                        break;
                    default:
                        break;
                }
                break;
            case CombatDungeonState.Reset:
                this.compSceneBgMove && this.compSceneBgMove.initBgPos();
                this.updateLevelInfo();

                this.dungeonPveCtl.resetCombat();
                this.sceneCtl.resetPlayerInfo(player);
                this.resetPlayerSkillInfo(player);
                this.updatePlayerHpState(player, true);
                this.updatePlayerDamageState();
                this.dialogCtl.clearAllDialog();

                this.changeState(CombatDungeonState.Ready);
                break;
            default:
                break;
        }

        if (state === CombatDungeonState.Success || state === CombatDungeonState.Failure) {
            if (
                CombatLog.getInstance().getShowState(CombatLogId.CombatResultData) &&
                CombatLog.getInstance().getShowStateByDungeonType(baseData.type)
            ) {
                allPlayer.forEach((e) => {
                    const tempPlayerData = e.getData();

                    CombatLog.getInstance().logTitle(CombatLogId.CombatResultData, e);

                    const logData = {
                        combatTime: baseData.combatTime,
                        skillRecord: Utils.clone(tempPlayerData.skillRecord),
                    };
                    cc.log(logData);
                });
            }
        }
    }

    /**
     * 更新状态
     * @param dt
     */
    public updateState(dt: number): void {
        const baseData = this.dungeonPveCtl.getBaseData();
        const allPlayer = this.dungeonPveCtl.getAllPlayer();
        const allMonster = this.dungeonPveCtl.getAllMonster();

        switch (baseData.state) {
            case CombatDungeonState.Load:
                this.updataStateByLoad(dt);
                break;
            case CombatDungeonState.Enter:
                allPlayer.forEach((e) => {
                    const playerData = e.getData();

                    if (
                        playerData.tankData.state === CombatMemberState.WaitEnter ||
                        playerData.tankData.state === CombatMemberState.Enter
                    ) {
                        const compTankItem = this.sceneCtl.getTankItem(e.getBaseData().uuid);
                        compTankItem.updateState(dt, playerData.type);
                    }
                });

                if (
                    allPlayer.findIndex((e) => {
                        const playerData = e.getData();
                        return (
                            playerData.tankData.state === CombatMemberState.Load ||
                            playerData.tankData.state === CombatMemberState.WaitEnter ||
                            playerData.tankData.state === CombatMemberState.Enter
                        );
                    }) === -1 &&
                    allMonster.findIndex((e) => {
                        const monsterData = e.getData();
                        return (
                            monsterData.info.type === EnumMonsterBaseType.BossMonster &&
                            (monsterData.state === CombatMemberState.Load ||
                                monsterData.state === CombatMemberState.WaitEnter ||
                                monsterData.state === CombatMemberState.Enter)
                        );
                    }) === -1
                ) {
                    this.changeState(CombatDungeonState.Combat);
                }
                break;
            case CombatDungeonState.Combat:
                this.actionSkillCd(allPlayer, allMonster, dt);
                this.actionSkillRelease(allPlayer, allMonster, dt);

                this.actionSkillShow([...allPlayer, ...allMonster], dt);
                this.actionSkillEffect(allPlayer, allMonster, dt);
                this.actionSkill([...allPlayer, ...allMonster], dt);

                this.actionBuff([...allPlayer, ...allMonster], dt);

                this.actionPlayer(dt);

                this.actionMonster(dt);
                this.sceneCtl.actionMonsterByZIndex();

                this.judgeResult(dt);
                break;
            case CombatDungeonState.Success:
            case CombatDungeonState.Failure:
                this.actionSkillShow([...allPlayer, ...allMonster], dt);
                break;
            default:
                break;
        }

        this.dialogCtl.actionDialog(allPlayer, allMonster);

        this.sceneCtl.actionDamage();

        if (baseData.resetTime > 0) {
            baseData.resetTime -= dt;
            if (baseData.resetTime <= 0) {
                UI.getInstance().closeToWindow(this.node.name);
                UI.getInstance().close();
            }
        }

        if (this.skipCombatTime > 0) {
            const tempSkipCombatTime = Math.floor(this.skipCombatTime);
            this.skipCombatTime -= dt;
            if (Math.floor(this.skipCombatTime) !== tempSkipCombatTime || this.skipCombatTime === 0) {
                if (this.skipCombatTime <= 0) {
                    baseData.isSkipCombat = true;

                    this.nodeSkipCombat.child("lbtText").label(i18n.common0079);
                } else {
                    this.nodeSkipCombat.child("lbtText").label(`${MathUtils.ceil(this.skipCombatTime, 1)}s`);
                }
            }
        }
    }

    /**
     * 更新关卡信息
     * @param isInit 是否为初始化调用
     */
    public updateLevelInfo(isInit: boolean = false): void {
        const baseData = this.dungeonPveCtl.getBaseData();
        const pveData = this.dungeonPveCtl.getData();

        switch (baseData.type) {
            case DungeonType.Boss:
                {
                    const levelInfo = TBDungeonBoss.getInstance().getDataById(pveData.levelId);

                    pveData.totalWave = 1;
                    pveData.wave = 0;
                    pveData.totalLimitTime = levelInfo.time;
                    pveData.monsterShowData = levelInfo.monster;
                    pveData.monsterFormationId = levelInfo.formation;

                    this.lbtName.string = TextUtils.format(i18n.dungeon0042, levelInfo.grade);
                }
                break;
            case DungeonType.Cloud:
                {
                    const levelInfo = TBDungeonCloud.getInstance().getDataById(pveData.levelId);

                    pveData.totalWave = 5;
                    pveData.wave = 0;
                    pveData.totalLimitTime = levelInfo.time;
                    pveData.monsterShowData = levelInfo.monster;
                    pveData.monsterFormationId = levelInfo.formation;

                    this.lbtName.string = TextUtils.format(i18n.dungeon0042, levelInfo.grade);
                }
                break;
            case DungeonType.Thief:
                {
                    const dungeonInfo = TBDungeon.getInstance().getDataByType(EnumDungeonType.DungeonThief);
                    const levelInfo = TBDungeonThief.getInstance().getDataById(pveData.levelId);
                    const monsterHpInfo = TBThiefBloodReward.getInstance().getList();

                    pveData.totalWave = 1;
                    pveData.wave = 0;
                    pveData.totalLimitTime = levelInfo.time;
                    pveData.monsterShowData = levelInfo.monster;
                    pveData.monsterFormationId = levelInfo.formation;
                    pveData.monsterHpData = [];
                    monsterHpInfo.forEach((e) => pveData.monsterHpData.push([e.needHp, e.addHp]));
                    pveData.monsterHpIndex = 0;

                    this.lbtName.string = dungeonInfo.name;
                }
                break;
            case DungeonType.Tower:
                {
                    const levelInfo = TBDungeonTower.getInstance().getDataById(pveData.levelId);

                    pveData.totalWave = 1;
                    pveData.wave = 0;
                    pveData.totalLimitTime = levelInfo.time;
                    pveData.monsterShowData = levelInfo.monster;
                    pveData.monsterFormationId = levelInfo.formation;

                    this.lbtName.string = TextUtils.format(i18n.dungeon0043, levelInfo.layer, levelInfo.grade);
                }
                break;
            case DungeonType.Union:
                {
                    const monsterShowData = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionBossId);
                    const monsterHpInfo = TBUnionBoss.getInstance().getList();
                    const monsterInfo = TBMonsterBase.getInstance().getDataById(monsterShowData[0][0][0]);

                    pveData.totalWave = 1;
                    pveData.wave = 0;
                    pveData.totalLimitTime = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionBossTime);
                    pveData.monsterShowData = monsterShowData;
                    pveData.monsterFormationId = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionFormation);
                    pveData.monsterHpData = [];
                    monsterHpInfo.forEach((e) => pveData.monsterHpData.push([e.needHp, e.addHp]));
                    pveData.monsterHpIndex = 0;

                    this.lbtName.string = monsterInfo.name;
                }
                break;
            case DungeonType.Trial:
                {
                    const levelInfo = TBTrialBoss.getInstance().getDataById(pveData.levelId);

                    pveData.totalWave = 1;
                    pveData.wave = 0;
                    pveData.totalLimitTime = levelInfo.time;
                    pveData.monsterShowData = levelInfo.monster;
                    pveData.monsterFormationId = levelInfo.formation;

                    this.lbtName.string = TextUtils.format(i18n.dungeon0042, pveData.levelId);
                }
                break;
            default:
                break;
        }

        const combatInfo = TBDungeonCombat.getInstance().getDataByType(baseData.type);
        const isShow = !!combatInfo.skipSwitch;
        isInit && (this.nodeSkipCombat.active = isShow);
        if (isShow) {
            switch (baseData.type) {
                case DungeonType.Tower:
                    if (pveData.levelId > combatInfo.skipUnlock[1]) {
                        const levelInfo = TBDungeonTower.getInstance().getDataById(pveData.levelId);
                        this.skipCombatTime =
                            levelInfo.grade === combatInfo.skipUnlock[2]
                                ? combatInfo.skipUnlock[3]
                                : combatInfo.skipUnlock[0];
                    }
                    break;
                default:
                    this.skipCombatTime = combatInfo.skipUnlock[0];
                    break;
            }
            baseData.isSkipCombat = this.skipCombatTime <= 0;
            baseData.isSkipingCombat = false;
            this.nodeSkipCombat
                .child("lbtText")
                .label(this.skipCombatTime > 0 ? `${MathUtils.ceil(this.skipCombatTime, 1)}s` : i18n.common0079);
        }
    }

    /**
     * 更新波数信息
     */
    public updateWaveInfo(): void {
        const pveData = this.dungeonPveCtl.getData();

        const isFinalWave = pveData.wave === pveData.totalWave;
        this.nodeWave.active = !isFinalWave;
        this.nodeWave2.active = !isFinalWave;
        if (!isFinalWave) {
            this.nodeWave.children.forEach((e, i) => {
                const nodePassedTag = e.child("spPassedTag");
                nodePassedTag && (nodePassedTag.active = i + 1 < pveData.wave);
            });
            this.nodeWave2.children.forEach((e, i) => {
                const nodePassedTag = e.child("spPassedTag");
                nodePassedTag && (nodePassedTag.active = i + 1 < pveData.wave);
            });
        }
        this.prgMonsterHp.node.active = isFinalWave;
        this.prgTime.node.active = isFinalWave;
        isFinalWave && this.updateLimitTimeState(true);
    }

    /**
     * 更新限制时间状态
     * @param isInit 是否为初始化调用
     */
    public updateLimitTimeState(isInit: boolean = false): void {
        const baseData = this.dungeonPveCtl.getBaseData();
        const pveData = this.dungeonPveCtl.getData();

        if (baseData.isSkipingCombat) {
            if (isInit) {
                baseData.combatTime = 0;
                pveData.limitTime = pveData.totalLimitTime;
            }

            pveData.refreshTime = pveData.refreshDuration;
            return;
        }

        if (isInit) {
            baseData.combatTime = 0;
            pveData.limitTime = pveData.totalLimitTime;

            const progressTime = MathUtils.floor(pveData.limitTime / pveData.totalLimitTime, 3);
            this.prgTime.progress = progressTime;
            this.scheduleOnce(() => {
                this.prgTime.barSprite.node.opacity = 255;
            });
        } else {
            const progressTime = MathUtils.floor(pveData.limitTime / pveData.totalLimitTime, 3);
            cc.Tween.stopAllByTarget(this.prgTime);
            cc.tween(this.prgTime)
                .to(0.8, { progress: progressTime })
                .call(() => {
                    this.prgTime.barSprite.node.opacity = progressTime !== 0 ? 255 : 0;
                })
                .start();
        }
        this.lbtTime.string = `${Math.max(MathUtils.ceil(pveData.limitTime, 1), 0)}s`;

        pveData.refreshTime = pveData.refreshDuration;
    }

    /**
     * 更新玩家血量状态
     * @param player 玩家
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public updatePlayerHpState(player: CombatMemberPlayer, isInit: boolean = false, isPlayAni: boolean = false): void {
        const playerBaseData = player.getBaseData();

        if (isInit) {
            const compLeadItem = this.sceneCtl.getLeadItem(playerBaseData.uuid);
            compLeadItem.updateHpState(player, isInit);
        }
    }

    /**
     * 更新玩家伤害状态
     */
    public updatePlayerDamageState(): void {}

    /**
     * 初始化怪兽信息
     */
    public initMonsterInfo(): void {
        const pveBaseData = this.dungeonPveCtl.getBaseData();
        const pveData = this.dungeonPveCtl.getData();

        const allMonster = this.dungeonPveCtl.initAllMonster(pveBaseData.type, pveData.levelId);
        const formationInfo = TBMonsterFormation.getInstance().getDataById(
            pveData.monsterFormationId[pveData.wave - 1]
        );
        const formationData: number[][] = [];
        formationInfo.formationPara.forEach(([enterDelay, ...formation]) => {
            formation.forEach((e) => formationData.push([e, enterDelay]));
        });
        ArrayUtils.shuffle(formationData, true);
        const { width, height } = this.sceneCtl.getMonsterSize(CombatPlayerType.Self);
        const scaleX = this.sceneCtl.getSkillEffectScaleX(CombatPlayerType.Self);
        allMonster.forEach((e) => {
            const monsterData = e.getData();

            const index = formationData.findIndex(([formation]) => {
                switch (monsterData.info.moveType) {
                    case EnumMonsterBaseMoveType.Walk:
                        return WALK_MONSTER_FORMATION.includes(formation);
                    case EnumMonsterBaseMoveType.Flight:
                        return FLY_MONSTER_FORMATION.includes(formation);
                    default:
                        return false;
                }
            });
            const [formation, enterDelay] = formationData[index];
            formationData.splice(index, 1);

            monsterData.initPos = cc.v2(width, (height / MONSTER_FORMATION_NUM) * (formation - 1 + 0.5));
            monsterData.targetPos = cc.v2();
            switch (monsterData.info.moveType) {
                case EnumMonsterBaseMoveType.Walk:
                    monsterData.targetPos.y = (height / MONSTER_FORMATION_NUM) * (WALK_MONSTER_FORMATION.length / 4);
                    monsterData.targetPos.y += (height / 2 / MONSTER_FORMATION_NUM) * (formation - 1 + 0.5);
                    break;
                case EnumMonsterBaseMoveType.Flight:
                    monsterData.targetPos.x = width / 2;
                    monsterData.targetPos.y = monsterData.initPos.y;
                    break;
                default:
                    break;
            }
            monsterData.scaleX = scaleX;
            const radian = Math.atan2(
                monsterData.targetPos.y - monsterData.initPos.y,
                monsterData.targetPos.x - monsterData.initPos.x
            );
            monsterData.angle = MathUtils.a2d(radian);
            monsterData.enterDelay = enterDelay;

            this.sceneCtl.initMonsterItem(CombatPlayerType.Self, e);

            this.updateMonsterHpState(e, true);
        });
    }

    /**
     * 更新怪兽血量状态
     * @param monster 怪兽
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    public updateMonsterHpState(
        monster: CombatMemberMonster,
        isInit: boolean = false,
        isPlayAni: boolean = false
    ): void {
        const pveData = this.dungeonPveCtl.getData();
        if (pveData.wave !== pveData.totalWave) {
            return;
        }
        if (monster.getData().info.type !== EnumMonsterBaseType.BossMonster) {
            return;
        }

        if (pveData.monsterHpData.length === 0) {
            this.updateMonsterHpStateBySingleHp(monster, isInit, isPlayAni);
        } else {
            this.isUpdateMultipleHpState && this.updateMonsterHpStateByMultipleHp(monster, isInit);
        }
    }

    /**
     * 更新怪兽血量状态-单血条
     * @param monster 怪兽
     * @param isInit 是否为初始化调用
     * @param isPlayAni 是否播放动画
     */
    private updateMonsterHpStateBySingleHp(monster: CombatMemberMonster, isInit: boolean, isPlayAni: boolean): void {
        const pveData = this.dungeonPveCtl.getData();
        const monsterBaseData = monster.getBaseData();
        const monsterData = monster.getData();

        let shield = 0;
        monsterBaseData.buffData.forEach((e) => {
            if (e.info.effectType === EnumSkillEffectEffectType.Buff703) {
                e.layerData.forEach(([, , tempShield]) => {
                    shield += tempShield;
                });
            }
        });
        const progressHp = MathUtils.floor(monsterBaseData.hp / (monsterBaseData.totalHp + shield), 3);
        const progressShield = MathUtils.floor((monsterBaseData.hp + shield) / (monsterBaseData.totalHp + shield), 3);
        if (isInit) {
            this.prgMonsterHp.progress = progressHp;
            this.prgMonsterHp2.progress = progressHp;
            this.prgMonsterHp3.progress = progressShield;
            this.scheduleOnce(() => {
                this.prgMonsterHp.barSprite.node.opacity = 255;
                this.prgMonsterHp2.barSprite.node.opacity = 255;
                this.prgMonsterHp3.barSprite.node.opacity = 255;
            });
        } else {
            this.prgMonsterHp.progress = progressHp;
            this.prgMonsterHp.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
            this.prgMonsterHp3.progress = progressShield;
            this.prgMonsterHp3.barSprite.node.opacity = progressShield !== 0 ? 255 : 0;
            if (!isPlayAni) {
                this.prgMonsterHp2.progress = progressHp;
                this.prgMonsterHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
            } else {
                cc.tween(this.prgMonsterHp2)
                    .to(0.8, { progress: progressHp })
                    .call(() => {
                        this.prgMonsterHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
                    })
                    .start();
            }

            if (monsterData.state === CombatMemberState.Die) {
                pveData.refreshTime = 0;
            }
        }
        this.lbtMonsterHp.string = `${MathUtils.ceil((monsterBaseData.hp / monsterBaseData.totalHp) * 100, 2)}%`;
    }

    /**
     * 更新怪兽血量状态-多血条
     * @param monster 怪兽
     * @param isInit 是否为初始化调用
     * @param isQuickLoseHp 是否快速掉血
     */
    protected updateMonsterHpStateByMultipleHp(
        monster: CombatMemberMonster,
        isInit: boolean,
        isQuickLoseHp: boolean = false
    ): void {
        const pveData = this.dungeonPveCtl.getData();
        const monsterBaseData = monster.getBaseData();
        const monsterData = monster.getData();

        const totalHp = pveData.monsterHpData[pveData.monsterHpIndex][0];
        const startHp = pveData.monsterHpIndex !== 0 ? pveData.monsterHpData[pveData.monsterHpIndex - 1][1] : 0;
        const damage = monsterBaseData.totalHp - monsterBaseData.hp;
        const curHp = totalHp - Math.min(damage - startHp, totalHp);
        const progressHp = MathUtils.floor(curHp / totalHp, 3);
        let tempMonsterHpIndex = pveData.monsterHpData.findIndex(([, tempEndHp]) => damage < tempEndHp);
        tempMonsterHpIndex === -1 && (tempMonsterHpIndex = pveData.monsterHpData.length - 1);
        if (isInit) {
            this.prgMonsterHp.barSprite.node.sprite(this.sfMonsterHp[pveData.monsterHpIndex % 5]);
            this.prgMonsterHp2.barSprite.node.sprite(this.sfMonsterHp2[pveData.monsterHpIndex % 5]);
            if (pveData.monsterHpIndex + 1 <= pveData.monsterHpData.length - 1) {
                this.spMonsterBaseHp.node.sprite(this.sfMonsterHp[(pveData.monsterHpIndex + 1) % 5]);
            } else {
                this.spMonsterBaseHp.node.sprite(null);
            }
            this.prgMonsterHp.progress = progressHp;
            this.prgMonsterHp2.progress = progressHp;
            this.scheduleOnce(() => {
                this.prgMonsterHp.barSprite.node.opacity = 255;
                this.prgMonsterHp2.barSprite.node.opacity = 255;
            });
        } else {
            !isQuickLoseHp && tempMonsterHpIndex - pveData.monsterHpIndex > 1 && (isQuickLoseHp = true);
            this.prgMonsterHp.progress = progressHp;
            this.prgMonsterHp.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;
            this.isUpdateMultipleHpState = !(
                progressHp === 0 && pveData.monsterHpIndex !== pveData.monsterHpData.length - 1
            );
            cc.Tween.stopAllByTarget(this.prgMonsterHp2);
            cc.tween(this.prgMonsterHp2)
                .to(isQuickLoseHp ? 0.1 : 0.8, { progress: progressHp })
                .call(() => {
                    this.prgMonsterHp2.barSprite.node.opacity = progressHp !== 0 ? 255 : 0;

                    if (!this.isUpdateMultipleHpState) {
                        this.isUpdateMultipleHpState = true;
                        pveData.monsterHpIndex++;
                        pveData.monsterHpIndex = Math.min(pveData.monsterHpIndex, pveData.monsterHpData.length - 1);
                        isQuickLoseHp && tempMonsterHpIndex === pveData.monsterHpIndex && (isQuickLoseHp = false);
                        this.prgMonsterHp.barSprite.node.sprite(this.sfMonsterHp[pveData.monsterHpIndex % 5]);
                        this.prgMonsterHp2.barSprite.node.sprite(this.sfMonsterHp2[pveData.monsterHpIndex % 5]);
                        if (pveData.monsterHpIndex + 1 <= pveData.monsterHpData.length - 1) {
                            this.spMonsterBaseHp.node.sprite(this.sfMonsterHp[(pveData.monsterHpIndex + 1) % 5]);
                        } else {
                            this.spMonsterBaseHp.node.sprite(null);
                        }
                        this.prgMonsterHp.progress = 1;
                        this.prgMonsterHp.barSprite.node.opacity = 255;
                        this.prgMonsterHp2.progress = 1;
                        this.prgMonsterHp2.barSprite.node.opacity = 255;
                        this.updateMonsterHpStateByMultipleHp(monster, isInit, isQuickLoseHp);
                    }
                })
                .start();

            if (monsterData.state === CombatMemberState.Die) {
                pveData.refreshTime = 0;
            }
        }
        this.lbtMonsterHp.string = `x${pveData.monsterHpData.length - pveData.monsterHpIndex}`;
    }

    /**
     * 更新开关状态-自动释放技能
     */
    public updateAutoState(): void {
        const allPlayer = this.dungeonPveCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerBaseData = player.getBaseData();

        const nodeIcon = this.nodeAuto.child("spIcon2");
        const nodeIcon2 = this.nodeAuto.child("spIcon3");
        cc.Tween.stopAllByTarget(nodeIcon);
        cc.Tween.stopAllByTarget(nodeIcon2);
        nodeIcon.angle = 30;
        nodeIcon2.angle = 0;
        if (playerBaseData.isAuto) {
            cc.tween(nodeIcon)
                .repeatForever(
                    cc
                        .tween()
                        .to(2, { angle: 390 })
                        .call(() => {
                            nodeIcon.angle = 30;
                        })
                )
                .start();
            cc.tween(nodeIcon2)
                .repeatForever(
                    cc
                        .tween()
                        .to(4, { angle: 360 })
                        .call(() => {
                            nodeIcon2.angle = 0;
                        })
                )
                .start();
        }
        this.nodeAutoTag.active = playerBaseData.isAuto;
    }

    /**
     * 更新游戏速度状态
     * @param isInit 是否为初始化调用
     */
    public updateGameSpeedState(isInit: boolean = false): void {
        const baseData = this.dungeonPveCtl.getBaseData();
        const combatInfo = TBDungeonCombat.getInstance().getDataByType(baseData.type);
        const isShow = !!combatInfo.accelerationSwitch;
        isInit && (this.nodeGameSpeed.active = isShow);
        if (isShow) {
            let gameSpeed = 1;
            const settingId = Combat.getInstance().getGameSpeedSettingId(baseData.type);
            if (Setting.getInstance().getSwitchState(settingId)) {
                gameSpeed = combatInfo.battleAcceleration[1];
            }
            this.nodeGameSpeed.child("lbtSpeed").label(`x${gameSpeed}`);
        }
    }

    /**
     * 判断结果
     * @param dt
     */
    public judgeResult(dt: number): void {
        if (this.isPlayingPassWaveAni) {
            return;
        }

        const baseData = this.dungeonPveCtl.getBaseData();
        const pveData = this.dungeonPveCtl.getData();
        const allPlayer = this.dungeonPveCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const allMonster = this.dungeonPveCtl.getAllMonster();

        if (player.getBaseData().hp <= 0) {
            this.changeState(CombatDungeonState.Failure);
            return;
        }

        if (allMonster.length === 0) {
            if (pveData.wave === pveData.totalWave) {
                this.changeState(CombatDungeonState.Ready);
            } else {
                this.playPassWaveAni();
            }
            return;
        }

        if (pveData.refreshTime > 0) {
            baseData.combatTime += dt;
            pveData.limitTime -= dt;
            pveData.refreshTime -= dt;
            if (pveData.refreshTime <= 0 || pveData.limitTime <= 0) {
                this.updateLimitTimeState();

                if (pveData.limitTime <= 0) {
                    this.changeState(CombatDungeonState.Failure);
                    return;
                }
            }
        }
    }

    /**
     * 播放过波动画
     */
    private playPassWaveAni(): void {
        const baseData = this.dungeonPveCtl.getBaseData();
        const allPlayer = this.dungeonPveCtl.getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const playerBaseData = player.getBaseData();

        this.isPlayingPassWaveAni = true;
        this.compSceneBgMove.setGameSpeed(baseData.gameSpeed);
        this.compSceneBgMove.setMoveState(true);
        const compTankItem = this.sceneCtl.getTankItem(playerBaseData.uuid);
        compTankItem.changeState(CombatMemberState.Enter);
        cc.tween(compTankItem.node)
            .by(2 / baseData.gameSpeed, { x: 150 })
            .call(() => {
                compTankItem.changeState(CombatMemberState.Wait);
            })
            .by(0.5 / baseData.gameSpeed, { x: -150 })
            .call(() => {
                this.compSceneBgMove.setMoveState(false);
                this.isPlayingPassWaveAni = false;

                this.changeState(CombatDungeonState.Ready);
            })
            .start();
    }

    /**
     * 暂停
     */
    protected onClickPause(): void {
        const pveBaseData = this.dungeonPveCtl.getBaseData();
        if (!pveBaseData || pveBaseData.state !== CombatDungeonState.Combat) {
            return;
        }
        if (this.isPlayingPassWaveAni) {
            return;
        }

        this.changeState(CombatDungeonState.Pause);
    }

    /**
     * 自动-释放技能
     */
    protected onClickAuto(): void {
        const pveBaseData = this.dungeonPveCtl.getBaseData();
        if (!pveBaseData || pveBaseData.state !== CombatDungeonState.Combat) {
            return;
        }

        const playerBaseData = this.dungeonPveCtl
            .getAllPlayer()
            .find((e) => e.getData().type === CombatPlayerType.Self)
            .getBaseData();
        playerBaseData.isAuto = !playerBaseData.isAuto;
        switch (playerBaseData.dungeonType) {
            case DungeonType.Boss:
                CombatSetting.getInstance().setSettingState(CombatSettingId.DungeonBossAutoReleaseSkill);
                break;
            case DungeonType.Cloud:
                CombatSetting.getInstance().setSettingState(CombatSettingId.DungeonCloudAutoReleaseSkill);
                break;
            case DungeonType.Thief:
                CombatSetting.getInstance().setSettingState(CombatSettingId.DungeonThiefAutoReleaseSkill);
                break;
            case DungeonType.Tower:
                CombatSetting.getInstance().setSettingState(CombatSettingId.DungeonTowerAutoReleaseSkill);
                break;
            case DungeonType.Union:
                CombatSetting.getInstance().setSettingState(CombatSettingId.DungeonUnionAutoReleaseSkill);
                break;
            case DungeonType.Trial:
                CombatSetting.getInstance().setSettingState(CombatSettingId.DungeonTrialAutoReleaseSkill);
                break;
            default:
                break;
        }
        this.updateAutoState();
    }

    /**
     * 切换游戏速度
     */
    protected onClickSwitchGameSpeed(): void {
        const baseData = this.dungeonPveCtl.getBaseData();
        if (!baseData || baseData.state === CombatDungeonState.Init) {
            return;
        }
        const combatInfo = TBDungeonCombat.getInstance().getDataByType(baseData.type);
        if (!combatInfo.accelerationSwitch) {
            return;
        }
        switch (baseData.type) {
            case DungeonType.Boss:
            case DungeonType.Cloud:
            case DungeonType.Thief:
            case DungeonType.Tower:
            case DungeonType.Trial:
                const pveData = this.dungeonPveCtl.getData();
                if (pveData.levelId <= combatInfo.battleAcceleration[0]) {
                    Tips.getInstance().info(
                        TextUtils.format(
                            i18n.dungeon0051,
                            combatInfo.battleAcceleration[0] - pveData.levelId + 1,
                            combatInfo.battleAcceleration[1]
                        )
                    );
                    return;
                }
                break;
            default:
                break;
        }

        this.switchGameSpeed();
    }

    /**
     * 跳过战斗
     */
    protected onClickSkipCombat(): void {
        const baseData = this.dungeonPveCtl.getBaseData();
        if (!baseData || baseData.state === CombatDungeonState.Init) {
            return;
        }
        switch (baseData.type) {
            case DungeonType.Tower:
                const pveData = this.dungeonPveCtl.getData();
                const combatInfo = TBDungeonCombat.getInstance().getDataByType(baseData.type);
                if (pveData.levelId <= combatInfo.skipUnlock[1]) {
                    Tips.getInstance().info(
                        TextUtils.format(i18n.dungeon0048, combatInfo.skipUnlock[1] - pveData.levelId + 1)
                    );
                    return;
                }
                break;
            default:
                break;
        }

        this.skipCombat();
    }
}
