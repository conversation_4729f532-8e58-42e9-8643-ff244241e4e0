/*
 * @Author: chenx
 * @Date: 2024-12-14 15:45:50
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:41:27
 */
import Loader from "../../../nsn/core/Loader";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI, { UIEvent } from "../../../nsn/ui/UI";
import CocosExt from "../../../nsn/util/CocosExt";
import MathUtils from "../../../nsn/util/MathUtils";
import { EquipDecomposeRet, EquipReplaceRet, IEquipInfo, IItemInfo } from "../../../protobuf/proto";
import Guide, { GuideEvent } from "../../core/Guide";
import { EnumItemType } from "../../data/base/BaseItem";
import { EnumUniversalPara } from "../../data/base/BaseUniversal";
import { GAME_SWITCH_ID } from "../../data/parser/TBGameSwitch";
import TBItem, { ITEM_ID } from "../../data/parser/TBItem";
import TBLeadEquip from "../../data/parser/TBLeadEquip";
import TBUniversal from "../../data/parser/TBUniversal";
import { DungeonType } from "../../game/Combat";
import { CombatDungeon } from "../../game/combat/CombatDungeon";
import CombatDungeonMain from "../../game/combat/CombatDungeonMain";
import { CombatPlayerType } from "../../game/combat/CombatMemberPlayer";
import CombatScore from "../../game/CombatScore";
import CombatSetting, { CombatSettingId } from "../../game/CombatSetting";
import DungeonMain, { DungeonMainEvent } from "../../game/DungeonMain";
import Equip, { EquipEvent } from "../../game/Equip";
import GameSwitch from "../../game/GameSwitch";
import ImageUtils from "../../utils/ImageUtils";

/**
 * 道具掉落数据
 */
export interface ICombatItemDropData {
    uuid: number; // 道具掉落uuid
    itemData?: IItemInfo; // 道具数据
    equipData?: IEquipInfo; // 装备数据

    worldPos: cc.Vec2; // 世界位置
    height: number; // 高度

    nodeItem: cc.Node;
}

/**
 * 刷新时间段-检测待分解装备
 */
const REFRESH_DURATION = 5;

/**
 * 道具width
 */
const ITEM_WIDTH = 105;

/**
 * 跳跃距离
 */
const JUMP_DIS = 100;

/**
 * 跳跃高度
 */
const JUMP_HEIGHT = 200;

/**
 * 跳跃距离
 */
const JUMP_DIS_2 = 40;

/**
 * 跳跃高度
 */
const JUMP_HEIGHT_2 = 200;

/**
 * 二次跳跃距离
 */
const JUMP_SECOND_DIS = 60;

/**
 * 二次跳跃高度
 */
const JUMP_SECOND_HEIGHT = 20;

/**
 * 道具掉落布局
 */
const ITEM_DROP_LAYOUT = [
    [0.5, 1],
    [-0.5, 1],
    [1.5, -1],
    [-1.5, -1],
    [2.5, 1],
    [-2.5, 1],
    [3.5, -1],
    [-3.5, -1],
    [4.5, 1],
    [-4.5, 1],

    [0.5, -1],
    [-0.5, -1],
    [1.5, 1],
    [-1.5, 1],
    [2.5, -1],
    [-2.5, -1],
    [3.5, 1],
    [-3.5, 1],
    [4.5, -1],
    [-4.5, -1],
];

/**
 * 装备掉落布局
 */
const EQUIP_DROP_LAYOUT = [
    [0.25, -1],
    [-0.25, -1],
    [1, 1],
    [-1, 1],
    [2, -1],
    [-2, -1],
    [3, 1],
    [-3, 1],
    [4, -1],
    [-4, -1],

    [0.25, 1],
    [-0.25, 1],
    [1, -1],
    [-1, -1],
    [2, 1],
    [-2, 1],
    [3, -1],
    [-3, -1],
    [4, 1],
    [-4, 1],
];

const { ccclass, property } = cc._decorator;

/**
 * 道具掉落
 */
@ccclass
export default class PrefabCombatItemDrop extends I18nComponent {
    @property(cc.Node)
    nodeDrop: cc.Node = null; // 掉落
    @property(cc.Node)
    nodeDropItem: cc.Node = null; // 掉落item
    @property(cc.Node)
    nodeDecompose: cc.Node = null; // 分解
    @property(cc.Node)
    nodeDecomposeItem: cc.Node = null; // 分解item
    @property(cc.Node)
    nodeMonster: cc.Node = null; // 怪兽

    private dungeonCtl: CombatDungeon = null; // 副本ctl

    private itemDropData: ICombatItemDropData[] = []; // 道具掉落数据
    private waitShowEquipUuid: number[] = []; // 待展示装备uuid
    private waitDecomposeEquipUuid: number[] = []; // 待分解装备uuid
    private refreshTime: number = 0; // 刷新时间-检测待分解装备
    private isShowingEquipInfo: boolean = false; // 是否正在展示装备信息

    private nodeTarget: cc.Node = null; // 目标-钻石
    private nodeTarget2: cc.Node = null; // 目标-万宝箱
    private posTarget: cc.Vec2 = null; // 目标位置-钻石
    private posTarget2: cc.Vec2 = null; // 目标位置-万宝箱
    private scaleTarget: number = 0; // 目标缩放-钻石
    private scaleTarget2: number = 0; // 目标缩放-万宝箱

    private dropItemPool: cc.Node[] = []; // 掉落item对象池
    private decomposeItemPool: cc.Node[] = []; // 分解item对象池

    protected onLoad(): void {
        this.nodeDropItem.parent = null;
        this.nodeDecomposeItem.parent = null;

        this.registerHandler();
    }

    protected onDestroy(): void {
        this.nodeDropItem.destroy();
        this.nodeDecomposeItem.destroy();
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.waitDecomposeEquipUuid.forEach((e) => Equip.getInstance().sendEquipDecompose(e));

                this.refreshTime = REFRESH_DURATION;
            }
        }

        const nodeDropItem = this.nodeDrop.children.concat();
        nodeDropItem.sort((a, b) => b.y - a.y);
        nodeDropItem.forEach((e, i) => {
            e.zIndex = i + 1;
        });
        const nodeDecomposeItem = this.nodeDecompose.children.concat();
        nodeDecomposeItem.sort((a, b) => b.y - a.y);
        nodeDecomposeItem.forEach((e, i) => {
            e.zIndex = i + 1;
        });
    }

    protected registerHandler(): void {
        // 主线副本-道具掉落
        DungeonMain.getInstance().on(
            DungeonMainEvent.ItemDrop,
            (itemDropData: ICombatItemDropData[]) => {
                if (this.dungeonCtl.getBaseData().type !== DungeonType.Main) {
                    return;
                }

                this.itemDropData.push(...itemDropData);
                let layout = itemDropData[0].itemData ? ITEM_DROP_LAYOUT.concat() : EQUIP_DROP_LAYOUT.concat();
                itemDropData.forEach((e) => {
                    const initPos = this.nodeDrop.convertToNodeSpaceAR(e.worldPos);
                    let layoutData: number[] = null;
                    while (!layoutData) {
                        const tempIndex = MathUtils.getRandomInt(0, layout.length);
                        let jumpDis = initPos.x + JUMP_DIS * layout[tempIndex][0];
                        jumpDis += layout[tempIndex][0] >= 0 ? JUMP_SECOND_DIS : -JUMP_SECOND_DIS;
                        if (jumpDis >= this.nodeMonster.x && jumpDis <= this.nodeMonster.x + this.nodeMonster.width) {
                            layoutData = layout[tempIndex];
                        }
                        layout.splice(tempIndex, 1);
                        if (layout.length === 0) {
                            layout = itemDropData[0].itemData ? ITEM_DROP_LAYOUT.concat() : EQUIP_DROP_LAYOUT.concat();
                        }
                    }
                    e.itemData && this.playItemDropAni(e, initPos, JUMP_DIS, layoutData);
                    e.equipData && this.playEquipDropAni(e, initPos, JUMP_DIS, layoutData);
                });
            },
            this
        );
        // 装备-替换
        Equip.getInstance().on(
            EquipReplaceRet.prototype.clazzName,
            (data: EquipReplaceRet) => {
                if (this.dungeonCtl.getBaseData().type !== DungeonType.Main) {
                    return;
                }

                const index = this.itemDropData.findIndex((e) => e.equipData && e.equipData.uuid === data.newUuid);
                data.oldUuid !== 0 && this.playEquipDecomposeAni(this.itemDropData[index]);
                this.putDropItem(this.itemDropData[index].nodeItem);
                DungeonMain.getInstance().emit(DungeonMainEvent.ClearItemDrop, this.itemDropData[index].uuid);
                this.itemDropData.splice(index, 1);
            },
            this
        );
        // 装备-分解
        Equip.getInstance().on(
            EquipDecomposeRet.prototype.clazzName,
            (data: EquipDecomposeRet) => {
                if (this.dungeonCtl.getBaseData().type !== DungeonType.Main) {
                    return;
                }

                const index = this.waitDecomposeEquipUuid.findIndex((e) => e === data.uuid);
                if (index !== -1) {
                    this.waitDecomposeEquipUuid.splice(index, 1);
                    this.waitDecomposeEquipUuid.length === 0 && (this.refreshTime = 0);
                }

                const index2 = this.itemDropData.findIndex((e) => e.equipData && e.equipData.uuid === data.uuid);
                if (index2 !== -1) {
                    this.playEquipDecomposeAni(this.itemDropData[index2]);
                    this.putDropItem(this.itemDropData[index2].nodeItem);
                    DungeonMain.getInstance().emit(DungeonMainEvent.ClearItemDrop, this.itemDropData[index2].uuid);
                    this.itemDropData.splice(index2, 1);
                }
            },
            this
        );
        // 主线副本-检测装备掉落
        DungeonMain.getInstance().on(
            DungeonMainEvent.CheckEquipDrop,
            () => {
                if (this.dungeonCtl.getBaseData().type !== DungeonType.Main) {
                    return;
                }

                this.isShowingEquipInfo = false;
                this.checkEquipDrop();
            },
            this
        );
        // 引导-关闭
        Guide.getInstance().on(
            GuideEvent.Close,
            () => {
                if (this.dungeonCtl.getBaseData().type !== DungeonType.Main) {
                    return;
                }
                if (!UI.getInstance().isEmpty()) {
                    return;
                }

                if (this.waitShowEquipUuid.length > 0 && !this.isShowingEquipInfo) {
                    this.checkEquipDrop();
                }
            },
            this
        );
        // 窗口-关闭
        UI.getInstance().on(
            UIEvent.Close,
            () => {
                if (this.dungeonCtl.getBaseData().type !== DungeonType.Main) {
                    return;
                }

                if (this.waitShowEquipUuid.length > 0 && !this.isShowingEquipInfo) {
                    this.checkEquipDrop();
                }
            },
            this
        );
        // 主线副本-回收展示装备
        DungeonMain.getInstance().on(
            DungeonMainEvent.PutShowEquip,
            (equipUuid: number) => {
                if (this.dungeonCtl.getBaseData().type !== DungeonType.Main) {
                    return;
                }

                this.isShowingEquipInfo = false;
                this.waitShowEquipUuid.unshift(equipUuid);
                this.checkEquipDrop();
            },
            this
        );
        // 装备-清理装备
        Equip.getInstance().on(
            EquipEvent.ClearEquip,
            () => {
                if (this.dungeonCtl.getBaseData().type !== DungeonType.Main) {
                    return;
                }

                for (let i = this.itemDropData.length - 1; i >= 0; i--) {
                    if (!this.itemDropData[i].equipData) {
                        continue;
                    }

                    const dressedEquipUuid = Equip.getInstance().getEquippedList();
                    if (
                        dressedEquipUuid.includes(this.itemDropData[i].equipData.uuid) ||
                        !Equip.getInstance().getInfoByUuid(this.itemDropData[i].equipData.uuid)
                    ) {
                        this.putDropItem(this.itemDropData[i].nodeItem);
                        DungeonMain.getInstance().emit(DungeonMainEvent.ClearItemDrop, this.itemDropData[i].uuid);

                        const index = this.waitDecomposeEquipUuid.findIndex(
                            (e) => e === this.itemDropData[i].equipData.uuid
                        );
                        index !== -1 && this.waitDecomposeEquipUuid.splice(index, 1);

                        this.itemDropData.splice(i, 1);
                    }
                }

                this.waitDecomposeEquipUuid.length === 0 && (this.refreshTime = 0);
            },
            this
        );
    }

    /**
     * 设置副本ctl
     * @param dungeonCtl 副本ctl
     */
    public setDungeonCtl(dungeonCtl: CombatDungeon): void {
        this.dungeonCtl = dungeonCtl;
    }

    /**
     * 更新道具信息
     * @param nodeDropItem 掉落item
     * @param itemId 道具id
     */
    private updateItemInfo(nodeDropItem: cc.Node, itemId: number): void {
        const itemInfo = TBItem.getInstance().getDataById(itemId);
        const nodeSpLight = nodeDropItem.child("spLight");
        nodeSpLight.opacity = 255;
        nodeSpLight.spriteAsync(`texture/itemDrop/qualityLight${itemInfo.quality}`);
        ImageUtils.setItemIcon(nodeDropItem.child("spIcon"), itemId);
    }

    /**
     * 更新装备信息
     * @param nodeDropItem 掉落item
     * @param equipId 装备id
     */
    private updateEquipInfo(nodeDropItem: cc.Node, equipId: number): void {
        const equipInfo = TBLeadEquip.getInstance().getDataById(equipId);
        const nodeSpineLight = nodeDropItem.child("spineLight");
        nodeSpineLight.skeletonAsync(`spine/itemDrop/efEquip${equipInfo.qualityLight}`, () => {
            nodeSpineLight.opacity = 255;
            const spineLight = nodeSpineLight.getComponent(sp.Skeleton);
            spineLight.setAnimation(0, "wait", true);
        });
        nodeDropItem.child("spineLight2").opacity = 255;
        ImageUtils.setEquipIcon(nodeDropItem.child("spIcon"), equipInfo.res);
    }

    /**
     * 展示装备信息
     * @param itemDropData 道具掉落数据
     */
    private showEquipInfo(itemDropData: ICombatItemDropData): void {
        const isShow = this.isShowEquipInfo(itemDropData.equipData);
        if (!isShow) {
            return;
        }
        let isHide = Guide.getInstance().isGuideShowing();
        if (!isHide) {
            const lastWindowData = UI.getInstance().getLastWindowInfo();
            if (lastWindowData) {
                const windowName: string[] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.EquipmentPopup);
                isHide = !windowName.includes(lastWindowData.windowName);
            }
        }
        if (isHide) {
            this.waitShowEquipUuid.push(itemDropData.equipData.uuid);
            DungeonMain.getInstance().emit(DungeonMainEvent.Pause, true);
            return;
        }

        if (!this.isShowingEquipInfo) {
            this.isShowingEquipInfo = true;
            UI.getInstance().open("FloatEquipReplace", itemDropData.equipData.uuid);
            DungeonMain.getInstance().emit(DungeonMainEvent.Pause, true);
        } else {
            this.waitShowEquipUuid.push(itemDropData.equipData.uuid);
        }
    }

    /**
     * 检测装备掉落
     */
    private checkEquipDrop(): void {
        if (this.waitShowEquipUuid.length === 0) {
            this.isShowingEquipInfo = false;
            DungeonMain.getInstance().emit(DungeonMainEvent.Pause, false);
            return;
        }
        const equipUuid = this.waitShowEquipUuid.shift();
        const equipData = Equip.getInstance().getInfoByUuid(equipUuid);
        const isShow = this.isShowEquipInfo(equipData);
        if (!isShow) {
            this.checkEquipDrop();
            return;
        }
        let isHide = Guide.getInstance().isGuideShowing();
        if (!isHide) {
            const lastWindowData = UI.getInstance().getLastWindowInfo();
            if (lastWindowData) {
                const windowName: string[] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.EquipmentPopup);
                isHide = !windowName.includes(lastWindowData.windowName);
            }
        }
        if (isHide) {
            this.waitShowEquipUuid.unshift(equipUuid);
            DungeonMain.getInstance().emit(DungeonMainEvent.Pause, true);
            return;
        }

        if (!this.isShowingEquipInfo) {
            this.isShowingEquipInfo = true;
            UI.getInstance().open("FloatEquipReplace", equipUuid);
            DungeonMain.getInstance().emit(DungeonMainEvent.Pause, true);
        } else {
            this.waitShowEquipUuid.unshift(equipUuid);
        }
    }

    /**
     * 是否展示装备信息
     * @param equipData 装备数据
     */
    private isShowEquipInfo(equipData: IEquipInfo): boolean {
        let isShow = false;
        if (CombatSetting.getInstance().getSettingState(CombatSettingId.EquipHideInfo)) {
            Equip.getInstance().sendEquipDecompose(equipData.uuid);
            this.waitDecomposeEquipUuid.push(equipData.uuid);
            this.refreshTime = REFRESH_DURATION;
            return isShow;
        }

        if (!CombatSetting.getInstance().getSettingState(CombatSettingId.EquipScoreImproveShowInfo)) {
            const dressedEquipUuid = Equip.getInstance().getEquippedList();
            const tempEquipUuid = dressedEquipUuid.find((e) => {
                const tempEquipUuid = Equip.getInstance().getInfoByUuid(e);
                return tempEquipUuid.position === equipData.position;
            });
            if (!tempEquipUuid) {
                isShow = true;
            } else {
                const score = CombatScore.getInstance().getScore();
                Equip.getInstance().preReplace(equipData.uuid, tempEquipUuid);
                const allPlayer = CombatDungeonMain.getInstance().getAllPlayer();
                const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
                player.updateAttrByCul();
                player.sumAttrByScore();
                CombatScore.getInstance().updateScore(true);
                const newScore = CombatScore.getInstance().getScore();
                Equip.getInstance().clearPreReplaceData();
                player.updateAttrByCul();
                player.sumAttrByScore();
                CombatScore.getInstance().updateScore(true);
                if (newScore >= score + 1) {
                    isShow = true;
                } else {
                    Equip.getInstance().sendEquipDecompose(equipData.uuid);
                    this.waitDecomposeEquipUuid.push(equipData.uuid);
                    this.refreshTime = REFRESH_DURATION;
                }
            }
        } else {
            isShow = true;
        }

        return isShow;
    }

    /**
     * 播放掉落动画
     * @param nodeItem
     * @param initPos
     * @param height
     * @param jumpDis
     * @param isRight
     * @param index
     * @param finishCb
     */
    private playDropAni(
        nodeItem: cc.Node,
        initPos: cc.Vec2,
        height: number,
        jumpDis: number,
        isRight: boolean,
        index: number,
        finishCb: () => void
    ): void {
        const targetPos = cc.v2(initPos.x, initPos.y - height);
        if (isRight) {
            targetPos.x += index === 0 ? jumpDis / 4 : jumpDis * index;
        } else {
            targetPos.x -= index === 0 ? jumpDis / 4 : jumpDis * index;
        }
        const dy = MathUtils.getRandomInt(-ITEM_WIDTH, ITEM_WIDTH);
        targetPos.y += dy;
        const dx2 = MathUtils.getRandomInt(JUMP_SECOND_DIS / 3, JUMP_SECOND_DIS);
        const dy2 = dy >= 0 ? MathUtils.getRandomInt(0, ITEM_WIDTH / 8) : MathUtils.getRandomInt(-ITEM_WIDTH / 8, 0);
        const targetPos2 = cc.v2(targetPos.x + (isRight ? dx2 : -dx2), targetPos.y + dy2);
        const flyHeight = JUMP_HEIGHT + MathUtils.getRandomInt(-40, 40);
        const flyHeight2 = JUMP_SECOND_HEIGHT + MathUtils.getRandomInt(-5, 5);
        const flyDuration = MathUtils.floor(0.5 + MathUtils.getRandomValue(-0.1, 0.1), 2);
        const flyDuration2 = MathUtils.floor(0.3 + MathUtils.getRandomValue(-0.1, 0.1), 2);
        cc.tween(nodeItem)
            .bezierTo(
                flyDuration,
                cc.v2(initPos.x, initPos.y + flyHeight),
                cc.v2(targetPos.x, targetPos.y + flyHeight),
                targetPos
            )
            .bezierTo(
                flyDuration2,
                cc.v2(targetPos.x, targetPos.y + flyHeight2),
                cc.v2(targetPos2.x, targetPos2.y + flyHeight2),
                targetPos2
            )
            .delay(0.5)
            .call(() => {
                finishCb && finishCb();
            })
            .start();
    }

    /**
     * 播放掉落动画
     * @param nodeItem
     * @param initPos
     * @param height
     * @param jumpDis
     * @param layoutData
     * @param finishCb
     */
    private playDropAni2(
        nodeItem: cc.Node,
        initPos: cc.Vec2,
        height: number,
        jumpDis: number,
        layoutData: number[],
        finishCb: () => void,
        delayFinishCb: number
    ): void {
        const [layoutIndex, isTop] = layoutData;
        const targetPos = cc.v2(initPos.x + jumpDis * layoutIndex, initPos.y - height);
        const dy = isTop === 1 ? MathUtils.getRandomInt(0, ITEM_WIDTH) : MathUtils.getRandomInt(-ITEM_WIDTH, 0);
        targetPos.y += dy;
        const dx2 = MathUtils.getRandomInt(JUMP_SECOND_DIS / 3, JUMP_SECOND_DIS);
        const dy2 =
            isTop === 1 ? MathUtils.getRandomInt(0, ITEM_WIDTH / 8) : MathUtils.getRandomInt(-ITEM_WIDTH / 8, 0);
        const targetPos2 = cc.v2(targetPos.x + (layoutIndex >= 0 ? dx2 : -dx2), targetPos.y + dy2);
        const flyHeight = JUMP_HEIGHT + MathUtils.getRandomInt(-50, 50);
        const flyHeight2 = JUMP_SECOND_HEIGHT + MathUtils.getRandomInt(-5, 5);
        const flyDuration = MathUtils.floor(0.5 + MathUtils.getRandomValue(-0.1, 0.1), 2);
        const flyDuration2 = MathUtils.floor(0.3 + MathUtils.getRandomValue(-0.1, 0.1), 2);
        cc.tween(nodeItem)
            .bezierTo(
                flyDuration,
                cc.v2(initPos.x, initPos.y + flyHeight),
                cc.v2(targetPos.x, targetPos.y + flyHeight),
                targetPos
            )
            .bezierTo(
                flyDuration2,
                cc.v2(targetPos.x, targetPos.y + flyHeight2),
                cc.v2(targetPos2.x, targetPos2.y + flyHeight2),
                targetPos2
            )
            .delay(delayFinishCb)
            .call(() => {
                finishCb && finishCb();
            })
            .start();
    }

    /**
     * 播放收集动画
     * @param nodeItem
     * @param nodeTarget
     * @param posTarget
     * @param scaleTarget
     * @param isMotionStreak 是否拖尾
     */
    private playCollectAni(
        nodeItem: cc.Node,
        nodeTarget: cc.Node,
        posTarget: cc.Vec2,
        scaleTarget: number,
        isMotionStreak: boolean = false
    ): void {
        if (isMotionStreak) {
            nodeItem.getChildByName("motionStreakLight").getComponent(cc.MotionStreak).enabled = true;
            nodeItem.getChildByName("motionStreakLight2").getComponent(cc.MotionStreak).enabled = true;
            nodeItem.getChildByName("particleLight").getComponent(cc.ParticleSystem).resetSystem();
        }
        const isRight = Math.random() >= 0.5;
        const targetPos2 = isRight
            ? cc.v2(nodeItem.x, nodeItem.y - (nodeItem.y - posTarget.y) / 3)
            : cc.v2(nodeItem.x - (nodeItem.x - posTarget.x) / 3, nodeItem.y);
        targetPos2.x += MathUtils.getRandomInt(-ITEM_WIDTH / 2, ITEM_WIDTH / 2);
        targetPos2.y += MathUtils.getRandomInt(-ITEM_WIDTH / 2, ITEM_WIDTH / 2);
        const flyDuration = MathUtils.floor(0.7 + MathUtils.getRandomValue(-0.1, 0.1), 2);
        cc.tween(nodeItem)
            .bezierTo(flyDuration, targetPos2, targetPos2, posTarget)
            .call(() => {
                nodeItem.child("spLight").opacity = 0;
                nodeItem.child("spIcon").opacity = 0;
                if (isMotionStreak) {
                    nodeItem.getChildByName("particleLight").getComponent(cc.ParticleSystem).stopSystem();
                }

                cc.Tween.stopAllByTarget(nodeTarget);
                nodeTarget.scale = scaleTarget;
                cc.tween(nodeTarget)
                    .to(0.1, { scale: scaleTarget * 1.1 })
                    .to(0.1, { scale: scaleTarget })
                    .start();
            })
            .delay(0.5)
            .call(() => {
                if (isMotionStreak) {
                    nodeItem.getChildByName("motionStreakLight").getComponent(cc.MotionStreak).enabled = false;
                    nodeItem.getChildByName("motionStreakLight2").getComponent(cc.MotionStreak).enabled = false;
                }
                this.putDropItem(nodeItem);
            })
            .start();
    }

    /**
     * 播放道具掉落动画
     * @param itemDropData 道具掉落数据
     * @param initPos
     * @param jumpDis
     * @param layoutData
     */
    private playItemDropAni(
        itemDropData: ICombatItemDropData,
        initPos: cc.Vec2,
        jumpDis: number,
        layoutData: number[]
    ): void {
        itemDropData.nodeItem = this.getDropItem();
        itemDropData.nodeItem.setPosition(initPos);
        this.updateItemInfo(itemDropData.nodeItem, itemDropData.itemData.itemInfoId);
        this.playDropAni2(
            itemDropData.nodeItem,
            initPos,
            itemDropData.height,
            jumpDis,
            layoutData,
            () => {
                DungeonMain.getInstance().emit(DungeonMainEvent.ClearItemDrop, itemDropData.uuid);
                const tempIndex = this.itemDropData.findIndex((e) => e.uuid === itemDropData.uuid);
                this.itemDropData.splice(tempIndex, 1);

                if (itemDropData.itemData.itemInfoId === ITEM_ID.DIAMOND) {
                    if (!this.nodeTarget) {
                        const nodeRes = cc.find(
                            "Canvas/baseNode/BaseHome/contentModule/PrefabHomeFunctionUI/nodeRes/playerResNode"
                        );
                        if (nodeRes) {
                            const nodeResItem = nodeRes.children.find(
                                (e) => CocosExt.getButtonData(e.child("icon")) === ITEM_ID.DIAMOND
                            );
                            if (nodeResItem) {
                                this.nodeTarget = nodeResItem.child("icon");
                                this.posTarget = this.nodeTarget.convertToWorldSpaceAR(cc.v2());
                                this.posTarget = this.nodeDrop.convertToNodeSpaceAR(this.posTarget);
                                this.scaleTarget = 0.5;
                            }
                        }
                    }
                    if (this.nodeTarget) {
                        this.playCollectAni(
                            itemDropData.nodeItem,
                            this.nodeTarget,
                            this.posTarget,
                            this.scaleTarget,
                            true
                        );
                        return;
                    }
                } else {
                    const itemInfo = TBItem.getInstance().getDataById(itemDropData.itemData.itemInfoId);
                    if (
                        itemInfo.type === EnumItemType.BoxSystem &&
                        GameSwitch.getInstance().check(GAME_SWITCH_ID.BOX).result
                    ) {
                        if (!this.nodeTarget2) {
                            this.nodeTarget2 = cc.find(
                                "Canvas/baseNode/BaseHome/contentModule/PrefabHomeFunctionUI/nodeSystemEntry/nodeLeft/menuLayout/PopupBox"
                            );
                            if (this.nodeTarget2) {
                                this.posTarget2 = this.nodeTarget2.convertToWorldSpaceAR(cc.v2());
                                this.posTarget2 = this.nodeDrop.convertToNodeSpaceAR(this.posTarget2);
                                this.scaleTarget2 = 1;
                            }
                        }
                        if (this.nodeTarget2) {
                            this.playCollectAni(
                                itemDropData.nodeItem,
                                this.nodeTarget2,
                                this.posTarget2,
                                this.scaleTarget2
                            );
                            return;
                        }
                    }
                }
                cc.tween(itemDropData.nodeItem)
                    .to(0.1, { opacity: 0 })
                    .call(() => {
                        this.putDropItem(itemDropData.nodeItem);
                    })
                    .start();
            },
            0.5
        );
    }

    /**
     * 播放装备掉落动画
     * @param itemDropData 道具掉落数据
     * @param initPos
     * @param jumpDis
     * @param layoutData
     */
    private playEquipDropAni(
        itemDropData: ICombatItemDropData,
        initPos: cc.Vec2,
        jumpDis: number,
        layoutData: number[]
    ): void {
        itemDropData.nodeItem = this.getDropItem();
        itemDropData.nodeItem.setPosition(initPos);
        this.updateEquipInfo(itemDropData.nodeItem, itemDropData.equipData.equipId);
        this.playDropAni2(
            itemDropData.nodeItem,
            initPos,
            itemDropData.height,
            jumpDis,
            layoutData,
            () => {
                this.showEquipInfo(itemDropData);
            },
            1.5
        );
    }

    /**
     * 播放装备分解动画
     * @param itemDropData 道具掉落数据
     */
    private playEquipDecomposeAni(itemDropData: ICombatItemDropData): void {
        const nodeDecomposeItem = this.getDecomposeItem();
        nodeDecomposeItem.setPosition(itemDropData.nodeItem.getPosition());
        const spineDecomposeItem = nodeDecomposeItem.getComponent(sp.Skeleton);
        spineDecomposeItem.setCompleteListener(() => {
            spineDecomposeItem.setCompleteListener(null);

            spineDecomposeItem.clearTracks();
            this.putDecomposeItem(nodeDecomposeItem);
        });
        spineDecomposeItem.setAnimation(0, "wait", false);

        let rightIndex = 0;
        let leftIndex = 0;
        for (let i = 0; i < 4; i++) {
            let isRight = i % 2 === 0;
            const initPos = cc.v2(itemDropData.nodeItem.x, itemDropData.nodeItem.y + JUMP_HEIGHT_2);
            if (isRight) {
                let jumpDis = initPos.x;
                jumpDis += rightIndex === 0 ? JUMP_DIS_2 / 4 : JUMP_DIS_2 * rightIndex;
                jumpDis += JUMP_SECOND_DIS;
                if (jumpDis > this.nodeMonster.x + this.nodeMonster.width) {
                    let jumpDis2 = initPos.x;
                    jumpDis2 -= leftIndex === 0 ? JUMP_DIS_2 / 4 : JUMP_DIS_2 * leftIndex;
                    jumpDis2 -= JUMP_SECOND_DIS;
                    if (jumpDis2 >= this.nodeMonster.x) {
                        isRight = false;
                    } else {
                        if (rightIndex > 0) {
                            rightIndex = 0;
                        } else {
                            isRight = false;
                            leftIndex = 0;
                        }
                    }
                }
            } else {
                let jumpDis = initPos.x;
                jumpDis -= leftIndex === 0 ? JUMP_DIS_2 / 4 : JUMP_DIS_2 * leftIndex;
                jumpDis -= JUMP_SECOND_DIS;
                if (jumpDis < this.nodeMonster.x) {
                    let jumpDis2 = initPos.x;
                    jumpDis2 += rightIndex === 0 ? JUMP_DIS_2 / 4 : JUMP_DIS_2 * rightIndex;
                    jumpDis2 += JUMP_SECOND_DIS;
                    if (jumpDis2 <= this.nodeMonster.x + this.nodeMonster.width) {
                        isRight = true;
                    } else {
                        if (leftIndex > 0) {
                            leftIndex = 0;
                        } else {
                            isRight = true;
                            rightIndex = 0;
                        }
                    }
                }
            }

            const nodeDropItem = this.getDropItem();
            nodeDropItem.setPosition(initPos);
            const equipInfo = TBLeadEquip.getInstance().getDataById(itemDropData.equipData.equipId);
            this.updateItemInfo(nodeDropItem, equipInfo.destructionReward[0][0]);
            this.playDropAni(
                nodeDropItem,
                initPos,
                JUMP_HEIGHT_2,
                JUMP_DIS_2,
                isRight,
                isRight ? rightIndex++ : leftIndex++,
                () => {
                    cc.tween(nodeDropItem)
                        .to(0.1, { opacity: 0 })
                        .call(() => {
                            this.putDropItem(nodeDropItem);
                        })
                        .start();
                }
            );
        }
    }

    /**
     * 获取掉落item
     * @returns
     */
    private getDropItem(): cc.Node {
        let nodeItem: cc.Node = null;
        if (this.dropItemPool.length > 0) {
            nodeItem = this.dropItemPool.shift();
        } else {
            nodeItem = Loader.getInstance().instantiate(this.nodeDropItem);
            this.nodeDrop.addChild(nodeItem);
        }

        nodeItem.opacity = 255;
        nodeItem.child("spIcon").opacity = 255;

        return nodeItem;
    }

    /**
     * 丢弃掉落item
     * @param nodeItem
     */
    private putDropItem(nodeItem: cc.Node): void {
        nodeItem.opacity = 0;
        nodeItem.child("spLight").opacity = 0;
        nodeItem.child("spineLight").opacity = 0;
        nodeItem.child("spineLight2").opacity = 0;
        nodeItem.child("spIcon").opacity = 0;

        this.dropItemPool.push(nodeItem);
    }

    /**
     * 获取分解item
     * @returns
     */
    private getDecomposeItem(): cc.Node {
        let nodeItem: cc.Node = null;
        if (this.decomposeItemPool.length > 0) {
            nodeItem = this.decomposeItemPool.shift();
        } else {
            nodeItem = Loader.getInstance().instantiate(this.nodeDecomposeItem);
            this.nodeDecompose.addChild(nodeItem);
        }

        nodeItem.opacity = 255;

        return nodeItem;
    }

    /**
     * 丢弃分解item
     * @param nodeItem
     */
    private putDecomposeItem(nodeItem: cc.Node): void {
        nodeItem.opacity = 0;

        this.decomposeItemPool.push(nodeItem);
    }
}
