/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 09:18:36
 */

import ListView from "../../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import TextUtils from "../../../nsn/util/TextUtils";
import TimeFormat, { TimeDurationFormatType } from "../../../nsn/util/TimeFormat";
import { PlayerInfo, PlayerInfoReplaceRet } from "../../../protobuf/proto";
import GrayComp from "../../comp/GrayComp";
import i18n from "../../config/i18n/I18n";
import RedPoint from "../../core/redPoint/RedPoint";
import { RedPointId } from "../../core/redPoint/RedPointId";
import { EnumItemType } from "../../data/base/BaseItem";
import DataItem from "../../data/extend/DataItem";
import TBAttribute from "../../data/parser/TBAttribute";
import TBEconomyAttribute from "../../data/parser/TBEconomyAttribute";
import TBItem, { ITEM_ID } from "../../data/parser/TBItem";
import TBItemAttribute from "../../data/parser/TBItemAttribute";
import Bag from "../../game/Bag";
import Player, { PlayerEvent } from "../../game/Player";
import PlayerInfoUtils from "../../utils/PlayerInfoUtils";

const { ccclass, property } = cc._decorator;

enum UIType {
    Avatar,
    AvatarFrame,
    Title,
}

const UI_TAB = [UIType.Avatar, UIType.AvatarFrame, UIType.Title];

const RED_POINT_TAB = [
    RedPointId.LeadPersonalizedOthersAvatar,
    RedPointId.LeadPersonalizedOthersAvatarFrame,
    RedPointId.LeadPersonalizedOthersTitle,
];

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

@ccclass
export default class PrefabPersonalizedOthers extends I18nComponent {
    @property(cc.Node)
    nodeLimitTime: cc.Node = null; // 限时
    @property(cc.RichText)
    rtLimitTime: cc.RichText = null; // 限时
    @property(cc.Node)
    nodeHead: cc.Node = null; // 头像
    @property(cc.Node)
    nodeTitle: cc.Node = null; // 称号
    @property(cc.Label)
    lbtAttr: cc.Label = null; // 属性
    @property(ListView)
    listView: ListView = null;
    @property(cc.Node)
    btnSave: cc.Node = null;
    @property(cc.Node)
    btnText: cc.Node = null;
    @property(cc.Label)
    title: cc.Label = null;
    @property(cc.Node)
    lock: cc.Node = null;
    @property(cc.Node)
    btns: cc.Node = null;

    private uiType: UIType = UIType.Avatar;
    private avatarId: number = 0;
    private avatarListData: DataItem[] = [];
    private avatarFrameId: number = 0;
    private avatarFrameListData: DataItem[] = [];
    private titleId: number = 0;
    private titleListData: DataItem[] = [];
    private itemId: number = -1; // 道具id
    private refreshTime: number = 0; // 刷新时间

    protected onLoad(): void {
        for (let i = 0; i < this.btns.childrenCount; i++) {
            this.btns.children[i].button(UI_TAB[i]);
        }
        const info = Player.getInstance().getInfo();
        this.titleId = info.title;
        this.avatarId = info.avatar;
        this.avatarFrameId = info.avatarFrame;
        this.registerHandler();
        this.updateUI();
    }

    protected onDestroy(): void {
        RedPoint.getInstance().cancelRecord(RED_POINT_TAB[this.uiType]);
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.updateLimitTimeState();
            }
        }
    }

    protected registerHandler(): void {
        Player.getInstance().on(
            PlayerEvent.SelectedItem,
            (itemId: number) => {
                const data = TBItem.getInstance().getDataById(itemId);
                switch (data.type) {
                    case EnumItemType.Title:
                        this.titleId = itemId;
                        break;
                    case EnumItemType.AvatarFrame:
                        this.avatarFrameId = itemId;
                        break;
                    case EnumItemType.HeadSculpture:
                        this.avatarId = itemId;
                        break;
                    default:
                        break;
                }
                this.updateUI();
            },
            this
        );
        Player.getInstance().on(
            PlayerInfoReplaceRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateInfo();
        this.updateList();
    }

    private updateInfo(): void {
        this.nodeHead.active = this.uiType === UIType.Avatar || this.uiType === UIType.AvatarFrame;
        this.nodeTitle.active = this.uiType === UIType.Title;
        let data: DataItem = null;
        let isCur = false;
        const info = Player.getInstance().getInfo();
        switch (this.uiType) {
            case UIType.Avatar:
                data = TBItem.getInstance().getDataById(this.avatarId);
                PlayerInfoUtils.updateHead(
                    this.nodeHead,
                    PlayerInfo.create({ avatar: this.avatarId, avatarFrame: this.avatarFrameId })
                );
                isCur = info.avatar === data.id;
                break;
            case UIType.AvatarFrame:
                data = TBItem.getInstance().getDataById(this.avatarFrameId);
                PlayerInfoUtils.updateHead(
                    this.nodeHead,
                    PlayerInfo.create({ avatar: this.avatarId, avatarFrame: this.avatarFrameId })
                );
                isCur = info.avatarFrame === data.id;
                break;
            case UIType.Title:
                data = TBItem.getInstance().getDataById(this.titleId);
                PlayerInfoUtils.updateTitle(this.nodeTitle, PlayerInfo.create({ title: this.titleId }));
                isCur = info.title === data.id;
                break;
            default:
                break;
        }
        this.itemId = data.id;
        this.title.string = data.name;
        let attrDesc = "";
        const itemAttrInfo = TBItemAttribute.getInstance().getDataById(this.itemId);
        if (itemAttrInfo) {
            itemAttrInfo.attribute.forEach(([attrId, attrValue], i) => {
                const { name, value } = TBAttribute.getInstance().formatAttribute([attrId, attrValue]);
                attrDesc += name + value;
                i !== itemAttrInfo.attribute.length - 1 && (attrDesc += "    ");
            });
            itemAttrInfo.attribute.length !== 0 && itemAttrInfo.economyAttribute.length !== 0 && (attrDesc += "    ");
            itemAttrInfo.economyAttribute.forEach(([attrId, attrValue], i) => {
                const { name, value } = TBEconomyAttribute.getInstance().formatAttribute([attrId, attrValue]);
                attrDesc += name + value;
                i !== itemAttrInfo.economyAttribute.length - 1 && (attrDesc += "    ");
            });
        }
        this.lbtAttr.string = attrDesc;
        const { isOwn } = Bag.getInstance().getLimitTimeItemState(data.id);
        this.lock.active = !isOwn;
        !isOwn && this.lock.label(data.desc);
        this.btnSave.active = isOwn;
        if (isOwn) {
            this.btnSave.getComponent(GrayComp).gray = isCur;
            this.btnSave.getComponent(cc.Button).interactable = !isCur;
            this.btnText.label(isCur ? i18n.personalized0002 : i18n.personalized0001);
        }
        this.updateLimitTimeState(true);
    }

    private updateList(scrollToTop: boolean = false): void {
        let data: { data: DataItem; curSelected: number }[] = [];
        const info = Player.getInstance().getInfo();
        switch (this.uiType) {
            case UIType.Title:
                if (!this.titleListData.length) {
                    const titles = TBItem.getInstance().getDataByType(EnumItemType.Title);
                    const curId = info.title;
                    const a: DataItem[] = [];
                    const b: DataItem[] = [];
                    for (const e of titles) {
                        if (curId !== e.id) {
                            if (Bag.getInstance().getItemCountById(e.id)) {
                                a.push(e);
                            } else {
                                b.push(e);
                            }
                        }
                    }
                    this.titleListData = [TBItem.getInstance().getDataById(curId)].concat(a).concat(b);
                }
                data = this.titleListData.map((v) => {
                    return {
                        data: v,
                        curSelected: this.titleId,
                    };
                });
                this.listView.setListData(data);
                break;
            case UIType.AvatarFrame:
                if (!this.avatarFrameListData.length) {
                    const avatarFrames = TBItem.getInstance().getDataByType(EnumItemType.AvatarFrame);
                    const curId = info.avatarFrame;
                    const a: DataItem[] = [];
                    const b: DataItem[] = [];
                    for (const e of avatarFrames) {
                        if (curId !== e.id) {
                            if (Bag.getInstance().getItemCountById(e.id)) {
                                a.push(e);
                            } else {
                                b.push(e);
                            }
                        }
                    }
                    this.avatarFrameListData = [TBItem.getInstance().getDataById(curId)].concat(a).concat(b);
                }
                data = this.avatarFrameListData.map((v) => {
                    return {
                        data: v,
                        curSelected: this.avatarFrameId,
                    };
                });
                this.listView.setListData(data);
                break;
            case UIType.Avatar:
                if (!this.avatarListData.length) {
                    const avatars = TBItem.getInstance().getDataByType(EnumItemType.HeadSculpture);
                    const curId = info.avatar;
                    const a: DataItem[] = [];
                    const b: DataItem[] = [];
                    for (const e of avatars) {
                        if (curId !== e.id) {
                            if (Bag.getInstance().getItemCountById(e.id)) {
                                a.push(e);
                            } else {
                                b.push(e);
                            }
                        }
                    }
                    this.avatarListData = [TBItem.getInstance().getDataById(curId)].concat(a).concat(b);
                }
                data = this.avatarListData.map((v) => {
                    return {
                        data: v,
                        curSelected: this.avatarId,
                    };
                });
                this.listView.setListData(data);
                break;

            default:
                break;
        }
    }

    /**
     * 更新限时状态
     * @param isInit 是否为初始化调用
     */
    private updateLimitTimeState(isInit: boolean = false): void {
        const { isOwn, isLimitTime, remainingTime } = Bag.getInstance().getLimitTimeItemState(this.itemId);
        const isShow = remainingTime > 0;
        if (isInit) {
            this.refreshTime = 0;
            this.nodeLimitTime.active = isShow;

            if (isLimitTime && !isOwn) {
                switch (this.uiType) {
                    case UIType.AvatarFrame:
                        if (this.itemId === Player.getInstance().getAvatarFrame()) {
                            Player.getInstance().sendPlayerInfoReplace(ITEM_ID.DEFAULT_AVATAR_FRAME);
                        }
                        break;
                    case UIType.Title:
                        if (this.itemId === Player.getInstance().getTitle()) {
                            Player.getInstance().sendPlayerInfoReplace(ITEM_ID.DEFAULT_TITLE);
                        }
                        break;
                    default:
                        break;
                }
            }
        } else {
            !isShow && this.updateUI();
        }
        if (!isShow) {
            return;
        }

        this.rtLimitTime.string = TextUtils.format(
            i18n.personalized0003,
            TimeFormat.getInstance().getTextByDuration(remainingTime, TimeDurationFormatType.D_H_M_S_2)
        );

        this.refreshTime = REFRESH_DURATION;
    }

    protected onClickTab(sender: cc.Event.EventTouch, type: UIType): void {
        if (this.uiType === type) {
            return;
        }
        for (let i = 0; i < this.btns.childrenCount; i++) {
            this.btns.children[i].child("tab").active = UI_TAB[i] === type;
            this.btns.children[i].child("text").color = UI_TAB[i] === type ? "#775D48" : "#C0B3A2";
        }
        RedPoint.getInstance().cancelRecord(RED_POINT_TAB[this.uiType]);
        this.uiType = type;
        this.updateUI();
    }

    protected onClickSave(): void {
        let itemId = 0;
        switch (this.uiType) {
            case UIType.Title:
                itemId = this.titleId;
                break;
            case UIType.AvatarFrame:
                itemId = this.avatarFrameId;
                break;
            case UIType.Avatar:
                itemId = this.avatarId;
                break;
            default:
                break;
        }
        Player.getInstance().sendPlayerInfoReplace(itemId);
    }
}
