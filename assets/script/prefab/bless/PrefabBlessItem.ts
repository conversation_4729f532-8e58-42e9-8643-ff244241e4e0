/*
 * @Author: <PERSON><PERSON>d
 * @Date: 2024-04-01 17:07:52
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-15 15:35:35
 */

import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import TextUtils from "../../../nsn/util/TextUtils";
import TimeFormat, { TimeDurationFormatType } from "../../../nsn/util/TimeFormat";
import i18n from "../../config/i18n/I18n";
import DataBlessing from "../../data/extend/DataBlessing";
import TBAttribute from "../../data/parser/TBAttribute";
import { RECHARGE_ID } from "../../data/parser/TBRecharge";
import Ad from "../../game/Ad";
import Bless from "../../game/Bless";
import Recharge from "../../game/Recharge";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabBlessItem extends ListViewItem {
    @property(cc.Node)
    topBg: cc.Node = null;
    @property([cc.SpriteFrame])
    bgs: cc.SpriteFrame[] = [];
    @property(cc.Label)
    lbtName: cc.Label = null;
    @property(cc.Label)
    lbtDesc: cc.Label = null;
    @property(cc.Label)
    lbtLv: cc.Label = null;

    @property(cc.Label)
    lbtPro: cc.Label = null;
    @property(cc.ProgressBar)
    pro: cc.ProgressBar = null;

    @property(cc.Node)
    btnFree: cc.Node = null;
    @property(cc.Node)
    btnAd: cc.Node = null;

    @property(cc.Node)
    blessing: cc.Node = null;
    @property(cc.Node)
    blessingTime: cc.Node = null;
    @property(cc.Node)
    blessingIcon: cc.Node = null;

    @property(cc.Node)
    forever: cc.Node = null;
    @property(cc.Node)
    foreverIcon: cc.Node = null;

    public data: DataBlessing = null;
    private dt: number = 1;

    public onLoad(): void {
        super.onLoad();
        cc.tween(this.blessingIcon).by(0.2, { angle: 20 }).repeatForever().start();
        cc.tween(this.foreverIcon).by(0.2, { angle: 20 }).repeatForever().start();
    }

    public unuse(): void {
        this.data = null;
    }

    public reuse(): void {}

    public updateData(data: DataBlessing): void {
        this.data = data;
    }

    protected update(dt: number): void {
        this.dt += dt;
        if (this.dt < 1) {
            return;
        }
        if (!this.data) {
            return;
        }
        this.dt -= 1;
        this.updateUI();
    }

    private updateUI(): void {
        const tb = this.data;
        const data = Bless.getInstance().getFormatInfoById(tb.id);
        if (!data) {
            return;
        }
        const { info, leftTime, active } = data;
        const buyGift = Recharge.getInstance().isRecharged(RECHARGE_ID.BLESS);
        const curLv = info.level;
        const val = TBAttribute.getInstance().formatAttribute([
            tb.stepAttribute[0][0],
            tb.stepAttribute[0][2] * (curLv - 1) + tb.stepAttribute[0][1],
        ]);
        this.topBg.sprite(this.bgs[tb.res]);
        this.lbtName.string = this.data.name;
        this.lbtDesc.string = TextUtils.format(tb.desc, val.value);
        this.lbtLv.string = "Lv." + curLv;
        const cur = info.exp;
        const total = tb.experienceBar;
        const isMax = curLv === tb.maxLevel;
        this.lbtPro.string = isMax ? i18n.bless0001 : `${cur}/${total}`;
        this.pro.progress = isMax ? 1 : cur / total;
        if (buyGift) {
            this.forever.active = true;
            this.btnAd.active = false;
            this.btnFree.active = false;
            this.blessing.active = false;
        } else {
            this.forever.active = false;
            if (active) {
                this.btnAd.active = false;
                this.btnFree.active = false;
                this.blessing.active = true;
                this.blessingTime.label(
                    TimeFormat.getInstance().getTextByDuration(leftTime, TimeDurationFormatType.MM_SS)
                );
            } else {
                if (info.level === 1 && info.exp === 0) {
                    this.btnFree.active = true;
                    this.btnAd.active = false;
                } else {
                    this.btnFree.active = false;
                    this.btnAd.active = true;
                }
                this.blessing.active = false;
            }
        }
    }

    protected onClickAd(): void {
        Ad.getInstance().show(this.data.adId);
    }

    protected onClickFree(): void {
        Bless.getInstance().sendBlessStart(this.data.id);
    }
}
