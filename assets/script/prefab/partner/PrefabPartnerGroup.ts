/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-05-12 14:18:39
 */

import ListView from "../../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import { ArcherGroupActivateRewardRet } from "../../../protobuf/proto";
import { EnumGroupType } from "../../data/base/BaseGroup";
import { EnumGroupEffectConditionType } from "../../data/base/BaseGroupEffect";
import TBGroup from "../../data/parser/TBGroup";
import TBGroupEffect from "../../data/parser/TBGroupEffect";
import TBRule, { RULE_ID } from "../../data/parser/TBRule";
import Archer from "../../game/Archer";
import { AttrSourceType } from "../../game/Attribute";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabPartnerGroup extends I18nComponent {
    @property(cc.Node)
    title: cc.Node = null;
    @property(ListView)
    list: ListView = null;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.title,
                url: "texture/syncUI/partner/spPartnerTitle",
            },
        ];
    }

    protected onLoad(): void {
        this.registerHandler();
    }

    protected onEnable(): void {
        this.updateUI();
    }

    private registerHandler(): void {
        Archer.getInstance().on(
            ArcherGroupActivateRewardRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateList();
    }

    private updateList(): void {
        const list = TBGroup.getInstance().getDataByType(EnumGroupType.Archer);
        const data = list.concat().sort((a, b) => a.sort - b.sort);
        this.list.setListData(data);
        const rewardGroupId = this.getRewardGroupId();
        const index = data.findIndex((v) => v.id === rewardGroupId);
        this.list.scrollTo(index);
    }

    private getRewardGroupId(): number {
        const list = TBGroup.getInstance().getDataByType(EnumGroupType.Archer);
        for (const v of list) {
            const data = TBGroupEffect.getInstance().getDataByGroupId(v.id);
            const groupEffectIds = Archer.getInstance().getGroupEffectIds();
            for (const e of data) {
                if (!groupEffectIds.includes(e.id)) {
                    switch (e.conditionType) {
                        case EnumGroupEffectConditionType.ArcherId:
                            const own = Archer.getInstance().getDataById(e.condition);
                            if (own) {
                                return v.id;
                            }
                            break;
                        case EnumGroupEffectConditionType.ArcherStar:
                            let count = 0;
                            for (const e of v.groupItemId) {
                                const info = Archer.getInstance().getDataById(e);
                                if (info) {
                                    count += info.star;
                                }
                            }
                            if (count >= e.condition) {
                                return v.id;
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        return -1;
    }

    /**
     * 属性显示
     */
    protected onClickAttrShow(): void {
        UI.getInstance().open("PopupAttrShowSystem", AttrSourceType.ArcherGroup);
    }

    protected onClickBtnRule(): void {
        const data = TBRule.getInstance().getDataById(RULE_ID.PARTNER);
        if (!data) {
            return;
        }
        UI.getInstance().open("FloatRule", data);
    }
}
