/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-03-20 14:00:32
 */

import Loader from "../../../nsn/core/Loader";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import TextUtils from "../../../nsn/util/TextUtils";
import Tips from "../../../nsn/util/Tips";
import { WeaponBattleRet, WeaponCreateRet, WeaponUpgradeLevelRet } from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { EnumWeaponType } from "../../data/base/BaseWeapon";
import TBAttribute from "../../data/parser/TBAttribute";
import TBWeapon from "../../data/parser/TBWeapon";
import TBWeaponLevel from "../../data/parser/TBWeaponLevel";
import { AttrSourceType } from "../../game/Attribute";
import Bag from "../../game/Bag";
import Weapon from "../../game/Weapon";
import ImageUtils from "../../utils/ImageUtils";
import ItemUtils from "../../utils/ItemUtils";
import SpineUtils from "../../utils/SpineUtils";
import TweenUtil from "../../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

const STAR_RES = [0, 1, 2, 0, 1, 2, 0, 1, 2, 3];

@ccclass
export default class PrefabWeapon extends I18nComponent {
    @property(cc.Node)
    nodeTitle: cc.Node = null;
    @property(cc.Node)
    weaponIcon: cc.Node = null;
    @property(cc.Node)
    quality: cc.Node = null;
    @property(cc.Node)
    btnFight: cc.Node = null;
    @property(cc.Node)
    lock: cc.Node = null;
    @property(cc.Node)
    lockText: cc.Node = null;
    @property(cc.Node)
    show: cc.Node = null;

    @property(cc.Node)
    weaponName: cc.Node = null;
    @property(cc.Node)
    stars: cc.Node = null;
    @property(cc.Node)
    starLevel: cc.Node = null;

    @property(cc.Node)
    btnUpgrade: cc.Node = null;
    @property(cc.Node)
    btnUpgradeText: cc.Node = null;
    @property(cc.Node)
    upgradeCostIcon: cc.Node = null;
    @property(cc.Node)
    upgradeCostCount: cc.Node = null;
    @property(cc.Node)
    btnLight: cc.Node = null;
    @property(cc.Node)
    btnMax: cc.Node = null;
    @property(cc.Node)
    btnLeft: cc.Node = null;
    @property(cc.Node)
    btnRight: cc.Node = null;

    @property(cc.Node)
    upgradeAni1: cc.Node = null;
    @property(cc.Node)
    upgradeAni2: cc.Node = null;
    @property(cc.Node)
    upgradeTitle1: cc.Node = null;
    @property(cc.Node)
    upgradeTitle2: cc.Node = null;

    @property(cc.Node)
    efBoom: cc.Node = null;

    @property([cc.SpriteFrame])
    spStar1: cc.SpriteFrame[] = [];
    @property([cc.SpriteFrame])
    spStar2: cc.SpriteFrame[] = [];

    private weaponList: number[] = [];
    private curWeaponIndex: number = 0;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.upgradeTitle1,
                url: "texture/result/spResultTitle10",
            },
            {
                sprite: this.upgradeTitle2,
                url: "texture/result/spResultTitle4",
            },
            {
                sprite: this.nodeTitle,
                url: "texture/syncUI/weapon/spWeaponSystemTitle",
            },
        ];
    }

    protected onLoad(): void {
        this.efBoom.parent = null;
        this.registerHandler();
    }

    protected onDestroy(): void {
        this.efBoom.destroy();
    }

    protected onEnable(): void {
        this.updateUI();
    }

    protected onDisable(): void {
        Weapon.getInstance().setLevelUpgradeTip(false);
    }

    private registerHandler(): void {
        Weapon.getInstance().on(
            [WeaponBattleRet.prototype.clazzName, WeaponCreateRet.prototype.clazzName],
            () => {
                this.updateList();
                this.updateWeapon();
                this.updateBtns();
            },
            this
        );
        Weapon.getInstance().on(
            WeaponUpgradeLevelRet.prototype.clazzName,
            () => {
                this.updateStars();
                this.updateBtns();
                this.playAnimation();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateList();
        this.updateWeapon();
        this.updateStars();
        this.updateBtns();
    }

    private updateList(): void {
        const ordinaryWeapon = TBWeapon.getInstance()
            .getDataByType(EnumWeaponType.OrdinaryWeapon)
            .map((v) => v.id);
        const curWeaponId = Weapon.getInstance().getTeamId();
        if (ordinaryWeapon.includes(curWeaponId)) {
            this.weaponList = ordinaryWeapon;
        } else {
            this.weaponList = [curWeaponId].concat(ordinaryWeapon);
        }
        this.curWeaponIndex = this.weaponList.indexOf(curWeaponId);
    }

    private updateWeapon(): void {
        const curWeaponId = this.weaponList[this.curWeaponIndex];
        const data = TBWeapon.getInstance().getDataById(curWeaponId);
        ImageUtils.setQuality6(this.quality, data.quality);
        SpineUtils.setWeapon(this.weaponIcon, data.res);
        const teamId = Weapon.getInstance().getTeamId();
        const weaponIds = Weapon.getInstance().getWeaponIds();
        if (weaponIds.includes(curWeaponId)) {
            this.lock.active = false;
            this.btnFight.active = true;
            this.btnFight.spriteAsync(
                "texture/syncUI/tank/" + (curWeaponId === teamId ? "btnChariotGo2" : "btnChariotGo1")
            );
        } else {
            this.lock.active = true;
            this.lockText.label(
                TextUtils.format(i18n.weapon0002, TBWeaponLevel.getInstance().getUnlockLevelByWeaponId(curWeaponId))
            );
            this.btnFight.active = false;
        }
        this.show.active = curWeaponId === teamId;
        this.show.getComponent(cc.Toggle).isChecked = Weapon.getInstance().isShowWeapon();
        this.weaponName.label(data.name);
    }

    private updateStars(): void {
        const level = Weapon.getInstance().getLevel();
        const nextLevel = level + 1;
        const nextData = TBWeaponLevel.getInstance().getDataById(nextLevel);
        if (nextData) {
            for (let i = 0; i < this.stars.childrenCount; i++) {
                const node = this.stars.children[i];
                if (i < STAR_RES.length) {
                    node.sprite(i < (level - 1) % 10 ? this.spStar1[STAR_RES[i]] : this.spStar2[STAR_RES[i]]);
                }
                node.child("selected").active = i === (level - 1) % 10;
                node.button(i);
            }
            this.starLevel.color = "#ffe0b1";
        } else {
            for (let i = 0; i < this.stars.childrenCount; i++) {
                const node = this.stars.children[i];
                if (i < STAR_RES.length) {
                    node.sprite(this.spStar1[STAR_RES[i]]);
                }
                node.child("selected").active = false;
                node.button(i);
            }
            this.starLevel.color = "#fffa99";
        }

        this.starLevel.label(TextUtils.format(i18n.weapon0003, Math.ceil(level / 10)));
    }

    private updateBtns(): void {
        const curLevel = Weapon.getInstance().getLevel();
        const nextLevel = curLevel + 1;
        const nextData = TBWeaponLevel.getInstance().getDataById(nextLevel);
        if (nextData) {
            this.btnUpgrade.active = true;
            this.btnMax.active = false;
            ImageUtils.setItemIcon(this.upgradeCostIcon, nextData.upgradeCost[0][0]);
            ItemUtils.refreshCount(this.upgradeCostCount, nextData.upgradeCost[0][0], nextData.upgradeCost[0][1]);
            this.btnUpgradeText.label(curLevel % 10 ? i18n.common0071 : i18n.common0060);
            this.btnLight.active = Bag.getInstance().isEnough(nextData.upgradeCost[0][0], nextData.upgradeCost[0][1]);
            if (this.btnLight.active) {
                this.btnLight.stopAllActions();
                TweenUtil.breath(this.btnLight);
            }
        } else {
            this.btnUpgrade.active = false;
            this.btnMax.active = true;
        }

        this.btnLeft.active = this.weaponList.length > 1;
        this.btnRight.active = this.weaponList.length > 1;
    }

    private playAnimation(): void {
        const level = Weapon.getInstance().getLevel();
        if (level % 10 !== 1) {
            this.upgradeAni1.active = true;
            const spine = this.upgradeAni1.getComponent(sp.Skeleton);
            spine.clearTracks();
            cc.tween(this.upgradeAni1).stop();

            spine.setAnimation(0, "wait1", false);
            spine.setCompleteListener(() => {
                spine.setCompleteListener(null);
                cc.tween(this.upgradeAni1)
                    .delay(1)
                    .call(() => {
                        spine.setAnimation(0, "wait2", false);
                        spine.setCompleteListener(() => {
                            spine.setCompleteListener(null);
                            this.upgradeAni1.active = false;
                        });
                    })
                    .start();
            });
        } else {
            this.upgradeAni2.active = true;
            const spine = this.upgradeAni2.getComponent(sp.Skeleton);
            spine.clearTracks();
            cc.tween(this.upgradeAni2).stop();

            spine.setAnimation(0, "wait1", false);
            spine.setCompleteListener(() => {
                spine.setCompleteListener(null);
                cc.tween(this.upgradeAni2)
                    .delay(1)
                    .call(() => {
                        spine.setAnimation(0, "wait2", false);
                        spine.setCompleteListener(() => {
                            spine.setCompleteListener(null);
                            this.upgradeAni2.active = false;
                        });
                    })
                    .start();
            });
        }

        const index = (level - 2) % 10;
        const node = Loader.getInstance().instantiate(this.efBoom);
        node.parent = this.stars.children[index];
        node.active = true;
        const spine = node.getComponent(sp.Skeleton);
        spine.setCompleteListener(() => {
            spine.setCompleteListener(null);
            node.destroy();
        });
    }

    protected onClickFight(): void {
        const curWeaponId = this.weaponList[this.curWeaponIndex];
        const weaponIds = Weapon.getInstance().getWeaponIds();
        if (!weaponIds.includes(curWeaponId)) {
            return;
        }
        const teamId = Weapon.getInstance().getTeamId();
        if (curWeaponId === teamId) {
            return;
        }
        Weapon.getInstance().sendWeaponBattle(curWeaponId);
    }

    protected onClickLeft(): void {
        if (this.curWeaponIndex <= 0) {
            this.curWeaponIndex = this.weaponList.length - 1;
        } else {
            this.curWeaponIndex--;
        }
        this.updateWeapon();
        this.updateStars();
        this.updateBtns();
    }

    protected onClickRight(): void {
        if (this.curWeaponIndex >= this.weaponList.length - 1) {
            this.curWeaponIndex = 0;
        } else {
            this.curWeaponIndex++;
        }
        this.updateWeapon();
        this.updateStars();
        this.updateBtns();
    }

    protected onClickUpgrade(): void {
        const curLevel = Weapon.getInstance().getLevel();
        const nextLevel = curLevel + 1;
        const nextData = TBWeaponLevel.getInstance().getDataById(nextLevel);
        if (!Bag.getInstance().isEnough(nextData.upgradeCost[0][0], nextData.upgradeCost[0][1])) {
            UI.getInstance().open("FloatItemSource", nextData.upgradeCost[0][0]);
            return;
        }
        Weapon.getInstance().sendWeaponUpgradeLevel();
    }

    protected onClickSkin(): void {
        UI.getInstance().open("PopupWeaponSkin");
    }

    protected onToggleShow(toggle: cc.Toggle): void {
        Weapon.getInstance().sendWeaponShow(toggle.isChecked);
        Tips.getInstance().show(toggle.isChecked ? i18n.weapon0004 : i18n.weapon0005);
    }

    protected onClickStar(sender: cc.Event.EventTouch, index: number): void {
        const level = Weapon.getInstance().getLevel();
        const id = level - (level % 10) + index + 2;
        let data = TBWeaponLevel.getInstance().getDataById(id);
        data = data ?? TBWeaponLevel.getInstance().getDataById(id - 10);
        const { name, value } = TBAttribute.getInstance().formatAttribute(data.showAttribute[0]);
        const pos = sender.target.convertToWorldSpaceAR(cc.v2());
        UI.getInstance().open("FloatTextTips", {
            pos,
            text: name + "+" + value,
            offset: cc.v2(0, id % 10 !== 1 ? 110 : 170),
        });
    }

    /**
     * 属性显示
     */
    protected onClickAttrShow(): void {
        UI.getInstance().open("PopupAttrShowSystem", AttrSourceType.Weapon);
    }
}
