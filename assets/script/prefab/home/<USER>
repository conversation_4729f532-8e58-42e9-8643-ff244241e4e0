/*
 * @Author: chenx
 * @Date: 2024-09-03 18:28:41
 * @Last Modified by: chenx
 * @Last Modified time: 2024-11-13 17:51:07
 */
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import TextUtils from "../../../nsn/util/TextUtils";
import Time from "../../../nsn/util/Time";
import { EquipDungeonInviteNoticeRet } from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { EnumDungeonEquipmentTotalPara } from "../../data/base/BaseDungeonEquipmentTotal";
import TBDungeonEquipmentTotal from "../../data/parser/TBDungeonEquipmentTotal";
import DungeonEquip from "../../game/DungeonEquip";
import { IDungeonEquipBeInvitedData } from "../dungeon/PrefabDungeonEquipBeInvitedItem";

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 装备副本提示
 */
@ccclass
export default class PrefabHomeDungeonEquipTips extends I18nComponent {
    @property(cc.Node)
    nodeBg: cc.Node = null; // 提示bg
    @property(cc.Label)
    lbtTips: cc.Label = null; // 提示

    beInvitedData: IDungeonEquipBeInvitedData[] = []; // 被邀请数据
    refreshTime: number = 0; // 刷新时间
    isUnfold: boolean = false; // 是否展开

    protected onLoad(): void {
        this.registerHandler();
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                const dataCount = this.beInvitedData.length;
                const nowTime = Time.getInstance().now();
                for (let i = this.beInvitedData.length - 1; i >= 0; i--) {
                    const isExpired =
                        nowTime - this.beInvitedData[i].time >=
                        TBDungeonEquipmentTotal.getInstance().getValueByPara(
                            EnumDungeonEquipmentTotalPara.InvitationDisappearCd
                        ) *
                            1000;
                    isExpired && this.beInvitedData.splice(i, 1);
                }
                this.beInvitedData.length !== dataCount && this.updateTipsState();
                this.beInvitedData.length === 0 && this.isUnfold && this.playTipsAni();

                this.beInvitedData.length !== 0 && (this.refreshTime = REFRESH_DURATION);
            }
        }
    }

    protected registerHandler(): void {
        DungeonEquip.getInstance().on(
            EquipDungeonInviteNoticeRet.prototype.clazzName,
            (data: EquipDungeonInviteNoticeRet) => {
                this.beInvitedData.push({
                    playerData: data.playerInfo,
                    teamId: data.teamId,
                    levelId: data.dungeonId,
                    time: Time.getInstance().now(),
                });
                this.updateTipsState();
                !this.isUnfold && this.playTipsAni();

                this.refreshTime = REFRESH_DURATION;
            },
            this
        );
    }

    /**
     * 更新提示状态
     */
    private updateTipsState(): void {
        this.lbtTips.string = TextUtils.format(i18n.dungeon0017, this.beInvitedData.length);
    }

    /**
     * 播放提示动画
     */
    private playTipsAni(): void {
        cc.Tween.stopAllByTarget(this.nodeBg);
        this.isUnfold = !this.isUnfold;
        if (this.isUnfold) {
            cc.tween(this.nodeBg)
                .to(0.4, { x: cc.winSize.width / 2 }, { easing: cc.easing.sineOut })
                .start();
        } else {
            cc.tween(this.nodeBg)
                .to(0.4, { x: cc.winSize.width / 2 + this.nodeBg.width }, { easing: cc.easing.sineIn })
                .start();
        }
    }

    /**
     * 被邀请
     */
    protected onClickBeInvited(): void {
        if (this.beInvitedData.length === 0) {
            return;
        }

        UI.getInstance().open("PopupDungeonEquipBeInvited", this.beInvitedData);
    }
}
