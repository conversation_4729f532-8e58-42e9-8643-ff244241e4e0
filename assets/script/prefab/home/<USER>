/*
 * @Author: zhangwj
 * @Date: 2024-5-16 17:30:07
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-24 14:54:42
 */
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import MathUtils from "../../../nsn/util/MathUtils";
import TextUtils from "../../../nsn/util/TextUtils";
import Tips from "../../../nsn/util/Tips";
import {
    BagUpdateRet,
    ChatGetChannelMessagesRet,
    ChatMessageInfo,
    IChatMessageInfo,
    PlayerInfoReplaceRet,
    PlayerRenameRet,
    PowerBreakOutRet,
    PowerPromotionRet,
    PowerUpgradeRet,
    TaskUpdateRet,
} from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import Chat, { Chat<PERSON>vent, IChatMsgFormatData } from "../../core/Chat";
import RedPoint from "../../core/redPoint/RedPoint";
import { RedPointId } from "../../core/redPoint/RedPointId";
import { CHAT_CHANNEL } from "../../data/parser/TBChat";
import TBChatText from "../../data/parser/TBChatText";
import { ITEM_ID } from "../../data/parser/TBItem";
import TBPowerLevel from "../../data/parser/TBPowerLevel";
import TBRedPacket from "../../data/parser/TBRedPacket";
import Bag from "../../game/Bag";
import Combat, { CombatEvent } from "../../game/Combat";
import CombatScore, { CombatScoreEvent } from "../../game/CombatScore";
import Player from "../../game/Player";
import Power from "../../game/Power";
import RedPacket from "../../game/RedPacket";
import Task from "../../game/Task";
import Union from "../../game/Union";
import CombatScoreUtils from "../../utils/CombatScoreUtils";
import ImageUtils from "../../utils/ImageUtils";
import PlayerInfoUtils from "../../utils/PlayerInfoUtils";
import PlayerResUtils from "../../utils/PlayerResUtils";

/**
 * 聊天提示最大长度
 */
const CHAT_TIPS_MAX_LENGTH = 10;

const { ccclass, property } = cc._decorator;

/**
 * 主界面-功能UI
 */
@ccclass
export default class PrefabHomeFunctionUI extends I18nComponent {
    // 个人信息
    @property(cc.Node)
    nodeHead: cc.Node = null; // 头像
    @property(cc.Node)
    nodeCombatScore: cc.Node = null; // 战斗力
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Sprite)
    spPowerIcon: cc.Sprite = null; // 王权icon
    @property(cc.Node)
    nodePowerStar: cc.Node = null; // 王权星星
    @property(cc.Label)
    lbtPowerName: cc.Label = null; // 王权名称
    @property(cc.Node)
    nodeRes: cc.Node = null; // 资源栏

    // 系统入口
    @property(cc.Node)
    nodeRightStorage: cc.Node = null; // 右侧收纳

    // 聊天
    @property(cc.RichText)
    rtTips: cc.RichText = null; // 提示

    // 王权
    @property(sp.Skeleton)
    spinePower: sp.Skeleton = null; // 王权
    @property([sp.Skeleton])
    spineAction: sp.Skeleton[] = []; // 动作

    private chatChannel: string = CHAT_CHANNEL.WORLD; // 聊天渠道
    private chatParam: string = ""; // 聊天参数
    private isShow: boolean = false; // 是否显示-右侧收纳
    private isPlayingAni: boolean = false; // 是否正在播放动画-右侧收纳
    private isShow2: boolean = false; // 是否显示-王权红点

    protected onLoad(): void {
        this.registerHandler();

        // 个人信息
        this.updateHead();
        this.updateName();
        this.updateCombatScore(-1);
        this.updatePowerInfo();
        PlayerResUtils.create(ITEM_ID.DIAMOND, this.nodeRes);

        // 聊天
        this.updateChatInfo();

        // 王权
        this.updatePowerState();
        this.updatePowerAction();
    }

    /**
     * 注册事件
     */
    protected registerHandler(): void {
        // 玩家-信息替换
        Player.getInstance().on(
            PlayerInfoReplaceRet.prototype.clazzName,
            () => {
                this.updateHead();
            },
            this
        );
        // 玩家-重命名
        Player.getInstance().on(
            PlayerRenameRet.prototype.clazzName,
            () => {
                this.updateName();
            },
            this
        );
        // 战斗力-更新
        CombatScore.getInstance().on(
            CombatScoreEvent.Update,
            (score: number) => {
                this.updateCombatScore(score);
            },
            this
        );
        // 王权-升级/突破/晋升
        Power.getInstance().on(
            [
                PowerUpgradeRet.prototype.clazzName,
                PowerBreakOutRet.prototype.clazzName,
                PowerPromotionRet.prototype.clazzName,
            ],
            () => {
                this.updatePowerInfo();
                this.updatePowerState();
            },
            this
        );
        // 聊天-获取渠道消息/删除好友消息
        Chat.getInstance().on(
            [ChatGetChannelMessagesRet.prototype.clazzName, ChatEvent.DeleteFriendMsg],
            () => {
                this.updateChatInfo();
            },
            this
        );
        // 聊天-广播公共消息
        Chat.getInstance().on(
            ChatEvent.BroadcastCommonMsg,
            (data: IChatMessageInfo) => {
                const player = Chat.getInstance().getPlayerInfoById(data.sender);
                switch (data.channel) {
                    case CHAT_CHANNEL.WORLD:
                        this.rtTips.string = this.formatChatMsg({
                            msg: data,
                            channelName: i18n.chat0005,
                            name: player.name,
                            channel: CHAT_CHANNEL.WORLD,
                            param: "",
                        });
                        break;
                    case CHAT_CHANNEL.UNION:
                        this.rtTips.string = this.formatChatMsg({
                            msg: data,
                            channelName: i18n.chat0006,
                            name: player.name,
                            channel: CHAT_CHANNEL.UNION,
                            param: "",
                        });
                        break;
                    default:
                        break;
                }
            },
            this
        );
        // 背包-更新
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                this.updatePowerState();
            },
            this
        );
        // 任务-更新
        Task.getInstance().on(
            TaskUpdateRet.prototype.clazzName,
            () => {
                this.updatePowerState();
            },
            this
        );
        // 战斗-主界面触摸
        Combat.getInstance().on(
            CombatEvent.HomeTouch,
            (touchWorldPos: cc.Vec2) => {
                if (!this.isPlayingAni && this.isShow) {
                    const pos = this.nodeRightStorage.parent.convertToNodeSpaceAR(touchWorldPos);
                    if (!this.nodeRightStorage.getBoundingBox().contains(pos)) {
                        this.onClickSetRightStorageState();
                    }
                }
            },
            this
        );
    }

    private updateHead(): void {
        const info = Player.getInstance().getInfo();
        PlayerInfoUtils.updateHead(this.nodeHead, info);
    }

    /**
     * 更新玩家名称
     */
    private updateName(): void {
        this.lbtName.string = Player.getInstance().getName();
    }

    /**
     * 更新战斗力
     * @param combatScore 战斗力
     */
    private updateCombatScore(combatScore: number): void {
        if (combatScore === -1) {
            combatScore = CombatScore.getInstance().getScore();
        }
        CombatScoreUtils.update(this.nodeCombatScore, combatScore);
    }

    /**
     * 更新王权信息
     */
    private updatePowerInfo(): void {
        const powerData = Power.getInstance().getPowerInfo();
        ImageUtils.setPowerIcon2(this.spPowerIcon.node, this.nodePowerStar, powerData.kingLevelId);
        const levelInfo = TBPowerLevel.getInstance().getDataById(powerData.kingLevelId);
        this.lbtPowerName.string = levelInfo.name;
    }

    /**
     * 更新聊天信息
     */
    private updateChatInfo(): void {
        const msgs: IChatMsgFormatData[] = [];
        const msg1 = Chat.getInstance().getMessagesByChannel(CHAT_CHANNEL.WORLD);
        if (msg1.length) {
            const player = Chat.getInstance().getPlayerInfoById(msg1[msg1.length - 1].sender);
            msgs.push({
                msg: msg1[msg1.length - 1],
                channelName: i18n.chat0005,
                name: player.name,
                channel: CHAT_CHANNEL.WORLD,
                param: "",
            });
        }
        const msg2 = Chat.getInstance().getMessagesByChannel(CHAT_CHANNEL.UNION);
        if (msg2.length) {
            const player = Chat.getInstance().getPlayerInfoById(msg2[msg2.length - 1].sender);
            msgs.push({
                msg: msg2[msg2.length - 1],
                channelName: i18n.chat0006,
                name: player.name,
                channel: CHAT_CHANNEL.UNION,
                param: "",
            });
        }
        msgs.sort((a, b) => a.msg.sendAt - b.msg.sendAt);
        if (msgs.length > 0) {
            this.rtTips.string = this.formatChatMsg(msgs[msgs.length - 1]);
        }
    }

    /**
     * 格式化聊天消息
     * @param data 格式化数据
     */
    private formatChatMsg(data: IChatMsgFormatData): string {
        const c1 = "#dfcab5";
        const c2 = "#dfcab5";
        this.chatChannel = data.channel;
        this.chatParam = data.param;
        const t1 = i18n.chat0012 + data.channelName + i18n.chat0013;
        let content = "";
        switch (data.msg.type) {
            case ChatMessageInfo.Type.Text:
                content = data.msg.content;
                break;
            case ChatMessageInfo.Type.TextTemp:
                {
                    const textInfo = TBChatText.getInstance().getDataById(parseInt(data.msg.content));
                    content = TextUtils.format(textInfo.text, data.msg.attrs);
                }
                break;
            case ChatMessageInfo.Type.RedPackTextTemp:
                {
                    const redPacketId = data.msg.attrs[0];
                    const redPacketData = RedPacket.getInstance().getData(redPacketId);
                    const redPacketInfo = TBRedPacket.getInstance().getDataById(redPacketData.packetId);
                    const textInfo = TBChatText.getInstance().getDataByRedPacketId(redPacketData.packetId);
                    content = Chat.getInstance().formatChatText(
                        textInfo.type,
                        redPacketInfo.text,
                        "",
                        redPacketData.paras
                    );
                }
                break;
            default:
                break;
        }
        const text = (data.name ? data.name + "：" : "") + content;
        const t2 = text.length > CHAT_TIPS_MAX_LENGTH ? text.substring(0, CHAT_TIPS_MAX_LENGTH) + "..." : text;
        return `<color=${c1}>${t1}</color><color=${c2}>${t2}</color>`;
    }

    /**
     * 更新王权状态
     */
    private updatePowerState(): void {
        const isShow = RedPoint.getInstance().checkRedPoint(RedPointId.PowerAll);
        if (this.isShow2 !== isShow) {
            this.isShow2 = isShow;
            this.spinePower.clearTrack(0);
            if (this.isShow2) {
                this.spinePower.setAnimation(0, "wait2", true);
            } else {
                this.spinePower.setAnimation(0, "wait", true);
            }
        }
    }

    /**
     * 更新王权动作
     */
    private updatePowerAction(): void {
        const index = MathUtils.getRandomInt(0, this.spineAction.length);
        const nodeAction = this.spineAction[index].node;
        const spineAction = this.spineAction[index];
        nodeAction.active = true;
        cc.tween(nodeAction).to(0.4, { opacity: 255 }).start();
        spineAction.setCompleteListener(() => {
            spineAction.setCompleteListener(null);

            cc.tween(nodeAction)
                .to(0.4, { opacity: 0 })
                .call(() => {
                    nodeAction.active = false;
                    spineAction.clearTrack(0);

                    this.scheduleOnce(this.updatePowerAction, MathUtils.getRandomInt(10, 20));
                })
                .start();
        });
        spineAction.setAnimation(0, "wait", true);
    }

    /**
     * 玩家信息
     */
    protected onClickPlayerInfo(): void {
        UI.getInstance().open("PopupPlayerInfo");
    }

    /**
     * 设置右侧收纳状态
     */
    protected onClickSetRightStorageState(): void {
        if (this.isPlayingAni) {
            return;
        }

        this.isShow = !this.isShow;
        this.isPlayingAni = true;
        if (this.isShow) {
            cc.tween(this.nodeRightStorage)
                .to(0.2, { x: this.node.width / 2 })
                .call(() => {
                    this.isPlayingAni = false;
                })
                .start();
        } else {
            cc.tween(this.nodeRightStorage)
                .to(0.2, { x: this.node.width / 2 + this.nodeRightStorage.width })
                .call(() => {
                    this.isPlayingAni = false;
                })
                .start();
        }
    }

    /**
     * 聊天
     */
    protected onClickChat(): void {
        switch (this.chatChannel) {
            case CHAT_CHANNEL.UNION:
                const union = Union.getInstance().getInfo();
                if (!union) {
                    Tips.getInstance().show(i18n.chat0008);
                    return;
                }
                break;
            default:
                break;
        }

        UI.getInstance().open("PopupChat", { channel: this.chatChannel, param: this.chatParam });
    }

    /**
     * 红包
     */
    protected onClickRedPacket(): void {
        UI.getInstance().open("PopupRedPacket");
    }

    /**
     * 王权
     */
    protected onClickPower(): void {
        UI.getInstance().open("UIPower");
    }
}
