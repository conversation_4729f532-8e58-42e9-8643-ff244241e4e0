/*
 * @Author: chenx
 * @Date: 2024-02-28 14:45:56
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-17 10:37:25
 */
import Loader from "../../../nsn/core/Loader";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import CocosExt from "../../../nsn/util/CocosExt";
import MathUtils from "../../../nsn/util/MathUtils";
import TextUtils from "../../../nsn/util/TextUtils";
import Time from "../../../nsn/util/Time";
import Tips from "../../../nsn/util/Tips";
import {
    ArcherBattleRet,
    ArrowGroupSyncRet,
    ArrowGroupUnlockArrowCellRet,
    BagUpdateRet,
    ForgeCompleteRet,
    ForgeCompleteShowRet,
    ForgeCultivateUpgradeRet,
    ForgeDressSkinRet,
    IArrowCellInfo,
    IBattleCellInfo,
    IItemInfo,
    PowerBreakOutRet,
    PowerPromotionRet,
    PowerUpgradeRet,
    TaskTakeAwardRet,
    TaskUpdateRet,
} from "../../../protobuf/proto";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../../config/AudioEffectConfig";
import i18n from "../../config/i18n/I18n";
import { EnumEconomyAttributeType } from "../../data/base/BaseEconomyAttribute";
import { EnumExpandType, EnumExpandUnlockType } from "../../data/base/BaseExpand";
import { EnumItemType } from "../../data/base/BaseItem";
import { EnumTaskDetailType } from "../../data/base/BaseTaskDetail";
import { EnumTaskSeqModule } from "../../data/base/BaseTaskSeq";
import { EnumUniversalPara } from "../../data/base/BaseUniversal";
import DataItem from "../../data/extend/DataItem";
import TBArcher from "../../data/parser/TBArcher";
import TBArrow from "../../data/parser/TBArrow";
import TBExpand from "../../data/parser/TBExpand";
import TBForgeCultivateLevel from "../../data/parser/TBForgeCultivateLevel";
import TBForgeSkin from "../../data/parser/TBForgeSkin";
import { GAME_SWITCH_ID } from "../../data/parser/TBGameSwitch";
import TBItem, { ITEM_ID } from "../../data/parser/TBItem";
import { JumpType } from "../../data/parser/TBJump";
import TBPowerLevel from "../../data/parser/TBPowerLevel";
import TBPrivilegeConfig, { PRIVILEGE_ID } from "../../data/parser/TBPrivilegeConfig";
import TBTaskDetail from "../../data/parser/TBTaskDetail";
import TBTaskSeq from "../../data/parser/TBTaskSeq";
import TBUniversal from "../../data/parser/TBUniversal";
import Archer from "../../game/Archer";
import { IEconomyAttr } from "../../game/Attribute";
import Bag from "../../game/Bag";
import EconomyAttribute, { EconomyAttributeEvent } from "../../game/EconomyAttribute";
import Forge from "../../game/Forge";
import GameSwitch from "../../game/GameSwitch";
import MakeArrow, { MakeArrowEvent } from "../../game/MakeArrow";
import Power from "../../game/Power";
import Privilege, { PrivilegeEvent } from "../../game/Privilege";
import Setting, { SettingId } from "../../game/Setting";
import Task from "../../game/Task";
import AudioUtils from "../../utils/AudioUtils";
import ImageUtils from "../../utils/ImageUtils";
import JumpUtils from "../../utils/JumpUtils";
import NumberUtils from "../../utils/NumberUtils";

/**
 * 制作状态
 */
enum MakeState {
    Init = 1, // 初始化
    Wait = 2, // 待机
}

/**
 * 制作数据
 */
interface IMakeData {
    state: MakeState; // 状态

    attr: IEconomyAttr; // 属性

    stoneHaveCount: number; // 弓箭石拥有数量
    stoneTotalCostCount: number; // 弓箭石总消耗数量
    stoneCostCount: number; // 弓箭石消耗数量
    stoneCostRate: number; // 制作石消耗倍率

    makeArrowId: number; // 制作弓箭id
    maxArrowId: number; // 最大弓箭id

    makeNum: number; // 制作数量
    makeNumStats: number[]; // 制作数量统计
    makeNumStatsTime: number; // 制作数量统计时间
    makeNumStatsDuration: number; // 制作数量统计时间段

    makeTime: number; // 制作时间
    makeDuration: number; // 制作时间段
    mixTime: number; // 合成时间
    mixDuration: number; // 合成时间段
    isOpened: boolean; // 是否已开启-自动制作

    syncTime: number; // 同步时间
    syncDuration: number; // 同步时间段

    makeReward: IItemInfo; // 制作奖励
    forgeCulUpgradeCostHaveNum: number; // 锻造台培养升级消耗拥有数
    forgeCulUpgradeCostNum: number[]; // 锻造台培养升级消耗数
    isCheckForgeCulUpgrade: boolean; // 是否检测锻造台培养升级

    createArrowItemInfo: DataItem[]; // 生成箭矢道具信息
}

/**
 * 弓箭手格数据
 */
interface IArcherGridData {
    gridId: number; // 弓箭手格id
    archerId: number; // 弓箭手id
    arrowId: number; // 弓箭id

    expandId: number; // 扩充id
    preExpandId: number; // 前置扩充id
    unlockType: EnumExpandUnlockType; // 解锁类型
    unlockPara: number; // 解锁参数
    unlockCost: IItemInfo; // 解锁消耗
    isUnlock: boolean; // 是否已解锁

    nodeItem: cc.Node; // 弓箭手格item
}

/**
 * 弓箭格数据
 */
interface IArrowGridData {
    gridId: number; // 弓箭格id
    arrowId: number; // 弓箭id
    isNoOperable: boolean; // 是否不可操作

    expandId: number; // 扩充id
    preExpandId: number; // 前置扩充id
    unlockType: EnumExpandUnlockType; // 解锁类型
    unlockPara: number; // 解锁参数
    unlockCost: IItemInfo; // 解锁消耗
    isUnlock: boolean; // 是否已解锁

    nodeItem: cc.Node; // 弓箭格item
}

/**
 * 动画格数据
 */
interface IAniGridData {
    arrowGridId: number; // 弓箭格id
    arrowId: number; // 弓箭id
    targetArcherGridId: number; // 目标弓箭手格id
    targetArrowGridId: number; // 目标箭矢格id

    nodeItem: cc.Node; // 动画格item
}

/**
 * 触摸数据
 */
interface ITouchData {
    touchId: number; // 触摸id
    touchStartPos: cc.Vec2; // 触摸开始位置
    isCancelTouch: boolean; // 是否取消触摸
    firstClickArrowGridId: number; // 第一次点击的弓箭格id
    firstClickTime: number; // 第一次点击的时间（时间戳）
    secondClickArrowGridId: number; // 第二次点击的弓箭格id
    secondClickTime: number; // 第二次点击的时间（时间戳）
}

/**
 * 双击生效时间段
 */
const DOUBLE_CLICK_EFFECT_DURATION = 0.5;

/**
 * 触摸移动生效距离
 */
const TOUCH_MOVE_EFFECT_DIS = 20;

const { ccclass, property } = cc._decorator;

/**
 * 制作
 */
@ccclass
export default class PrefabHomeMake extends I18nComponent {
    @property(cc.Node)
    nodeMake: cc.Node = null; // 制作区域
    @property(cc.Node)
    nodeTouch: cc.Node = null; // 触摸

    @property(cc.Node)
    nodeArcherGrid: cc.Node = null; // 弓箭手格
    @property(cc.Node)
    nodeItem: cc.Node = null; // 弓箭手格item
    @property(cc.Node)
    nodeArcherGrid2: cc.Node = null; // 弓箭手格
    @property(cc.Node)
    nodeArrowGrid: cc.Node = null; // 箭矢格
    @property(cc.Node)
    nodeItem2: cc.Node = null; // 箭矢格item
    @property(cc.Node)
    nodeAniGrid: cc.Node = null; // 动画格
    @property(cc.Node)
    nodeItem3: cc.Node = null; // 动画格item

    @property(cc.Node)
    nodeAnvil: cc.Node = null; // 铁砧
    @property(cc.Node)
    nodeAnvil2: cc.Node = null; // 铁砧
    @property(cc.Sprite)
    spStoneIcon: cc.Sprite = null; // 弓箭石icon
    @property(cc.Label)
    lbtStoneCount: cc.Label = null; // 弓箭石数量
    @property(cc.Node)
    nodeSwitchIcon: cc.Node = null; // 开关icon

    @property(cc.Node)
    nodeEffectContent: cc.Node = null; // 特效content
    @property(sp.Skeleton)
    spineMix: sp.Skeleton = null; // 合成弓箭
    @property(sp.Skeleton)
    spineHammer: sp.Skeleton = null; // 铁锤
    @property(sp.Skeleton)
    spineHammerSpark: sp.Skeleton = null; // 铁锤火花
    @property(sp.Skeleton)
    spineWear: sp.Skeleton = null; // 佩戴

    @property(cc.Label)
    forgeLevel: cc.Label = null;

    // 任务
    @property(cc.RichText)
    rtDesc: cc.RichText = null; // 描述
    @property(cc.Node)
    nodeReward: cc.Node = null; // 奖励
    @property(cc.Node)
    nodeGet: cc.Node = null; // 可领取
    @property(cc.Node)
    nodeJump: cc.Node = null; // 跳转按钮

    private data: IMakeData = null; // 制作数据
    private archerGridData: IArcherGridData[] = []; // 弓箭手格数据
    private arrowGridData: IArrowGridData[] = []; // 弓箭格数据
    private aniGridData: IAniGridData = null; // 动画格数据
    private touchData: ITouchData = null; // 触摸数据

    private aniGridItemPool: cc.Node[] = []; // 动画格item节点池
    private mixEffectPool: cc.Node[] = []; // 合成弓箭特效节点池
    private hammerEffectPool: cc.Node[] = []; // 铁锤特效节点池
    private hammerSparkEffectPool: cc.Node[] = []; // 铁锤火花特效节点池
    private wearEffectPool: cc.Node[] = []; // 佩戴特效节点池

    // 任务
    private taskSeqId: number = -1; // 任务序列id
    private taskId: number = -1; // 任务id

    protected onLoad(): void {
        this.nodeItem.parent = null;
        this.nodeItem2.parent = null;
        this.nodeItem3.parent = null;
        this.spineMix.node.parent = null;
        this.spineHammer.node.parent = null;
        this.spineHammerSpark.node.parent = null;
        this.spineWear.node.parent = null;

        this.registerHandler();
        this.changeState(MakeState.Init);

        // 任务
        this.updateTaskInfo();
    }

    protected onDestroy(): void {
        this.nodeItem.destroy();
        this.nodeItem2.destroy();
        this.nodeItem3.destroy();
        this.spineMix.node.destroy();
        this.spineHammer.node.destroy();
        this.spineHammerSpark.node.destroy();
        this.spineWear.node.destroy();
    }

    protected onEnable(): void {
        this.nodeTouch.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.nodeTouch.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.nodeTouch.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.nodeTouch.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    }

    protected onDisable(): void {
        this.nodeTouch.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.nodeTouch.off(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.nodeTouch.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.nodeTouch.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    }

    protected update(dt: number): void {
        if (!this.data) {
            return;
        }
        if (this.data.state !== MakeState.Wait) {
            return;
        }

        let isExecute = true;
        if (this.data.isOpened && this.data.makeTime > 0) {
            if (this.data.makeTime - dt > 0 || isExecute) {
                this.data.makeTime -= dt;
            }
            if (this.data.makeTime <= 0) {
                this.makeArrow(true);
                this.data.makeTime = this.data.makeDuration;

                isExecute = false;
            }
        }

        if (this.data.isOpened && this.data.mixTime > 0) {
            if (this.data.mixTime - dt > 0 || isExecute) {
                this.data.mixTime -= dt;
            }
            if (this.data.mixTime <= 0 && isExecute) {
                this.autoMixArrow();
                this.data.mixTime = this.data.mixDuration;

                isExecute = false;
            }
        }

        if (this.data.syncTime > 0) {
            this.data.syncTime -= dt;
            if (this.data.syncTime <= 0) {
                this.syncGridData();
            }
        }

        if (this.data.makeNumStatsTime > 0) {
            this.data.makeNumStatsTime -= dt;
            if (this.data.makeNumStatsTime <= 0) {
                this.updateAnvilState();
                this.data.makeNumStatsTime = this.data.makeNumStatsDuration;
            }
        }
    }

    protected registerHandler(): void {
        // 背包-更新
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                const haveCount = Bag.getInstance().getItemCountById(ITEM_ID.MAKE_ARROW_STONE);
                if (this.data.stoneHaveCount !== haveCount) {
                    if (haveCount < this.data.stoneHaveCount) {
                        const subCount = this.data.stoneHaveCount - haveCount;
                        this.data.stoneTotalCostCount -= subCount;
                    }
                    this.data.stoneHaveCount = haveCount;
                    this.updateStoneCount();
                }

                this.checkForgeCulUpgrade(true);

                if (
                    data.gotItem.findIndex(
                        (e) => this.data.createArrowItemInfo.findIndex((e2) => e2.id === e.itemInfoId) !== -1
                    ) !== -1
                ) {
                    this.syncGridData();
                }
            },
            this
        );
        // 王权-升级/突破/晋升
        Power.getInstance().on(
            [
                PowerUpgradeRet.prototype.clazzName,
                PowerBreakOutRet.prototype.clazzName,
                PowerPromotionRet.prototype.clazzName,
            ],
            () => {
                this.updateArcherGridUnlockState();
                this.updateArcherGridInfo();
                this.updateArrowGridUnlockState();
                this.updateArrowGridInfo();
            },
            this
        );
        // 任务-领奖
        Task.getInstance().on(
            TaskTakeAwardRet.prototype.clazzName,
            () => {
                this.updateArcherGridUnlockState();
                this.updateArcherGridInfo();
                this.updateArrowGridUnlockState();
                this.updateArrowGridInfo();
            },
            this
        );
        // 弓箭格-解锁
        MakeArrow.getInstance().on(
            ArrowGroupUnlockArrowCellRet.prototype.clazzName,
            () => {
                this.updateArcherGridUnlockState();
                this.updateArcherGridInfo();
                this.updateArrowGridUnlockState();
                this.updateArrowGridInfo();
            },
            this
        );
        // 制作-同步数据
        MakeArrow.getInstance().on(
            ArrowGroupSyncRet.prototype.clazzName,
            () => {
                const tempMaxArrowId = this.data.maxArrowId;
                this.data.maxArrowId = MakeArrow.getInstance().getMaxGridLevel();
                if (this.data.maxArrowId > tempMaxArrowId) {
                    this.updateArcherGridUnlockState();
                    this.updateArcherGridInfo();
                    this.updateArrowGridUnlockState();
                    this.updateArrowGridInfo();
                }
            },
            this
        );
        // 弓箭手-上阵
        Archer.getInstance().on(
            ArcherBattleRet.prototype.clazzName,
            () => {
                this.updateArcherGridData();
                this.updateArcherGridInfo();
            },
            this
        );
        // 经济属性-更新
        EconomyAttribute.getInstance().on(
            EconomyAttributeEvent.Update,
            () => {
                this.updateAttr();
            },
            this
        );
        // 锻造台-穿戴皮肤
        Forge.getInstance().on(
            ForgeDressSkinRet.prototype.clazzName,
            () => {
                this.updateAnvilSkin();
            },
            this
        );
        // 锻造-升阶成功
        Forge.getInstance().on(
            ForgeCompleteShowRet.prototype.clazzName,
            () => {
                this.updateForgeLevel();
            },
            this
        );
        // 制作弓箭-自动制作开关
        MakeArrow.getInstance().on(
            MakeArrowEvent.AutoMakeSwitch,
            () => {
                this.updateSwitchState();
            },
            this
        );
        // 特权-更新生效状态
        Privilege.getInstance().on(
            PrivilegeEvent.UpdateEffectState,
            () => {
                this.updateStoneCostRate();
            },
            this
        );
        // 制作-更新制作消耗
        MakeArrow.getInstance().on(
            MakeArrowEvent.UpdateMakeCost,
            () => {
                this.updateStoneCostRate();
            },
            this
        );
        // 锻造台-培养升级/升级
        Forge.getInstance().on(
            [ForgeCultivateUpgradeRet.prototype.clazzName, ForgeCompleteRet.prototype.clazzName],
            () => {
                this.checkForgeCulUpgrade(true);
            },
            this
        );
        // 任务-更新
        Task.getInstance().on(
            TaskUpdateRet.prototype.clazzName,
            (taskSeqIdList: number[]) => {
                if (taskSeqIdList.includes(this.taskSeqId)) {
                    this.updateTaskInfo2();
                }
            },
            this
        );
        // 任务-领取奖励
        Task.getInstance().on(
            TaskTakeAwardRet.prototype.clazzName,
            (taskSeqId: number) => {
                if (taskSeqId === this.taskSeqId) {
                    this.updateTaskInfo();
                }
            },
            this
        );
    }

    /**
     * 切换状态
     * @param state 状态
     */
    private changeState(state: MakeState): void {
        if (state !== MakeState.Init) {
            this.data.state = state;
        }
        switch (state) {
            case MakeState.Init:
                this.initModule();
                this.initArcherGrid();
                this.updateArcherGridUnlockState();
                this.updateArcherGridInfo();
                this.initArrowGrid();
                this.updateArrowGridUnlockState();
                this.updateArrowGridInfo();
                this.initTouchData();
                this.updateForgeLevel();
                this.syncGridData(false);

                this.changeState(MakeState.Wait);
                break;
            default:
                break;
        }
    }

    /**
     * 初始化弓箭模块
     */
    private initModule(): void {
        const para: number[][] = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.ForgeExpChange);
        this.data = {
            state: MakeState.Init,

            attr: null,

            stoneHaveCount: Bag.getInstance().getItemCountById(ITEM_ID.MAKE_ARROW_STONE),
            stoneTotalCostCount: 0,
            stoneCostCount: 0,
            stoneCostRate: 1,

            makeArrowId: 0,
            maxArrowId: MakeArrow.getInstance().getMaxGridLevel(),

            makeNum: 0,
            makeNumStats: [],
            makeNumStatsTime: 0,
            makeNumStatsDuration: 1,

            makeTime: 0,
            makeDuration: 0,
            mixTime: 0,
            mixDuration: 0,
            isOpened: false,

            syncTime: TBUniversal.getInstance().getValueByPara(EnumUniversalPara.MergeSyncTime),
            syncDuration: TBUniversal.getInstance().getValueByPara(EnumUniversalPara.MergeSyncTime),

            makeReward: { itemInfoId: para[0][0], num: para[0][1] },
            forgeCulUpgradeCostHaveNum: 0,
            forgeCulUpgradeCostNum: [],
            isCheckForgeCulUpgrade: false,

            createArrowItemInfo: TBItem.getInstance().getDataByType(EnumItemType.SpecialArrows),
        };

        this.updateAttr(true);
        this.updateAnvilSkin();
        this.updateAnvilState(true);
        this.updateStoneCostRate();
        ImageUtils.setItemIcon(this.spStoneIcon, ITEM_ID.MAKE_ARROW_STONE);
        this.updateStoneCount();
        this.updateSwitchState();
        this.checkForgeCulUpgrade(true);
    }

    /**
     * 更新弓箭石数量
     */
    private updateStoneCount(): void {
        this.lbtStoneCount.string = this.data.stoneHaveCount - this.data.stoneTotalCostCount + "";
    }

    /**
     * 更新属性
     * @param isInit 是否为初始化调用
     */
    private updateAttr(isInit: boolean = false): void {
        this.data.attr = EconomyAttribute.getInstance().getMakeArrowAttr();

        const tempMakeArrowId = this.data.makeArrowId;
        this.data.makeArrowId = this.data.attr[EnumEconomyAttributeType.ArrowProduceLevel].value;
        if (!isInit && this.data.makeArrowId !== tempMakeArrowId) {
            let isSync = false;
            this.archerGridData.forEach((e) => {
                if (e.arrowId !== -1) {
                    const tempArrowId = e.arrowId;
                    e.arrowId = Math.max(e.arrowId, this.data.makeArrowId);
                    if (e.arrowId !== tempArrowId) {
                        isSync = true;
                        this.updateArcherGridInfo(e.gridId);
                    }
                }
            });
            this.arrowGridData.forEach((e) => {
                if (e.arrowId !== -1) {
                    const tempArrowId = e.arrowId;
                    e.arrowId = Math.max(e.arrowId, this.data.makeArrowId);
                    if (e.arrowId !== tempArrowId) {
                        isSync = true;
                        this.updateArrowGridInfo(e.gridId);
                    }
                }
            });
            isSync && this.syncGridData();
        }

        this.data.makeDuration = MathUtils.floor(
            this.data.attr[EnumEconomyAttributeType.AutoArrowProduce].info.para[0] /
                this.data.attr[EnumEconomyAttributeType.AutoArrowProduce].value,
            2
        );
        this.data.isOpened && (this.data.makeTime = Math.min(this.data.makeTime, this.data.makeDuration));

        this.data.mixDuration = MathUtils.floor(
            this.data.attr[EnumEconomyAttributeType.AutoArrowMerge].info.para[0] /
                this.data.attr[EnumEconomyAttributeType.AutoArrowMerge].value,
            2
        );
        this.data.isOpened && (this.data.mixTime = Math.min(this.data.mixTime, this.data.mixDuration));
    }

    /**
     * 更新制作石消耗倍率
     */
    private updateStoneCostRate(): void {
        if (Setting.getInstance().getSwitchState(SettingId.MakeArrowCost)) {
            const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.MAKE_COST);
            const isEffect = Privilege.getInstance().hasPrivilege(privilegeInfo.id);
            this.data.stoneCostRate = isEffect ? privilegeInfo.para.value : 1;
        } else if (Setting.getInstance().getSwitchState(SettingId.MakeArrowCost2)) {
            const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.MAKE_COST_2);
            const isEffect = Privilege.getInstance().hasPrivilege(privilegeInfo.id);
            this.data.stoneCostRate = isEffect ? privilegeInfo.para.value : 1;
        } else {
            this.data.stoneCostRate = 1;
        }
    }

    /**
     * 更新铁砧皮肤
     */
    private updateAnvilSkin(): void {
        const forgeId = Forge.getInstance().getDressSkinId();
        const forgeInfo = TBForgeSkin.getInstance().getDataById(forgeId);
        ImageUtils.setForgeIconA(this.nodeAnvil, forgeInfo.res);
        ImageUtils.setForgeIconB(this.nodeAnvil2, forgeInfo.res);
    }

    /**
     * 更新铁砧状态
     * @param isInit 是否为初始化调用
     */
    private updateAnvilState(isInit: boolean = false): void {
        if (isInit) {
            this.data.makeNumStatsTime = this.data.makeNumStatsDuration;
            return;
        }

        this.data.makeNumStats.push(this.data.makeNum);
        this.data.makeNum = 0;
        this.data.makeNumStats = this.data.makeNumStats.splice(
            Math.max(this.data.makeNumStats.length - 5, 0),
            this.data.makeNumStats.length
        );

        let makeNum = 0;
        if (this.data.makeNumStats.length > 0) {
            this.data.makeNumStats.forEach((e) => {
                makeNum += e;
            });
            makeNum = makeNum / this.data.makeNumStats.length;
        }
        cc.Tween.stopAllByTarget(this.nodeAnvil2);
        cc.tween(this.nodeAnvil2)
            .to(1, { opacity: MathUtils.floor(255 * Math.min(Math.max(makeNum - 1.2, 0), 1), 3) })
            .start();
    }

    /**
     * 更新锻造等级
     */
    private updateForgeLevel(): void {
        this.forgeLevel.string = TextUtils.format(i18n.forge0004, Forge.getInstance().getForgeLevel());
    }

    /**
     * 更新开关状态-自动制作
     */
    private updateSwitchState(): void {
        this.data.isOpened = Setting.getInstance().getSwitchState(SettingId.MakeArrowAutoDispose);
        this.data.makeTime = this.data.isOpened ? this.data.makeDuration : 0;
        this.data.mixTime = this.data.isOpened ? this.data.mixDuration : 0;

        cc.Tween.stopAllByTarget(this.nodeSwitchIcon);
        this.nodeSwitchIcon.angle = 0;
        if (this.data.isOpened) {
            cc.tween(this.nodeSwitchIcon)
                .repeatForever(
                    cc
                        .tween()
                        .to(4, { angle: 360 })
                        .call(() => {
                            this.nodeSwitchIcon.angle = 0;
                        })
                        .delay(1)
                )
                .start();
        }
    }

    /**
     * 检测锻造台培养升级
     * @param isInit 是否为初始化调用
     */
    private checkForgeCulUpgrade(isInit: boolean = false): void {
        if (isInit) {
            this.data.forgeCulUpgradeCostHaveNum = Bag.getInstance().getItemCountById(this.data.makeReward.itemInfoId);
            this.data.forgeCulUpgradeCostNum = [];
            const culData = Forge.getInstance().getCultivateInfos();
            culData.forEach((e) => {
                const culInfo = TBForgeCultivateLevel.getInstance().getDataById(e.cultivateLevelId);
                const [, costNum] = culInfo.upgradeCost[0];
                if (e.num < culInfo.num && !this.data.forgeCulUpgradeCostNum.includes(costNum)) {
                    this.data.forgeCulUpgradeCostNum.push(costNum);
                }
            });
            this.data.isCheckForgeCulUpgrade =
                this.data.forgeCulUpgradeCostNum.findIndex((e) => e > this.data.forgeCulUpgradeCostHaveNum) !== -1;
            return;
        }
        if (!this.data.isCheckForgeCulUpgrade) {
            return;
        }

        const tempNum =
            this.data.forgeCulUpgradeCostHaveNum +
            this.data.makeReward.num * (this.data.stoneCostCount - 1 * this.data.stoneCostRate);
        const tempNum2 = this.data.forgeCulUpgradeCostHaveNum + this.data.makeReward.num * this.data.stoneCostCount;
        if (this.data.forgeCulUpgradeCostNum.findIndex((e) => e > tempNum && e <= tempNum2) !== -1) {
            this.syncGridData();
        }
    }

    /**
     * 初始化弓箭手格
     */
    private initArcherGrid(): void {
        const expandInfo = TBExpand.getInstance().getDataByType(EnumExpandType.ArcherTeamPositionUnlock);
        let gridId = 0;
        const battleData = Archer.getInstance().getTeam();
        expandInfo.forEach((e) => {
            for (let i = 0; i < e.expandCount; i++) {
                const gridData: IArcherGridData = {
                    gridId: ++gridId,
                    archerId: -1,
                    arrowId: -1,

                    expandId: e.id,
                    preExpandId: TBExpand.getInstance().getPreExpandId(EnumExpandType.ArcherTeamPositionUnlock, e.id),
                    unlockType: e.unlockType,
                    unlockPara: e.unlockValue,
                    unlockCost:
                        e.expandCost.length !== 0 ? { itemInfoId: e.expandCost[0][0], num: e.expandCost[0][1] } : null,
                    isUnlock: false,

                    nodeItem: this.getArcherGridItem(gridId),
                };
                const tempBattleData = battleData.find((e2) => e2.cellId === gridData.gridId);
                gridData.archerId = tempBattleData ? tempBattleData.archerId : -1;
                const tempGridData = MakeArrow.getInstance().getArcherGridData(gridData.gridId);
                if (tempGridData) {
                    gridData.arrowId =
                        tempGridData.level !== 0 ? Math.max(tempGridData.level, this.data.makeArrowId) : -1;
                }

                this.archerGridData.push(gridData);
            }
        });

        this.nodeArcherGrid2.children.forEach((e) => {
            const nodeLock = e.child("nodeLock");
            nodeLock.spriteAsync("texture/arrow/ui/spMIFusionBase3");
            nodeLock.child("spIcon").spriteAsync("texture/arrow/ui/spMILock");
        });
    }

    /**
     * 更新弓箭手格数据
     */
    private updateArcherGridData(): void {
        const battleData = Archer.getInstance().getTeam();
        this.archerGridData.forEach((e) => {
            const tempBattleData = battleData.find((e2) => e2.cellId === e.gridId);
            e.archerId = tempBattleData ? tempBattleData.archerId : -1;
        });
    }

    /**
     * 更新弓箭手格解锁状态
     */
    private updateArcherGridUnlockState(): void {
        this.archerGridData.forEach((e) => {
            !e.isUnlock && (e.isUnlock = MakeArrow.getInstance().isUnlockByArcherGrid(e.expandId));
        });
    }

    /**
     * 更新弓箭手格信息
     * @param gridId 弓箭手格id
     */
    private updateArcherGridInfo(gridId?: number): void {
        for (const data of this.archerGridData) {
            if (gridId && data.gridId !== gridId) {
                continue;
            }

            data.nodeItem.child("nodeLock").opacity = !data.isUnlock ? 255 : 0;
            const nodeArrowIcon = data.nodeItem.child("spArrowIcon");
            const nodeArcherIcon = data.nodeItem.child("spArcherIcon");
            const nodeMix = data.nodeItem.child("nodeMix");
            if (!data.isUnlock || data.archerId === -1 || data.arrowId === -1) {
                nodeArrowIcon.opacity = 0;
                nodeArcherIcon.opacity = 0;
                data.nodeItem.child("lbtLevel").label("");
                nodeMix.opacity = 0;
            } else {
                nodeArrowIcon.opacity = 255;
                if (CocosExt.getNodeOrCompData(nodeArrowIcon, "arrowId") !== data.arrowId) {
                    CocosExt.setNodeOrCompData(nodeArrowIcon, "arrowId", data.arrowId);
                    const arrowInfo = TBArrow.getInstance().getDataById(data.arrowId);
                    ImageUtils.setArrowIcon(nodeArrowIcon, arrowInfo.res);
                }
                nodeArcherIcon.opacity = 255;
                if (CocosExt.getNodeOrCompData(nodeArcherIcon, "archerId") !== data.archerId) {
                    CocosExt.setNodeOrCompData(nodeArcherIcon, "archerId", data.archerId);
                    const archerInfo = TBArcher.getInstance().getDataById(data.archerId);
                    ImageUtils.setArcherIcon3(nodeArcherIcon, archerInfo.res);
                }
                data.nodeItem.child("lbtLevel").label(data.arrowId + "");
                const isMix =
                    this.aniGridData &&
                    data.gridId === this.aniGridData.targetArcherGridId &&
                    data.arrowId === this.aniGridData.arrowId;
                nodeMix.opacity = isMix ? 255 : 0;
            }
        }
    }

    /**
     * 初始化弓箭格
     */
    private initArrowGrid(): void {
        const expandInfo = TBExpand.getInstance().getDataByType(EnumExpandType.ArrowUnlock);
        let gridId = 0;
        expandInfo.forEach((e) => {
            for (let i = 0; i < e.expandCount; i++) {
                const gridData: IArrowGridData = {
                    gridId: ++gridId,
                    arrowId: -1,
                    isNoOperable: false,

                    expandId: e.id,
                    preExpandId: TBExpand.getInstance().getPreExpandId(EnumExpandType.ArrowUnlock, e.id),
                    unlockType: e.unlockType,
                    unlockPara: e.unlockValue,
                    unlockCost:
                        e.expandCost.length !== 0 ? { itemInfoId: e.expandCost[0][0], num: e.expandCost[0][1] } : null,
                    isUnlock: false,

                    nodeItem: this.getArrowGridItem(gridId),
                };
                const tempGridData = MakeArrow.getInstance().getArrowGridData(gridData.gridId);
                if (tempGridData) {
                    gridData.arrowId =
                        tempGridData.level !== 0 ? Math.max(tempGridData.level, this.data.makeArrowId) : -1;
                }

                this.arrowGridData.push(gridData);
            }
        });
    }

    /**
     * 更新弓箭格解锁状态
     */
    private updateArrowGridUnlockState(): void {
        this.arrowGridData.forEach((e) => {
            !e.isUnlock && (e.isUnlock = MakeArrow.getInstance().isUnlockByArrowGrid(e.expandId));
        });
    }

    /**
     * 更新弓箭格信息
     * @param gridId 弓箭格id
     */
    private updateArrowGridInfo(gridId?: number): void {
        for (const data of this.arrowGridData) {
            if (gridId && data.gridId !== gridId) {
                continue;
            }

            data.nodeItem.child("nodeLock").opacity = !data.isUnlock ? 255 : 0;
            const nodeArrowIcon = data.nodeItem.child("spArrowIcon");
            const nodeEmpty = data.nodeItem.child("nodeEmpty");
            const nodeMix = data.nodeItem.child("nodeMix");
            const nodeNoMix = data.nodeItem.child("nodeNoMix");
            if (!data.isUnlock || data.arrowId === -1) {
                nodeArrowIcon.opacity = 0;
                data.nodeItem.child("lbtLevel").label("");
            } else {
                nodeArrowIcon.opacity = 255;
                if (CocosExt.getNodeOrCompData(nodeArrowIcon, "arrowId") !== data.arrowId) {
                    CocosExt.setNodeOrCompData(nodeArrowIcon, "arrowId", data.arrowId);
                    const arrowInfo = TBArrow.getInstance().getDataById(data.arrowId);
                    ImageUtils.setArrowIcon(nodeArrowIcon, arrowInfo.res);
                }
                data.nodeItem.child("lbtLevel").label(data.arrowId + "");
            }
            if (data.isUnlock) {
                const isEmpty = data.arrowId === -1 || data.isNoOperable;
                nodeEmpty.opacity = isEmpty ? 255 : 0;
                const isMix =
                    !isEmpty &&
                    this.aniGridData &&
                    data.gridId === this.aniGridData.targetArrowGridId &&
                    data.arrowId === this.aniGridData.arrowId;
                nodeMix.opacity = isMix ? 255 : 0;
                const isNoMix = !isEmpty && this.aniGridData && data.arrowId !== this.aniGridData.arrowId;
                nodeNoMix.opacity = isNoMix ? 255 : 0;
            } else {
                nodeEmpty.opacity = 0;
                nodeMix.opacity = 0;
                nodeNoMix.opacity = 0;
            }
        }
    }

    /**
     * 初始化触摸数据
     */
    private initTouchData(): void {
        this.touchData = {
            touchId: -1,
            touchStartPos: null,
            isCancelTouch: false,
            firstClickArrowGridId: -1,
            firstClickTime: 0,
            secondClickArrowGridId: -1,
            secondClickTime: 0,
        };
    }

    /**
     * 清除触摸数据
     */
    private clearTouchData(): void {
        this.touchData.firstClickArrowGridId = -1;
        this.touchData.firstClickTime = 0;
        this.touchData.secondClickArrowGridId = -1;
        this.touchData.secondClickTime = 0;
    }

    /**
     * 制作弓箭
     * @param isAuto 是否为自动调用
     */
    private makeArrow(isAuto: boolean = false): void {
        const gridData = this.arrowGridData.find((v) => v.isUnlock && v.arrowId === -1);
        if (!gridData) {
            if (!isAuto) {
                Tips.getInstance().info(i18n.arrow0004);
            }
            return;
        }
        if (this.data.stoneTotalCostCount + 1 * this.data.stoneCostRate > this.data.stoneHaveCount) {
            if (!isAuto) {
                UI.getInstance().open("FloatItemSource", ITEM_ID.MAKE_ARROW_STONE);
            }
            return;
        }

        this.data.makeNum++;
        let isCost = true;
        if (this.data.attr[EnumEconomyAttributeType.FreeProduce].value >= MathUtils.getRandomValue(0, 1)) {
            isCost = false;
        }
        if (isCost) {
            this.data.stoneTotalCostCount += 1 * this.data.stoneCostRate;
            this.data.stoneCostCount += 1 * this.data.stoneCostRate;
            this.updateStoneCount();
        }

        gridData.isNoOperable = true;
        gridData.arrowId = this.data.makeArrowId;
        let tempRate = this.data.stoneCostRate;
        while (tempRate !== 1) {
            gridData.arrowId++;

            tempRate /= 2;
        }

        if (this.data.attr[EnumEconomyAttributeType.LuckyProduce].value >= MathUtils.getRandomValue(0, 1)) {
            gridData.arrowId += this.data.attr[EnumEconomyAttributeType.LuckyProduce].info.para[0];
        }

        Task.getInstance().setTaskProgress(EnumTaskDetailType.ProduceArrowTask, 1);

        const nodeEffect = this.getHammerEffect();
        let pos = this.nodeAnvil.convertToWorldSpaceAR(cc.v2(0, 60));
        pos = this.nodeEffectContent.convertToNodeSpaceAR(pos);
        nodeEffect.setPosition(pos);
        nodeEffect.zIndex = 3;
        nodeEffect.opacity = 255;
        const spineEffect = nodeEffect.getComponent(sp.Skeleton);
        spineEffect.setCompleteListener(() => {
            spineEffect.setCompleteListener(null);

            spineEffect.clearTrack(0);
            this.putHammerEffect(nodeEffect);
        });
        spineEffect.setAnimation(0, "wait", false);

        const nodeEffect2 = this.getHammerSparkEffect();
        let pos2 = this.nodeAnvil.convertToWorldSpaceAR(cc.v2(0, 60));
        pos2 = this.nodeEffectContent.convertToNodeSpaceAR(pos2);
        nodeEffect2.setPosition(pos2);
        nodeEffect2.zIndex = 4;
        nodeEffect2.opacity = 255;
        const spineEffect2 = nodeEffect2.getComponent(sp.Skeleton);
        spineEffect2.setCompleteListener(() => {
            spineEffect2.setCompleteListener(null);

            spineEffect2.clearTrack(0);
            this.putHammerSparkEffect(nodeEffect2);
        });
        spineEffect2.setAnimation(0, "wait", false);

        let pos3 = this.nodeAnvil.convertToWorldSpaceAR(cc.v2(0, (this.nodeAnvil.height + this.nodeItem3.height) / 2));
        pos3 = this.nodeAniGrid.convertToNodeSpaceAR(pos3);
        const nodeAniGridItem = this.getAniGridItem(pos3, 0.6, gridData.arrowId);
        let pos4 = gridData.nodeItem.convertToWorldSpaceAR(cc.v2());
        pos4 = this.nodeAniGrid.convertToNodeSpaceAR(pos4);
        cc.tween(nodeAniGridItem)
            .delay(0.2)
            .call(() => {
                nodeAniGridItem.opacity = 255;
            })
            .parallel(cc.tween().to(0.2, { scale: 1 }), cc.tween().to(0.4, { position: pos4 }, { easing: "quadOut" }))
            .call(() => {
                gridData.isNoOperable = false;
                this.updateArrowGridInfo(gridData.gridId);
                this.putAniGridItem(nodeAniGridItem);
            })
            .start();

        AudioUtils.playCombatEffect(AUDIO_EFFECT_TYPE.ARROW_MAKE, AUDIO_EFFECT_PATH.ADVENTURE);

        isCost && this.checkForgeCulUpgrade();
    }

    /**
     * 合成弓箭
     * @param arrowGridData 弓箭格数据
     * @param arrowGridData2 弓箭格数据
     * @param isAuto 是否为自动调用
     */
    private mixArrow(arrowGridData: IArrowGridData, arrowGridData2: IArrowGridData, isAuto: boolean): void {
        if (!isAuto || this.data.attr[EnumEconomyAttributeType.FreeMerge].value < MathUtils.getRandomValue(0, 1)) {
            arrowGridData.arrowId = -1;
        }
        arrowGridData2.arrowId++;
        if (this.data.attr[EnumEconomyAttributeType.LuckyMerge].value >= MathUtils.getRandomValue(0, 1)) {
            arrowGridData2.arrowId += this.data.attr[EnumEconomyAttributeType.LuckyMerge].info.para[0];
        }
        this.updateArrowGridInfo(arrowGridData.gridId);
        this.updateArrowGridInfo(arrowGridData2.gridId);

        let pos = arrowGridData2.nodeItem.convertToWorldSpaceAR(cc.v2());
        pos = this.nodeEffectContent.convertToNodeSpaceAR(pos);
        this.playMixArrowAni(pos);

        Task.getInstance().setTaskProgress(EnumTaskDetailType.MergeArrowTask, 1);

        AudioUtils.playCombatEffect(AUDIO_EFFECT_TYPE.ARROW_MIX, AUDIO_EFFECT_PATH.ADVENTURE);

        if (arrowGridData2.arrowId > this.data.maxArrowId) {
            this.syncGridData();
        }
    }

    /**
     * 自动合成弓箭
     */
    private autoMixArrow(): void {
        this.arrowGridData.sort((a, b) => {
            const isMixA = a.isUnlock && a.arrowId !== -1 && !a.isNoOperable;
            const isMixB = b.isUnlock && b.arrowId !== -1 && !b.isNoOperable;
            if (isMixA || isMixB) {
                if (isMixA && isMixB) {
                    return a.arrowId !== b.arrowId ? b.arrowId - a.arrowId : a.gridId - b.gridId;
                } else {
                    return isMixA ? -1 : 1;
                }
            }
            return a.gridId - b.gridId;
        });
        for (const data of this.arrowGridData) {
            if (!data.isUnlock || data.arrowId === -1 || data.isNoOperable) {
                break;
            }

            for (const data2 of this.arrowGridData) {
                if (data2.gridId === data.gridId) {
                    continue;
                }
                if (!data2.isUnlock || data2.arrowId === -1 || data2.isNoOperable) {
                    break;
                }
                if (data2.arrowId !== data.arrowId) {
                    continue;
                }

                this.mixArrow(data2, data, true);
                if (this.touchData.firstClickArrowGridId !== -1) {
                    if (
                        data.gridId === this.touchData.firstClickArrowGridId ||
                        data2.gridId === this.touchData.firstClickArrowGridId
                    ) {
                        this.clearTouchData();
                    }
                }
                return;
            }
        }
    }

    /**
     * 佩戴弓箭
     * @param arrowGridData 弓箭格数据
     * @param archerGridData 弓箭手格数据
     */
    private wearArrow(arrowGridData: IArrowGridData, archerGridData: IArcherGridData): void {
        const isMixArrow = arrowGridData.arrowId === archerGridData.arrowId;
        if (isMixArrow) {
            arrowGridData.arrowId = -1;
            archerGridData.arrowId++;
            if (this.data.attr[EnumEconomyAttributeType.LuckyMerge].value >= MathUtils.getRandomValue(0, 1)) {
                archerGridData.arrowId += this.data.attr[EnumEconomyAttributeType.LuckyMerge].info.para[0];
            }
        } else {
            const tempArrowId = arrowGridData.arrowId;
            arrowGridData.arrowId = archerGridData.arrowId;
            archerGridData.arrowId = tempArrowId;
        }
        this.updateArrowGridInfo(arrowGridData.gridId);
        this.updateArcherGridInfo(archerGridData.gridId);
        Task.getInstance().setTaskProgress(EnumTaskDetailType.WearArrowsTask, 1); // 佩戴弓箭前端计数

        let pos = archerGridData.nodeItem.convertToWorldSpaceAR(cc.v2());
        pos = this.nodeEffectContent.convertToNodeSpaceAR(pos);
        if (isMixArrow) {
            this.playMixArrowAni(pos, true);

            Task.getInstance().setTaskProgress(EnumTaskDetailType.MergeArrowTask, 1);

            // 音效
            AudioUtils.playCombatEffect(AUDIO_EFFECT_TYPE.ARROW_MIX, AUDIO_EFFECT_PATH.ADVENTURE);
        } else {
            this.playWearAni(pos);
        }
    }

    /**
     * 创建箭矢
     * @returns
     */
    private createArrow(): IItemInfo[] {
        const costData: IItemInfo[] = [];
        this.data.createArrowItemInfo.forEach((e) => {
            const itemNum = Bag.getInstance().getItemCountById(e.id);
            for (let i = 0; i < itemNum; i++) {
                const gridData = this.arrowGridData.find((e2) => e2.isUnlock && e2.arrowId === -1);
                if (!gridData) {
                    let index = -1;
                    this.arrowGridData.forEach((e2, i2) => {
                        if (e2.isUnlock && e2.arrowId !== -1 && !e2.isNoOperable) {
                            (index === -1 || e2.arrowId < this.arrowGridData[index].arrowId) && (index = i2);
                        }
                    });
                    if (index !== -1) {
                        const minGridData = this.arrowGridData[index];
                        minGridData.arrowId = Math.max(e.effectPara[0], minGridData.arrowId);
                        this.updateArrowGridInfo(minGridData.gridId);
                    }
                } else {
                    gridData.arrowId = Math.max(e.effectPara[0], this.data.makeArrowId);
                    this.updateArrowGridInfo(gridData.gridId);
                }

                const tempCostData = costData.find((e2) => e2.itemInfoId === e.id);
                if (tempCostData) {
                    tempCostData.num += 1;
                } else {
                    costData.push({ itemInfoId: e.id, num: 1 });
                }
            }
        });

        return costData;
    }

    /**
     * 箭矢格-排序
     */
    private sortArrowGrid(): void {
        this.arrowGridData.sort((a, b) => a.gridId - b.gridId);
        const gridIdArr: number[] = [];
        const arrowIdArr: number[] = [];
        for (const e of this.arrowGridData) {
            if (!e.isUnlock) {
                continue;
            }
            if (e.isNoOperable) {
                continue;
            }

            gridIdArr.push(e.gridId);
            arrowIdArr.push(e.arrowId);
        }
        arrowIdArr.sort((a, b) => b - a);
        this.arrowGridData.forEach((v) => {
            const index = gridIdArr.findIndex((v2) => v2 === v.gridId);
            if (index !== -1) {
                v.arrowId = arrowIdArr[index];
                this.updateArrowGridInfo(v.gridId);
            }
        });
        this.clearTouchData();
    }

    /**
     * 同步格子数据
     * @param isForces 是否强制
     */
    private syncGridData(isForces: boolean = true): void {
        const costData = this.createArrow();
        if (costData.length === 0 && !isForces) {
            return;
        }

        const tempArcherGridData: IBattleCellInfo[] = [];
        this.archerGridData.forEach((v) => {
            if (MakeArrow.getInstance().isUnlockByArcherGrid(v.expandId, true) || !v.unlockCost) {
                tempArcherGridData.push({ id: v.gridId, level: v.arrowId !== -1 ? v.arrowId : this.data.makeArrowId });
            }
        });
        const tempArrowGridData: IArrowCellInfo[] = [];
        this.arrowGridData.forEach((v) => {
            if (MakeArrow.getInstance().isUnlockByArrowGrid(v.expandId, true) || !v.unlockCost) {
                tempArrowGridData.push({ id: v.gridId, level: v.arrowId !== -1 ? v.arrowId : 0 });
            }
        });
        MakeArrow.getInstance().sendArrowGroupSync(
            tempArcherGridData,
            tempArrowGridData,
            this.data.stoneCostCount,
            0,
            costData
        );
        this.data.stoneCostCount = 0;

        this.data.syncTime = this.data.syncDuration;
    }

    /**
     * 检测目标格-动画格
     */
    private checkTargetGridByAniGrid(): void {
        let isArcherGrid = true;
        let gridId = -1;
        let size = 0;
        const boundingBox = this.aniGridData.nodeItem.getBoundingBox();
        this.archerGridData.forEach((v) => {
            if (v.isUnlock && v.archerId !== -1 && v.arrowId !== -1) {
                const intersection = new cc.Rect();
                v.nodeItem.getBoundingBox().intersection(intersection, boundingBox);
                const tempSize =
                    intersection.width >= 0 && intersection.height >= 0 ? intersection.width * intersection.height : 0;
                if (tempSize > size) {
                    isArcherGrid = true;
                    gridId = v.gridId;
                    size = tempSize;
                }
            }
        });
        this.arrowGridData.forEach((v) => {
            if (v.isUnlock && !v.isNoOperable && v.arrowId !== -1 && v.arrowId === this.aniGridData.arrowId) {
                const intersection = new cc.Rect();
                v.nodeItem.getBoundingBox().intersection(intersection, boundingBox);
                const tempSize =
                    intersection.width >= 0 && intersection.height >= 0 ? intersection.width * intersection.height : 0;
                if (tempSize > size) {
                    isArcherGrid = false;
                    gridId = v.gridId;
                    size = tempSize;
                }
            }
        });
        this.aniGridData.targetArcherGridId = isArcherGrid ? gridId : -1;
        this.aniGridData.targetArrowGridId = !isArcherGrid ? gridId : -1;
        this.archerGridData.forEach((v) => {
            if (v.isUnlock && v.archerId !== -1 && v.arrowId !== -1) {
                this.updateArcherGridInfo(v.gridId);
            }
        });
        this.arrowGridData.forEach((v) => {
            if (v.isUnlock && !v.isNoOperable && v.arrowId !== -1 && v.arrowId === this.aniGridData.arrowId) {
                this.updateArrowGridInfo(v.gridId);
            }
        });
    }

    /**
     * 播放合成弓箭动画
     * @param pos 位置
     * @param isWear 是否佩戴
     */
    private playMixArrowAni(pos: cc.Vec2, isWear: boolean = false): void {
        const nodeEffect = this.getMixEffect();
        nodeEffect.setPosition(pos);
        nodeEffect.zIndex = 2;
        nodeEffect.opacity = 255;
        const spineEffect = nodeEffect.getComponent(sp.Skeleton);
        spineEffect.setCompleteListener(() => {
            spineEffect.setCompleteListener(null);

            spineEffect.clearTrack(0);
            this.putMixEffect(nodeEffect);

            isWear && this.playWearAni(pos);
        });
        spineEffect.setAnimation(0, "wait", false);
    }

    /**
     * 播放佩戴动画
     * @param pos 位置
     */
    private playWearAni(pos: cc.Vec2): void {
        const nodeEffect = this.getWearEffect();
        nodeEffect.setPosition(pos);
        nodeEffect.zIndex = 1;
        nodeEffect.opacity = 255;
        const spineEffect = nodeEffect.getComponent(sp.Skeleton);
        spineEffect.setCompleteListener(() => {
            spineEffect.setCompleteListener(null);

            spineEffect.clearTrack(0);
            this.putWearEffect(nodeEffect);
        });
        spineEffect.setAnimation(0, "wait", false);
    }

    /**
     * 获取动画格item
     * @param pos 位置
     * @param scale 缩放
     * @param arrowId 箭矢id
     * @returns
     */
    private getAniGridItem(pos: cc.Vec2, scale: number, arrowId: number): cc.Node {
        let nodeItem: cc.Node = null;
        if (this.aniGridItemPool.length > 0) {
            nodeItem = this.aniGridItemPool.shift();

            nodeItem.opacity = 255;
        } else {
            nodeItem = Loader.getInstance().instantiate(this.nodeItem3);
            this.nodeAniGrid.addChild(nodeItem);

            nodeItem.child("spBg").spriteAsync("texture/arrow/ui/spMIFusionBase1");
            const lbtLevel = nodeItem.child("lbtLevel").getComponent(cc.Label);
            Loader.getInstance().loadBMFont(`texture/arrow/ui/fontArrowNum`, (bmf: cc.BitmapFont) => {
                if (cc.isValid(lbtLevel)) {
                    lbtLevel.font = bmf;
                }
            });
        }

        nodeItem.setPosition(pos);
        nodeItem.scale = scale;
        const nodeArrowIcon = nodeItem.child("spArrowIcon");
        nodeArrowIcon.opacity = 255;
        if (CocosExt.getNodeOrCompData(nodeArrowIcon, "arrowId") !== arrowId) {
            CocosExt.setNodeOrCompData(nodeArrowIcon, "arrowId", arrowId);
            const arrowInfo = TBArrow.getInstance().getDataById(arrowId);
            ImageUtils.setArrowIcon(nodeArrowIcon, arrowInfo.res);
        }
        nodeItem.child("lbtLevel").label(arrowId + "");

        return nodeItem;
    }

    /**
     * 丢弃动画格item
     * @param nodeItem 动画格item
     */
    private putAniGridItem(nodeItem: cc.Node): void {
        nodeItem.scale = 1;
        nodeItem.opacity = 0;
        nodeItem.child("spArrowIcon").opacity = 0;
        nodeItem.child("lbtLevel").label("");

        this.aniGridItemPool.push(nodeItem);
    }

    /**
     * 获取合成弓箭特效
     * @returns
     */
    private getMixEffect(): cc.Node {
        let nodeEffect: cc.Node = null;
        if (this.mixEffectPool.length > 0) {
            nodeEffect = this.mixEffectPool.shift();
        } else {
            nodeEffect = Loader.getInstance().instantiate(this.spineMix.node);
            this.nodeEffectContent.addChild(nodeEffect);
        }

        return nodeEffect;
    }

    /**
     * 丢弃合成弓箭特效
     * @param nodeEffect 合成弓箭特效
     */
    private putMixEffect(nodeEffect: cc.Node): void {
        nodeEffect.opacity = 0;

        this.mixEffectPool.push(nodeEffect);
    }

    /**
     * 获取铁锤特效
     * @returns
     */
    private getHammerEffect(): cc.Node {
        let nodeEffect: cc.Node = null;
        if (this.hammerEffectPool.length > 0) {
            nodeEffect = this.hammerEffectPool.shift();
        } else {
            nodeEffect = Loader.getInstance().instantiate(this.spineHammer.node);
            this.nodeEffectContent.addChild(nodeEffect);
        }

        return nodeEffect;
    }

    /**
     * 丢弃铁锤特效
     * @param nodeEffect 铁锤特效
     */
    private putHammerEffect(nodeEffect: cc.Node): void {
        nodeEffect.opacity = 0;

        this.hammerEffectPool.push(nodeEffect);
    }

    /**
     * 获取铁锤火花特效
     * @returns
     */
    private getHammerSparkEffect(): cc.Node {
        let nodeEffect: cc.Node = null;
        if (this.hammerSparkEffectPool.length > 0) {
            nodeEffect = this.hammerSparkEffectPool.shift();
        } else {
            nodeEffect = Loader.getInstance().instantiate(this.spineHammerSpark.node);
            this.nodeEffectContent.addChild(nodeEffect);
        }

        return nodeEffect;
    }

    /**
     * 丢弃铁锤火花特效
     * @param nodeEffect 铁锤火花特效
     */
    private putHammerSparkEffect(nodeEffect: cc.Node): void {
        nodeEffect.opacity = 0;

        this.hammerSparkEffectPool.push(nodeEffect);
    }

    /**
     * 获取佩戴特效
     * @returns
     */
    private getWearEffect(): cc.Node {
        let nodeEffect: cc.Node = null;
        if (this.wearEffectPool.length > 0) {
            nodeEffect = this.wearEffectPool.shift();
        } else {
            nodeEffect = Loader.getInstance().instantiate(this.spineWear.node);
            this.nodeEffectContent.addChild(nodeEffect);
        }

        return nodeEffect;
    }

    /**
     * 丢弃佩戴特效
     * @param nodeEffect 佩戴特效
     */
    private putWearEffect(nodeEffect: cc.Node): void {
        nodeEffect.opacity = 0;

        this.wearEffectPool.push(nodeEffect);
    }

    /**
     * 获取弓箭手格item
     * @param gridId 弓箭手格id
     * @returns
     */
    private getArcherGridItem(gridId: number): cc.Node {
        const nodeItem = Loader.getInstance().instantiate(this.nodeItem);
        this.nodeArcherGrid.addChild(nodeItem);

        CocosExt.setNodeOrCompData(nodeItem, "archerGridId", gridId);
        nodeItem.child("spBg").spriteAsync("texture/arrow/ui/spMIFusionBase1");
        const nodeLock = nodeItem.child("nodeLock");
        nodeLock.spriteAsync("texture/arrow/ui/spMIFusionBase3");
        nodeLock.child("spIcon").spriteAsync("texture/arrow/ui/spMILock");
        nodeItem.child("nodeMix").spriteAsync("texture/arrow/ui/spMIFusionMask2");
        const lbtLevel = nodeItem.child("lbtLevel").getComponent(cc.Label);
        Loader.getInstance().loadBMFont(`texture/arrow/ui/fontArrowNum`, (bmf: cc.BitmapFont) => {
            if (cc.isValid(lbtLevel)) {
                lbtLevel.font = bmf;
            }
        });

        return nodeItem;
    }

    /**
     * 获取弓箭格item
     * @param gridId 弓箭格id
     * @returns
     */
    private getArrowGridItem(gridId: number): cc.Node {
        const nodeItem = Loader.getInstance().instantiate(this.nodeItem2);
        this.nodeArrowGrid.addChild(nodeItem);

        CocosExt.setNodeOrCompData(nodeItem, "arrowGridId", gridId);
        nodeItem.child("spBg").spriteAsync("texture/arrow/ui/spMIFusionBase1");
        const nodeLock = nodeItem.child("nodeLock");
        nodeLock.spriteAsync("texture/arrow/ui/spMIFusionBase3");
        nodeLock.child("spIcon").spriteAsync("texture/arrow/ui/spMILock");
        nodeItem.child("nodeMix").spriteAsync("texture/arrow/ui/spMIFusionMask2");
        nodeItem.child("nodeNoMix").spriteAsync("texture/arrow/ui/spMIFusionMask1");
        nodeItem.child("nodeEmpty").spriteAsync("texture/arrow/ui/spMIFusionBase2");
        const lbtLevel = nodeItem.child("lbtLevel").getComponent(cc.Label);
        Loader.getInstance().loadBMFont(`texture/arrow/ui/fontArrowNum`, (bmf: cc.BitmapFont) => {
            if (cc.isValid(lbtLevel)) {
                lbtLevel.font = bmf;
            }
        });

        return nodeItem;
    }

    /**
     * 更新任务信息
     */
    private updateTaskInfo(): void {
        const taskSeqConfig = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.MainLineTask);
        const taskDetailIdList = Task.getInstance().getCurrentTaskIdList(taskSeqConfig.id);
        const taskDetailConfig = TBTaskDetail.getInstance().getDataById(taskDetailIdList[0]);
        const taskInfo = Task.getInstance().getTaskInfo(taskDetailConfig.id, taskSeqConfig.id);

        if (taskInfo.progress >= taskDetailConfig.reachValue) {
            this.rtDesc.string = `${TextUtils.format(
                taskDetailConfig.desc,
                taskDetailConfig.reachValue
            )}<color=#688a28>（${Math.min(taskInfo.progress, taskDetailConfig.reachValue)}/${
                taskDetailConfig.reachValue
            }）</c>`;
        } else {
            this.rtDesc.string = `${TextUtils.format(
                taskDetailConfig.desc,
                taskDetailConfig.reachValue
            )}<color=#a5413c>（${Math.min(taskInfo.progress, taskDetailConfig.reachValue)}/${
                taskDetailConfig.reachValue
            }）</c>`;
        }

        this.nodeGet.active = taskInfo.progress >= taskDetailConfig.reachValue;

        this.nodeJump.button(taskDetailConfig.id);

        this.taskSeqId = taskSeqConfig.id;
        this.taskId = taskDetailConfig.id;

        this.nodeReward.child("lbtNum").label(taskDetailConfig.reward[0][1]);
        if (CocosExt.getNodeOrCompData(this.nodeReward, "itemId") !== taskDetailConfig.reward[0][0]) {
            CocosExt.setNodeOrCompData(this.nodeReward, "itemId", taskDetailConfig.reward[0][0]);
            ImageUtils.setItemIcon(this.nodeReward.child("spIcon"), taskDetailConfig.reward[0][0]);
        }
    }

    /**
     * 更新任务信息
     */
    private updateTaskInfo2(): void {
        const taskDetailConfig = TBTaskDetail.getInstance().getDataById(this.taskId);
        const taskData = Task.getInstance().getTaskInfo(this.taskId, this.taskSeqId);

        if (taskData.progress >= taskDetailConfig.reachValue) {
            this.rtDesc.string = `${TextUtils.format(
                taskDetailConfig.desc,
                taskDetailConfig.reachValue
            )}<color=#688a28>（${Math.min(taskData.progress, taskDetailConfig.reachValue)}/${
                taskDetailConfig.reachValue
            }）</c>`;
        } else {
            this.rtDesc.string = `${TextUtils.format(
                taskDetailConfig.desc,
                taskDetailConfig.reachValue
            )}<color=#a5413c>（${Math.min(taskData.progress, taskDetailConfig.reachValue)}/${
                taskDetailConfig.reachValue
            }）</c>`;
        }

        this.nodeGet.active = taskData.progress >= taskDetailConfig.reachValue;
    }

    /**
     * 监听触摸事件
     * @param event 触摸事件
     */
    private onTouchStart(event: cc.Event.EventTouch): void {
        if (!event) {
            return;
        }
        if (this.touchData.touchId !== -1) {
            return;
        }

        this.touchData.isCancelTouch = false;
        this.touchData.touchId = event.getID();
        const pos = event.getLocation();
        const pos2 = this.nodeArrowGrid.parent.convertToNodeSpaceAR(pos);
        if (!this.nodeArrowGrid.getBoundingBox().contains(pos2)) {
            this.touchData.isCancelTouch = true;
            return;
        }
        const pos3 = this.nodeArrowGrid.convertToNodeSpaceAR(pos);
        const nodeGridItem = this.nodeArrowGrid.children.find((v) => v.getBoundingBox().contains(pos3));
        if (!nodeGridItem) {
            this.touchData.isCancelTouch = true;
            return;
        }
        const gridId: number = CocosExt.getNodeOrCompData(nodeGridItem, "arrowGridId");
        const gridData = this.arrowGridData.find((v) => v.gridId === gridId);
        if (!gridData) {
            this.touchData.isCancelTouch = true;
            return;
        }
        if (!gridData.isUnlock) {
            this.touchData.isCancelTouch = true;
            switch (gridData.unlockType) {
                case EnumExpandUnlockType.ArrowLevel:
                    if (this.data.maxArrowId < gridData.unlockPara) {
                        Tips.getInstance().info(TextUtils.format(i18n.arrow0001, gridData.unlockPara + ""));
                        return;
                    }
                    break;
                case EnumExpandUnlockType.PowerLevel:
                    const powerData = Power.getInstance().getPowerInfo();
                    if (powerData.kingLevelId < gridData.unlockPara) {
                        const powerInfo = TBPowerLevel.getInstance().getDataById(gridData.unlockPara);
                        Tips.getInstance().info(TextUtils.format(i18n.common0067, powerInfo.name));
                        return;
                    }
                    break;
                case EnumExpandUnlockType.MainTask:
                    const seqInfo = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.MainLineTask);
                    const taskData = Task.getInstance().getTaskInfo(gridData.unlockPara, seqInfo.id);
                    if (!taskData.isAwarded) {
                        const taskId = Task.getInstance().getCurrentTaskIdList(seqInfo.id);
                        Tips.getInstance().info(
                            TextUtils.format(i18n.gameSwitch0009, gridData.unlockPara - taskId[0] + 1)
                        );
                        return;
                    }
                    break;
                default:
                    break;
            }
            if (gridData.preExpandId !== -1 && !MakeArrow.getInstance().isUnlockByArrowGrid(gridData.preExpandId)) {
                Tips.getInstance().info(i18n.arrow0003);
                return;
            }
            if (gridData.unlockCost) {
                const itemInfo = TBItem.getInstance().getDataById(gridData.unlockCost.itemInfoId);
                UI.getInstance().open("FloatInfo", {
                    text: TextUtils.format(
                        i18n.arrow0002,
                        itemInfo.name,
                        NumberUtils.format(gridData.unlockCost.num, 1, 0)
                    ),
                    confirmCb: () => {
                        const count = Bag.getInstance().getItemCountById(gridData.unlockCost.itemInfoId);
                        if (count < gridData.unlockCost.num) {
                            Tips.getInstance().info(i18n.common0025);
                            return;
                        }

                        MakeArrow.getInstance().sendArrowGroupUnlockArrowCell(gridData.expandId);
                    },
                });
            }
            return;
        }
        if (gridData.arrowId === -1 || gridData.isNoOperable) {
            this.touchData.isCancelTouch = true;
            return;
        }

        const nowTimestamp = Time.getInstance().now();
        if (this.touchData.firstClickArrowGridId === -1) {
            this.touchData.firstClickArrowGridId = gridData.gridId;
            this.touchData.firstClickTime = nowTimestamp;
        } else {
            if (
                gridData.gridId !== this.touchData.firstClickArrowGridId ||
                Math.abs(nowTimestamp - this.touchData.firstClickTime) / 1000 >= DOUBLE_CLICK_EFFECT_DURATION
            ) {
                this.touchData.firstClickArrowGridId = gridId;
                this.touchData.firstClickTime = nowTimestamp;
            } else {
                this.touchData.secondClickArrowGridId = gridId;
                this.touchData.secondClickTime = nowTimestamp;
            }
        }
        this.touchData.touchStartPos = pos;
    }

    /**
     * 监听触摸事件
     * @param event 触摸事件
     */
    private onTouchMove(event: cc.Event.EventTouch): void {
        if (!event) {
            return;
        }
        const touchId = event.getID();
        if (touchId !== this.touchData.touchId) {
            return;
        }
        if (this.touchData.isCancelTouch) {
            return;
        }

        const pos = event.getLocation();
        if (this.touchData.firstClickArrowGridId !== -1 || this.touchData.secondClickArrowGridId !== -1) {
            if (MathUtils.distance(pos, this.touchData.touchStartPos) >= TOUCH_MOVE_EFFECT_DIS) {
                const touchArrowGridId = this.touchData.firstClickArrowGridId;
                this.clearTouchData();

                const data = this.arrowGridData.find((v) => v.gridId === touchArrowGridId);
                if (data.arrowId === -1) {
                    return;
                }

                data.isNoOperable = true;

                const pos2 = this.nodeAniGrid.convertToNodeSpaceAR(pos);
                this.aniGridData = {
                    arrowGridId: data.gridId,
                    arrowId: data.arrowId,
                    targetArcherGridId: -1,
                    targetArrowGridId: -1,
                    nodeItem: this.getAniGridItem(pos2, 1, data.arrowId),
                };

                this.arrowGridData.forEach((v) => {
                    if (
                        v.gridId === this.aniGridData.arrowGridId ||
                        (v.isUnlock && !v.isNoOperable && v.arrowId !== -1 && v.arrowId !== this.aniGridData.arrowId)
                    ) {
                        this.updateArrowGridInfo(v.gridId);
                    }
                });

                this.checkTargetGridByAniGrid();
            }
        } else if (this.aniGridData) {
            const pos2 = this.nodeAniGrid.convertToNodeSpaceAR(pos);
            this.aniGridData.nodeItem.setPosition(pos2);

            this.checkTargetGridByAniGrid();
        }
    }

    /**
     * 监听触摸事件
     * @param event 触摸事件
     */
    private onTouchEnd(event: cc.Event.EventTouch): void {
        if (!event) {
            return;
        }
        const touchId = event.getID();
        if (touchId !== this.touchData.touchId) {
            return;
        }
        this.touchData.touchId = -1;
        if (this.touchData.isCancelTouch) {
            return;
        }

        const pos = event.getLocation();
        if (this.touchData.firstClickArrowGridId !== -1 || this.touchData.secondClickArrowGridId !== -1) {
            const gridData = this.arrowGridData.find((v) => v.gridId === this.touchData.firstClickArrowGridId);
            const pos2 = this.nodeArrowGrid.convertToNodeSpaceAR(pos);
            if (gridData.nodeItem.getBoundingBox().contains(pos2)) {
                if (this.touchData.secondClickArrowGridId !== -1) {
                    this.arrowGridData.sort((a, b) => {
                        return a.gridId - b.gridId;
                    });
                    for (const data of this.arrowGridData) {
                        if (data.gridId === gridData.gridId) {
                            continue;
                        }
                        if (!data.isUnlock || data.arrowId === -1 || data.isNoOperable) {
                            continue;
                        }
                        if (data.arrowId !== gridData.arrowId) {
                            continue;
                        }

                        this.mixArrow(data, gridData, false);
                        break;
                    }
                } else {
                    return;
                }
            }

            this.clearTouchData();
        } else if (this.aniGridData) {
            const pos2 = this.nodeAniGrid.convertToNodeSpaceAR(pos);
            this.aniGridData.nodeItem.setPosition(pos2);

            const arrowGridId = this.aniGridData.arrowGridId;
            const targetArcherGridId = this.aniGridData.targetArcherGridId;
            const targetArrowGridId = this.aniGridData.targetArrowGridId;
            this.putAniGridItem(this.aniGridData.nodeItem);
            this.aniGridData = null;

            const data = this.arrowGridData.find((v) => v.gridId === arrowGridId);
            data.isNoOperable = false;

            let isWear = false;
            if (targetArcherGridId !== -1) {
                const targetData = this.archerGridData.find((v) => v.gridId === targetArcherGridId);
                if (targetData.isUnlock && targetData.archerId !== -1 && targetData.arrowId !== -1) {
                    isWear = true;
                    this.wearArrow(data, targetData);

                    if (this.touchData.firstClickArrowGridId !== -1) {
                        if (data.gridId === this.touchData.firstClickArrowGridId) {
                            this.clearTouchData();
                        }
                    }
                }
            } else if (targetArrowGridId !== -1) {
                const targetData = this.arrowGridData.find((v) => v.gridId === targetArrowGridId);
                if (
                    targetData.isUnlock &&
                    !targetData.isNoOperable &&
                    targetData.arrowId !== -1 &&
                    targetData.arrowId === data.arrowId
                ) {
                    this.mixArrow(data, targetData, false);

                    if (this.touchData.firstClickArrowGridId !== -1) {
                        if (
                            data.gridId === this.touchData.firstClickArrowGridId ||
                            targetData.gridId === this.touchData.firstClickArrowGridId
                        ) {
                            this.clearTouchData();
                        }
                    }
                }
            }

            this.archerGridData.forEach((v) => {
                if (v.isUnlock && v.archerId !== -1 && v.arrowId !== -1) {
                    this.updateArcherGridInfo(v.gridId);
                }
            });
            this.arrowGridData.forEach((v) => {
                if (v.isUnlock && !v.isNoOperable && v.arrowId !== -1) {
                    this.updateArrowGridInfo(v.gridId);
                }
            });

            if (isWear) {
                this.sortArrowGrid();
                this.syncGridData();
            }
        }
    }

    /**
     * 监听触摸事件
     * @param event 触摸事件
     */
    private onTouchCancel(event: cc.Event.EventTouch): void {
        if (!event) {
            return;
        }
        const touchId = event.getID();
        if (touchId !== this.touchData.touchId) {
            return;
        }
        this.touchData.touchId = -1;
        if (this.touchData.isCancelTouch) {
            return;
        }

        const pos = event.getLocation();
        if (this.touchData.firstClickArrowGridId !== -1 || this.touchData.secondClickArrowGridId !== -1) {
            this.clearTouchData();
        } else if (this.aniGridData) {
            const pos2 = this.nodeAniGrid.convertToNodeSpaceAR(pos);
            this.aniGridData.nodeItem.setPosition(pos2);

            const arrowGridId = this.aniGridData.arrowGridId;
            this.putAniGridItem(this.aniGridData.nodeItem);
            this.aniGridData = null;

            const data = this.arrowGridData.find((v) => v.gridId === arrowGridId);
            data.isNoOperable = false;

            this.archerGridData.forEach((v) => {
                if (v.isUnlock && v.archerId !== -1 && v.arrowId !== -1) {
                    this.updateArcherGridInfo(v.gridId);
                }
            });
            this.arrowGridData.forEach((v) => {
                if (v.isUnlock && !v.isNoOperable && v.arrowId !== -1) {
                    this.updateArrowGridInfo(v.gridId);
                }
            });
        }
    }

    /**
     * 打开弓箭手信息界面
     * @param event 触摸事件
     */
    protected onClickOpenArcherInfo(event: cc.Event.EventTouch): void {
        if (!event) {
            return;
        }

        const nodeGridItem = event.getCurrentTarget();
        const gridId: number = CocosExt.getNodeOrCompData(nodeGridItem, "archerGridId");
        const gridData = this.archerGridData.find((v) => v.gridId === gridId);
        if (!gridData) {
            return;
        }
        if (!gridData.isUnlock) {
            switch (gridData.unlockType) {
                case EnumExpandUnlockType.ArrowLevel:
                    if (this.data.maxArrowId < gridData.unlockPara) {
                        Tips.getInstance().info(TextUtils.format(i18n.arrow0001, gridData.unlockPara + ""));
                        return;
                    }
                    break;
                case EnumExpandUnlockType.PowerLevel:
                    const powerData = Power.getInstance().getPowerInfo();
                    if (powerData.kingLevelId < gridData.unlockPara) {
                        const powerInfo = TBPowerLevel.getInstance().getDataById(gridData.unlockPara);
                        Tips.getInstance().info(TextUtils.format(i18n.common0067, powerInfo.name));
                        return;
                    }
                    break;
                case EnumExpandUnlockType.MainTask:
                    const seqInfo = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.MainLineTask);
                    const taskData = Task.getInstance().getTaskInfo(gridData.unlockPara, seqInfo.id);
                    if (!taskData.isAwarded) {
                        const taskId = Task.getInstance().getCurrentTaskIdList(seqInfo.id);
                        Tips.getInstance().info(
                            TextUtils.format(i18n.gameSwitch0009, gridData.unlockPara - taskId[0] + 1)
                        );
                        return;
                    }
                    break;
                default:
                    break;
            }
            if (gridData.preExpandId !== -1 && !MakeArrow.getInstance().isUnlockByArcherGrid(gridData.preExpandId)) {
                Tips.getInstance().info(i18n.arrow0003);
                return;
            }
            return;
        }
        if (gridData.archerId === -1) {
            return;
        }
        const arrowData: IArcherGridData = this.archerGridData.find((v) => v.gridId === gridId);
        const pos = event.target.convertToWorldSpaceAR(cc.v2());
        UI.getInstance().open("PopupArrowDetails", { pos, gridId, arrowId: arrowData.arrowId });
    }

    /**
     * 制作弓箭
     */
    protected onClickMakeArrow(): void {
        this.makeArrow();
    }

    /**
     * 开关
     */
    protected onClickSwitch(): void {
        const { result, msg } = GameSwitch.getInstance().check(GAME_SWITCH_ID.AUTO_MAKE_ARROW);
        if (!result) {
            Tips.getInstance().info(msg);
            return;
        }
        UI.getInstance().open("PopupHomeAutoSet");
    }

    /**
     * 锻造
     */
    protected onClickForge(): void {
        const { result, msg } = GameSwitch.getInstance().check(GAME_SWITCH_ID.FORGE);
        if (!result) {
            Tips.getInstance().info(msg);
            return;
        }
        UI.getInstance().open("PopupForge");
    }

    /**
     * 排序
     */
    protected onClickSort(): void {
        this.sortArrowGrid();
    }

    /**
     * 未开放提示
     */
    protected onClickUnopenTips(): void {
        Tips.getInstance().info(i18n.common0017);
    }

    /**
     * 领取任务奖励
     * @param event
     * @param taskId 任务id
     */
    protected onClickGetTaskReward(event: cc.Event.EventTouch, taskId: number): void {
        if (!taskId) {
            return;
        }

        const taskSeqConfig = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.MainLineTask);
        const taskDetailConfig = TBTaskDetail.getInstance().getDataById(taskId);
        const taskInfo = Task.getInstance().getTaskInfo(taskId, taskSeqConfig.id);
        if (taskInfo.progress < taskDetailConfig.reachValue) {
            taskDetailConfig.jumpId && JumpUtils.jump(JumpType.Task, taskDetailConfig.id);
            return;
        }
        Task.getInstance().sendTaskTakeAward(this.taskSeqId, taskId);
    }
}
