/*
 * @Author: Jr<PERSON>d
 * @Date: 2024-04-15 15:28:34
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-03-19 16:08:30
 */

import ListView from "../../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import Time, { DAY_TO_SECOND } from "../../../nsn/util/Time";
import { RechargeGetPackInfoRet, RechargeNoticeRet, RechargeReceivePackRet } from "../../../protobuf/proto";
import { EnumPackShowType } from "../../data/base/BasePack";
import { EnumRechargeTabMainTab } from "../../data/base/BaseRechargeTab";
import DataRechargeTab from "../../data/extend/DataRechargeTab";
import TBPack from "../../data/parser/TBPack";
import TBRecharge from "../../data/parser/TBRecharge";
import TBRechargeTab from "../../data/parser/TBRechargeTab";
import GameSwitch from "../../game/GameSwitch";
import Player from "../../game/Player";
import Recharge from "../../game/Recharge";
import TweenUtil from "../../utils/TweenUtils";
import { IPrefabTabFundItem } from "./PrefabTabFundItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabTabFund extends I18nComponent {
    @property(ListView)
    listView: ListView = null;

    public data: DataRechargeTab = null;

    private tab: EnumRechargeTabMainTab = null;

    protected onLoad(): void {
        this.tab = EnumRechargeTabMainTab.Fund;
        this.registerHandler();
        this.refreshUI();
        TweenUtil.playListViewVerticalAni(this.listView);
    }

    /**
     * 注册事件
     */
    protected registerHandler(): void {
        // 礼包信息获取
        Recharge.getInstance().on(
            [RechargeGetPackInfoRet.prototype.clazzName, RechargeReceivePackRet.prototype.clazzName],
            () => {
                this.refreshUI();
            },
            this
        );
        // 购买通知
        Recharge.getInstance().on(
            RechargeNoticeRet.prototype.clazzName,
            () => {
                this.refreshUI();
            },
            this
        );
    }

    private refreshUI(): void {
        const tabList = TBRechargeTab.getInstance().getDataByMainTab(this.tab);
        const base = tabList[0];
        const arr: number[] = [];
        for (const e of base.tabPack) {
            const rechargeData = TBRecharge.getInstance().getDataById(e);
            const packData = TBPack.getInstance().getDataById(rechargeData.pack[0]);
            let isShow = false;
            switch (packData.showType) {
                case EnumPackShowType.ChargeDisplay:
                    const sumPrice = Recharge.getInstance().getSumPrice();
                    isShow = sumPrice >= packData.showPara;
                    break;
                case EnumPackShowType.RegisterDisplay:
                    const openTime = Player.getInstance().getRegisterDate();
                    const openTimeZero = Time.getInstance().getZeroOfTime(openTime);
                    const time = Time.getInstance().now() - openTimeZero;
                    const day = Math.ceil(time / (DAY_TO_SECOND * 1000));
                    isShow = day >= packData.showPara;
                    break;
                case EnumPackShowType.SystemDisplay:
                    const { result } = GameSwitch.getInstance().check(packData.showPara);
                    isShow = result;
                    break;
                default:
                    isShow = true;
                    break;
            }
            if (isShow) {
                arr.push(e);
            }
        }
        const list: IPrefabTabFundItem[] = arr.map((rechargeId: number, idx: number) => {
            return {
                base,
                rechargeId,
                idx,
            };
        });
        this.listView.setListData(list);
    }
}
