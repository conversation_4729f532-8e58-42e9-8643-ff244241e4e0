/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:38:23
 */

import ListViewItem from "../../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../../nsn/ui/UI";
import TextUtils from "../../../../nsn/util/TextUtils";
import Tips from "../../../../nsn/util/Tips";
import { ActivityNewTrialFreeRet, BagUpdateRet, ShopPurchase } from "../../../../protobuf/proto";
import GrayComp from "../../../comp/GrayComp";
import i18n from "../../../config/i18n/I18n";
import { EnumActivityUniversalPara } from "../../../data/base/BaseActivityUniversal";
import DataTrialBoss from "../../../data/extend/DataTrialBoss";
import { ACTIVITY_ID } from "../../../data/parser/TBActivity";
import TBActivityUniversal from "../../../data/parser/TBActivityUniversal";
import TBItem from "../../../data/parser/TBItem";
import Activity from "../../../game/Activity";
import ActivityNewbieTrial from "../../../game/activity/ActivityNewbieTrial";
import Bag from "../../../game/Bag";
import { DungeonType } from "../../../game/Combat";
import ItemUtils from "../../../utils/ItemUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabActivityNewbieTrialItem extends ListViewItem {
    @property(cc.Label)
    desc: cc.Label = null;
    @property(cc.Node)
    rewardContent: cc.Node = null;
    @property(cc.Node)
    btnFight: cc.Node = null;
    @property(cc.Node)
    got: cc.Node = null;
    @property(cc.Node)
    spRed: cc.Node = null;
    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private dungeonId: number = 0;

    public onLoad(): void {
        this.registerHandler();
    }

    protected registerHandler(): void {
        ActivityNewbieTrial.getInstance().on(
            ActivityNewTrialFreeRet.prototype.clazzName,
            () => {
                this.updateRed();
            },
            this
        );
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                if (data.srcReq === ShopPurchase.prototype.clazzName) {
                    this.updateRed();
                }
            },
            this
        );
    }

    public unuse(): void {}
    public reuse(): void {}

    public updateData(data: DataTrialBoss): void {
        this.dungeonId = data.id;
        this.desc.string = TextUtils.format(i18n.dungeon0042, data.id);
        ItemUtils.refreshView(this.rewardContent, this.prefabItem, data.reward);
        const info = ActivityNewbieTrial.getInstance().getInfo();
        if (info.dungeonId > data.id) {
            this.got.active = true;
            this.btnFight.active = false;
            this.spRed.active = false;
        } else if (info.dungeonId < data.id) {
            this.got.active = false;
            this.btnFight.active = true;
            this.btnFight.getComponent(GrayComp).gray = true;
            this.btnFight.getComponent(cc.Button).interactable = false;
            this.spRed.active = false;
        } else {
            this.got.active = false;
            this.btnFight.active = true;
            this.btnFight.getComponent(GrayComp).gray = false;
            this.btnFight.getComponent(cc.Button).interactable = true;
            this.updateRed();
        }
    }

    protected updateRed(): void {
        const info = ActivityNewbieTrial.getInstance().getInfo();
        const para: number[][] = TBActivityUniversal.getInstance().getValueByPara(
            EnumActivityUniversalPara.TrialCostItem
        );
        const [costId, costCount] = para[0];
        const isEnough = Bag.getInstance().isEnough(costId, costCount);
        this.spRed.active = isEnough && info.dungeonId === this.dungeonId;
    }

    protected onClickFight(): void {
        const isOpening = Activity.getInstance().isOpeningById(ACTIVITY_ID.NEWBIE_TRIAL);
        if (!isOpening) {
            Tips.getInstance().show(i18n.activity0001);
            return;
        }
        const info = ActivityNewbieTrial.getInstance().getInfo();
        if (this.dungeonId > info.dungeonId) {
            Tips.getInstance().show(i18n.newbieTrial0002);
            return;
        }

        const para: number[][] = TBActivityUniversal.getInstance().getValueByPara(
            EnumActivityUniversalPara.TrialCostItem
        );
        const [costId, costCount] = para[0];
        if (!Bag.getInstance().isEnough(costId, costCount)) {
            const data = TBItem.getInstance().getDataById(costId);
            Tips.getInstance().show(TextUtils.format(i18n.common0049, data.name));
            return;
        }

        UI.getInstance().open("UIDungeonCombatTrial", {
            type: DungeonType.Trial,
            levelId: this.dungeonId,
        });
    }
}
