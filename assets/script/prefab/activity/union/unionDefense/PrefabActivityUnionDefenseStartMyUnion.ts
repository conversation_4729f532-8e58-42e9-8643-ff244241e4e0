import Loader from "../../../../../nsn/core/Loader";
import I18nComponent from "../../../../../nsn/i18n/I18nComponent";
import UI from "../../../../../nsn/ui/UI";
import TextUtils from "../../../../../nsn/util/TextUtils";
import Time, { HOUR_TO_SECOND } from "../../../../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../../../../nsn/util/TimeFormat";
import {
    PlayerInfoQueryRet,
    UnionSiegeEndChallengeNoticeRet,
    UnionSiegeEndChallengeRet,
    UnionSiegeGetRet,
    UnionSiegeStartChallengeNoticeRet,
    UnionSiegeStartChallengeRet,
    UnionSiegeSweetChallengeRet,
} from "../../../../../protobuf/proto";
import i18n from "../../../../config/i18n/I18n";
import { EnumUnionPara } from "../../../../data/base/BaseUnion";
import TBUnion from "../../../../data/parser/TBUnion";
import CombatScore from "../../../../game/CombatScore";
import Player from "../../../../game/Player";
import Union from "../../../../game/Union";
import UnionSiege from "../../../../game/UnionSiege";
import CombatScoreUtils from "../../../../utils/CombatScoreUtils";
import ImageUtils from "../../../../utils/ImageUtils";
import { IUnionDefenseLeadItemData } from "./PrefabActivityUnionDefenseLeadItem";

const SLICE_COUNT = 11; // 首屏背景11只

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabActivityUnionDefenseStartMyUnion extends I18nComponent {
    @property(cc.Node)
    time: cc.Node = null;
    @property(cc.Node)
    combat: cc.Node = null; // 我的战力
    @property([cc.Node])
    unionInfo: cc.Node[] = []; // 我方、敌方公会信息
    @property(cc.Node)
    myServeName: cc.Node = null;
    @property(cc.Node)
    enemyServeName: cc.Node = null;
    @property(cc.Node)
    challengeCount: cc.Node = null; // 挑战次数

    @property(cc.Node)
    firstBg: cc.Node = null; // 首屏背景
    @property(cc.Node)
    content: cc.Node = null;
    @property([cc.Node])
    bgs: cc.Node[] = []; // 背景数组
    @property(cc.Node)
    lastBg: cc.Node = null; // 最后一张背景
    @property([cc.Node])
    leads: cc.Node[] = [];
    @property(cc.Prefab)
    prefabLeadItem: cc.Prefab = null;

    private dt: number = 1;
    private activityId: number = 0;

    protected onLoad(): void {
        this.lastBg.parent = null;

        this.registerHandler();
        this.updateTopUI(true);
        this.updateLeadUI();
    }

    protected onDestroy(): void {
        this.lastBg.destroy();
    }

    protected registerHandler(): void {
        UnionSiege.getInstance().on(
            UnionSiegeGetRet.prototype.clazzName,
            () => {
                this.updateTopUI();
                this.updateLeadUI();
            },
            this
        );

        UnionSiege.getInstance().on(
            [
                UnionSiegeStartChallengeRet.prototype.clazzName,
                UnionSiegeStartChallengeNoticeRet.prototype.clazzName,
                UnionSiegeEndChallengeRet.prototype.clazzName,
                UnionSiegeEndChallengeNoticeRet.prototype.clazzName,
            ],
            () => {
                this.updateTopUI();
                this.updateLeadUI();
            },
            this
        );

        UnionSiege.getInstance().on(
            UnionSiegeSweetChallengeRet.prototype.clazzName,
            () => {
                this.updateTopUI();
            },
            this
        );

        Player.getInstance().on(
            PlayerInfoQueryRet.prototype.clazzName,
            (data: PlayerInfoQueryRet) => {
                const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
                const unionInfo = Union.getInstance().getInfo();
                const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
                const rivalUnionInfo = unionSiegeInfo.find((e) => e.rivalUnionId === unionInfo.id); // 对方
                if (data.playerInfo.playerId === myInfo?.leaderInfo?.playerId) {
                    this.myServeName.label(data.serverName);
                } else if (data.playerInfo.playerId === rivalUnionInfo?.leaderInfo?.playerId) {
                    this.enemyServeName.label(data.serverName);
                }
            },
            this
        );
    }

    protected update(dt: number): void {
        this.dt += dt;
        if (this.dt > 1) {
            this.dt -= dt;
            const now = Time.getInstance().now();
            const dayZero = Time.getInstance().getTodayZero(); // 本日零点
            const unionData1 = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseBattleTime); // 获取对决期配置
            const time3 = dayZero + unionData1[1] * HOUR_TO_SECOND * 1000;
            this.time.label(
                TextUtils.format(
                    i18n.unionDefense0006,
                    TimeFormat.getInstance().getTextByDuration(time3 - now, TimeDurationFormatType.D_H_M_S_3)
                )
            );
        }
    }

    public updateActivityId(id: number): void {
        this.activityId = id;
    }

    private updateLeadUI(): void {
        const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
        const unionInfo = Union.getInstance().getInfo();
        const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
        const unionData1 = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseDefensivePersonnel);

        if (myInfo.siegeDetailInfos.length >= SLICE_COUNT) {
            const count = Math.ceil((unionData1 - SLICE_COUNT) / 2); // 获取需要显示背景数量
            for (let i = 0; i < this.bgs.length; i++) {
                this.bgs[i].active = i < count;
            }
        } else {
            for (const e of this.bgs) {
                e.active = false;
            }
        }

        // 增加最后一张背景
        const childNode = this.content.child("lastBg");
        if (!childNode) {
            const lastBg = Loader.getInstance().instantiate(this.lastBg);
            lastBg.parent = this.content;
            lastBg.active = true;
            lastBg.zIndex = cc.macro.MAX_ZINDEX;
        } else {
            childNode.zIndex = cc.macro.MAX_ZINDEX;
        }

        myInfo.siegeDetailInfos.sort((a, b) => {
            return a.rank - b.rank;
        });

        for (let i = 0; i < this.leads.length; i++) {
            const info = myInfo.siegeDetailInfos[i];
            const node = this.leads[i];
            if (i < unionData1) {
                if (info) {
                    node.active = true;
                    const leadNode = node.child("PrefabActivityUnionDefenseLeadItem");
                    const obj: IUnionDefenseLeadItemData = {
                        data: info,
                        unionId: myInfo.unionId,
                        activityId: this.activityId,
                        isMine: true,
                    };
                    if (!leadNode) {
                        this.scheduleOnce(() => {
                            const item = Loader.getInstance().instantiate(this.prefabLeadItem);
                            item.parent = node;
                            item.getComponent(item.name).updateUI(obj); // 更新UI
                        });
                    } else {
                        leadNode.getComponent(leadNode.name).updateUI(obj); // 更新UI
                    }
                } else {
                    node.active = false;
                }
            } else {
                node.active = false;
            }
        }
    }

    private updateTopUI(initServeName: boolean = false): void {
        const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
        const unionInfo = Union.getInstance().getInfo();
        const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
        const rivalUnionInfo = unionSiegeInfo.find((e) => e.rivalUnionId === unionInfo.id); // 对方
        const myDetailObjs = UnionSiege.getInstance().getMyUnionSingeDetailObj();
        const unionData = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseBattleNumber);
        for (let i = 0; i < this.unionInfo.length; i++) {
            const e = this.unionInfo[i];
            const unionFlag = e.child("flag");
            const unionName = e.child("unionName");
            const unionCount = e.child("layout").child("count");
            const unionStar = e.child("layout").child("star");

            if (i === 0) {
                ImageUtils.setUnionFlag(unionFlag, myInfo.unionInfo.flag);
                unionFlag.button(myInfo.unionInfo.id);
                unionName.label(myInfo.unionInfo.name);
                initServeName && Player.getInstance().sendPlayerInfoQuery(myInfo.leaderInfo.playerId);
                unionStar.spriteAsync("texture/activity/union/defense/spUBStar2");
                unionCount.label(myInfo.totalStarCount + "");
            } else if (i === 1) {
                ImageUtils.setUnionFlag(unionFlag, rivalUnionInfo.unionInfo.flag);
                unionFlag.button(rivalUnionInfo.unionInfo.id);
                unionName.label(rivalUnionInfo.unionInfo.name);
                initServeName && Player.getInstance().sendPlayerInfoQuery(rivalUnionInfo.leaderInfo.playerId);
                unionStar.spriteAsync("texture/activity/union/defense/spUBStar2");
                unionCount.label(rivalUnionInfo.totalStarCount + "");
            }
        }
        CombatScoreUtils.update(this.combat, CombatScore.getInstance().getScore());
        this.challengeCount.label(
            TextUtils.format(i18n.unionDefense0014, unionData - myDetailObjs.todayChallengeCount)
        );
    }

    protected onClickUnionInfo(sender: cc.Event.EventTouch, unionId: number): void {
        const isRoundEmpty = UnionSiege.getInstance().isUnionRoundEmpty();
        const isUnionEmpty = UnionSiege.getInstance().isUnionEmpty();
        if (!isUnionEmpty || isRoundEmpty) {
            return; // 没有数据的情况
        }

        UI.getInstance().open("PopupActivityUnionDefenseUnionDetails", unionId);
    }

    protected onClickLog(): void {
        UI.getInstance().open("PopupActivityUnionDefenseDayLog");
    }

    protected onClickAchieve(): void {
        UI.getInstance().open("PopupActivityUnionDefenseAchievement", this.activityId);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }
}
