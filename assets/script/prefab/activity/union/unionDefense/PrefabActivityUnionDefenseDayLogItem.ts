/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-24 16:01:47
 */

import ListViewItem from "../../../../../nsn/comp/3rd/List/ListViewItem";
import TextUtils from "../../../../../nsn/util/TextUtils";
import TimeFormat, { TimeFormatType } from "../../../../../nsn/util/TimeFormat";
import { IUnionSiegeLogObj } from "../../../../../protobuf/proto";
import i18n from "../../../../config/i18n/I18n";
import { EnumUnionPara } from "../../../../data/base/BaseUnion";
import TBUnion from "../../../../data/parser/TBUnion";
import Union from "../../../../game/Union";
import UnionSiege, { UNION_DEFENSE_EXT_ADD_START } from "../../../../game/UnionSiege";
import CombatScoreUtils from "../../../../utils/CombatScoreUtils";
import ImageUtils from "../../../../utils/ImageUtils";
import PlayerInfoUtils from "../../../../utils/PlayerInfoUtils";

enum UIType {
    AllRecord,
    MyRecord,
}

const { property, ccclass } = cc._decorator;

@ccclass
export default class PrefabActivityUnionDefenseDayLogItem extends ListViewItem {
    @property(cc.Node)
    winBg: cc.Node = null;
    @property(cc.Node)
    loserBg: cc.Node = null;
    @property(cc.Node)
    extBg: cc.Node = null; // 扫荡背景
    @property(cc.Node)
    scoreBg: cc.Node = null; // 分数
    @property(cc.Node)
    logTime: cc.Node = null; // 对战时间
    @property(cc.Node)
    myHead: cc.Node = null;
    @property(cc.Node)
    myName: cc.Node = null;
    @property(cc.Node)
    myCombat: cc.Node = null;
    @property(cc.Node)
    myIcon: cc.Node = null;
    @property(cc.Node)
    enemyHead: cc.Node = null;
    @property(cc.Node)
    enemyName: cc.Node = null;
    @property(cc.Node)
    enemyCombat: cc.Node = null;
    @property(cc.Node)
    enemyIcon: cc.Node = null;
    @property([cc.SpriteFrame])
    icons: cc.SpriteFrame[] = [];

    public data: IUnionSiegeLogObj = null;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.winBg,
                url: `texture/syncUI/activity/union/defense/spUBResultLabel1`,
            },
            {
                sprite: this.loserBg,
                url: `texture/syncUI/activity/union/defense/spUBResultLabel2`,
            },
            {
                sprite: this.extBg,
                url: `texture/syncUI/activity/union/defense/spUBResultLabel`,
            },
        ];
    }

    public unuse(): void {}
    public reuse(): void {}

    public updateData(info: { data: IUnionSiegeLogObj; type: UIType }): void {
        this.data = info.data;
        const unionData = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseDifficulty);
        const unionSiegeInfo = UnionSiege.getInstance().getUnionSiegeInfo();
        const unionInfo = Union.getInstance().getInfo();
        const myInfo = unionSiegeInfo.find((e) => e.unionId === unionInfo.id); // 我方
        const rivalUnionInfo = unionSiegeInfo.find((e) => e.rivalUnionId === unionInfo.id); // 对方

        const ourAttack = myInfo.siegeDetailInfos.find(
            (e) => e.playerInfo.playerId === info.data.attackPlayInfo.playerId
        ); // 我方是否是攻击
        const ext = info.data.challengeDifficulty === 0; // challengeDifficulty为0表示扫荡

        const star = this.scoreBg.child("star");
        const count = this.scoreBg.child("count");
        this.myIcon.sprite(ourAttack ? this.icons[1] : this.icons[0]);
        this.enemyIcon.sprite(ourAttack ? this.icons[0] : this.icons[1]);

        if (ext) {
            this.winBg.active = false;
            this.loserBg.active = false;
            this.extBg.active = true;
            this.logTime.label(
                TimeFormat.getInstance().getTextByTime(TimeFormatType.MM_DD_HH_mm_ss, info.data.logTime)
            );

            if (ourAttack) {
                this.scoreBg.active = true;
                ImageUtils.setUnionDefenseStar(star, true);
                count.label("×" + UNION_DEFENSE_EXT_ADD_START + "");

                this.myName.label(info.data.attackPlayInfo.name);
                PlayerInfoUtils.updateHead(this.myHead, info.data.attackPlayInfo);
                CombatScoreUtils.update(this.myCombat, info.data.attackCombat);
                this.enemyName.label(info.data.defenderPlayInfo.name);
                PlayerInfoUtils.updateHead(this.enemyHead, info.data.defenderPlayInfo);
                CombatScoreUtils.update(this.enemyCombat, info.data.defenderCombat);
                this.scheduleOnce(() => {
                    const score2 = this.enemyCombat.child("PrefabCombatScore");
                    score2.anchorX = 1;
                    score2.getComponent(cc.Layout).horizontalDirection = cc.Layout.HorizontalDirection.RIGHT_TO_LEFT;
                    score2.getComponent(cc.Layout).updateLayout();
                });
            } else {
                this.scoreBg.active = false;

                this.myName.label(info.data.defenderPlayInfo.name);
                PlayerInfoUtils.updateHead(this.myHead, info.data.defenderPlayInfo);
                CombatScoreUtils.update(this.myCombat, info.data.defenderCombat);
                this.enemyName.label(info.data.attackPlayInfo.name);
                PlayerInfoUtils.updateHead(this.enemyHead, info.data.attackPlayInfo);
                CombatScoreUtils.update(this.enemyCombat, info.data.attackCombat);
                this.scheduleOnce(() => {
                    const score2 = this.enemyCombat.child("PrefabCombatScore");
                    score2.anchorX = 1;
                    score2.getComponent(cc.Layout).horizontalDirection = cc.Layout.HorizontalDirection.RIGHT_TO_LEFT;
                    score2.getComponent(cc.Layout).updateLayout();
                });
            }
            return;
        }

        this.logTime.label(
            TextUtils.format(
                i18n.unionDefense0016,
                unionData[info.data.challengeDifficulty - 1] * 100,
                TimeFormat.getInstance().getTextByTime(TimeFormatType.MM_DD_HH_mm_ss, info.data.logTime)
            )
        );
        this.extBg.active = false;
        if (ourAttack) {
            const type = rivalUnionInfo.siegeDetailInfos.find(
                (e) => e.playerInfo.playerId === info.data.defenderPlayInfo.playerId
            )?.memberType;
            if (info.data.isWin) {
                this.scoreBg.active = true;
                const num = UnionSiege.getInstance().getStarCount(type, info.data.challengeDifficulty);
                ImageUtils.setUnionDefenseStar(star, true);
                count.label("×" + num + "");
                this.winBg.active = true;
                this.loserBg.active = false;
            } else {
                this.scoreBg.active = false;
                this.winBg.active = false;
                this.loserBg.active = true;
            }

            this.myName.label(info.data.attackPlayInfo.name);
            PlayerInfoUtils.updateHead(this.myHead, info.data.attackPlayInfo);
            CombatScoreUtils.update(this.myCombat, info.data.attackCombat);

            this.enemyName.label(info.data.defenderPlayInfo.name);
            PlayerInfoUtils.updateHead(this.enemyHead, info.data.defenderPlayInfo);
            CombatScoreUtils.update(this.enemyCombat, info.data.defenderCombat);
            this.scheduleOnce(() => {
                const score2 = this.enemyCombat.child("PrefabCombatScore");
                score2.anchorX = 1;
                score2.getComponent(cc.Layout).horizontalDirection = cc.Layout.HorizontalDirection.RIGHT_TO_LEFT;
                score2.getComponent(cc.Layout).updateLayout();
            });
        } else {
            this.scoreBg.active = false;
            // 防守方战斗结果isWin为false表示我赢
            if (info.data.isWin) {
                this.winBg.active = false;
                this.loserBg.active = true;
            } else {
                this.winBg.active = true;
                this.loserBg.active = false;
            }

            this.myName.label(info.data.defenderPlayInfo.name);
            PlayerInfoUtils.updateHead(this.myHead, info.data.defenderPlayInfo);
            CombatScoreUtils.update(this.myCombat, info.data.defenderCombat);

            this.enemyName.label(info.data.attackPlayInfo.name);
            PlayerInfoUtils.updateHead(this.enemyHead, info.data.attackPlayInfo);
            CombatScoreUtils.update(this.enemyCombat, info.data.attackCombat);
            this.scheduleOnce(() => {
                const score2 = this.enemyCombat.child("PrefabCombatScore");
                score2.anchorX = 1;
                score2.getComponent(cc.Layout).horizontalDirection = cc.Layout.HorizontalDirection.RIGHT_TO_LEFT;
                score2.getComponent(cc.Layout).updateLayout();
            });
        }
    }
}
