/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-16 15:05:19
 */

import I18nComponent from "../../../../../nsn/i18n/I18nComponent";
import UI from "../../../../../nsn/ui/UI";
import Tips from "../../../../../nsn/util/Tips";
import { IUnionSiegeDetailObj, IUnionSiegeShowObj } from "../../../../../protobuf/proto";
import i18n from "../../../../config/i18n/I18n";
import { EnumUnionPara } from "../../../../data/base/BaseUnion";
import TBLeadSkin from "../../../../data/parser/TBLeadSkin";
import TBUnion from "../../../../data/parser/TBUnion";
import TBWeapon from "../../../../data/parser/TBWeapon";
import TBWing from "../../../../data/parser/TBWing";
import UnionSiege, { EnumUnionSiegeMemberType } from "../../../../game/UnionSiege";
import CombatScoreUtils from "../../../../utils/CombatScoreUtils";
import ImageUtils from "../../../../utils/ImageUtils";
import SpineUtils from "../../../../utils/SpineUtils";

/**
 * 参战人员信息
 */
export interface IUnionDefenseLeadItemData {
    data: IUnionSiegeDetailObj;
    unionId: number;
    activityId: number;
    isMine: boolean;
}

const { property, ccclass } = cc._decorator;

@ccclass
export default class PrefabActivityUnionDefenseLeadItem extends I18nComponent {
    @property(cc.Node)
    nodeLead: cc.Node = null;
    @property(cc.Node)
    dieNode: cc.Node = null;
    @property(cc.Node)
    stars1: cc.Node = null;
    @property(cc.Node)
    stars2: cc.Node = null;
    @property(cc.Node)
    stars3: cc.Node = null;
    @property(cc.Node)
    leadName: cc.Node = null;
    @property(cc.Node)
    dieLeadName: cc.Node = null;
    @property(cc.Node)
    dieTip: cc.Node = null;
    @property(cc.Node)
    combat: cc.Node = null;
    @property(cc.Node)
    btnChallenge: cc.Node = null; // 挑战
    @property(cc.Node)
    btnExt: cc.Node = null; // 扫荡

    public data: IUnionDefenseLeadItemData = null;

    protected updateUI(data: IUnionDefenseLeadItemData): void {
        this.data = data;
        const isDie = this.isDie();
        if (isDie) {
            this.nodeLead.active = false;
            this.dieNode.active = true;
            this.stars1.active = false;
            this.stars2.active = false;
            this.stars3.active = false;
            this.leadName.active = false;
            this.dieLeadName.active = true;
            this.dieTip.active = true;

            this.dieLeadName.label(data.data.playerInfo.name);
            CombatScoreUtils.update(this.combat, data.data.showInfo.combat, {
                gary: true,
            });
        } else {
            this.nodeLead.active = true;
            this.dieNode.active = false;
            this.leadName.active = true;
            this.dieLeadName.active = false;
            this.dieTip.active = false;

            const starCount = this.getStarCount(data.data.memberType); // 目前拥有的星星数
            switch (data.data.memberType) {
                case EnumUnionSiegeMemberType.Leader:
                    this.stars1.active = true;
                    this.stars2.active = false;
                    this.stars3.active = false;
                    for (let i = this.stars1.childrenCount - 1; i >= 0; i--) {
                        ImageUtils.setUnionDefenseStar(this.stars1.children[i], i + 1 <= starCount);
                    }
                    break;
                case EnumUnionSiegeMemberType.Elite:
                    this.stars1.active = false;
                    this.stars2.active = true;
                    this.stars3.active = false;
                    for (let i = this.stars2.childrenCount - 1; i >= 0; i--) {
                        ImageUtils.setUnionDefenseStar(this.stars2.children[i], i + 1 <= starCount);
                    }
                    break;
                case EnumUnionSiegeMemberType.Other:
                    this.stars1.active = false;
                    this.stars2.active = false;
                    this.stars3.active = true;
                    for (let i = this.stars3.childrenCount - 1; i >= 0; i--) {
                        ImageUtils.setUnionDefenseStar(this.stars3.children[i], i + 1 <= starCount);
                    }
                    break;
                default:
                    break;
            }

            this.leadName.label(data.data.playerInfo.name);
            this.setLeadUI(this.nodeLead, data.data.showInfo);
            CombatScoreUtils.update(this.combat, data.data.showInfo.combat);
        }

        this.btnChallenge.active = !data.isMine && !isDie;
        this.btnExt.active = !data.isMine && isDie;
    }

    private setLeadUI(node: cc.Node, data: IUnionSiegeShowObj): void {
        const spineLead = node.child("spineLead").getComponent(sp.Skeleton);
        const spineWeapon = node.child("nodeWeapon").child("spineWeapon").getComponent(sp.Skeleton);
        const spineWing = node.child("nodeWing").child("spineWing").getComponent(sp.Skeleton);

        // 主角
        const skinData = TBLeadSkin.getInstance().getDataById(data.leadSkinId);
        SpineUtils.setLeadWithoutAniName(spineLead, skinData.res, () => {
            // @ts-ignore
            const attachUtil = spineLead.attachUtil;
            attachUtil.destroyAllAttachedNodes();
            attachUtil.generateAllAttachedNodes();

            spineLead.setAnimation(0, "attackWait", true);
        });

        // 神器
        if (data.weaponId !== 0) {
            node.child("nodeWeapon").active = true;
            const weaponData = TBWeapon.getInstance().getDataById(data.weaponId);
            SpineUtils.setWeaponWithoutAniName(spineWeapon, weaponData.res, () => {
                // @ts-ignore
                const attachUtil = spineWeapon.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                const boneHand = spineWeapon.findBone("hand");
                spineWeapon.node.setPosition(cc.v2(-boneHand.x / boneHand.scaleX, -boneHand.y / boneHand.scaleY));
                spineWeapon.setAnimation(0, "attackWait", true);
            });
        } else {
            node.child("nodeWeapon").active = false;
        }

        // 背饰
        if (data.wingsId !== 0) {
            node.child("nodeWing").active = true;
            const wingData = TBWing.getInstance().getDataById(data.wingsId);
            SpineUtils.setWingWithoutAniName(spineWing, wingData.res, () => {
                spineWing.setAnimation(0, "attackWait", true);
            });
        } else {
            node.child("nodeWing").active = false;
        }
    }

    private isDie(): boolean {
        let count = 0;
        const info = UnionSiege.getInstance().getUnionSiegeDetailObjByUnionIdAndPlayerId(
            this.data.unionId,
            this.data.data.playerInfo.playerId
        );
        for (const e of info.challengeInfos) {
            if (e.isChallengeSuccess) {
                count += 1;
            }
        }
        return count >= info.challengeInfos.length;
    }

    /**
     * 获取目前拥有的星数和总星数
     * @param type
     * @param difficulty
     * @returns
     */
    private getStarCount(type: EnumUnionSiegeMemberType): number {
        const allCount = UnionSiege.getInstance().getTotalStarCountByMemberType(type); // 总星数
        const info = UnionSiege.getInstance().getUnionSiegeDetailObjByUnionIdAndPlayerId(
            this.data.unionId,
            this.data.data.playerInfo.playerId
        );
        let count = allCount;
        for (const e of info.challengeInfos) {
            if (e.isChallengeSuccess) {
                const difficultiesCount = UnionSiege.getInstance().getStarCount(type, e.challengeDifficulty); // 每个难度对应的星数
                count -= difficultiesCount;
            }
        }
        return count;
    }

    /**
     * 开始挑战
     */
    protected onClickChallenge(): void {
        UI.getInstance().open("PopupActivityUnionDefenseChallenge", this.data);
    }

    /**
     * 扫荡
     */
    protected onClickSweepAway(): void {
        const isDie = this.isDie();
        if (!isDie) {
            return;
        }
        const myDetailObjs = UnionSiege.getInstance().getMyUnionSingeDetailObj();
        const times = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseBattleNumber);
        if (times - myDetailObjs.todayChallengeCount <= 0) {
            Tips.getInstance().show(i18n.unionDefense0023);
            return;
        }

        UnionSiege.getInstance().sendUnionSiegeSweetChallenge(
            this.data.unionId,
            this.data.data.playerInfo.playerId,
            this.data.activityId
        );
    }

    protected onClickItem(): void {
        UI.getInstance().open("FloatOtherPlayerInfo", this.data.data.playerInfo?.playerId);
    }
}
