/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-23 17:22:04
 */

import Audio from "../../../nsn/audio/Audio";
import Channel from "../../../nsn/config/Channel";
import Version from "../../../nsn/config/Version";
import Language from "../../../nsn/core/Language";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import Platform from "../../../nsn/platform/Platform";
import UI from "../../../nsn/ui/UI";
import Tips from "../../../nsn/util/Tips";
import i18n from "../../config/i18n/I18n";
import { LanguageType } from "../../config/LanguageConfig";
import { GameProtocol } from "../../core/GameProtocol";
import Login from "../../core/Login";
import DebugEntry from "../../debug/DebugEntry";
import NsnHelp, { NsnHelpEntranceId } from "../../sdk/NsnHelp";
import SceneUtils from "../../utils/SceneUtils";

const { ccclass, property } = cc._decorator;

const LANGUAGE = [LanguageType.Zhs, LanguageType.Zht, LanguageType.En];

@ccclass
export default class PrefabPlayerSetting extends I18nComponent {
    @property(cc.Slider)
    musicSlider: cc.Slider = null;
    @property(cc.ProgressBar)
    musicProgressBar: cc.ProgressBar = null;
    @property(cc.Slider)
    effectSlider: cc.Slider = null;
    @property(cc.ProgressBar)
    effectProgressBar: cc.ProgressBar = null;
    @property([cc.Node])
    languages: cc.Node[] = [];
    @property(cc.Node)
    btnService: cc.Node = null;
    @property(cc.Node)
    btnAccountBind: cc.Node = null;
    @property(cc.Node)
    btnSocial: cc.Node = null;
    @property(cc.Node)
    btnWxAdd: cc.Node = null;
    @property(cc.Node)
    version: cc.Node = null;

    private languageTabType: number = -1; // 切换语言页签类型
    private languageArr: LanguageType[] = [];

    protected onLoad(): void {
        this.languageArr = Channel.getInstance().getConfig().getLanguage();
        this.languages.forEach((v, i) => {
            v.active = this.languageArr.includes(LANGUAGE[i]);
            v.button(i);
        });
        this.updateSetting();
    }

    public updateSetting(): void {
        const language = Language.getInstance().readLanguage() || Language.getInstance().getLanguage();
        this.languageTabType = LANGUAGE.indexOf(language);
        this.updateTabState();

        this.version.label(i18n.setting0008 + ": " + Version.getInstance().getGameVersion());
        this.musicSlider.progress = Audio.getInstance().getMusicVolume();
        this.musicProgressBar.progress = Audio.getInstance().getMusicVolume();
        this.effectSlider.progress = Audio.getInstance().getEffectVolume();
        this.effectProgressBar.progress = Audio.getInstance().getEffectVolume();
        this.btnService.active = Channel.getInstance().getConfig().supportNsnHelp();
        this.btnAccountBind.active = !!Channel.getInstance().getConfig().getBindType().length;
        this.btnSocial.active = !!Channel.getInstance().getConfig().getSocialType().length;
        this.btnWxAdd.active = Platform.getInstance().isWechatMinigame();
    }

    protected onDestroy(): void {
        Audio.getInstance().setMusicVolume(this.musicSlider.progress);
        Audio.getInstance().setEffectVolume(this.effectSlider.progress);
    }

    private updateTabState(): void {
        this.languages.forEach((v, index) => {
            v.child("select").active = index === this.languageTabType;
        });
    }

    protected onClickDebug(): void {
        this.version.getComponent(DebugEntry).increaseSecretTouchCount1();
    }

    protected onLanguageToggleChanged(event: cc.Event.EventTouch, type: number): void {
        if (this.languageTabType === type) {
            return;
        }
        this.languageTabType = type;
        this.updateTabState();
        const language = LANGUAGE[type];
        const curLanguage = Language.getInstance().readLanguage() || Language.getInstance().getLanguage();
        if (language !== curLanguage) {
            Language.getInstance().saveLanguage(language);
            Tips.getInstance().show(i18n.setting0009);
        }
    }

    protected onClickExit(): void {
        Login.getInstance().setAutoLogin(false);
        SceneUtils.exitInGameScene();
    }

    protected onClickDelete(): void {
        UI.getInstance().open("PopupDeleteAccount");
    }

    protected onClickCDK(): void {
        UI.getInstance().open("PopupExchangeCode");
    }

    protected onClickService(): void {
        NsnHelp.getInstance().show(NsnHelpEntranceId.Settings);
    }

    protected onClickAccountBind(): void {
        UI.getInstance().open("FloatAccountBind");
    }

    protected onClickSocial(): void {
        UI.getInstance().open("PopupSocial");
    }

    protected onClickWxAdd(): void {
        UI.getInstance().open("PopupWxFavorite");
    }

    protected onClickUserProtocol(): void {
        GameProtocol.openUserProtocol();
    }

    protected onClickPrivateProtocol(): void {
        GameProtocol.openSensitiveProtocol();
    }

    protected onMusicSliderChanged(s: cc.Slider): void {
        this.musicProgressBar.progress = s.progress;
        Audio.getInstance().setMusicVolume(s.progress, false);
    }

    protected onEffectSliderChanged(s: cc.Slider): void {
        this.effectProgressBar.progress = s.progress;
        Audio.getInstance().setEffectVolume(s.progress, false);
    }
}
