/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-21 16:57:31
 */

import Channel from "../../../nsn/config/Channel";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import Platform from "../../../nsn/platform/Platform";
import UI from "../../../nsn/ui/UI";
import Tips from "../../../nsn/util/Tips";
import {
    LeadSkinMagicalRet,
    PetMagicalRet,
    PlayerInfoReplaceRet,
    PlayerRenameRet,
    TankMagicalRet,
    WeaponMagicalRet,
    WingsMagicalRet,
} from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { EnumTaskDetailType } from "../../data/base/BaseTaskDetail";
import { GAME_SWITCH_ID } from "../../data/parser/TBGameSwitch";
import TBLeadSkin from "../../data/parser/TBLeadSkin";
import TBPet from "../../data/parser/TBPet";
import TBPopup from "../../data/parser/TBPopup";
import TBTank from "../../data/parser/TBTank";
import TBWeapon from "../../data/parser/TBWeapon";
import TBWing from "../../data/parser/TBWing";
import Bulletin from "../../game/Bulletin";
import CombatScore from "../../game/CombatScore";
import GameSwitch from "../../game/GameSwitch";
import LeadSkin from "../../game/LeadSkin";
import Pet from "../../game/Pet";
import Player from "../../game/Player";
import Recharge from "../../game/Recharge";
import Tank from "../../game/Tank";
import Task from "../../game/Task";
import Union from "../../game/Union";
import Weapon from "../../game/Weapon";
import Wing from "../../game/Wing";
import CombatScoreUtils from "../../utils/CombatScoreUtils";
import PlayerInfoUtils from "../../utils/PlayerInfoUtils";
import SpineUtils from "../../utils/SpineUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabPlayerInfo extends I18nComponent {
    @property(cc.Node)
    tank: cc.Node = null;
    @property(cc.Node)
    nodeLead: cc.Node = null;
    @property(sp.Skeleton)
    spineLead: sp.Skeleton = null; // 主角
    @property(sp.Skeleton)
    spineWeapon: sp.Skeleton = null; // 武器
    @property(sp.Skeleton)
    spineWing: sp.Skeleton = null; // 背饰
    @property(cc.Node)
    pet: cc.Node = null;
    @property(cc.Node)
    btnMail: cc.Node = null;
    @property(cc.Node)
    btnBulletin: cc.Node = null;
    @property(cc.Node)
    btnVipCustomerService: cc.Node = null;
    @property(cc.Node)
    nodeHead: cc.Node = null;
    @property(cc.Node)
    playerName: cc.Node = null;
    @property(cc.Node)
    playerId: cc.Node = null;
    @property(cc.Node)
    titleIcon: cc.Node = null;
    @property(cc.Node)
    serverName: cc.Node = null;
    @property(cc.Node)
    powerIcon: cc.Node = null;
    @property(cc.Node)
    unionName: cc.Node = null;
    @property(cc.Node)
    combat: cc.Node = null;

    protected onLoad(): void {
        this.registerHandler();
        this.updatePlayerInfo();
    }

    protected registerHandler(): void {
        Player.getInstance().on(
            PlayerRenameRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        LeadSkin.getInstance().on(
            LeadSkinMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        // 玩家-信息替换
        Player.getInstance().on(
            PlayerInfoReplaceRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        Tank.getInstance().on(
            TankMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        Weapon.getInstance().on(
            WeaponMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        Wing.getInstance().on(
            WingsMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
        Pet.getInstance().on(
            PetMagicalRet.prototype.clazzName,
            () => {
                this.updatePlayerInfo();
            },
            this
        );
    }

    private updatePlayerInfo(): void {
        const info = Player.getInstance().getInfo();
        const severInfo = Player.getInstance().getServerInfo();
        // 个人信息
        PlayerInfoUtils.updateHead(this.nodeHead, info);
        this.playerName.label(info.name);
        this.playerId.label("ID：" + info.gameId);
        PlayerInfoUtils.updateTitle(this.titleIcon, info);
        this.serverName.label(severInfo.name); // 服务器名称
        PlayerInfoUtils.updatePower(this.powerIcon, info);
        const unionInfo = Union.getInstance().getInfo();
        this.unionName.label(unionInfo ? unionInfo.name : i18n.union0062);
        CombatScoreUtils.update(this.combat, CombatScore.getInstance().getScore());
        this.btnMail.spriteAsync("texture/systemEntry/iconEntranceYJ");
        this.btnBulletin.spriteAsync("texture/systemEntry/iconEntranceGG");
        const supportVipCustomerService = Channel.getInstance().getConfig().supportVipCustomerService();
        const { result } = GameSwitch.getInstance().check(GAME_SWITCH_ID.VIP_CUSTOMER_SERVICE);
        const isReachMinRecharge =
            Recharge.getInstance().getSumPrice() >=
            Math.min(
                ...TBPopup.getInstance()
                    .getDataListByUIName("PopupVipCustomerService")
                    .map((v) => v.value[0])
            );
        this.btnVipCustomerService.active = supportVipCustomerService && result && isReachMinRecharge;

        // 个性化
        const skinId = LeadSkin.getInstance().getMagicalId();
        const skinData = TBLeadSkin.getInstance().getDataById(skinId);
        SpineUtils.setLeadWithoutAniName(this.spineLead, skinData.res, () => {
            // @ts-ignore
            const attachUtil = this.spineLead.attachUtil;
            attachUtil.destroyAllAttachedNodes();
            attachUtil.generateAllAttachedNodes();

            this.spineLead.setAnimation(0, "attackWait", true);
        });

        // 穿戴神器
        if (GameSwitch.getInstance().check(GAME_SWITCH_ID.WEAPON).result) {
            const weaponId = Weapon.getInstance().getMagicalId();
            const weaponData = TBWeapon.getInstance().getDataById(weaponId);
            SpineUtils.setWeaponWithoutAniName(this.spineWeapon, weaponData.res, () => {
                // @ts-ignore
                const attachUtil = this.spineWeapon.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                const boneHand = this.spineWeapon.findBone("hand");
                this.spineWeapon.node.setPosition(cc.v2(-boneHand.x / boneHand.scaleX, -boneHand.y / boneHand.scaleY));

                this.spineWeapon.setAnimation(0, "attackWait", true);
            });
        } else {
            const weaponData = TBWeapon.getInstance().getDataById(skinData.initialWeapon);
            SpineUtils.setWeaponWithoutAniName(this.spineWeapon, weaponData.res, () => {
                // @ts-ignore
                const attachUtil = this.spineWeapon.attachUtil;
                attachUtil.destroyAllAttachedNodes();
                attachUtil.generateAllAttachedNodes();

                const boneHand = this.spineWeapon.findBone("hand");
                this.spineWeapon.node.setPosition(cc.v2(-boneHand.x / boneHand.scaleX, -boneHand.y / boneHand.scaleY));

                this.spineWeapon.setAnimation(0, "attackWait", true);
            });
        }

        // 穿戴翅膀
        if (GameSwitch.getInstance().check(GAME_SWITCH_ID.WING).result) {
            const wingId = Wing.getInstance().getMagicalId();
            if (wingId !== 0) {
                const wingData = TBWing.getInstance().getDataById(wingId);
                SpineUtils.setWingWithoutAniName(this.spineWing, wingData.res, () => {
                    this.spineWing.setAnimation(0, "attackWait", true);
                });
            }
        }

        if (GameSwitch.getInstance().check(GAME_SWITCH_ID.TANK).result) {
            const tankId = Tank.getInstance().getMagicalId();
            const tankData = TBTank.getInstance().getDataById(tankId);
            SpineUtils.setTank(this.tank, tankData.res);
            this.nodeLead.x = -202;
        } else {
            this.nodeLead.x = 0;
        }

        if (GameSwitch.getInstance().check(GAME_SWITCH_ID.PET).result && Pet.getInstance().getHistoryPetIds().length) {
            const petId = Pet.getInstance().getMagicalId();
            const petData = TBPet.getInstance().getDataById(petId);
            SpineUtils.setPet(this.pet, petData.res);
        }
    }

    protected onClickPlayerRename(): void {
        Task.getInstance().setTaskProgress(EnumTaskDetailType.ClickRenameTask, 1);
        UI.getInstance().open("FloatPlayerRename");
    }

    protected onClickCopyID(): void {
        const id = Player.getInstance().getGameId();
        Platform.getInstance().copyToClipboard(id + "");
    }

    /**
     * 点击个性化
     */
    protected onClickPersonal(): void {
        const { result, msg } = GameSwitch.getInstance().check(GAME_SWITCH_ID.LEAD_PERSONALIZED);
        if (!result) {
            Tips.getInstance().show(msg);
            return;
        }
        UI.getInstance().open("PopupPersonalized");
    }

    /**
     * 属性显示
     */
    protected onClickAttrShow(): void {
        UI.getInstance().open("PopupAttrShowTotal");
    }

    /**
     * 邮件
     */
    protected onClickMail(): void {
        UI.getInstance().open("PopupMail");
    }

    /**
     * 公告
     */
    public onClickBulletin(): void {
        UI.getInstance().showLoading(i18n.bulletin0003);
        Bulletin.getInstance().request(() => {
            UI.getInstance().hideLoading();
            const data = Bulletin.getInstance().getBulletinInfo();
            if (!data || !data.length) {
                Tips.getInstance().show(i18n.bulletin0001);
                return;
            }
            UI.getInstance().open("PopupBulletin");
        });
    }

    /**
     * vip客服
     */
    protected onClickVipCustomerService(): void {
        UI.getInstance().open("PopupVipCustomerService");
    }
}
