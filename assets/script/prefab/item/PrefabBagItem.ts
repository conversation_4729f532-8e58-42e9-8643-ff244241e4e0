/*
 * @Author: zhangwj
 * @Date: 2024-6-14 09:55:11
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-15 14:33:43
 */

import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import { IItemInfo } from "../../../protobuf/proto";
import { EnumItemEffectType, EnumItemType } from "../../data/base/BaseItem";
import TBItem from "../../data/parser/TBItem";
import TBItemChange from "../../data/parser/TBItemChange";
import Bag from "../../game/Bag";
import ItemUtils from "../../utils/ItemUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabBagItem extends ListViewItem {
    @property(cc.Node)
    nodeItem: cc.Node = null;
    @property(cc.Node)
    btnItem: cc.Node = null;

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;
    @property(cc.Node)
    redDot: cc.Node = null;

    public unuse(): void {}
    public reuse(): void {}

    private itemId: number = 0;

    public updateData(data: IItemInfo): void {
        this.itemId = data.itemInfoId;
        ItemUtils.refreshView(this.nodeItem, this.prefabItem, [data]);
        const showRed = this.showRed();
        this.redDot.active = showRed;
    }

    private showRed(): boolean {
        let show = false;
        const itemTB = TBItem.getInstance().getDataById(this.itemId);
        switch (itemTB.type) {
            case EnumItemType.Box:
                show = true;
                break;
            default:
                break;
        }

        switch (itemTB.effectType) {
            case EnumItemEffectType.PetFragment:
                const data = TBItemChange.getInstance().getDataById(this.itemId);
                show = Math.floor(Bag.getInstance().getItemCountById(this.itemId) / Math.floor(1 / data.change)) >= 1;
                break;
            default:
                break;
        }
        return show;
    }

    protected onClickItem(): void {
        const itemTB = TBItem.getInstance().getDataById(this.itemId);

        switch (itemTB.effectType) {
            case EnumItemEffectType.DiyBox:
                UI.getInstance().open("FloatDiyBoxConfirm", { id: this.itemId });
                break;
            case EnumItemEffectType.RandomBox:
                if (itemTB.type === EnumItemType.PetEgg) {
                    UI.getInstance().open("FloatItemDetail", { itemId: this.itemId }); // 宠物蛋不走随机宝箱
                } else {
                    UI.getInstance().open("FloatRandomBox", { id: this.itemId });
                }
                break;
            case EnumItemEffectType.PetFragment:
                UI.getInstance().open("FloatBagExchange", { id: this.itemId });
                break;
            case EnumItemEffectType.ReputationPackageType:
                UI.getInstance().open("PopupItemUse", {
                    itemInfoId: this.itemId,
                    useItem: (count: number) => {
                        Bag.getInstance().sendBagUseProp(this.itemId, count);
                    },
                });
                break;
            default:
                ItemUtils.showInfo(this.itemId);
                break;
        }
    }
}
