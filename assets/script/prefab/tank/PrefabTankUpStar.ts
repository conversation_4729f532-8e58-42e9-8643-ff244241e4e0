/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-03-28 18:20:12
 */

import ListView from "../../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import CocosExt from "../../../nsn/util/CocosExt";
import TextUtils from "../../../nsn/util/TextUtils";
import Tips from "../../../nsn/util/Tips";
import { TankBattleRet, TankCreateRet, TankUpgradeStarRet } from "../../../protobuf/proto";
import GrayComp from "../../comp/GrayComp";
import i18n from "../../config/i18n/I18n";
import { EnumTankType } from "../../data/base/BaseTank";
import DataTank from "../../data/extend/DataTank";
import TBAttribute from "../../data/parser/TBAttribute";
import { JumpType } from "../../data/parser/TBJump";
import TBRule, { RULE_ID } from "../../data/parser/TBRule";
import TBSkill, { EnumSkillParamLevelType } from "../../data/parser/TBSkill";
import TBTank from "../../data/parser/TBTank";
import TBTankStar from "../../data/parser/TBTankStar";
import Bag from "../../game/Bag";
import Skill from "../../game/Skill";
import Tank from "../../game/Tank";
import ImageUtils from "../../utils/ImageUtils";
import ItemUtils from "../../utils/ItemUtils";
import JumpUtils from "../../utils/JumpUtils";
import SpineUtils from "../../utils/SpineUtils";
import TweenUtil from "../../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabTankUpStar extends I18nComponent {
    @property(cc.Node)
    spTitle: cc.Node = null;
    @property(cc.Node)
    tankSpine: cc.Node = null;
    @property(cc.Node)
    fateNode: cc.Node = null;
    @property(cc.Node)
    tankQuality: cc.Node = null;
    @property(cc.Node)
    stars: cc.Node = null;

    @property(cc.Node)
    tankName: cc.Node = null;
    @property(cc.Node)
    playIcon1: cc.Node = null;
    @property(cc.Node)
    playIcon2: cc.Node = null;
    @property(cc.Node)
    skillIcon: cc.Node = null;
    @property(cc.Node)
    skillName: cc.Node = null;
    @property(cc.Node)
    skillDetails: cc.Node = null;

    @property(cc.Node)
    attrNode: cc.Node = null;
    @property([cc.Node])
    attrs: cc.Node[] = [];
    @property(cc.Node)
    curLevel: cc.Node = null;
    @property(cc.Node)
    nextLevel: cc.Node = null;
    @property(sp.Skeleton)
    attrLight: sp.Skeleton = null;
    @property(ListView)
    listView: ListView = null;
    @property(cc.Node)
    btnUpStar: cc.Node = null;
    @property(cc.Node)
    btnJump: cc.Node = null;
    @property(cc.Node)
    jumpText: cc.Node = null;
    @property(cc.Node)
    upStarCost: cc.Node = null;
    @property(cc.Node)
    upStarCostIcon: cc.Node = null;
    @property(cc.Node)
    upStarCostCount: cc.Node = null;
    @property(cc.Node)
    btnUpStarLight: cc.Node = null;

    private curIndex: number = 0; // 当前选中的技能
    private data: DataTank[] = null; // 当前拥有的技能

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.spTitle,
                url: `texture/syncUI/tank/spChariotTitle`,
            },
        ];
    }

    private registerHandler(): void {
        Tank.getInstance().on(
            TankUpgradeStarRet.prototype.clazzName,
            (data: TankUpgradeStarRet) => {
                UI.getInstance().open("FloatTankUpStar", data.tankId);
                this.playUpgradeAnimation();
                this.updateUI();
                this.listView.setListData(this.data);
            },
            this
        );
        Tank.getInstance().on(
            TankBattleRet.prototype.clazzName,
            () => {
                Tips.getInstance().show(i18n.tank0012);
                this.updateUI();
                this.listView.setListData(this.data);
            },
            this
        );
        Tank.getInstance().on(
            TankCreateRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    protected onLoad(): void {
        this.registerHandler();
    }

    protected onEnable(): void {
        this.initList();
        this.updateUI();
    }

    protected onDisable(): void {
        Tank.getInstance().deleteAllNewTankInfos(); // 退出页面时清除所有新战车红点
    }

    private initList(): void {
        this.curIndex = 0;
        const tankData = TBTank.getInstance().getDataByType(EnumTankType.AdvancedTank);
        const isLockData = [];
        const isUnlockData = [];
        for (const e of tankData) {
            const data = Tank.getInstance().getInfoById(e.id);
            if (data) {
                isLockData.push(e);
            } else {
                isUnlockData.push(e);
            }
        }

        isLockData.sort((a, b) => {
            const aInfo = TBTank.getInstance().getDataById(a.id);
            const bInfo = TBTank.getInstance().getDataById(b.id);
            if (aInfo.quality !== bInfo.quality) {
                return b.quality - a.quality;
            }
            return a.id - b.id;
        });

        isUnlockData.sort((a, b) => {
            const aInfo = TBTank.getInstance().getDataById(a.id);
            const bInfo = TBTank.getInstance().getDataById(b.id);
            if (aInfo.quality !== bInfo.quality) {
                return b.quality - a.quality;
            }
            return a.id - b.id;
        });
        this.data = isLockData.concat(isUnlockData);
        const data = Tank.getInstance().getData();
        const findIndex = this.data.findIndex((item) => item.id === data.magicalId);
        if (findIndex !== -1) {
            const currentTank = this.data[findIndex]; // 如果已出战 将该项放置在数组第一项
            this.data.splice(findIndex, 1);
            this.data.unshift(currentTank);
        }
        this.listView.setListData(this.data);
        this.listView.scrollTo(0, 0.2);
    }

    private updateUI(): void {
        this.updateFateAttr();
        this.updateTankInfoUI();
        this.updateSkillUI();
        this.updateAttrUI();
        this.updateUpStarBtn();
    }

    private updateFateAttr(): void {
        const { id, quality } = this.data[this.curIndex];
        const info = Tank.getInstance().getInfoById(id);
        const starData = TBTankStar.getInstance().getTankStarByQualityAndStar(quality, info ? info.star : 0);
        if (starData.attribute.length > 1) {
            const fateAttr = TBAttribute.getInstance().getFateAttrs(starData.attribute);
            if (fateAttr) {
                this.fateNode.active = true;
                const { name, value } = TBAttribute.getInstance().formatAttribute(fateAttr);
                this.fateNode.child("text").label(name + "+" + value);
            } else {
                this.fateNode.active = false;
            }
        } else {
            this.fateNode.active = false;
        }
    }

    private updateTankInfoUI(): void {
        const { id, quality, res } = this.data[this.curIndex];
        SpineUtils.setTank(this.tankSpine, res);
        ImageUtils.setQuality6(this.tankQuality, quality);
        const isWear = Tank.getInstance().isWearById(id); // 是否穿戴
        const isOwn = Tank.getInstance().own(id);
        const data = TBTank.getInstance().getDataById(id);
        this.tankName.label(data.name);
        const url = isWear ? "texture/syncUI/tank/btnChariotGo2" : "texture/syncUI/tank/btnChariotGo1";
        if (isOwn) {
            if (isWear) {
                this.playIcon1.active = true;
                this.playIcon2.active = false;
                CocosExt.setSpriteFrameAsync(this.playIcon1, url);
            } else {
                this.playIcon1.active = false;
                this.playIcon2.active = true;
                CocosExt.setSpriteFrameAsync(this.playIcon2, url);
            }
            this.stars.active = true;
            const info = Tank.getInstance().getInfoById(id);
            ImageUtils.setStarsIcon(this.stars, info.star);
        } else {
            this.stars.active = false;
            this.playIcon1.active = false;
            this.playIcon2.active = false;
        }
    }

    private updateSkillUI(): void {
        const { id, quality } = this.data[this.curIndex];
        const data = TBTank.getInstance().getDataById(id);
        const info = Tank.getInstance().getInfoById(id);
        let curLevel = -1;
        if (info) {
            curLevel = info.star;
        } else {
            curLevel = TBTankStar.getInstance().getTankStarFirstDataByQuality(quality).star;
        }
        const { desc, res, name } = TBSkill.getInstance().getDataById(data.skillId[0]);
        const skillData = Skill.getInstance().getSkillValueById(data.skillId[0], {
            [EnumSkillParamLevelType.TankStar]: curLevel,
        });
        this.skillName.label(name);
        this.skillDetails.richText(TextUtils.format(desc, skillData));
        ImageUtils.setLeadSkillIcon(this.skillIcon, res);
    }

    private updateAttrUI(): void {
        const { id, quality } = this.data[this.curIndex];
        const info = Tank.getInstance().getInfoById(id);
        let curLevel = -1;
        if (info) {
            curLevel = info.star;
        } else {
            curLevel = TBTankStar.getInstance().getTankStarFirstDataByQuality(quality).star;
        }
        const nextLevel = curLevel + 1;
        const curData = TBTankStar.getInstance().getTankStarByQualityAndStar(quality, curLevel);
        const nextData = TBTankStar.getInstance().getTankStarByQualityAndStar(quality, nextLevel);
        this.curLevel.label(i18n.tank0008);
        if (nextData) {
            this.curLevel.x = 0;
            this.nextLevel.active = true;
            this.nextLevel.label(TextUtils.format(i18n.tank0009, nextData.star));
        } else {
            this.curLevel.x = 290;
            this.nextLevel.active = false;
        }
        for (let i = 0; i < this.attrs.length; i++) {
            const { name, value: curValue } = TBAttribute.getInstance().formatAttribute(curData.attribute[i]);
            const v1 = this.attrs[i].child("v1");
            const v2 = this.attrs[i].child("v2");
            const v3 = this.attrs[i].child("v3");
            const arrow = this.attrs[i].child("arrow");
            v1.label(name);
            v2.label(curValue);
            if (nextData) {
                v2.x = 0;
                v3.active = true;
                arrow.active = true;
                const { value: nextValue } = TBAttribute.getInstance().formatAttribute(nextData.attribute[i]);
                v3.label(nextValue);
            } else {
                v2.x = 290;
                v3.active = false;
                arrow.active = false;
            }
        }
    }

    private updateUpStarBtn(): void {
        const { id, quality } = this.data[this.curIndex];
        const info = Tank.getInstance().getInfoById(id);
        let curLevel = -1;
        if (info) {
            curLevel = info.star;
        } else {
            curLevel = TBTankStar.getInstance().getTankStarFirstDataByQuality(quality).star;
        }

        const nextLevel = curLevel + 1;
        const nextData = TBTankStar.getInstance().getTankStarByQualityAndStar(quality, nextLevel);
        const maxLevel = TBTankStar.getInstance().getTankStarLastDataByQuality(quality);
        if (info) {
            this.btnUpStar.active = true;
            this.btnJump.active = false;
            this.jumpText.active = false;
            if (nextData && nextLevel <= maxLevel.star) {
                this.upStarCost.active = true;
                this.upStarCostIcon.active = true;
                this.upStarCostCount.active = true;
                this.btnUpStar.child("text").label(i18n.tank0010);
                this.btnUpStar.getComponent(GrayComp).gray = false;
                this.btnUpStar.getComponent(cc.Button).interactable = true;
                ImageUtils.setItemIcon(this.upStarCostIcon, id);
                ItemUtils.refreshCount(this.upStarCostCount, id, nextData.upgradeCost);
                this.btnUpStarLight.active = Bag.getInstance().isEnough(id, nextData.upgradeCost);
                if (this.btnUpStarLight.active) {
                    this.btnUpStarLight.stopAllActions();
                    TweenUtil.breath(this.btnUpStarLight);
                }
            } else {
                this.upStarCost.active = false;
                this.upStarCostIcon.active = false;
                this.upStarCostCount.active = false;
                this.btnUpStarLight.active = false;
                this.btnUpStar.child("text").label(i18n.common0064);
                this.btnUpStar.getComponent(GrayComp).gray = true;
                this.btnUpStar.getComponent(cc.Button).interactable = false;
            }
        } else {
            this.btnUpStar.active = false;
            this.upStarCost.active = false;
            this.upStarCostIcon.active = false;
            this.upStarCostCount.active = false;
            this.btnJump.active = true;
            this.jumpText.active = true;
            const data = TBTank.getInstance().getDataById(id);
            this.jumpText.label(data.desc);
        }
    }

    /**
     * 升级扫光
     */
    private playUpgradeAnimation(): void {
        this.attrLight.node.active = true;
        this.attrLight.setAnimation(0, "wait", false);
        this.attrLight.setCompleteListener(() => {
            this.attrLight.setCompleteListener(null);
            this.attrLight.node.active = false;
        });
    }

    protected onRenderEvent(nodeItem: cc.Node, index: number): void {
        index = Math.abs(index);
        const info = Tank.getInstance().getInfoById(this.data[index].id);
        const isWear = Tank.getInstance().isWearById(this.data[index].id);
        const isOwn = Tank.getInstance().own(this.data[index].id);
        const isUpStar = this.isUpStar(this.data[index].id);
        CocosExt.setButtonData(nodeItem, index);
        const light = nodeItem.child("light");
        const quality = nodeItem.child("quality");
        const icon = nodeItem.child("icon");
        const status = nodeItem.child("status");
        const mask = nodeItem.child("mask");
        const lock = nodeItem.child("lock");
        const spRed = nodeItem.child("spRed");
        const starts = nodeItem.child("starts");
        const data = TBTank.getInstance().getDataById(this.data[index].id);
        light.active = this.curIndex === index;
        status.active = isOwn && isWear;
        ImageUtils.setQuality7(quality, data.quality);
        ImageUtils.setTankIcon(icon, data.res);
        mask.active = lock.active = !info;
        spRed.active = Tank.getInstance().isNewTankById(this.data[index].id) || isUpStar;
        ImageUtils.setStarsIcon(starts, info ? info.star : 0);
    }

    private isUpStar(id: number): boolean {
        const info = Tank.getInstance().getInfoById(id);
        const data = TBTank.getInstance().getDataById(id);
        if (info) {
            const tbStarData = TBTankStar.getInstance().getTankStarByQualityAndStar(data.quality, info.star + 1);
            const count = Bag.getInstance().getItemCountById(info.tankId);
            if (tbStarData && count >= tbStarData.upgradeCost) {
                return true;
            }
        }
        return false;
    }

    protected onClickCheck(event: cc.Event.EventTouch, index: number): void {
        this.curIndex = index;
        this.updateUI();
        Tank.getInstance().deleteNewTankInfoByID(this.data[index].id); // 点击战车时清除新战车红点
        this.listView.setListData(this.data);
    }

    protected onClickUpStar(): void {
        const id = this.data[this.curIndex].id;
        const info = Tank.getInstance().getInfoById(id);
        const tbTank = TBTank.getInstance().getDataById(id);
        const tbStart = TBTankStar.getInstance().getTankStarByQualityAndStar(tbTank.quality, info.star + 1);
        if (!tbStart) {
            return;
        }

        if (!Bag.getInstance().isEnough(id, tbStart.upgradeCost)) {
            UI.getInstance().open("FloatItemSource", id);
            return;
        }

        Tank.getInstance().sendTankUpgradeStar(id);
    }

    protected onClickJump(): void {
        const data = TBTank.getInstance().getDataById(this.data[this.curIndex].id);
        if (!data.jumpId) {
            Tips.getInstance().show(i18n.activity0019);
            return;
        }
        JumpUtils.jump(JumpType.System, data.jumpId);
    }

    public onClickWear(): void {
        Tank.getInstance().sendTankBattle(this.data[this.curIndex].id); // 出战
    }

    protected onClickPreview(): void {
        UI.getInstance().open("PopupTankStarPreview", this.data[this.curIndex].id);
    }

    protected onClickClose(): void {
        UI.getInstance().close();
    }

    protected onClickFate(): void {
        UI.getInstance().open("FloatTankFatePreview", this.data[this.curIndex].id);
    }

    protected onClickBtnRule(): void {
        const data = TBRule.getInstance().getDataById(RULE_ID.TANK);
        if (!data) {
            return;
        }
        UI.getInstance().open("FloatRule", data);
    }
}
