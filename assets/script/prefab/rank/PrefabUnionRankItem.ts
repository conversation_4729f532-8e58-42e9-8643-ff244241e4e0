/*
 * @Author: linds
 * @Date: 2023-07-25 13:54:51
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-15 15:13:14
 */

import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import { IRankUnionItem } from "../../../protobuf/proto";
import TBUnionLevel from "../../data/parser/TBUnionLevel";
import ImageUtils from "../../utils/ImageUtils";
import NumberUtils from "../../utils/NumberUtils";

const COLOR = ["#F8CB3A", "#A8E7FF", "#F7CAA1", "#E3CEC5"];

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabUnionRankItem extends ListViewItem {
    @property(cc.Node)
    rank: cc.Node = null;
    @property(cc.Node)
    icon: cc.Node = null;
    @property(cc.Node)
    lbtName: cc.Node = null;
    @property(cc.Node)
    lv: cc.Node = null;
    @property(cc.Node)
    combat: cc.Node = null;
    @property([cc.SpriteFrame])
    spBg: cc.SpriteFrame[] = [];

    protected unionId: number = 0;

    public unuse(): void {}
    public reuse(): void {}

    public updateData(data: IRankUnionItem): void {
        const { union, rankNo } = data;
        this.unionId = union.id;
        const unionLevel = TBUnionLevel.getInstance().getLevelByExp(union.exp);
        ImageUtils.setUnionFlag(this.icon, union.flag);
        this.lbtName.label(union.name);
        this.rank.label(rankNo + "");
        this.lv.label("Lv." + unionLevel);
        this.combat.label(NumberUtils.format(Number(union.combatScore), 1, 0));

        switch (rankNo) {
            case 1:
                this.node.sprite(this.spBg[0]);
                this.rank.color = COLOR[0];
                break;
            case 2:
                this.node.sprite(this.spBg[1]);
                this.rank.color = COLOR[1];
                break;
            case 3:
                this.node.sprite(this.spBg[2]);
                this.rank.color = COLOR[2];
                break;
            default:
                this.node.sprite(this.spBg[3]);
                this.rank.color = COLOR[3];
                break;
        }
    }

    protected onClickItem(): void {
        UI.getInstance().open("FloatUnionRankDetails", this.unionId);
    }
}
