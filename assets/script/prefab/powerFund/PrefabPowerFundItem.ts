/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: chenx
 * @Last Modified time: 2025-06-05 18:28:13
 */

import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import TextUtils from "../../../nsn/util/TextUtils";
import { IItemInfo } from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { EnumFundMarkupType } from "../../data/base/BaseFundMarkup";
import DataFundMarkup from "../../data/extend/DataFundMarkup";
import TBFundMarkup from "../../data/parser/TBFundMarkup";
import { ITEM_ID } from "../../data/parser/TBItem";
import TBPack from "../../data/parser/TBPack";
import TBRecharge, { RECHARGE_ID } from "../../data/parser/TBRecharge";
import <PERSON>Arrow from "../../game/MakeArrow";
import Recharge from "../../game/Recharge";
import ImageUtils from "../../utils/ImageUtils";
import ItemUtils from "../../utils/ItemUtils";
import NumberUtils from "../../utils/NumberUtils";

const { ccclass, property } = cc._decorator;

interface IUIState {
    progressTextColor: cc.Color;
    progressIcon: number;
    itemBg: number;
    costColor: cc.Color;
    costTextColor: cc.Color;
    desc: string;
}

const UI_STATE: IUIState[] = [
    {
        progressTextColor: cc.color(127, 87, 68),
        itemBg: 0,
        progressIcon: 0,
        costColor: cc.color(194, 135, 107),
        costTextColor: cc.color(183, 134, 112),
        desc: i18n.powerFund0001,
    },
    {
        progressTextColor: cc.color(127, 87, 68),
        itemBg: 1,
        progressIcon: 1,
        costColor: cc.color(106, 72, 54),
        costTextColor: cc.color(135, 97, 62),
        desc: i18n.powerFund0002,
    },
    {
        progressTextColor: cc.color(217, 167, 143),
        itemBg: 1,
        progressIcon: 2,
        costColor: cc.color(106, 72, 54),
        costTextColor: cc.color(135, 97, 62),
        desc: i18n.powerFund0002,
    },
];

@ccclass
export default class PrefabPowerFundItem extends ListViewItem {
    @property(cc.Node)
    itemBg: cc.Node = null;
    @property(cc.Node)
    costIcon: cc.Node = null;
    @property(cc.Node)
    costCount: cc.Node = null;
    @property(cc.Node)
    deco: cc.Node = null;
    @property(cc.Node)
    costText: cc.Node = null;
    @property(cc.Node)
    zeroCostText: cc.Node = null;
    @property(cc.Node)
    desc: cc.Node = null;
    @property(cc.Node)
    reward: cc.Node = null;
    @property(cc.Node)
    got: cc.Node = null;
    @property(cc.Node)
    red: cc.Node = null;
    @property(cc.Node)
    lock: cc.Node = null;
    @property(cc.Node)
    progress1: cc.Node = null;
    @property(cc.Node)
    progress2: cc.Node = null;
    @property(cc.Node)
    progressIcon: cc.Node = null;
    @property(cc.Node)
    progressText: cc.Node = null;

    @property([cc.SpriteFrame])
    spProgressIcon: cc.SpriteFrame[] = [];
    @property([cc.SpriteFrame])
    spItemBg: cc.SpriteFrame[] = [];

    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private fundData: DataFundMarkup = null;

    public unuse(): void {}
    public reuse(): void {}

    public updateData(data: DataFundMarkup): void {
        this.fundData = data;
        const list = TBFundMarkup.getInstance().getDataByType(EnumFundMarkupType.ReputationPackage);
        const first = list[0];
        const last = list[list.length - 1];
        if (data === first) {
            this.progress1.active = false;
            this.progress2.active = true;
        } else if (data === last) {
            this.progress1.active = true;
            this.progress2.active = false;
        } else {
            this.progress1.active = true;
            this.progress2.active = true;
        }

        let uiState: IUIState = null;
        const isRecharge = Recharge.getInstance().isRecharged(RECHARGE_ID.POWER_FUND);
        const { pack } = TBRecharge.getInstance().getDataById(RECHARGE_ID.POWER_FUND);
        const index = list.findIndex((v) => v === data);
        const packId = pack[index];
        const packData = TBPack.getInstance().getDataById(packId);

        if (!isRecharge) {
            uiState = UI_STATE[2];
            this.lock.active = true;
            this.progressText.active = true;
            this.progress1.color = cc.color(127, 87, 68);
            this.progress2.color = cc.color(127, 87, 68);
            this.got.active = false;
            this.red.active = false;
        } else {
            const count = MakeArrow.getInstance().getForgeIronCostCounts();
            const current = list.find((v) => v.cost > count);
            if (current) {
                if (data.id < current.id - 1) {
                    uiState = UI_STATE[0];
                    this.lock.active = false;
                    this.progressText.active = true;
                    this.progress1.color = cc.color(225, 175, 89);
                    this.progress2.color = cc.color(225, 175, 89);
                } else if (data.id === current.id - 1) {
                    uiState = UI_STATE[1];
                    this.lock.active = false;
                    this.progressText.active = false;
                    this.progress1.color = cc.color(225, 175, 89);
                    this.progress2.color = cc.color(127, 87, 68);
                } else {
                    uiState = UI_STATE[2];
                    this.lock.active = true;
                    this.progressText.active = true;
                    this.progress1.color = cc.color(127, 87, 68);
                    this.progress2.color = cc.color(127, 87, 68);
                }
            } else {
                if (data !== last) {
                    uiState = UI_STATE[0];
                    this.progressText.active = true;
                } else {
                    uiState = UI_STATE[1];
                    this.progressText.active = false;
                }
                this.lock.active = false;
                this.progress1.color = cc.color(225, 175, 89);
                this.progress2.color = cc.color(225, 175, 89);
            }

            const isEnough = MakeArrow.getInstance().getForgeIronCostCounts() >= data.cost;
            const isGot = Recharge.getInstance().getBuyPackReceived(packId);
            if (isGot) {
                this.got.active = true;
                this.red.active = false;
            } else {
                this.got.active = false;
                this.red.active = isEnough;
            }
        }
        this.itemBg.sprite(this.spItemBg[uiState.itemBg]);
        if (data.cost) {
            this.costIcon.active = true;
            this.costCount.active = true;
            this.deco.active = true;
            this.costText.active = true;
            this.zeroCostText.active = false;

            ImageUtils.setItemIcon(this.costIcon, ITEM_ID.MAKE_ARROW_STONE);
            this.costCount.label(NumberUtils.format(data.cost));
            this.costCount.color = uiState.costColor;
            this.deco.color = uiState.costColor;
            this.costText.color = uiState.costColor;
        } else {
            this.costIcon.active = false;
            this.costCount.active = false;
            this.deco.active = false;
            this.costText.active = false;
            this.zeroCostText.active = true;
        }

        ItemUtils.refreshView(this.reward, this.prefabItem, packData.reward, {
            click: (itemInfo) => {
                this.onClickGet(itemInfo);
            },
        });
        this.desc.richText(
            TextUtils.format(uiState.desc, (data.economyAttribute[0][1] * 100).toFixed(0), packData.reward[0][1])
        );
        this.progressIcon.sprite(this.spProgressIcon[uiState.progressIcon]);
        this.progressText.color = uiState.progressTextColor;
        this.progressText.label(data.id + "");
    }

    private onClickGet(itemInfo: IItemInfo): void {
        const isRecharge = Recharge.getInstance().isRecharged(RECHARGE_ID.POWER_FUND);
        if (!isRecharge) {
            UI.getInstance().open("FloatItemDetail", { itemId: itemInfo.itemInfoId });
            return;
        }

        const isEnough = MakeArrow.getInstance().getForgeIronCostCounts() >= this.fundData.cost;
        if (!isEnough) {
            UI.getInstance().open("FloatItemDetail", { itemId: itemInfo.itemInfoId });
            return;
        }
        const { pack } = TBRecharge.getInstance().getDataById(RECHARGE_ID.POWER_FUND);
        const list = TBFundMarkup.getInstance().getDataByType(EnumFundMarkupType.ReputationPackage);
        const index = list.findIndex((v) => v === this.fundData);
        const packId = pack[index];
        const isGot = Recharge.getInstance().getBuyPackReceived(packId);
        if (isGot) {
            UI.getInstance().open("FloatItemDetail", { itemId: itemInfo.itemInfoId });
            return;
        }
        Recharge.getInstance().sendRechargeReceivePack(RECHARGE_ID.POWER_FUND);
    }
}
