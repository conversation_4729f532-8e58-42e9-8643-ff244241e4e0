/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-16 14:51:07
 */

import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import TextUtils from "../../../nsn/util/TextUtils";
import Time, { HOUR_TO_SECOND } from "../../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../../nsn/util/TimeFormat";
import Tips from "../../../nsn/util/Tips";
import i18n from "../../config/i18n/I18n";
import RedPoint from "../../core/redPoint/RedPoint";
import RedPointComponent, { RedPointStruct } from "../../core/redPoint/RedPointComponent";
import { RedPointId } from "../../core/redPoint/RedPointId";
import { EnumLimitedPlayType } from "../../data/base/BaseLimitedPlay";
import { EnumUnionPara } from "../../data/base/BaseUnion";
import DataLimitedPlay from "../../data/extend/DataLimitedPlay";
import TBUnion from "../../data/parser/TBUnion";
import Activity from "../../game/Activity";
import GameSwitch from "../../game/GameSwitch";
import UnionSiege, { EnumUnionSiegeStatus } from "../../game/UnionSiege";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabActivityUnionItem extends ListViewItem {
    @property(cc.Node)
    opening: cc.Node = null;
    @property(cc.Node)
    openingText: cc.Node = null;
    @property(cc.Node)
    openTime: cc.Node = null;
    @property(cc.Node)
    red: cc.Node = null;

    private limitPlayData: DataLimitedPlay = null;
    private dt: number = 1;

    public unuse(): void {}
    public reuse(): void {}

    public updateData(data: DataLimitedPlay): void {
        this.limitPlayData = data;
        this.node.spriteAsync("texture/syncUI/union/iconUnionEventPic" + data.res);
        this.red.active = false;
        this.openTime.label(TextUtils.format(i18n.union0088, data.desc));

        this.bindRedPoint();
    }

    /**
     * 绑定红点
     */
    public bindRedPoint(): void {
        const redPointComp = this.node.getComponent(RedPointComponent);
        redPointComp.clearRedPoint();
        const redPointId = this.getRedPointId();
        if (!redPointId) {
            return;
        }
        const redData = new RedPointStruct();
        redData.id = redPointId;
        redData.node = this.red;
        redPointComp.addRedPoint(redData);
        RedPoint.getInstance().check(redPointId);
    }

    protected update(dt: number): void {
        if (!this.limitPlayData) {
            return;
        }
        this.dt += dt;
        if (this.dt < 1) {
            return;
        }
        this.dt -= 1;
        const { result } = GameSwitch.getInstance().check(this.limitPlayData.switchID);
        switch (this.limitPlayData.type) {
            case EnumLimitedPlayType.UnionChallenge:
                const thisToday = Time.getInstance().getTodayZero();
                const endPointTime = TBUnion.getInstance().getValueByPara(EnumUnionPara.BossChallengeEndTime);
                const endTime = thisToday + endPointTime * HOUR_TO_SECOND * 1000;
                const timeGap = endTime - Time.getInstance().now();
                this.opening.active = result && timeGap > 0;
                if (this.opening.active) {
                    const time = TimeFormat.getInstance().getTextByDuration(timeGap, TimeDurationFormatType.D_H_M_S_2);
                    this.openingText.label(TextUtils.format(i18n.activity0018, time));
                }
                break;
            case EnumLimitedPlayType.UnionDefense:
                const status = UnionSiege.getInstance().getStatus(this.limitPlayData.activityID);
                const isCanEnter = UnionSiege.getInstance().isCanEnter(status);
                if (!isCanEnter) {
                    this.opening.active = false;
                    return;
                }

                const { duration } = Activity.getInstance().getTimeById(this.limitPlayData.activityID);
                this.opening.active = result && duration > 0;
                if (this.opening.active) {
                    const time = TimeFormat.getInstance().getTextByDuration(duration, TimeDurationFormatType.D_H_M_S_2);
                    this.openingText.label(TextUtils.format(i18n.activity0018, time));
                }
                break;
            default:
                break;
        }
    }

    private getRedPointId(): number {
        switch (this.limitPlayData.type) {
            case EnumLimitedPlayType.UnionChallenge:
                return RedPointId.UnionBoss; // 公会boss
            case EnumLimitedPlayType.UnionDefense:
                return RedPointId.ActivityUnionDefenseAll; // 公会攻防
            default:
                break;
        }
    }

    protected onClickItem(): void {
        const { result, msg } = GameSwitch.getInstance().check(this.limitPlayData.switchID);
        if (!result) {
            Tips.getInstance().info(msg);
            return;
        }

        if (this.limitPlayData.activityID) {
            const isValid = Activity.getInstance().isValidById(this.limitPlayData.activityID);
            if (!isValid) {
                Tips.getInstance().show(i18n.activity0019);
                return;
            }
        }

        switch (this.limitPlayData.type) {
            case EnumLimitedPlayType.UnionChallenge:
                const thisToday = Time.getInstance().getTodayZero();
                const endPointTime = TBUnion.getInstance().getValueByPara(EnumUnionPara.BossChallengeEndTime);
                const endTime = thisToday + endPointTime * HOUR_TO_SECOND * 1000;
                const timeGap = endTime - Time.getInstance().now();
                if (timeGap <= 0) {
                    Tips.getInstance().show(i18n.activity0019);
                    return;
                }
                UI.getInstance().open("PopupUnionBoss", this.limitPlayData.activityID);
                break;
            case EnumLimitedPlayType.UnionDefense:
                const status = UnionSiege.getInstance().getStatus(this.limitPlayData.activityID);
                if (status === EnumUnionSiegeStatus.Status8) {
                    Tips.getInstance().show(i18n.unionDefense0007);
                    return;
                }
                if (status === EnumUnionSiegeStatus.Status9) {
                    Tips.getInstance().show(i18n.activity0019);
                    return;
                }
                UI.getInstance().showSoftLoading();
                UnionSiege.getInstance().sendUnionSiegeGet(); // 同步公会活跃度
                UI.getInstance().open("UIActivityUnionDefense", this.limitPlayData.activityID);
                break;
            default:
                break;
        }
    }
}
