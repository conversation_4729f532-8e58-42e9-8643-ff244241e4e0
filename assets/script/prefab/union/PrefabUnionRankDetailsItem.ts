/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-15 15:14:28
 */

import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import { IUnionMember } from "../../../protobuf/proto";
import TBUnionPosition from "../../data/parser/TBUnionPosition";
import CombatScoreUtils from "../../utils/CombatScoreUtils";
import PlayerInfoUtils from "../../utils/PlayerInfoUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabUnionRankDetailsItem extends ListViewItem {
    @property(cc.Node)
    icon: cc.Node = null;
    @property(cc.Node)
    lbtName: cc.Node = null;
    @property(cc.Node)
    nodeCombatScore: cc.Node = null;
    @property(cc.Node)
    power: cc.Node = null;
    @property(cc.Node)
    position: cc.Node = null;
    @property(cc.Node)
    positionName: cc.Node = null;

    private memberInfo: IUnionMember = null;

    public unuse(): void {}
    public reuse(): void {}

    public updateData(data: IUnionMember): void {
        const { player, unionPositionId } = data;
        this.memberInfo = data;
        PlayerInfoUtils.updateHead(this.icon, player);
        this.lbtName.label(player.name);
        PlayerInfoUtils.updatePower(this.power, player);
        CombatScoreUtils.update(this.nodeCombatScore, player.combatScore);

        const positionName = TBUnionPosition.getInstance().getNameOfPosition(unionPositionId);
        if (positionName) {
            this.position.active = true;
            this.positionName.label(positionName);
            this.position.spriteAsync("texture/union/position/iconUnionTag" + unionPositionId);
        } else {
            this.position.active = false;
        }
    }

    protected onClickItem(): void {
        UI.getInstance().open("FloatOtherPlayerInfo", this.memberInfo.player.playerId);
    }
}
