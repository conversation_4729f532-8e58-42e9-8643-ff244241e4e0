/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-11 17:04:13
 */

import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import TimeFormat from "../../../nsn/util/TimeFormat";
import { IUnionMember } from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import TBUnionPosition, { UNION_POSITION_ID } from "../../data/parser/TBUnionPosition";
import Union from "../../game/Union";
import { UnionOpMenu } from "../../ui/FloatUnionOpMenu";
import CombatScoreUtils from "../../utils/CombatScoreUtils";
import PlayerInfoUtils from "../../utils/PlayerInfoUtils";

const { ccclass, property } = cc._decorator;

const STATUS_COLOR = ["#688A28", "#8B8B8B"];

@ccclass
export default class PrefabUnionMemberItem extends ListViewItem {
    @property(cc.Node)
    icon: cc.Node = null;
    @property(cc.Node)
    lbtName: cc.Node = null;
    @property(cc.Node)
    nodeCombatScore: cc.Node = null;
    @property(cc.Node)
    power: cc.Node = null;
    @property(cc.Node)
    position: cc.Node = null;
    @property(cc.Node)
    positionName: cc.Node = null;
    @property(cc.Label)
    contribution: cc.Label = null;
    @property(cc.Label)
    status: cc.Label = null;
    @property(cc.Node)
    btnMore: cc.Node = null;

    private memberInfo: IUnionMember = null;

    public unuse(): void {}
    public reuse(): void {}

    public updateData(data: IUnionMember): void {
        const { player, unionPositionId, online, loginOutTime } = data;
        this.memberInfo = data;
        PlayerInfoUtils.updateHead(this.icon, player);
        this.lbtName.label(player.name);
        CombatScoreUtils.update(this.nodeCombatScore, player.combatScore);
        PlayerInfoUtils.updatePower(this.power, player);

        this.status.string = online ? i18n.common0011 : TimeFormat.getInstance().getTextFromNow(loginOutTime);
        this.status.node.color = online ? STATUS_COLOR[0] : STATUS_COLOR[1];

        this.contribution.string = data.todayContribution + "/" + data.historyContribution;
        const positionName = TBUnionPosition.getInstance().getNameOfPosition(unionPositionId);
        if (positionName) {
            this.position.active = true;
            this.positionName.label(positionName);
            this.position.spriteAsync("texture/union/position/iconUnionTag" + unionPositionId);
        } else {
            this.position.active = false;
        }

        this.btnMore.active = !!this.getOpMenu().length;
    }

    private getOpMenu(): UnionOpMenu[] {
        const myInfo = Union.getInstance().getMyInfo();
        const unionPositionId = this.memberInfo.unionPositionId;
        let opMenus: UnionOpMenu[] = [];
        switch (myInfo.unionPositionId) {
            case UNION_POSITION_ID.OWNER:
                switch (unionPositionId) {
                    case UNION_POSITION_ID.OWNER:
                        break;
                    case UNION_POSITION_ID.VICE_OWNER:
                        opMenus = [
                            UnionOpMenu.SetMember,
                            UnionOpMenu.SetElite,
                            UnionOpMenu.SetOwner,
                            UnionOpMenu.KickOut,
                        ];
                        break;
                    case UNION_POSITION_ID.ELITE:
                        opMenus = [
                            UnionOpMenu.SetMember,
                            UnionOpMenu.SetViceOwner,
                            UnionOpMenu.SetOwner,
                            UnionOpMenu.KickOut,
                        ];
                        break;
                    case UNION_POSITION_ID.MEMBER:
                        opMenus = [
                            UnionOpMenu.SetElite,
                            UnionOpMenu.SetViceOwner,
                            UnionOpMenu.SetOwner,
                            UnionOpMenu.KickOut,
                        ];
                        break;
                    default:
                        break;
                }
                break;
            case UNION_POSITION_ID.VICE_OWNER:
                switch (unionPositionId) {
                    case UNION_POSITION_ID.OWNER:
                    case UNION_POSITION_ID.VICE_OWNER:
                        break;
                    case UNION_POSITION_ID.ELITE:
                        opMenus = [UnionOpMenu.SetMember, UnionOpMenu.KickOut];
                        break;
                    case UNION_POSITION_ID.MEMBER:
                        opMenus = [UnionOpMenu.SetElite, UnionOpMenu.KickOut];
                        break;
                    default:
                        break;
                }
                break;
            case UNION_POSITION_ID.ELITE:
            case UNION_POSITION_ID.MEMBER:
                break;
            default:
                break;
        }

        return opMenus;
    }

    protected onClickMore(sender: cc.Event.EventTouch): void {
        const pos = sender.target.convertToWorldSpaceAR(cc.v2());
        UI.getInstance().open("FloatUnionOpMenu", {
            pos,
            playerId: this.memberInfo.player.playerId,
            menu: this.getOpMenu(),
            offset: cc.v2(-150, 200),
        });
    }

    protected onClickItem(): void {
        UI.getInstance().open("FloatOtherPlayerInfo", this.memberInfo.player.playerId);
    }
}
