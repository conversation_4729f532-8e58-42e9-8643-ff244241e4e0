import ListView from "../../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import TextUtils from "../../../nsn/util/TextUtils";
import TimeFormat, { TimeDurationFormatType } from "../../../nsn/util/TimeFormat";
import Tips from "../../../nsn/util/Tips";
import { RechargeGetPackInfoRet, RechargeNoticeRet, RechargeReceivePackRet } from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { Sku } from "../../core/Sku";
import { EnumActivityUniversalPara } from "../../data/base/BaseActivityUniversal";
import { ACTIVITY_ID } from "../../data/parser/TBActivity";
import TBActivityUniversal from "../../data/parser/TBActivityUniversal";
import TBPack from "../../data/parser/TBPack";
import TBRecharge, { RECHARGE_ID } from "../../data/parser/TBRecharge";
import Activity from "../../game/Activity";
import Pack from "../../game/Pack";
import Recharge from "../../game/Recharge";
import ItemUtils from "../../utils/ItemUtils";
import ListViewUtils from "../../utils/ListViewUtils";
import { IPrefabFundItem } from "../fund/PrefabFundItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabWeekFund extends I18nComponent {
    @property(cc.Node)
    topBg: cc.Node = null;
    @property(cc.Node)
    time: cc.Node = null;
    @property(cc.Node)
    rewardText: cc.Node = null;
    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;
    @property(cc.Label)
    lbtPrice: cc.Label = null;
    @property(cc.Node)
    btnBuy: cc.Node = null;
    @property(cc.Node)
    level: cc.Node = null;
    @property(cc.Node)
    lockIcon: cc.Node = null;
    @property(cc.Node)
    baseReward: cc.Node = null;
    @property(cc.Node)
    advanceReward: cc.Node = null;
    @property(cc.Node)
    bottomLevel: cc.Node = null;
    @property(cc.Node)
    nodeItem: cc.Node = null;
    @property(cc.Prefab)
    prefabItem: cc.Prefab = null;

    private rechargeId: number = RECHARGE_ID.WEEK_FUND;
    private listView: ListView = null;
    private taskIndex: number = -1;
    private dt: number = 1;

    protected getI18nRes(): cc.II18nRes[] {
        return [
            {
                sprite: this.topBg,
                url: "texture/syncUI/dailyTask/spWeekBPPic",
            },
            {
                sprite: this.rewardText,
                url: "texture/syncUI/dailyTask/spWeekBPCount",
            },
        ];
    }

    protected onLoad(): void {
        this.registerHandler();

        const reTB = TBRecharge.getInstance().getDataById(this.rechargeId);
        const priceStr = Sku.getInstance().getPriceText(reTB.id);
        this.lbtPrice.string = priceStr;
        this.listView = this.scrollView.getComponent(ListView);
        this.refreshUI();
        this.scheduleOnce(() => {
            this.scrollToReceivable();
        });
        this.updateFundPreviewUI();
    }

    protected onEnable(): void {
        this.updateList();
    }

    protected registerHandler(): void {
        // 礼包信息获取
        Recharge.getInstance().on(
            [RechargeGetPackInfoRet.prototype.clazzName, RechargeReceivePackRet.prototype.clazzName],
            () => {
                this.refreshUI();
            },
            this
        );
        // 购买通知
        Recharge.getInstance().on(
            RechargeNoticeRet.prototype.clazzName,
            () => {
                this.refreshUI();
                Tips.getInstance().show(i18n.recharge0007);
            },
            this
        );
    }

    protected update(dt: number): void {
        this.dt += dt;
        if (this.dt > 1) {
            this.dt -= 1;
            const { duration } = Activity.getInstance().getTimeById(ACTIVITY_ID.WEEK_FUND);
            if (duration <= 0) {
                this.time.label(i18n.activity0001);
                return;
            }
            const time = TimeFormat.getInstance().getTextByDuration(duration, TimeDurationFormatType.D_H_M_S_2);
            this.time.label(TextUtils.format(i18n.activity0007, time));
        }
    }

    private refreshUI(): void {
        this.updateList();
        this.updateFundPreviewUI();
    }

    private updateList(): void {
        const reTB = TBRecharge.getInstance().getDataById(this.rechargeId);
        const pack = reTB.pack;
        const rechargeInfo = Recharge.getInstance().getRechargeInfo(this.rechargeId);
        const isBuy = !!rechargeInfo.buyCount;
        this.btnBuy.active = !isBuy;
        const data: IPrefabFundItem[] = [];
        let taskIndex = 0;
        for (const e of reTB.pack) {
            const { isLock } = Pack.getInstance().checkCondition(e);
            if (!isLock) {
                taskIndex++;
            }
        }
        for (let i = 0; i < pack.length; i++) {
            data.push({
                packId: pack[i],
                index: i,
                lastIndex: pack.length - 1,
                isBuy,
                rechargeId: this.rechargeId,
                isCurrent: i === taskIndex,
            });
        }
        this.listView.setListData(data);
        this.listView.scrollTo(taskIndex);
    }

    private scrollToReceivable(): void {
        const list = this.listView.getListData<IPrefabFundItem>();
        // 最近可领取的packId
        const packList = Recharge.getInstance().getReceivablePackIdByRechargeId(this.rechargeId);
        const packId = packList[0];
        let jumpData = null; // 需要跳转数据
        if (packId) {
            // 有可领奖的
            jumpData = list.find((data) => data.packId === packId);
        } else {
            // 无可领奖的 跳转到最近达成的
            for (const data of list.concat().reverse()) {
                // 倒序找
                const { isLock } = Pack.getInstance().checkCondition(data.packId);
                if (!isLock) {
                    jumpData = list.find((data) => data.packId === packId);
                    break;
                }
            }
        }
        jumpData && this.listView.scrollTo(list.indexOf(jumpData));
    }

    private updateFundPreviewUI(): void {
        const isRecharged = Recharge.getInstance().isRecharged(this.rechargeId);
        this.lockIcon.active = !isRecharged;
        const reTB = TBRecharge.getInstance().getDataById(this.rechargeId);
        let taskIndex = -1;
        for (const e of reTB.pack) {
            const { isLock } = Pack.getInstance().checkCondition(e);
            if (!isLock) {
                taskIndex++;
            }
        }
        this.taskIndex = taskIndex !== -1 ? taskIndex : 0; // 当前已完成任务索引
        const nowData = Pack.getInstance().getPlayerProgressText(reTB.pack[0]);
        this.level.label(nowData);
        const data = TBActivityUniversal.getInstance().getValueByPara(EnumActivityUniversalPara.WeekFund);
        const index = this.getCurrentFundPreview(this.taskIndex + 1, data, reTB.pack.length); // 获取当前需要展示的基金预览
        const packTB = TBPack.getInstance().getDataById(reTB.pack[index]);
        const buyReceived = Recharge.getInstance().getBuyPackReceived(reTB.pack[index]);
        ListViewUtils.refreshView({
            prefabOrNode: this.nodeItem,
            content: this.baseReward,
            list: packTB.freeReward,
            updateFunc: (node, itemData, idx) => {
                const content = node.getChildByName("content");
                const mask = node.getChildByName("mask");
                const check = node.getChildByName("check");
                ItemUtils.refreshView(content, this.prefabItem, [itemData]);
                mask.active = buyReceived;
                check.active = buyReceived;
            },
        });

        ListViewUtils.refreshView({
            prefabOrNode: this.nodeItem,
            content: this.advanceReward,
            list: packTB.reward,
            updateFunc: (node, itemData, idx) => {
                const content = node.getChildByName("content");
                const mask = node.getChildByName("mask");
                const check = node.getChildByName("check");
                ItemUtils.refreshView(content, this.prefabItem, [itemData]);
                mask.active = buyReceived;
                check.active = buyReceived;
            },
        });

        const itemPackData = Pack.getInstance().getPackProgressText(reTB.pack[index]);
        this.bottomLevel.richText(itemPackData);
    }

    /**
     * 获取当前需要展示的基金预览
     * @param playerLevel 玩家等级
     * @param configArr 配置数组
     * @param maxStage 最大阶段
     */
    private getCurrentFundPreview(playerLevel: number, configArr: number[], maxStage: number): number {
        if (configArr.length === 0) {
            return 0;
        }
        if (playerLevel === 0) {
            return 0;
        }
        if (playerLevel >= configArr[configArr.length - 1]) {
            return configArr[configArr.length - 1] - 1;
        }

        for (const e of configArr) {
            if (e > playerLevel && e <= maxStage) {
                return e - 1;
            }
        }
        return maxStage - 1; // 如果没找到 返回maxStage
    }

    protected onClickItem(): void {
        this.listView.scrollTo(this.taskIndex, 0.5); // 滚动
    }

    /**
     * 点击购买
     */
    protected onClickBuy(): void {
        Recharge.getInstance().prePay(this.rechargeId);
    }

    /**
     * 点击购买
     */
    protected onClickReceive(): void {
        Recharge.getInstance().sendRechargeReceivePack(this.rechargeId);
    }
}
