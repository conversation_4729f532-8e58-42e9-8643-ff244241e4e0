import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import TextUtils from "../../../nsn/util/TextUtils";
import Tips from "../../../nsn/util/Tips";
import i18n from "../../config/i18n/I18n";
import { JumpType } from "../../data/parser/TBJump";
import TBTaskDetail from "../../data/parser/TBTaskDetail";
import Task from "../../game/Task";
import ItemUtils from "../../utils/ItemUtils";
import JumpUtils from "../../utils/JumpUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabDailyTaskItem extends ListViewItem {
    @property(cc.Node)
    lbtName: cc.Node = null; // 任务名称
    @property(cc.ProgressBar)
    nodeProgress: cc.ProgressBar = null; // 任务进度
    @property(cc.Node)
    lbtProgress: cc.Node = null; // 任务进度（文本）
    @property(cc.Node)
    nodeRewardContent: cc.Node = null; // 奖励内容
    @property(cc.Node)
    lbtComplete: cc.Node = null; // 已完成标记
    @property(cc.Node)
    completeMask: cc.Node = null; // 已完成遮罩
    @property(cc.Node)
    btnReceive: cc.Node = null; // 领取
    @property(cc.Node)
    btnJump: cc.Node = null; // 前往
    @property(cc.Prefab)
    nodeRewardItem: cc.Prefab = null; // 奖励Item

    private clickFunc: () => void = null; // 任务ID

    public reuse(): void {}

    public unuse(): void {}

    public updateData(data: { taskDetailId: number; taskSeqId: number; clickFunc: () => void }): void {
        this.data = data;
        this.clickFunc = data.clickFunc;
        const taskDetailConfig = TBTaskDetail.getInstance().getDataById(data.taskDetailId);
        const taskInfo = Task.getInstance().getTaskInfo(data.taskDetailId);
        const receivable = (taskInfo?.progress || 0) >= taskDetailConfig.reachValue;
        this.lbtName.label(TextUtils.format(taskDetailConfig.desc, taskDetailConfig.reachValue));
        this.lbtProgress.label(
            `${Math.min(taskDetailConfig.reachValue, taskInfo?.progress || 0)}/${taskDetailConfig.reachValue}`
        );
        this.nodeProgress.node.active = !taskInfo || !taskInfo.isAwarded;
        this.nodeProgress.progress = Math.min(1, (taskInfo?.progress || 0) / taskDetailConfig.reachValue);
        this.lbtComplete.active = taskInfo && taskInfo.isAwarded;
        this.completeMask.active = taskInfo && taskInfo.isAwarded;
        this.btnReceive.active =
            (!taskInfo || !taskInfo.isAwarded) && // 未领取奖励
            receivable;
        this.btnReceive.button(data.taskDetailId);
        this.btnJump.button(taskDetailConfig.jumpId);
        this.btnJump.active =
            (!taskInfo || !taskInfo.isAwarded) && // 未领取奖励
            !receivable; // 任务进度还未达成
        ItemUtils.refreshView(this.nodeRewardContent, this.nodeRewardItem, taskDetailConfig.reward);
    }

    /**
     * 仅更新任务进度
     */
    public updateProgress(): void {
        const taskDetailConfig = TBTaskDetail.getInstance().getDataById(this.data.taskDetailId);
        const taskDetailData = Task.getInstance().getTaskInfo(this.data.taskDetailId);
        const receivable = (taskDetailData?.progress || 0) >= taskDetailConfig.reachValue; // 是否可领取

        // 进度文本
        this.lbtProgress.label(
            `${Math.min(taskDetailConfig.reachValue, taskDetailData?.progress || 0)}/${taskDetailConfig.reachValue}`
        );

        // 进度条
        this.nodeProgress.progress = Math.min(1, (taskDetailData?.progress || 0) / taskDetailConfig.reachValue);

        // 领取奖励按钮
        this.btnReceive.active =
            (!taskDetailData || !taskDetailData.isAwarded) && // 未领取奖励
            receivable;

        // 跳转按钮
        this.btnJump.active =
            (!taskDetailData || !taskDetailData.isAwarded) && // 未领取奖励
            !receivable; // 任务进度还未达成
    }

    protected onClickReceive(sender: cc.Event.EventTouch, taskDetailId: number): void {
        const taskDetailConfig = TBTaskDetail.getInstance().getDataById(taskDetailId);
        const taskInfo = Task.getInstance().getTaskInfo(taskDetailId);
        if ((taskInfo?.progress || 0) < taskDetailConfig.reachValue) {
            Tips.getInstance().info(i18n.task0003);
            return;
        }
        const taskDetailData = Task.getInstance().getTaskInfo(taskDetailId);
        if (taskDetailData && taskDetailData.isAwarded) {
            Tips.getInstance().info(i18n.task0002);
            return;
        }
        this.clickFunc && this.clickFunc();
        // Task.getInstance().batchTaskAward(this.getTaskActivityId(), EnumActivityItemType.Task, this.curTab);
    }

    protected onClickOpenItemInfo(sender: cc.Event.EventTouch, itemInfoId: number): void {
        ItemUtils.showInfo(itemInfoId);
    }

    protected onClickJump(sender: cc.Event.EventTouch, jumpId: number): void {
        JumpUtils.jump(JumpType.System, jumpId);
    }
}
