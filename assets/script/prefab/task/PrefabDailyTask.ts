import ListView from "../../../nsn/comp/3rd/List/ListView";
import Loader from "../../../nsn/core/Loader";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import CocosExt from "../../../nsn/util/CocosExt";
import { TaskBatchTakeAwardRet, TaskTakeAwardRet, TaskUpdateRet } from "../../../protobuf/proto";
import { EnumTaskSeqModule } from "../../data/base/BaseTaskSeq";
import { ACTIVITY_ID } from "../../data/parser/TBActivity";
import TBItem from "../../data/parser/TBItem";
import TBTaskDetail from "../../data/parser/TBTaskDetail";
import TBTaskGroup from "../../data/parser/TBTaskGroup";
import TBTaskSeq from "../../data/parser/TBTaskSeq";
import Task from "../../game/Task";
import ImageUtils from "../../utils/ImageUtils";
import ItemUtils from "../../utils/ItemUtils";
import PrefabDailyTaskItem from "./PrefabDailyTaskItem";

const PROGRESS_ICON_STATUS = [0, 1]; // 进度条图标状态 0:未完成 1:已完成

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabDailyTask extends I18nComponent {
    @property(cc.Node)
    lbtActive: cc.Node = null; // 活跃度
    @property(cc.Node)
    nodeRewardContent: cc.Node = null; // 进度任务奖励列表
    @property(cc.Node)
    nodeRewardItem: cc.Node = null; // 进度任务奖励Item
    @property(cc.ProgressBar)
    nodeProgress: cc.ProgressBar = null; // 进度条
    @property(ListView)
    nodeTaskContent: ListView = null; // 任务列表
    @property([cc.SpriteFrame])
    progressIcons: cc.SpriteFrame[] = []; // 进度条资源图标

    private curTab: EnumTaskSeqModule = EnumTaskSeqModule.DailyTask;
    private activityId: number = ACTIVITY_ID.DAILY_TASK;
    private activityProgressId: number = ACTIVITY_ID.DAILY_PROGRESS_TASK;

    protected registerHandler(): void {
        Task.getInstance().on(
            TaskUpdateRet.prototype.clazzName,
            (taskSeqIdList: number[]) => {
                let isRefresh = false; // 是否重新渲染任务列表
                const activityIdList = [this.activityProgressId, this.activityId];
                for (const activityId of activityIdList) {
                    const taskSeqConfig = TBTaskSeq.getInstance().getDataByModuleActivityId(this.curTab, activityId);
                    if (taskSeqIdList.includes(taskSeqConfig.id)) {
                        isRefresh = true;
                        break;
                    }
                }
                if (!isRefresh) {
                    return;
                }
                this.nodeTaskContent.node.getComponent(cc.ScrollView).content.children.forEach((node) => {
                    const component = node.getComponent(PrefabDailyTaskItem);
                    if (!component) {
                        return;
                    }
                    component.updateProgress();
                });
                this.updateProgressTaskProgress();
                this.updateProgressTaskList();
            },
            this
        );

        Task.getInstance().on(
            TaskTakeAwardRet.prototype.clazzName,
            (taskSeqId: number) => {
                if (
                    ![this.activityProgressId, this.activityId]
                        .map(
                            (activityId) =>
                                TBTaskSeq.getInstance().getDataByModuleActivityId(this.curTab, activityId)?.id
                        )
                        .includes(taskSeqId)
                ) {
                    return;
                }
                this.updateTaskList();
                this.updateProgressTaskProgress();
                this.updateProgressTaskList();
            },
            this
        );

        Task.getInstance().on(
            TaskBatchTakeAwardRet.prototype.clazzName,
            (taskSeqIds: number[]) => {
                const include = taskSeqIds.some((taskSeqId) => {
                    if (
                        [this.activityProgressId, this.activityId]
                            .map(
                                (activityId) =>
                                    TBTaskSeq.getInstance().getDataByModuleActivityId(this.curTab, activityId)?.id
                            )
                            .includes(taskSeqId)
                    ) {
                        return true;
                    }
                    return false;
                });
                if (!include) {
                    return;
                }
                this.updateTaskList();
                this.updateProgressTaskProgress();
                this.updateProgressTaskList();
            },
            this
        );
    }

    protected onLoad(): void {
        this.registerHandler();
        this.updateTaskList();
        this.updateProgressTaskList();
    }

    /**
     * 更新进度任务列表，重新渲染所有进度任务
     */
    private updateProgressTaskList(): void {
        const activityId = this.activityProgressId;
        const taskSeqConfig = TBTaskSeq.getInstance().getDataByModuleActivityId(this.curTab, activityId);
        const taskGroupConfig = TBTaskGroup.getInstance().getDataById(taskSeqConfig ? taskSeqConfig.taskGroup : 0);
        const taskDetailIdList = taskGroupConfig.taskID
            .map((taskDetailId) => taskDetailId)
            .sort((a, b) => {
                const taskDetailConfigA = TBTaskDetail.getInstance().getDataById(a);
                const taskDetailConfigB = TBTaskDetail.getInstance().getDataById(b);
                return taskDetailConfigA.reachValue - taskDetailConfigB.reachValue;
            });
        const progressList: number[] = [];
        const reachList: number[] = [];
        const max = Math.max(this.nodeRewardContent.childrenCount, taskDetailIdList.length);
        for (let i = 0; i < max; i++) {
            let node = this.nodeRewardContent.children[i];
            if (!node) {
                node = Loader.getInstance().instantiate(this.nodeRewardItem);
                node.parent = this.nodeRewardContent;
            }
            const taskDetailId = taskDetailIdList[i];
            if (!taskDetailId) {
                node.active = false;
                continue;
            }
            node.active = true;
            const taskDetailConfig = TBTaskDetail.getInstance().getDataById(taskDetailId);
            const taskDetailData = Task.getInstance().getTaskInfo(taskDetailId);
            const rewardList = taskDetailConfig.reward.map((item) => ({ itemInfoId: item[0], count: item[1] }));
            const itemConfig = TBItem.getInstance().getDataById(rewardList[0].itemInfoId);
            node.child("lbtProgress").label(taskDetailConfig.reachValue + "");
            node.child("lbtCount").label(rewardList[0].count);
            const progressRes =
                taskDetailData.progress >= taskDetailConfig.reachValue
                    ? PROGRESS_ICON_STATUS[1]
                    : PROGRESS_ICON_STATUS[0];
            node.child("progressIcon").sprite(this.progressIcons[progressRes]);
            node.child("canGet").active =
                taskDetailData && !taskDetailData.isAwarded && taskDetailData.progress >= taskDetailConfig.reachValue;

            node.child("nodeReceive").active = taskDetailData && taskDetailData.isAwarded;
            node.button(`${taskSeqConfig.id}#${taskDetailId}`);
            ImageUtils.setItemIcon(node.child("spIcon"), itemConfig.id);
            ImageUtils.setItemQuality(node.child("spBg"), itemConfig.id);
            progressList.push(taskDetailData.progress);
            reachList.push(taskDetailConfig.reachValue);
        }
        this.nodeProgress.progress = Math.min(Math.max(...progressList) / Math.max(...reachList), 1);
        this.lbtActive.label(Math.max(...progressList) + "");
    }

    /**
     * 进度任务 - 仅更新进度任务的进度，不对任务内容重新渲染
     */
    private updateProgressTaskProgress(): void {
        const progressList: number[] = [];
        const reachList: number[] = [];
        this.nodeRewardContent.children.forEach((node) => {
            if (!node.active) {
                return;
            }
            const [, taskDetailId] = CocosExt.getButtonData(node)
                .split("#")
                .map((str: string) => Number.parseInt(str));
            const taskDetailConfig = TBTaskDetail.getInstance().getDataById(taskDetailId);
            const taskDetailData = Task.getInstance().getTaskInfo(taskDetailId);
            node.child("lbtProgress").label(taskDetailConfig.reachValue + "");
            const progressRes =
                taskDetailData.progress >= taskDetailConfig.reachValue
                    ? PROGRESS_ICON_STATUS[1]
                    : PROGRESS_ICON_STATUS[0];
            node.child("progressIcon").sprite(this.progressIcons[progressRes]);
            node.child("nodeReceive").active = taskDetailData && taskDetailData.isAwarded;
            progressList.push(taskDetailData.progress);
            reachList.push(taskDetailConfig.reachValue);
        });
        this.nodeProgress.progress = Math.min(Math.max(...progressList) / Math.max(...reachList), 1);
        this.lbtActive.label(Math.max(...progressList) + "");
    }

    private updateTaskList(): void {
        const activityId = this.activityId;
        const taskSeqConfig = TBTaskSeq.getInstance().getDataByModuleActivityId(this.curTab, activityId);
        const taskGroupConfig = TBTaskGroup.getInstance().getDataById(taskSeqConfig ? taskSeqConfig.taskGroup : 0);
        this.nodeTaskContent.setListData(
            taskGroupConfig.taskID
                .sort((a, b) => {
                    const taskDetailConfigA = TBTaskDetail.getInstance().getDataById(a);
                    const taskDetailConfigB = TBTaskDetail.getInstance().getDataById(b);
                    const taskStateA = this.getTaskState(a, taskSeqConfig.id);
                    const taskStateB = this.getTaskState(b, taskSeqConfig.id);
                    if (taskStateA !== taskStateB) {
                        return taskStateB - taskStateA;
                    }
                    return taskDetailConfigA.order - taskDetailConfigB.order;
                })
                .map((taskDetailId) => ({
                    taskDetailId,
                    taskSeqId: taskSeqConfig.id,
                    clickFunc: () => {
                        Task.getInstance().batchTaskAwardByActivityItemId(activityId, this.curTab);
                    },
                }))
        );
    }

    /**
     * 获取任务状态
     * @param taskDetailId
     * @param taskSeqId
     * @returns
     */
    private getTaskState(taskDetailId: number, taskSeqId: number): number {
        const taskDetailConfig = TBTaskDetail.getInstance().getDataById(taskDetailId);
        const taskDetailData = Task.getInstance().getTaskInfo(taskDetailId, taskSeqId);
        // 已领取
        if (taskDetailData && taskDetailData.isAwarded) {
            return 0;
        }
        // 可领取
        if (taskDetailData.progress >= taskDetailConfig.reachValue) {
            return 2;
        }
        // 正在进行
        return 1;
    }

    protected onClickReceiveProgress(sender: cc.Event.EventTouch, data: string): void {
        const [, taskDetailId] = data.split("#").map((str) => Number.parseInt(str));
        const taskDetailConfig = TBTaskDetail.getInstance().getDataById(taskDetailId);
        const taskInfo = Task.getInstance().getTaskInfo(taskDetailId);
        if (taskInfo.progress < taskDetailConfig.reachValue) {
            ItemUtils.showInfo(taskDetailConfig.reward[0][0]);
            return;
        }

        if (taskInfo && taskInfo.isAwarded) {
            ItemUtils.showInfo(taskDetailConfig.reward[0][0]);
            return;
        }
        Task.getInstance().batchTaskAwardByActivityItemId(this.activityProgressId, this.curTab);
    }
}
