/*
 * @Author: chenx
 * @Date: 2024-10-11 11:40:47
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-30 11:11:40
 */
import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import TextUtils from "../../../nsn/util/TextUtils";
import Time from "../../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../../nsn/util/TimeFormat";
import Tips from "../../../nsn/util/Tips";
import i18n from "../../config/i18n/I18n";
import Confidant, { ConfidantEvent, IConfidantChildData } from "../../game/Confidant";
import ImageUtils from "../../utils/ImageUtils";
import NumberUtils from "../../utils/NumberUtils";

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 知己-未婚王储item
 */
@ccclass
export default class PrefabConfidantChildUnmarriedItem extends ListViewItem {
    @property(cc.Sprite)
    spHead: cc.Sprite = null; // 王储头像
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Label)
    lbtScore: cc.Label = null; // 评分
    @property(cc.Label)
    lbtTime: cc.Label = null; // 时间
    @property(cc.Label)
    lbtTips: cc.Label = null; // tips
    @property(cc.Node)
    nodeRaise: cc.Node = null; // 提亲按钮
    @property(cc.Node)
    nodeCancel: cc.Node = null; // 取消按钮
    @property(cc.Node)
    nodeSelectedTag: cc.Node = null; // 已选择tag

    refreshTime: number = 0; // 刷新时间

    protected update(dt: number): void {
        if (!this.data) {
            return;
        }

        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.updateTimeState(true);
            }
        }
    }

    public reuse(): void {}

    public unuse(): void {
        this.data = null;

        this.refreshTime = 0;
    }

    /**
     * 更新数据
     * @param data 王储数据
     */
    public updateData(data: IConfidantChildData): void {
        this.data = data;

        let tempRes = "";
        for (const [level, res] of data.info.res) {
            if (data.data.level >= level) {
                tempRes = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantHead(this.spHead, tempRes);
        this.lbtName.string = data.data.name;
        this.lbtScore.string = TextUtils.format(i18n.confidant0015, NumberUtils.format(data.score, 1, 0));
        this.nodeSelectedTag.active = data.isSelected;
        this.updateTimeState();
    }

    /**
     * 更新时间状态
     * @param isUpdate 是否为update调用
     */
    private updateTimeState(isUpdate: boolean = false): void {
        const data: IConfidantChildData = this.data;
        const nowTime = Time.getInstance().now();
        const isRaise = data.data.proposeExpireTime <= nowTime;
        this.nodeRaise.active = isRaise;
        this.nodeCancel.active = !isRaise;
        if (isRaise) {
            this.lbtTime.string = "";
            this.lbtTips.string = "";

            isUpdate && Confidant.getInstance().emit(ConfidantEvent.UpdateListUnmarried, data.data.childUuid);
        } else {
            this.lbtTime.string = TimeFormat.getInstance().getTextByDuration(
                data.data.proposeExpireTime - nowTime,
                TimeDurationFormatType.HH_MM_SS
            );
            this.lbtTips.string = data.data.appointPlayerInfo ? i18n.confidant0029 : i18n.confidant0023;

            this.refreshTime = REFRESH_DURATION;
        }
    }

    /**
     * 选择
     */
    protected onClickSelect(): void {
        if (!this.data) {
            return;
        }

        const data: IConfidantChildData = this.data;
        Confidant.getInstance().emit(ConfidantEvent.SelectUnmarriedChild, data.data.childUuid);
    }

    /**
     * 提亲
     */
    protected onClickRaise(): void {
        if (!this.data) {
            return;
        }

        const data: IConfidantChildData = this.data;
        const nowTime = Time.getInstance().now();
        if (data.data.proposeExpireTime > nowTime) {
            return;
        }

        UI.getInstance().open("PopupConfidantChildRaise", data);
    }

    /**
     * 取消
     */
    protected onClickCancel(): void {
        if (!this.data) {
            return;
        }

        const data: IConfidantChildData = this.data;
        const nowTime = Time.getInstance().now();
        if (data.data.proposeExpireTime <= nowTime) {
            return;
        }
        if (data.data.appointPlayerInfo) {
            Tips.getInstance().info(i18n.confidant0030);
            return;
        }

        Confidant.getInstance().sendChildCancelShare(data.data.childUuid);
    }
}
