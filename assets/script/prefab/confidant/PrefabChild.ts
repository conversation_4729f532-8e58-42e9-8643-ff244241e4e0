/*
 * @Author: chenx
 * @Date: 2024-09-27 18:02:09
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-11-29 18:02:23
 */
import ListView from "../../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import TextUtils from "../../../nsn/util/TextUtils";
import {
    ConfidantChildModifyNameRet,
    ConfidantChildUpgradeRet,
    ConfidantPrincessFavorRet,
    IChildInfo,
} from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { EnumPrincessTotalPara } from "../../data/base/BasePrincessTotal";
import DataPrincessChild from "../../data/extend/DataPrincessChild";
import TBAttribute from "../../data/parser/TBAttribute";
import TBItem from "../../data/parser/TBItem";
import TBPrincessChild from "../../data/parser/TBPrincessChild";
import TBPrincessTotal from "../../data/parser/TBPrincessTotal";
import Confidant from "../../game/Confidant";
import { IUIConfidantChildInfoArgs } from "../../ui/UIConfidantChildInfo";
import ImageUtils from "../../utils/ImageUtils";

/**
 * 王储数据
 */
interface IChildData {
    id: number; // 王储id
    quality: number; // 品质
    info: DataPrincessChild; // 王储信息
    data: IChildInfo; // 王储数据
}

const { ccclass, property } = cc._decorator;

/**
 * 知己-王储
 */
@ccclass
export default class PrefabChild extends I18nComponent {
    @property(cc.RichText)
    rtAttr: cc.RichText = null; // 属性
    @property(ListView)
    listChild: ListView = null; // 王储列表

    childData: IChildData[] = []; // 王储数据
    attr: number[][] = []; // 属性

    protected onLoad(): void {
        this.registerHandler();
        this.updateListInfo(true);
        this.updateAttrInfo();
    }

    protected onEnable(): void {
        this.listChild.updateAll();
    }

    protected registerHandler(): void {
        Confidant.getInstance().on(
            ConfidantPrincessFavorRet.prototype.clazzName,
            (data: ConfidantPrincessFavorRet) => {
                if (data.childInfos.length !== 0) {
                    this.updateListInfo(true);
                    this.updateAttrInfo();
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildModifyNameRet.prototype.clazzName,
            (data: ConfidantChildModifyNameRet) => {
                const childData = this.childData.find((e) => e.data.childUuid === data.childUuid);
                childData.data = Confidant.getInstance().getChildData(childData.data.childUuid);
                this.updateListInfo();
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantChildUpgradeRet.prototype.clazzName,
            (data: ConfidantChildUpgradeRet) => {
                const childData = this.childData.find((e) => e.data.childUuid === data.childUuid);
                childData.data = Confidant.getInstance().getChildData(childData.data.childUuid);
                this.updateListInfo(childData.data.isMaxLevel);
                this.updateAttrInfo();
            },
            this
        );
    }

    /**
     * 更新列表信息-王储列表
     * @param isInit 是否为初始化调用
     */
    private updateListInfo(isInit: boolean = false): void {
        if (isInit) {
            this.childData = [];
            const childData = Confidant.getInstance().getUnmarriedChildData2();
            childData.forEach((e) => {
                const itemInfo = TBItem.getInstance().getDataById(e.childId);
                const childInfo = TBPrincessChild.getInstance().getDataById(e.childId);
                this.childData.push({
                    id: e.childId,
                    quality: itemInfo.quality,
                    info: childInfo,
                    data: e,
                });
            });
        }

        this.childData.sort((a, b) => {
            if (a.quality !== b.quality) {
                return b.quality - a.quality;
            }
            if (a.data.level !== b.data.level) {
                return b.data.level - a.data.level;
            }
            if (a.id !== b.id) {
                return a.id - b.id;
            }
            return b.data.createTime - a.data.createTime;
        });

        this.listChild.scrollView.stopAutoScroll();
        this.listChild.setListData(this.childData);
    }

    /**
     * 更新属性信息
     */
    private updateAttrInfo(): void {
        this.attr = Confidant.getInstance().getAttrByChild();

        let attrDesc = "";
        const attrId: number[] = TBPrincessTotal.getInstance().getValueByPara(
            EnumPrincessTotalPara.CrownPrinceAttributeDisplay
        );
        attrId.forEach((e, i) => {
            let value = 0;
            const tempAttr = this.attr.find(([attrId2]) => attrId2 === e);
            if (tempAttr) {
                value = tempAttr[1];
            }
            const attrData = TBAttribute.getInstance().formatAttribute([e, value]);
            attrDesc += `${attrData.name}<color=#FF7301><outline color=#4A2D15 width=2>${attrData.value}</o></c>`;
            i !== attrId.length - 1 && (attrDesc += "      ");
        });
        this.rtAttr.string = attrDesc;
    }

    /**
     * 监听渲染事件-王储列表
     * @param nodeItem 列表item
     * @param index 列表index
     */
    protected onRenderEvent(nodeItem: cc.Node, index: number): void {
        index = Math.abs(index);
        const childData = this.childData[index];
        nodeItem.button(childData.data.childUuid);
        let tempRes = "";
        for (const [level, res] of childData.info.res) {
            if (childData.data.level >= level) {
                tempRes = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantIcon(nodeItem.child("spIcon"), tempRes);
        ImageUtils.setChildFrame(nodeItem.child("spFrame"), childData.quality);
        nodeItem.child("lbtLevel").label(TextUtils.format(i18n.dungeon0001, childData.data.level));
        nodeItem.child("lbtName").label(childData.data.name);
    }

    /**
     * 王储信息
     * @param event 事件
     * @param childUuid 王储uuid
     */
    protected onClickChildInfo(event: cc.Event.EventTouch, childUuid: string): void {
        if (!childUuid) {
            return;
        }

        const tempChildUuid: string[] = [];
        this.childData.forEach((e) => tempChildUuid.push(e.data.childUuid));
        const initData: IUIConfidantChildInfoArgs = {
            childUuid: tempChildUuid,
            childIndex: tempChildUuid.findIndex((e) => e === childUuid),
        };
        UI.getInstance().open("UIConfidantChildInfo", initData);
    }

    /**
     * 属性
     */
    protected onClickAttr(): void {
        const initData = {
            showAttrId: TBPrincessTotal.getInstance().getValueByPara(
                EnumPrincessTotalPara.CrownPrinceOverallAttributeDisplay
            ),
            attrData: this.attr,
        };
        UI.getInstance().open("FloatAttribute", initData);
    }
}
