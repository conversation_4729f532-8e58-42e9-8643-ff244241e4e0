/*
 * @Author: chenx
 * @Date: 2024-09-27 18:02:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-11-29 18:02:33
 */
import ListView from "../../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import CocosExt from "../../../nsn/util/CocosExt";
import MathUtils from "../../../nsn/util/MathUtils";
import TextUtils from "../../../nsn/util/TextUtils";
import Tips from "../../../nsn/util/Tips";
import {
    BagUpdateRet,
    ConfidantCreatePrincessNotice,
    ConfidantFavorType,
    ConfidantInitRet,
    ConfidantPrincessFavor,
    ConfidantPrincessFavorRet,
    ConfidantPrincessMarryRet,
    ConfidantPrincessUpgradeRet,
    IPrincessInfo,
} from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { EnumPrincessChildGroupType } from "../../data/base/BasePrincessChildGroup";
import { EnumPrincessTotalPara } from "../../data/base/BasePrincessTotal";
import DataPrincess from "../../data/extend/DataPrincess";
import TBAttribute from "../../data/parser/TBAttribute";
import TBItem from "../../data/parser/TBItem";
import TBPrincess from "../../data/parser/TBPrincess";
import TBPrincessChildGroup from "../../data/parser/TBPrincessChildGroup";
import TBPrincessTotal from "../../data/parser/TBPrincessTotal";
import Bag from "../../game/Bag";
import Confidant from "../../game/Confidant";
import { IFloatConfidantDialogArgs } from "../../ui/FloatConfidantDialog";
import { IUIConfidantInfoArgs } from "../../ui/UIConfidantInfo";
import ImageUtils from "../../utils/ImageUtils";
import ItemUtils from "../../utils/ItemUtils";

/**
 * 知己数据
 */
interface IConfidantData {
    id: number; // 知己id
    quality: number; // 品质
    info: DataPrincess; // 知己信息
    data: IPrincessInfo; // 知己数据
    isHave: boolean; // 是否拥有
}

const { ccclass, property } = cc._decorator;

/**
 * 知己-知己
 */
@ccclass
export default class PrefabConfidant extends I18nComponent {
    @property(cc.RichText)
    rtAttr: cc.RichText = null; // 属性
    @property(ListView)
    listConfidant: ListView = null; // 知己列表
    @property(cc.Label)
    lbtFreeTimes: cc.Label = null; // 随机宠幸免费次数
    @property(cc.Sprite)
    spCostIcon: cc.Sprite = null; // 随机宠幸消耗icon
    @property(cc.Label)
    lbtCostCount: cc.Label = null; // 随机宠幸消耗数量
    @property(cc.Button)
    btnFavor: cc.Button = null; // 随机宠幸

    confidantData: IConfidantData[] = []; // 知己数据
    attr: number[][] = []; // 属性

    protected onLoad(): void {
        this.registerHandler();
        this.updateListInfo(true);
        this.updateFavorState();
        this.updateAttrInfo();
    }

    protected registerHandler(): void {
        Confidant.getInstance().on(
            [ConfidantCreatePrincessNotice.prototype.clazzName, ConfidantPrincessUpgradeRet.prototype.clazzName],
            () => {
                this.updateListInfo();
                this.updateAttrInfo();
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantPrincessMarryRet.prototype.clazzName,
            () => {
                this.updateListInfo();
                this.updateFavorState();
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantInitRet.prototype.clazzName,
            () => {
                this.updateFavorState();
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantPrincessFavorRet.prototype.clazzName,
            (data: ConfidantPrincessFavorRet) => {
                if (data.favorType !== ConfidantFavorType.Random) {
                    return;
                }

                this.updateFavorState();

                const childGetGroupInfo = TBPrincessChildGroup.getInstance().getDataById(data.princessChildGroupId);
                const dialog = childGetGroupInfo.text.split("#");
                const index = MathUtils.getRandomInt(0, dialog.length - 1);
                const initData: IFloatConfidantDialogArgs = {
                    confidantId: data.princessId,
                    dialog: dialog[index],
                    getExp: 0,
                };
                UI.getInstance().open("FloatConfidantDialog", initData);

                if (childGetGroupInfo.type === EnumPrincessChildGroupType.GetSon) {
                    UI.getInstance().pushBuffer("FloatConfidantGetChild", data.childInfos[0].childId);
                }
            },
            this
        );
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                if (data.srcReq === ConfidantPrincessFavor.prototype.clazzName) {
                    if (data.gotItem.length > 0) {
                        UI.getInstance().pushBuffer("FloatReward", { gotItem: data.gotItem });
                    }
                }
            },
            this
        );
    }

    /**
     * 更新列表信息-知己列表
     * @param isInit 是否为初始化调用
     */
    private updateListInfo(isInit: boolean = false): void {
        if (isInit) {
            const confidantInfo = TBPrincess.getInstance().getList();
            confidantInfo.forEach((e) => {
                const itemInfo = TBItem.getInstance().getDataById(e.id);
                this.confidantData.push({
                    id: e.id,
                    quality: itemInfo.quality,
                    info: e,
                    data: null,
                    isHave: false,
                });
            });
        }

        this.confidantData.forEach((e) => {
            e.data = Confidant.getInstance().getConfidantData(e.id);
            e.isHave = !!e.data;
        });

        this.confidantData.sort((a, b) => {
            if (a.isHave !== b.isHave) {
                return a.isHave ? -1 : 1;
            }
            if (a.quality !== b.quality) {
                return a.quality - b.quality;
            }
            return a.id - b.id;
        });

        this.listConfidant.scrollView.stopAutoScroll();
        this.listConfidant.setListData(this.confidantData);
    }

    /**
     * 更新随机宠幸状态
     */
    private updateFavorState(): void {
        const isFavor = this.confidantData.findIndex((e) => e.isHave && e.data.isMarry) !== -1;
        if (!isFavor) {
            this.lbtFreeTimes.string = "";
            this.spCostIcon.node.button(null);
            this.spCostIcon.node.sprite(null);
            this.lbtCostCount.string = "";
        } else {
            const freeTimes = Confidant.getInstance().getRandomFavorFreeTimes();
            if (freeTimes > 0) {
                this.spCostIcon.node.button(null);
                this.spCostIcon.node.sprite(null);
                this.lbtCostCount.string = "";

                this.lbtFreeTimes.string = TextUtils.format(
                    i18n.confidant0012,
                    freeTimes,
                    TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.RandomlyFavorFreeNum)
                );
            } else {
                this.lbtFreeTimes.string = "";

                const [costId, count] = TBPrincessTotal.getInstance().getValueByPara(
                    EnumPrincessTotalPara.RandomFavorCost
                )[0];
                this.spCostIcon.node.button(costId);
                ImageUtils.setItemIcon(this.spCostIcon, costId);
                this.lbtCostCount.string = `${Bag.getInstance().getItemCountById(costId)}/${count}`;
            }
        }
        CocosExt.setButtonEnable(this.btnFavor, isFavor);
    }

    /**
     * 更新属性信息
     */
    private updateAttrInfo(): void {
        this.attr = Confidant.getInstance().getAttrByConfidant();

        let attrDesc = "";
        const attrId: number[] = TBPrincessTotal.getInstance().getValueByPara(
            EnumPrincessTotalPara.PrincessAttributeDisplay
        );
        attrId.forEach((e, i) => {
            let value = 0;
            const tempAttr = this.attr.find(([attrId2]) => attrId2 === e);
            if (tempAttr) {
                value = tempAttr[1];
            }
            const attrData = TBAttribute.getInstance().formatAttribute([e, value]);
            attrDesc += `${attrData.name}<color=#FF7301><outline color=#4A2D15 width=2>${attrData.value}</o></c>`;
            i !== attrId.length - 1 && (attrDesc += "      ");
        });
        this.rtAttr.string = attrDesc;
    }

    /**
     * 监听渲染事件-知己列表
     * @param nodeItem 列表item
     * @param index 列表index
     */
    protected onRenderEvent(nodeItem: cc.Node, index: number): void {
        index = Math.abs(index);
        const confidantData = this.confidantData[index];
        nodeItem.button(confidantData.id);
        ImageUtils.setConfidantIcon(nodeItem.child("spIcon"), confidantData.info.res);
        ImageUtils.setConfidantFrame(nodeItem.child("spFrame"), confidantData.quality);
        nodeItem.child("spLevelBg").active = confidantData.isHave;
        nodeItem.child("lbtLevel").label(confidantData.isHave ? confidantData.data.level + "" : "");
        nodeItem.child("lbtName").label(confidantData.info.name);
        nodeItem.child("spMarriedTag").active = confidantData.isHave && confidantData.data.isMarry;
        nodeItem.child("spNotOwnedTag").active = !confidantData.isHave;
    }

    /**
     * 知己信息
     * @param event 事件
     * @param confidantId 知己id
     */
    protected onClickConfidantInfo(event: cc.Event.EventTouch, confidantId: number): void {
        if (!confidantId) {
            return;
        }

        const tempConfidantId: number[] = [];
        this.confidantData.forEach((e) => tempConfidantId.push(e.id));
        const initData: IUIConfidantInfoArgs = {
            confidantId: tempConfidantId,
            confidantIndex: tempConfidantId.findIndex((e) => e === confidantId),
        };
        UI.getInstance().open("UIConfidantInfo", initData);
    }

    /**
     * 属性
     */
    protected onClickAttr(): void {
        const initData = {
            showAttrId: TBPrincessTotal.getInstance().getValueByPara(
                EnumPrincessTotalPara.PrincessOverallAttributeDisplay
            ),
            attrData: this.attr,
        };
        UI.getInstance().open("FloatAttribute", initData);
    }

    /**
     * 随机宠幸
     */
    protected onClickFavor(): void {
        const confidantData = this.confidantData.filter((e) => e.isHave && e.data.isMarry);
        if (confidantData.length === 0) {
            return;
        }
        const freeTimes = Confidant.getInstance().getRandomFavorFreeTimes();
        if (freeTimes <= 0) {
            const [costId, count] = TBPrincessTotal.getInstance().getValueByPara(
                EnumPrincessTotalPara.RandomFavorCost
            )[0];
            if (!Bag.getInstance().isEnough(costId, count)) {
                Tips.getInstance().info(i18n.common0025);
                return;
            }
        }

        const index = MathUtils.getRandomInt(0, confidantData.length - 1);
        Confidant.getInstance().sendFavorByConfidant(confidantData[index].id, ConfidantFavorType.Random);
    }

    /**
     * 道具信息
     * @param event 事件
     * @param itemId 道具id
     */
    protected onClickItemInfo(event: cc.Event.EventTouch, itemId: number): void {
        if (!itemId) {
            return;
        }

        ItemUtils.showInfo(itemId);
    }
}
