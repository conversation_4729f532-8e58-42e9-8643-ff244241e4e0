/*
 * @Author: chenx
 * @Date: 2024-09-27 18:02:14
 * @Last Modified by: chenx
 * @Last Modified time: 2024-10-30 14:31:24
 */
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import UI from "../../../nsn/ui/UI";
import MathUtils from "../../../nsn/util/MathUtils";
import TextUtils from "../../../nsn/util/TextUtils";
import Time from "../../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../../nsn/util/TimeFormat";
import Tips from "../../../nsn/util/Tips";
import { ConfidantExchangeStrengthRet, ConfidantTravelingRet } from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import RedPoint from "../../core/redPoint/RedPoint";
import { RedPointId } from "../../core/redPoint/RedPointId";
import { EnumEventType } from "../../data/base/BaseEvent";
import { EnumPrincessTotalPara } from "../../data/base/BasePrincessTotal";
import TBEvent from "../../data/parser/TBEvent";
import TBPrincessTotal from "../../data/parser/TBPrincessTotal";
import TBTraveling from "../../data/parser/TBTraveling";
import Confidant, { ConfidantEvent } from "../../game/Confidant";
import { IFloatConfidantDialogArgs } from "../../ui/FloatConfidantDialog";
import { IFloatRewardArgs } from "../../ui/FloatReward";
import { IFloatTravelRewardArgs } from "../../ui/FloatTravelReward";

/**
 * 地点数据
 */
interface IPlaceData {
    id: number; // 地点id
    nodePlace: cc.Node; // 地点
}

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 知己-游历
 */
@ccclass
export default class PrefabTravel extends I18nComponent {
    @property([cc.Node])
    nodePlace: cc.Node[] = []; // 地点

    @property(cc.Node)
    nodeTravel: cc.Node = null; // 游历按钮
    @property(cc.Node)
    nodeTravelRed: cc.Node = null; // 游历按钮红点
    @property(cc.Label)
    lbtPower: cc.Label = null; // 体力
    @property(cc.Node)
    nodeCheckIcon: cc.Node = null; // 勾选icon-批量游历
    @property(cc.Label)
    lbtPowerTips: cc.Label = null; // 体力tips
    @property(sp.Skeleton)
    spineNpc: sp.Skeleton = null; // npc特效

    @property(cc.Node)
    nodeBlockEvent: cc.Node = null; // 拦截事件

    placeData: IPlaceData[] = []; // 地点数据
    refreshTime: number = 0; // 刷新时间
    isCheck: boolean = false; // 是否勾选-批量游历

    protected onLoad(): void {
        this.registerHandler();
        this.initInfo();
        this.updatePowerState();
    }

    protected update(dt: number): void {
        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.updatePowerState(true);
            }
        }
    }

    protected registerHandler(): void {
        Confidant.getInstance().on(
            ConfidantEvent.RecoverTravelPower,
            () => {
                this.updatePowerState();
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantTravelingRet.prototype.clazzName,
            (data: ConfidantTravelingRet) => {
                this.updatePowerState();

                if (data.confidantEventInfos.length === 1) {
                    this.playTravelAni(data.confidantEventInfos[0].travelingId, () => {
                        const eventInfo = TBEvent.getInstance().getDataById(data.confidantEventInfos[0].eventId);
                        switch (eventInfo.type) {
                            case EnumEventType.RewardEvent:
                                {
                                    const initData: IFloatRewardArgs = { gotItem: [] };
                                    eventInfo.reward.forEach(([itemId, count]) =>
                                        initData.gotItem.push({ itemInfoId: itemId, num: count })
                                    );
                                    UI.getInstance().open("FloatReward", initData);
                                }
                                break;
                            case EnumEventType.Mediocre:
                                {
                                    const initData: IFloatConfidantDialogArgs = {
                                        confidantId: data.confidantEventInfos[0].princessInfos.princessId,
                                        dialog: eventInfo.text,
                                        getExp: eventInfo.reward[0][0],
                                    };
                                    UI.getInstance().open("FloatConfidantDialog", initData);
                                }
                                break;
                            case EnumEventType.Pretty:
                                {
                                    const initData: IFloatRewardArgs = { gotItem: [] };
                                    eventInfo.reward.forEach(([itemId, count]) =>
                                        initData.gotItem.push({ itemInfoId: itemId, num: count })
                                    );
                                    UI.getInstance().pushBuffer("FloatReward", initData);

                                    const initData2: IFloatConfidantDialogArgs = {
                                        confidantId: eventInfo.princessId,
                                        dialog: eventInfo.text,
                                        getExp: 0,
                                    };
                                    UI.getInstance().open("FloatConfidantDialog", initData2);
                                }
                                break;
                            default:
                                break;
                        }
                    });
                } else {
                    const placeId: number[] = [];
                    data.confidantEventInfos.forEach(
                        (e) => !placeId.includes(e.travelingId) && placeId.push(e.travelingId)
                    );
                    this.playTravelAni(placeId[MathUtils.getRandomInt(0, placeId.length - 1)], () => {
                        const initData: IFloatTravelRewardArgs = {
                            confidantData: [],
                            gotItem: [],
                        };
                        data.confidantEventInfos.forEach((e) => {
                            const eventInfo = TBEvent.getInstance().getDataById(e.eventId);
                            switch (eventInfo.type) {
                                case EnumEventType.RewardEvent:
                                    eventInfo.reward.forEach(([itemId, count]) => {
                                        const index = initData.gotItem.findIndex((e2) => e2.itemInfoId === itemId);
                                        if (index !== -1) {
                                            initData.gotItem[index].num += count;
                                        } else {
                                            initData.gotItem.push({ itemInfoId: itemId, num: count });
                                        }
                                    });
                                    break;
                                case EnumEventType.Mediocre:
                                    const index = initData.confidantData.findIndex(
                                        (e2) => e2.id === e.princessInfos.princessId
                                    );
                                    if (index !== -1) {
                                        initData.confidantData[index].exp += eventInfo.reward[0][0];
                                    } else {
                                        initData.confidantData.push({
                                            id: e.princessInfos.princessId,
                                            exp: eventInfo.reward[0][0],
                                        });
                                    }
                                    break;
                                case EnumEventType.Pretty:
                                    eventInfo.reward.forEach(([itemId, count]) => {
                                        const index = initData.gotItem.findIndex((e2) => e2.itemInfoId === itemId);
                                        if (index !== -1) {
                                            initData.gotItem[index].num += count;
                                        } else {
                                            initData.gotItem.push({ itemInfoId: itemId, num: count });
                                        }
                                    });
                                    break;
                                default:
                                    break;
                            }
                        });
                        UI.getInstance().open("FloatTravelReward", initData);
                    });
                }
            },
            this
        );
        Confidant.getInstance().on(
            ConfidantExchangeStrengthRet.prototype.clazzName,
            () => {
                this.updatePowerState();
            },
            this
        );
    }

    /**
     * 初始化信息
     */
    private initInfo(): void {
        this.nodePlace.forEach((e, i) => {
            const placeInfo = TBTraveling.getInstance().getDataByType(i + 1);
            e.child("lbtName").label(placeInfo.placeName);
            this.placeData.push({
                id: placeInfo.id,
                nodePlace: e,
            });
        });

        this.isCheck = RedPoint.getInstance().isRecord(RedPointId.ConfidantTravelBatch);
        this.nodeCheckIcon.active = this.isCheck;
    }

    /**
     * 更新体力状态
     * @param isUpdate 是否为update调用
     */
    private updatePowerState(isUpdate: boolean = false): void {
        if (!isUpdate) {
            const power = Confidant.getInstance().getTravelPower();
            const powerCost = TBPrincessTotal.getInstance().getValueByPara(
                EnumPrincessTotalPara.TravelingConsumeStrength
            );
            const powerLimit = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.StrengthLimit);
            this.nodeTravelRed.active = power >= powerCost;
            this.lbtPower.string = TextUtils.format(i18n.confidant0033, power, powerLimit);
            this.refreshTime = 0;
            if (power >= powerLimit) {
                this.lbtPowerTips.string = "";
            } else {
                this.updatePowerState(true);
            }
        } else {
            const recoverDuration =
                TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.StrengthRecoveryTime) * 1000;
            const recoverTime = Confidant.getInstance().getPowerRecoverTimeByTravel();
            const surplusDuration = Math.max(recoverDuration - (Time.getInstance().now() - recoverTime), 0);
            this.lbtPowerTips.string = TextUtils.format(
                i18n.confidant0038,
                TimeFormat.getInstance().getTextByDuration(surplusDuration, TimeDurationFormatType.MM_SS)
            );

            if (surplusDuration > 0) {
                this.refreshTime = REFRESH_DURATION;
            } else {
                Confidant.getInstance().updateTravelPowerState();
            }
        }
    }

    /**
     * 播放游历动画
     * @param placeId 地点id
     * @param playCb 播放回调
     */
    private playTravelAni(placeId: number, playCb: () => void): void {
        const placeData = this.placeData.find((e) => e.id === placeId);
        const initPos = this.nodeTravel.getPosition();
        const targetPos = placeData.nodePlace.getPosition();

        this.nodeBlockEvent.active = true;
        this.spineNpc.node.setPosition(initPos);
        this.spineNpc.node.scaleX = targetPos.x >= 0 ? -1 : 1;
        this.spineNpc.timeScale = 1;
        this.spineNpc.setAnimation(0, "move", true);
        this.spineNpc.node.opacity = 255;

        const moveDuration = MathUtils.distance(initPos, targetPos) / 600;
        cc.tween(this.spineNpc.node)
            .to(moveDuration, { position: targetPos })
            .call(() => {
                this.spineNpc.timeScale = 2;
                let jumpTimes = 0;
                this.spineNpc.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                    if (trackEntry.animation.name === "wait") {
                        jumpTimes++;
                        if (jumpTimes >= 2) {
                            this.spineNpc.setCompleteListener(null);

                            this.spineNpc.node.opacity = 0;
                            this.spineNpc.clearTracks();
                            this.nodeBlockEvent.active = false;

                            playCb();
                        }
                    }
                });
                this.spineNpc.setAnimation(0, "wait", true);
            })
            .start();
    }

    /**
     * 游历
     */
    protected onClickTravel(): void {
        const power = Confidant.getInstance().getTravelPower();
        const powerCost = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.TravelingConsumeStrength);
        if (power < powerCost) {
            Tips.getInstance().info(i18n.confidant0034);
            return;
        }

        Confidant.getInstance().sendTravelByConfidant(this.isCheck ? Math.min(power, 10) : 1);
    }

    /**
     * 恢复体力
     */
    protected onClickRecoverPower(): void {
        const power = Confidant.getInstance().getTravelPower();
        const powerLimit = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.StrengthLimit);
        if (power >= powerLimit) {
            Tips.getInstance().info(i18n.confidant0035);
            return;
        }

        const para = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.PhysicalRecovery2);
        const costId = para[0][0];
        const recoverPower = para[0][1];
        UI.getInstance().open("PopupItemUse", {
            itemInfoId: costId,
            getEffectText: (count: number) => {
                return TextUtils.format(i18n.confidant0036, count * recoverPower);
            },
            getMaxCount: () => {
                return Math.ceil((powerLimit - power) / recoverPower);
            },
            useItem: (count: number) => {
                if (!cc.isValid(this.node)) {
                    return;
                }
                if (count === 0) {
                    Tips.getInstance().info(i18n.common0025);
                    return;
                }

                Confidant.getInstance().sendGetTravelPowerByConfidant(
                    Math.min(count, Math.ceil((powerLimit - Confidant.getInstance().getTravelPower()) / recoverPower))
                );
            },
        });
    }

    /**
     * 勾选-批量游历
     */
    protected onClickCheck(): void {
        if (this.isCheck) {
            RedPoint.getInstance().cancelRecord(RedPointId.ConfidantTravelBatch);
        } else {
            RedPoint.getInstance().record(RedPointId.ConfidantTravelBatch);
        }
        this.isCheck = !this.isCheck;
        this.nodeCheckIcon.active = this.isCheck;
    }
}
