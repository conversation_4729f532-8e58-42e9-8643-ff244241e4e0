/*
 * @Author: chenx
 * @Date: 2024-10-12 11:26:02
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-02 15:10:59
 */
import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import TextUtils from "../../../nsn/util/TextUtils";
import Time from "../../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../../nsn/util/TimeFormat";
import { ChildDealType, IChildMarryApplyInfo } from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { GameServer } from "../../core/GameServer";
import TBAttribute from "../../data/parser/TBAttribute";
import TBPrincessChild from "../../data/parser/TBPrincessChild";
import Confidant, { ConfidantEvent } from "../../game/Confidant";
import Player from "../../game/Player";
import { IFloatChildPreviewArgs } from "../../ui/FloatChildPreview";
import ImageUtils from "../../utils/ImageUtils";
import NumberUtils from "../../utils/NumberUtils";

/**
 * 知己-王储申请数据
 */
export interface IChildApplyData {
    applyData: IChildMarryApplyInfo; // 申请数据
    childUuid: string; // 王储uuid
}

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 知己-王储申请item
 */
@ccclass
export default class PrefabConfidantChildApplyItem extends ListViewItem {
    @property(cc.Sprite)
    spHead: cc.Sprite = null; // 王储head
    @property(cc.Label)
    lbtPlayerName: cc.Label = null; // 玩家name
    @property(cc.Node)
    nodeServerTag: cc.Node = null; // 服务器tag
    @property(cc.Label)
    lbtServerName: cc.Label = null; // 服务器name
    @property(cc.Label)
    lbtScore: cc.Label = null; // 评分
    @property(cc.Label)
    lbtTime: cc.Label = null; // 过期time

    refreshTime: number = 0; // 刷新时间

    protected update(dt: number): void {
        if (!this.data) {
            return;
        }

        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.updateTimeState(true);
            }
        }
    }

    public reuse(): void {}

    public unuse(): void {
        this.data = null;

        this.refreshTime = 0;
    }

    /**
     * 更新数据
     * @param data 玩家数据
     */
    public updateData(data: IChildApplyData): void {
        this.data = data;

        const childInfo = TBPrincessChild.getInstance().getDataById(data.applyData.childId);
        let tempRes = "";
        for (const [level, res] of childInfo.res) {
            if (childInfo.levelLimit >= level) {
                tempRes = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantHead(this.spHead, tempRes);
        this.lbtPlayerName.string = data.applyData.playerInfo.name;
        const myServerData = Player.getInstance().getServerInfo();
        const isShowServerTag = data.applyData.playerInfo.serverId !== myServerData.serverId;
        this.nodeServerTag.active = isShowServerTag;
        if (isShowServerTag) {
            this.lbtServerName.string = GameServer.getInstance().getServerNameById(data.applyData.playerInfo.serverId);
        }
        let score = 0;
        childInfo.attribute.forEach(([attrId, init, step]) => {
            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
            score = score + (init + step * (childInfo.levelLimit - 1)) * attrInfo.combat;
        });
        this.lbtScore.string = TextUtils.format(i18n.confidant0015, NumberUtils.format(score, 1, 0));
        this.updateTimeState();
    }

    /**
     * 更新时间状态
     * @param isUpdate 是否为update调用
     */
    private updateTimeState(isUpdate: boolean = false): void {
        const data: IChildApplyData = this.data;
        const nowTime = Time.getInstance().now();
        this.lbtTime.string = TimeFormat.getInstance().getTextByDuration(
            Math.max(data.applyData.applyExpireTime - nowTime, 0),
            TimeDurationFormatType.HH_MM_SS
        );

        if (data.applyData.applyExpireTime <= nowTime) {
            isUpdate && Confidant.getInstance().emit(ConfidantEvent.UpdateListApply);
        } else {
            this.refreshTime = REFRESH_DURATION;
        }
    }

    /**
     * 王储信息
     */
    protected onClickChildInfo(): void {
        if (!this.data) {
            return;
        }

        const data: IChildApplyData = this.data;
        const initData: IFloatChildPreviewArgs = {
            childId: data.applyData.childId,
            playerData: data.applyData.playerInfo,
        };
        UI.getInstance().open("FloatChildPreview", initData);
    }

    /**
     * 同意
     */
    protected onClickAgree(): void {
        if (!this.data) {
            return;
        }
        const data: IChildApplyData = this.data;
        if (data.applyData.applyExpireTime <= Time.getInstance().now()) {
            return;
        }

        Confidant.getInstance().sendChildApplyProcess(data.childUuid, data.applyData.applyUuid, ChildDealType.Agree);
    }

    /**
     * 拒绝
     */
    protected onClickRefuse(): void {
        if (!this.data) {
            return;
        }
        const data: IChildApplyData = this.data;
        if (data.applyData.applyExpireTime <= Time.getInstance().now()) {
            return;
        }

        Confidant.getInstance().sendChildApplyProcess(data.childUuid, data.applyData.applyUuid, ChildDealType.Reject);
    }
}
