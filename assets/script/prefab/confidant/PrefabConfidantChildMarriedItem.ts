/*
 * @Author: chenx
 * @Date: 2024-10-11 11:40:57
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-02 15:07:58
 */
import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import TextUtils from "../../../nsn/util/TextUtils";
import TimeFormat, { TimeFormatType } from "../../../nsn/util/TimeFormat";
import i18n from "../../config/i18n/I18n";
import { GameServer } from "../../core/GameServer";
import TBAttribute from "../../data/parser/TBAttribute";
import TBPrincessChild from "../../data/parser/TBPrincessChild";
import Confidant, { ConfidantEvent, IConfidantChildData } from "../../game/Confidant";
import Player from "../../game/Player";
import ImageUtils from "../../utils/ImageUtils";
import NumberUtils from "../../utils/NumberUtils";

const { ccclass, property } = cc._decorator;

/**
 * 知己-已婚王储item
 */
@ccclass
export default class PrefabConfidantChildMarriedItem extends ListViewItem {
    @property(cc.Sprite)
    spHead: cc.Sprite = null; // 王储head
    @property(cc.Sprite)
    spHead2: cc.Sprite = null; // 王储head
    @property(cc.Label)
    lbtPlayerName: cc.Label = null; // 玩家name
    @property(cc.Node)
    nodeServerTag: cc.Node = null; // 服务器tag
    @property(cc.Label)
    lbtServerName: cc.Label = null; // 服务器name
    @property(cc.Label)
    lbtScore: cc.Label = null; // 评分
    @property(cc.Label)
    lbtTime: cc.Label = null; // 联姻time
    @property(cc.Node)
    nodeSelectedTag: cc.Node = null; // 已选择tag

    public reuse(): void {}

    public unuse(): void {
        this.data = null;
    }

    /**
     * 更新数据
     * @param data 王储数据
     */
    public updateData(data: IConfidantChildData): void {
        this.data = data;

        const childInfo = TBPrincessChild.getInstance().getDataById(data.data.childId);
        let tempRes = "";
        for (const [level, res] of childInfo.res) {
            if (childInfo.levelLimit >= level) {
                tempRes = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantHead(this.spHead, tempRes);
        const childInfo2 = TBPrincessChild.getInstance().getDataById(data.data.companionInfo.childId);
        let tempRes2 = "";
        for (const [level, res] of childInfo2.res) {
            if (childInfo2.levelLimit >= level) {
                tempRes2 = res + "";
            } else {
                break;
            }
        }
        ImageUtils.setConfidantHead(this.spHead2, tempRes2);
        this.lbtPlayerName.string = data.data.companionInfo.playerInfo.name;
        const myServerData = Player.getInstance().getServerInfo();
        const isShowServerTag = data.data.companionInfo.playerInfo.serverId !== myServerData.serverId;
        this.nodeServerTag.active = isShowServerTag;
        if (isShowServerTag) {
            this.lbtServerName.string = GameServer.getInstance().getServerNameById(
                data.data.companionInfo.playerInfo.serverId
            );
        }
        let score = 0;
        childInfo.attribute.forEach(([attrId, init, step]) => {
            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
            score = score + (init + step * (childInfo.levelLimit - 1)) * attrInfo.combat;
        });
        childInfo2.attribute.forEach(([attrId, init, step]) => {
            const attrInfo = TBAttribute.getInstance().getDataById(attrId);
            score = score + (init + step * (childInfo2.levelLimit - 1)) * attrInfo.combat;
        });
        this.lbtScore.string = TextUtils.format(i18n.confidant0015, NumberUtils.format(score, 1, 0));
        this.lbtTime.string = TimeFormat.getInstance().getTextByTime(
            TimeFormatType.YYYY_MM_DD,
            data.data.companionInfo.marryTime
        );
        this.nodeSelectedTag.active = data.isSelected;
    }

    /**
     * 选择
     */
    protected onClickSelect(): void {
        if (!this.data) {
            return;
        }

        const data: IConfidantChildData = this.data;
        Confidant.getInstance().emit(ConfidantEvent.SelectMarriedChild, data.data.childUuid);
    }

    /**
     * 王储联姻
     */
    protected onClickChildMarriage(): void {
        if (!this.data) {
            return;
        }

        const data: IConfidantChildData = this.data;
        UI.getInstance().open("PopupChildMarriage", data.data);
    }
}
