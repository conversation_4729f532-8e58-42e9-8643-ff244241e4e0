/*
 * @Author: chenx
 * @Date: 2024-09-02 15:26:25
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-02 15:13:26
 */
import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import CocosExt from "../../../nsn/util/CocosExt";
import TextUtils from "../../../nsn/util/TextUtils";
import Time from "../../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../../nsn/util/TimeFormat";
import { IPlayerInfo } from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { GameServer } from "../../core/GameServer";
import { EnumDungeonEquipmentTotalPara } from "../../data/base/BaseDungeonEquipmentTotal";
import TBDungeonEquipmentTotal from "../../data/parser/TBDungeonEquipmentTotal";
import DungeonEquip from "../../game/DungeonEquip";
import Player from "../../game/Player";
import CombatScoreUtils from "../../utils/CombatScoreUtils";
import PlayerInfoUtils from "../../utils/PlayerInfoUtils";

/**
 * 装备副本-邀请数据
 */
export interface IDungeonEquipInviteData {
    playerData: IPlayerInfo; // 玩家数据
    teamId: string; // 队伍id
    levelId: number; // 关卡id
}

/**
 * 刷新时间段
 */
const REFRESH_DURATION = 1;

const { ccclass, property } = cc._decorator;

/**
 * 装备副本-邀请item
 */
@ccclass
export default class PrefabDungeonEquipInviteItem extends ListViewItem {
    @property(cc.Node)
    nodeHead: cc.Node = null; // 头像
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    nodeServerTag: cc.Node = null; // 服务器tag
    @property(cc.Label)
    lbtServer: cc.Label = null; // 服务器
    @property(cc.Label)
    lbtLevel: cc.Label = null; // 等级
    @property(cc.Node)
    nodeCombatScore: cc.Node = null; // 战斗力

    @property(cc.Sprite)
    spStateIcon: cc.Sprite = null; // 状态icon
    @property(cc.Label)
    lbtState: cc.Label = null; // 状态
    @property(cc.Button)
    btnInvite: cc.Button = null; // 邀请按钮
    @property(cc.Label)
    lbtInvite: cc.Label = null; // 邀请按钮

    @property([cc.SpriteFrame])
    sfStateIcon: cc.SpriteFrame[] = []; // 状态icon

    refreshTime: number = 0; // 刷新时间

    public reuse(): void {}

    public unuse(): void {
        this.data = null;
        this.refreshTime = 0;
    }

    protected update(dt: number): void {
        if (!this.data) {
            return;
        }

        if (this.refreshTime > 0) {
            this.refreshTime -= dt;
            if (this.refreshTime <= 0) {
                this.udpateTimeState();
            }
        }
    }

    /**
     * 更新数据
     * @param data 邀请数据
     */
    public updateData(data: IDungeonEquipInviteData): void {
        this.data = data;

        PlayerInfoUtils.updateHead(this.nodeHead, data.playerData);
        this.lbtName.string = data.playerData.name;
        const myServerData = Player.getInstance().getServerInfo();
        const isShowServerTag = data.playerData.serverId !== myServerData.serverId;
        this.nodeServerTag.active = isShowServerTag;
        if (isShowServerTag) {
            this.lbtServer.string = GameServer.getInstance().getServerNameById(data.playerData.serverId);
        }
        this.lbtLevel.string = TextUtils.format(i18n.common0048, data.playerData.level);
        CombatScoreUtils.update(this.nodeCombatScore, data.playerData.combatScore);

        if (data.playerData.loginTime > data.playerData.logoutTime) {
            CocosExt.setSpriteFrame(this.spStateIcon, this.sfStateIcon[0]);
            CocosExt.setLabelText(this.lbtState, i18n.common0011, cc.color(1, 255, 20));
        } else {
            CocosExt.setSpriteFrame(this.spStateIcon, this.sfStateIcon[1]);
            CocosExt.setLabelText(
                this.lbtState,
                TimeFormat.getInstance().getTextFromNow(data.playerData.logoutTime),
                cc.color(187, 187, 187)
            );
        }
        this.udpateTimeState();
    }

    /**
     * 更新时间状态
     */
    private udpateTimeState(): void {
        const data: IDungeonEquipInviteData = this.data;
        const time = DungeonEquip.getInstance().getInviteTime(data.playerData.playerId);
        let isInvite = !time;
        if (!isInvite) {
            const duration = Time.getInstance().now() - time;
            const duration2 =
                TBDungeonEquipmentTotal.getInstance().getValueByPara(EnumDungeonEquipmentTotalPara.InvitationCd) * 1000;
            isInvite = duration >= duration2;
            if (!isInvite) {
                this.lbtInvite.string = TimeFormat.getInstance().getTextByDuration(
                    duration2 - duration,
                    TimeDurationFormatType.D_H_M_S_0
                );
            }
        }
        CocosExt.setButtonEnable(this.btnInvite, isInvite);
        if (isInvite) {
            this.lbtInvite.string = i18n.dungeon0014;
        }

        !isInvite && (this.refreshTime = REFRESH_DURATION);
    }

    /**
     * 邀请
     */
    protected onClickInvite(): void {
        if (!this.data) {
            return;
        }
        const data: IDungeonEquipInviteData = this.data;
        const time = DungeonEquip.getInstance().getInviteTime(data.playerData.playerId);
        if (time) {
            const isInvite =
                Time.getInstance().now() - time >=
                TBDungeonEquipmentTotal.getInstance().getValueByPara(EnumDungeonEquipmentTotalPara.InvitationCd) * 1000;
            if (!isInvite) {
                return;
            }
        }

        DungeonEquip.getInstance().sendInvite(data.teamId, data.levelId, [data.playerData.playerId]);

        DungeonEquip.getInstance().setInviteTime(data.playerData.playerId, Time.getInstance().now());
        this.udpateTimeState();
    }

    /**
     * 玩家信息
     */
    protected onClickPlayerInfo(): void {
        if (!this.data) {
            return;
        }

        const data: IDungeonEquipInviteData = this.data;
        UI.getInstance().open("FloatOtherPlayerInfo", data.playerData.playerId);
    }
}
