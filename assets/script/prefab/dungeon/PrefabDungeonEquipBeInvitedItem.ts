/*
 * @Author: chenx
 * @Date: 2024-09-02 15:26:35
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-02 15:11:12
 */
import ListViewItem from "../../../nsn/comp/3rd/List/ListViewItem";
import UI from "../../../nsn/ui/UI";
import TextUtils from "../../../nsn/util/TextUtils";
import Tips from "../../../nsn/util/Tips";
import { IPlayerInfo } from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { GameServer } from "../../core/GameServer";
import { EnumDungeonEquipmentTotalPara } from "../../data/base/BaseDungeonEquipmentTotal";
import TBDungeonEquipmentTotal from "../../data/parser/TBDungeonEquipmentTotal";
import DungeonEquip, { DungeonEquipEvent } from "../../game/DungeonEquip";
import Player from "../../game/Player";
import CombatScoreUtils from "../../utils/CombatScoreUtils";
import ImageUtils from "../../utils/ImageUtils";
import PlayerInfoUtils from "../../utils/PlayerInfoUtils";

/**
 * 装备副本-被邀请数据
 */
export interface IDungeonEquipBeInvitedData {
    playerData: IPlayerInfo; // 玩家数据
    teamId: string; // 队伍id
    levelId: number; // 关卡id
    time: number; // 时间
}

const { ccclass, property } = cc._decorator;

/**
 * 装备副本-被邀请item
 */
@ccclass
export default class PrefabDungeonEquipBeInvitedItem extends ListViewItem {
    @property(cc.Node)
    nodeHead: cc.Node = null; // 头像
    @property(cc.Label)
    lbtName: cc.Label = null; // 名称
    @property(cc.Node)
    nodeServerTag: cc.Node = null; // 服务器tag
    @property(cc.Label)
    lbtServer: cc.Label = null; // 服务器
    @property(cc.Label)
    lbtLevel: cc.Label = null; // 等级
    @property(cc.Node)
    nodeCombatScore: cc.Node = null; // 战斗力

    @property(cc.Sprite)
    spCostIcon: cc.Sprite = null; // 消耗icon
    @property(cc.Label)
    lbtCostCount: cc.Label = null; // 消耗count

    public reuse(): void {}

    public unuse(): void {
        this.data = null;
    }

    /**
     * 更新数据
     * @param data 被邀请数据
     */
    public updateData(data: IDungeonEquipBeInvitedData): void {
        this.data = data;

        PlayerInfoUtils.updateHead(this.nodeHead, data.playerData);
        this.lbtName.string = data.playerData.name;
        const myServerData = Player.getInstance().getServerInfo();
        const isShowServerTag = data.playerData.serverId !== myServerData.serverId;
        this.nodeServerTag.active = isShowServerTag;
        if (isShowServerTag) {
            this.lbtServer.string = GameServer.getInstance().getServerNameById(data.playerData.serverId);
        }
        this.lbtLevel.string = TextUtils.format(i18n.common0054, "", data.levelId);
        CombatScoreUtils.update(this.nodeCombatScore, data.playerData.combatScore);

        const costData: number[][] = TBDungeonEquipmentTotal.getInstance().getValueByPara(
            EnumDungeonEquipmentTotalPara.AssistConsume
        );
        ImageUtils.setItemIcon(this.spCostIcon, costData[0][0]);
        const helpedTimes = DungeonEquip.getInstance().getHelpedTimes();
        this.lbtCostCount.string = costData[Math.min(helpedTimes, costData.length - 1)][1] + "";
    }

    /**
     * 加入队伍
     */
    protected onClickJoinTeam(): void {
        if (!this.data) {
            return;
        }
        const data: IDungeonEquipBeInvitedData = this.data;
        if (!DungeonEquip.getInstance().isUnlockByLevel(data.levelId)) {
            Tips.getInstance().info(i18n.dungeon0029);
            return;
        }
        if (!DungeonEquip.getInstance().isHelp()) {
            Tips.getInstance().info(i18n.common0025);
            return;
        }

        DungeonEquip.getInstance().sendJoinTeam(data.teamId, data.levelId);
    }

    /**
     * 删除
     */
    protected onClickDelete(): void {
        if (!this.data) {
            return;
        }

        const data: IDungeonEquipBeInvitedData = this.data;
        DungeonEquip.getInstance().emit(DungeonEquipEvent.DeleteBeInviteData, data.playerData.playerId);
    }

    /**
     * 玩家信息
     */
    protected onClickPlayerInfo(): void {
        if (!this.data) {
            return;
        }

        const data: IDungeonEquipBeInvitedData = this.data;
        UI.getInstance().open("FloatOtherPlayerInfo", data.playerData.playerId);
    }
}
