/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-04-24 14:37:39
 */

import ListView from "../../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import Time, { DAY_TO_SECOND } from "../../../nsn/util/Time";
import { RechargeNoticeRet } from "../../../protobuf/proto";
import { EnumPackShowType } from "../../data/base/BasePack";
import { EnumRechargeTabMainTab, EnumRechargeTabTab } from "../../data/base/BaseRechargeTab";
import TBPack from "../../data/parser/TBPack";
import TBRecharge from "../../data/parser/TBRecharge";
import TBRechargeTab from "../../data/parser/TBRechargeTab";
import GameSwitch from "../../game/GameSwitch";
import Player from "../../game/Player";
import Recharge from "../../game/Recharge";
import TweenUtil from "../../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabMallLimit extends I18nComponent {
    @property(ListView)
    list: ListView = null;

    protected onLoad(): void {
        this.registerHandler();
        this.updateUI();
        TweenUtil.playListViewGridAni1(this.list);
    }

    private registerHandler(): void {
        Recharge.getInstance().on(
            RechargeNoticeRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateList();
    }

    private updateList(): void {
        const rechargeTabData = TBRechargeTab.getInstance().getDataByMainTab(EnumRechargeTabMainTab.LimitedTimeShop);
        const data: {
            type?: EnumRechargeTabTab;
            rechargeIds?: number[];
        }[] = [];
        for (const e1 of rechargeTabData) {
            data.push({ type: e1.tab });
            const tabPack: number[] = [];
            for (const e2 of e1.tabPack) {
                const rechargeData = TBRecharge.getInstance().getDataById(e2);
                const packData = TBPack.getInstance().getDataById(rechargeData.pack[0]);
                let isShow = false;
                switch (packData.showType) {
                    case EnumPackShowType.ChargeDisplay:
                        const sumPrice = Recharge.getInstance().getSumPrice();
                        isShow = sumPrice >= packData.showPara;
                        break;
                    case EnumPackShowType.RegisterDisplay:
                        const openTime = Player.getInstance().getRegisterDate();
                        const openTimeZero = Time.getInstance().getZeroOfTime(openTime);
                        const time = Time.getInstance().now() - openTimeZero;
                        const day = Math.ceil(time / (DAY_TO_SECOND * 1000));
                        isShow = day >= packData.showPara;
                        break;
                    case EnumPackShowType.SystemDisplay:
                        const { result } = GameSwitch.getInstance().check(packData.showPara);
                        isShow = result;
                        break;
                    default:
                        isShow = true;
                        break;
                }
                if (isShow) {
                    tabPack.push(e2);
                }
            }
            const sellOut = tabPack.filter((v) => Recharge.getInstance().isSellOut(v));
            const notSellOut = tabPack.filter((v) => !Recharge.getInstance().isSellOut(v));
            const allPacks = notSellOut.concat(sellOut);
            for (let i = 0; i < allPacks.length; i += 2) {
                data.push({ rechargeIds: allPacks.slice(i, i + 2) });
            }
        }
        this.list.setListData(data);
    }
}
