/*
 * @Author: zhangwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-04-24 14:37:47
 */

import ListView from "../../../nsn/comp/3rd/List/ListView";
import I18nComponent from "../../../nsn/i18n/I18nComponent";
import Time, { DAY_TO_SECOND } from "../../../nsn/util/Time";
import { RechargeNoticeRet } from "../../../protobuf/proto";
import { EnumPackShowType } from "../../data/base/BasePack";
import { EnumRechargeTabMainTab, EnumRechargeTabTab } from "../../data/base/BaseRechargeTab";
import TBPack from "../../data/parser/TBPack";
import TBRecharge from "../../data/parser/TBRecharge";
import TBRechargeTab from "../../data/parser/TBRechargeTab";
import GameSwitch from "../../game/GameSwitch";
import Player from "../../game/Player";
import Recharge from "../../game/Recharge";
import TweenUtil from "../../utils/TweenUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PrefabMallLife extends I18nComponent {
    @property(ListView)
    list: ListView = null;

    protected onLoad(): void {
        this.registerHandler();
        this.updateUI();
        TweenUtil.playListViewVerticalAni(this.list);
    }

    private registerHandler(): void {
        Recharge.getInstance().on(
            RechargeNoticeRet.prototype.clazzName,
            () => {
                this.updateUI();
            },
            this
        );
    }

    private updateUI(): void {
        this.updateList();
    }

    private updateList(): void {
        const rechargeTabData = TBRechargeTab.getInstance().getDataByMainTabAndTab(
            EnumRechargeTabMainTab.PackStore,
            EnumRechargeTabTab.NoTab
        );
        const pack = rechargeTabData.tabPack.filter((v) => {
            const rechargeData = TBRecharge.getInstance().getDataById(v);
            const packData = TBPack.getInstance().getDataById(rechargeData.pack[0]);
            let isShow = false;
            switch (packData.showType) {
                case EnumPackShowType.ChargeDisplay:
                    const sumPrice = Recharge.getInstance().getSumPrice();
                    isShow = sumPrice >= packData.showPara;
                    break;
                case EnumPackShowType.RegisterDisplay:
                    const openTime = Player.getInstance().getRegisterDate();
                    const openTimeZero = Time.getInstance().getZeroOfTime(openTime);
                    const time = Time.getInstance().now() - openTimeZero;
                    const day = Math.ceil(time / (DAY_TO_SECOND * 1000));
                    isShow = day >= packData.showPara;
                    break;
                case EnumPackShowType.SystemDisplay:
                    const { result } = GameSwitch.getInstance().check(packData.showPara);
                    isShow = result;
                    break;
                default:
                    isShow = true;
                    break;
            }
            return isShow;
        });
        const notBuy = pack.filter((v) => !Recharge.getInstance().isSellOut(v));
        const buy = pack.filter((v) => Recharge.getInstance().isSellOut(v));
        this.list.setListData(notBuy.concat(buy));
    }
}
