/*
 * @Author: <PERSON>y<PERSON><PERSON>
 * @Date: 2023-08-11 11:59:56
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-15 14:29:29
 */

import Loader from "../../nsn/core/Loader";
import Pool from "../../nsn/core/Pool";
import UI from "../../nsn/ui/UI";
import Logger from "../../nsn/util/Logger";
import TextUtils from "../../nsn/util/TextUtils";
import { IItemInfo } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumItemEffectType, EnumItemType } from "../data/base/BaseItem";
import TBItem from "../data/parser/TBItem";
import Bag from "../game/Bag";
import NumberUtils from "./NumberUtils";

/**
 * 界面类型参数
 */
export interface IPrefabItemConfig {
    showCount?: boolean;
    showLevel?: boolean;
    showName?: boolean;
    gray?: boolean;
    click?: (itemInfo: IItemInfo) => void;
}

export default class ItemUtils {
    /**
     * 刷新多个道具
     * @param parent
     * @param prefab
     * @param data
     */
    public static refreshView(
        parent: cc.Node,
        prefab: cc.Prefab,
        data: IItemInfo[] | number[][],
        config: IPrefabItemConfig = { showCount: true }
    ): void {
        if (parent.children.length !== data.length) {
            for (let i = parent.children.length - 1; i >= 0; i--) {
                Pool.getInstance().put(parent.children[i]);
            }
            for (const e of data) {
                const item = Pool.getInstance().get(prefab.name) || Loader.getInstance().instantiate(prefab);
                item.x = 0;
                item.y = 0;
                item.getComponent(prefab.name).updateData(e, config);
                item.parent = parent;
            }
        } else {
            for (let i = 0; i < data.length; i++) {
                const item = parent.children[i];
                item.getComponent(prefab.name).updateData(data[i], config);
            }
        }
    }

    /**
     * 刷新道具数量
     * @param richtext
     * @param itemId
     * @param cost
     * @param onlyShowCost
     * @param withOutline
     * @returns
     */
    public static refreshCount(
        richtext: cc.Node | cc.RichText,
        itemId: number,
        cost: number,
        onlyShowCost: boolean = false,
        moreTextTemplate: string = "",
        lessTextTemplate: string = ""
    ): void {
        if (!cc.isValid(richtext)) {
            Logger.warn("资源设置", "节点不存在或者已销毁");
            return;
        }
        const r = richtext instanceof cc.RichText ? richtext : richtext.getComponent(cc.RichText);
        const count = Bag.getInstance().getItemCountById(itemId);

        if (onlyShowCost) {
            r.string = TextUtils.format(i18n.common0057, NumberUtils.format(cost, 1, 0));
        } else {
            r.string = TextUtils.format(
                count >= cost
                    ? moreTextTemplate
                        ? moreTextTemplate
                        : i18n.common0055
                    : lessTextTemplate
                    ? lessTextTemplate
                    : i18n.common0056,
                NumberUtils.format(count, 1, 0),
                NumberUtils.format(cost, 1, 0)
            );
        }
    }

    /**
     * 显示道具详情
     * @param itemId
     */
    public static showInfo(itemId: number, closeCallBack?: () => void): void {
        const itemTB = TBItem.getInstance().getDataById(itemId);
        if (!itemTB) {
            return;
        }
        switch (itemTB.type) {
            // 宝箱
            case EnumItemType.Box:
                switch (itemTB.effectType) {
                    case EnumItemEffectType.DiyBox:
                        UI.getInstance().open("FloatDiyBox", { id: itemId, preview: true });
                        break;
                    case EnumItemEffectType.RandomBox:
                        UI.getInstance().open("FloatRandomBox", { id: itemId, preview: true });
                        break;
                    default:
                        UI.getInstance().open("FloatItemBox", itemId);
                        break;
                }
                break;
            // 特殊预览弹窗
            case EnumItemType.Archer:
                UI.getInstance().open("FloatPartnerDrawCardDetail", itemId);
                break;
            case EnumItemType.Magic:
                UI.getInstance().open("FloatMagicDrawCardDetail", itemId);
                break;
            case EnumItemType.GamerTank:
                UI.getInstance().open("FloatTankPreviewDetail", itemId);
                break;
            case EnumItemType.GamerFashion:
                UI.getInstance().open("FloatSkinPreviewDetail", itemId);
                break;
            case EnumItemType.GamerWing:
                UI.getInstance().open("FloatWingPreviewDetail", itemId);
                break;
            case EnumItemType.GamerWeapon:
                UI.getInstance().open("FloatWeaponPreviewDetail", itemId);
                break;
            case EnumItemType.Collection:
                UI.getInstance().open("FloatCollectionPreviewDetail", itemId);
                break;
            default:
                UI.getInstance().open("FloatItemDetail", { itemId, closeCallBack });
                break;
        }
    }
}
