/*
 * 常用图片加载设置工具
 * @Author: linds
 * @Date: 2021-09-01 15:56:31
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-22 15:12:46
 */

import Loader from "../../nsn/core/Loader";
import CocosExt from "../../nsn/util/CocosExt";
import TBItem from "../data/parser/TBItem";
import TBPower from "../data/parser/TBPower";
import TBPowerLevel from "../data/parser/TBPowerLevel";
import TBRecharge from "../data/parser/TBRecharge";
import TBSkill from "../data/parser/TBSkill";
import TBSystemEntry from "../data/parser/TBSystemEntry";

export default class ImageUtils {
    /**
     * 设置富文本道具icon
     * @param richText
     * @param itemsIds
     * @param successCb
     * @param failedCb
     */
    public static setRichTextItemIcons(
        richText: cc.RichText | cc.Node,
        itemsIds: string[],
        successCb?: (sa: cc.SpriteAtlas) => void,
        failedCb?: () => void
    ): void {
        const r = richText instanceof cc.RichText ? richText : richText.getComponent(cc.RichText);
        let count = 0;
        const sa = new cc.SpriteAtlas();
        for (const itemId of itemsIds) {
            Loader.getInstance().loadSpriteFrame(
                "texture/item/icon/" + itemId,
                (sp: cc.SpriteFrame) => {
                    if (!cc.isValid(r)) {
                        failedCb && failedCb();
                        return;
                    }
                    // @ts-ignore
                    sa._spriteFrames[sp.name] = sp;
                    count++;
                    if (count === itemsIds.length) {
                        r.imageAtlas = sa;
                        successCb && successCb(sa);
                    }
                },
                () => {
                    failedCb && failedCb();
                },
                false
            );
        }
    }

    /**
     * 设置道具icon
     * @param sprite
     * @param id
     * @param cb
     */
    public static setItemIcon(sprite: cc.Node | cc.Sprite, id: number, cb?: (sprite: cc.SpriteFrame) => void): void {
        const data = TBItem.getInstance().getDataById(id);
        const url = "texture/item/icon/prop" + data.res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb, id + "");
    }

    /**
     * 设置道具品质
     * @param sprite
     * @param id
     * @param cb
     */
    public static setItemQuality(sprite: cc.Node | cc.Sprite, id: number, cb?: (sprite: cc.SpriteFrame) => void): void {
        const itemInfo = TBItem.getInstance().getDataById(id);
        const url = "texture/item/quality/iconPropQuality" + itemInfo.quality;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置装备icon
     * @param sprite
     * @param id
     * @param cb
     */
    public static setEquipIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: (sprite: cc.SpriteFrame) => void): void {
        const url = "texture/item/icon/prop" + res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置系统入口图标
     * @param sprite
     * @param id
     * @param cb
     */
    public static setSystemEntryIcon(
        sprite: cc.Node | cc.Sprite,
        id: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const data = TBSystemEntry.getInstance().getDataById(id);
        if (data.res) {
            const url = "texture/systemEntry/" + data.res;
            CocosExt.setSpriteFrameAsync(sprite, url, cb);
        }
    }

    /**
     * 设置弓箭手icon-列表
     * @param sprite
     * @param res
     * @param cb
     */
    public static setArcherIcon1(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const url = "texture/partner/icon1/iconPartnerList" + res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置弓箭手icon-图鉴
     * @param sprite
     * @param res
     * @param cb
     */
    public static setArcherIcon2(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/partner/icon2/iconPartnerHandbook${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置弓箭手icon-标识
     * @param sprite
     * @param res
     * @param cb
     */
    public static setArcherIcon3(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/arrow/archerIcon/iconPartnerSign${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置弓箭icon
     * @param sprite
     * @param res
     * @param cb
     */
    public static setArrowIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/arrow/arrowIcon/spWeaponIcon${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置弓箭Name
     * @param sprite
     * @param res
     * @param cb
     */
    public static setArcherName(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/partner/name/iconPartnerName${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置玩法预告图
     * @param sprite
     * @param res
     * @param cb
     */
    public static setGamePlayPreviewIcon(
        sprite: cc.Node | cc.Sprite,
        res: string,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/gamePlayPreview/iconSystemUnlock" + res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置公会旗帜
     * @param sprite
     * @param flag
     * @param cb
     */
    public static setUnionFlag(sprite: cc.Node | cc.Sprite, flag: number, cb?: (sprite: cc.SpriteFrame) => void): void {
        const url = "texture/union/flag/iconUnionFlag" + flag;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置祝福
     * @param sprite
     * @param res
     * @param cb
     */
    public static setBlessIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: (sprite: cc.SpriteFrame) => void): void {
        const url = "texture/bless/iconBless" + res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置基金图标
     * @param sprite
     * @param res
     * @param cb
     */
    public static setFundIcon(sprite: cc.Node | cc.Sprite, id: number, cb?: (sprite: cc.SpriteFrame) => void): void {
        const reTabTB = TBRecharge.getInstance().getDataById(id);
        const url = "texture/fund/iconBPBanner" + reTabTB.image;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置基金返利图标
     * @param sprite
     * @param res
     * @param cb
     */
    public static setFundRewardIcon(
        sprite: cc.Node | cc.Sprite,
        id: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const reTabTB = TBRecharge.getInstance().getDataById(id);
        const url = "texture/fund/iconBPBannerDiscount" + reTabTB.image;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置基金入口
     * @param sprite
     * @param res
     * @param cb
     */
    public static setFundEntry(sprite: cc.Node | cc.Sprite, id: string, cb?: (sprite: cc.SpriteFrame) => void): void {
        const url = "texture/fund/iconBPEntrance" + id;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置技能
     * @param sprite
     * @param res
     * @param cb
     */
    public static setSkillIcon(sprite: cc.Node | cc.Sprite, id: number, cb?: (sprite: cc.SpriteFrame) => void): void {
        const data = TBSkill.getInstance().getDataById(id);
        const url = "texture/skill/icon/iconSkill" + data.res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置称号
     * @param sprite
     * @param res
     * @param cb
     */
    public static setTitle(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const url = "texture/title/iconTitle" + res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb, res);
    }

    /**
     * 设置头像icon
     * @param sprite
     * @param id
     * @param cb
     */
    public static setAvatarIcon(sprite: cc.Node | cc.Sprite, id: number, cb?: (sprite: cc.SpriteFrame) => void): void {
        const data = TBItem.getInstance().getDataById(id);
        const url = "texture/avatar/iconPlayerHead" + data.res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb, data.res);
    }

    /**
     * 设置时空试炼图标
     */
    public static setTimeBackBossRewardIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/activity/timeBack/boss/${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb);
    }

    /**
     * 设置角色立绘
     * @param sprite
     * @param res
     * @param cb
     */
    public static setSkinPortrait(
        sprite: cc.Node | cc.Sprite,
        res: string,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/leadSkin/role/iconRolePortrait" + res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置角色icon
     * @param sprite
     * @param res
     * @param cb
     */
    public static setLead(sprite: cc.Node | cc.Sprite, res: string, cb?: (sprite: cc.SpriteFrame) => void): void {
        const url = "texture/leadSkin/icon/iconRoleSkin" + res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置星级
     * @param stars
     * @param level
     */
    public static setStarsIcon(stars: cc.Node, level: number, show0Star: boolean = false): void {
        if (level) {
            const q1 = level % 5 === 0 ? Math.floor(level / 5) : Math.floor(level / 5) + 1;
            const q2 = level % 5 === 0 ? 5 : level % 5;
            for (let i = 0; i < stars.childrenCount; i++) {
                const star = stars.children[i];
                if (i < q2) {
                    star.active = true;
                    CocosExt.setSpriteFrameAsync(star, "texture/star/spStar" + q1);
                } else {
                    star.active = false;
                }
            }
        } else {
            for (let i = 0; i < stars.childrenCount; i++) {
                const star = stars.children[i];
                if (i === 0 && show0Star) {
                    star.active = true;
                    CocosExt.setSpriteFrameAsync(star, "texture/star/spStar0");
                } else {
                    star.active = false;
                }
            }
        }
    }

    /**
     * 设置王权爵位 大图标
     * @param icon
     * @param stars
     * @param level
     */
    public static setPowerIcon1(icon: cc.Node, stars: cc.Node, level: number): void {
        const data1 = TBPowerLevel.getInstance().getDataById(level);
        const data2 = TBPower.getInstance().getDataById(data1.titleId);
        const gradeRes = "texture/power/icon/iconGradeB";
        const starRes = "texture/power/star/iconGradeStar";
        icon.spriteAsync(gradeRes + data2.res);
        for (let i = 0; i < stars.childrenCount; i++) {
            if (i + 1 <= data1.rank) {
                stars.children[i].spriteAsync(starRes + data1.res + "1");
            } else {
                stars.children[i].spriteAsync(starRes + data1.res + "0");
            }
        }
    }

    /**
     * 设置王权爵位 小图标
     * @param icon
     * @param stars
     * @param level
     */
    public static setPowerIcon2(icon: cc.Node, stars: cc.Node, level: number): void {
        const data1 = TBPowerLevel.getInstance().getDataById(level);
        const data2 = TBPower.getInstance().getDataById(data1.titleId);
        const gradeRes = "texture/power/icon/iconGradeS";
        const starRes = "texture/power/star/iconGradeStar";
        icon.spriteAsync(gradeRes + data2.res);
        for (let i = 0; i < stars.childrenCount; i++) {
            if (i + 1 <= data1.rank) {
                stars.children[i].spriteAsync(starRes + data1.res + "1");
            } else {
                stars.children[i].spriteAsync(starRes + data1.res + "0");
            }
        }
    }

    /**
     * 设置技能属性标识
     * @param icon
     * @param res
     */
    public static setPropertyTagIcon(icon: cc.Node, res: number): void {
        CocosExt.setSpriteFrameAsync(icon, `texture/propertyTag/iconPropertyTag${res}`);
    }

    /**
     * 设置装备品质
     * @param sprite
     * @param quality
     * @param cb
     */
    public static setEquipQuality(
        sprite: cc.Node | cc.Sprite,
        quality: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/item/quality/iconPropQuality" + quality;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置品质2
     * 宠物
     * @param sprite
     * @param quality
     * @param cb
     */
    public static setQuality2(
        sprite: cc.Node | cc.Sprite,
        quality: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/quality/quality2/iconPetQuality" + quality;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置品质3
     * 宠物词条
     * @param sprite
     * @param quality
     * @param cb
     */
    public static setQuality3(
        sprite: cc.Node | cc.Sprite,
        quality: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/quality/quality3/iconPetProperty" + quality;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置品质4
     * 宠物阶数
     * @param sprite
     * @param quality
     * @param cb
     */
    public static setQuality4(
        sprite: cc.Node | cc.Sprite,
        quality: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/quality/quality4/iconPetGrade" + quality;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置品质6
     * @param sprite
     * @param res
     * @param cb
     */
    public static setQuality6(
        sprite: cc.Node | cc.Sprite,
        quality: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/quality/quality6/iconQualitySign" + quality;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置品质7
     * @param sprite
     * @param res
     * @param cb
     */
    public static setQuality7(
        sprite: cc.Node | cc.Sprite,
        quality: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/quality/quality7/iconQualityBase" + quality;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置品质8
     * @param sprite
     * @param res
     * @param cb
     */
    public static setQuality8(
        sprite: cc.Node | cc.Sprite,
        quality: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/quality/quality8/iconQualityTitle" + quality;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置品质9
     * @param sprite
     * @param res
     * @param cb
     */
    public static setQuality9(
        sprite: cc.Node | cc.Sprite,
        quality: number,
        cb?: (sprite: cc.SpriteFrame) => void
    ): void {
        const url = "texture/quality/quality9/iconHandbookBase" + quality;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置品质10
     * @param sprite
     * @param res
     */
    public static setQuality10(sprite: cc.Node | cc.Sprite, res: string): void {
        const url = "texture/quality/quality10/iconEquipColor" + res;
        CocosExt.setSpriteFrameAsync(sprite, url);
    }

    /**
     * 设置主角技能图标
     * @param icon
     * @param res
     */
    public static setLeadSkillIcon(icon: cc.Node, res: string): void {
        CocosExt.setSpriteFrameAsync(icon, `texture/skill/icon/iconSkill${res}`);
    }

    /**
     * 设置战车等级-星级
     * @param icon
     * @param isWear
     */
    public static setTankStarIcon(icon: cc.Node, stars: number): void {
        const url = "texture/tank/star/spChariotStar";
        for (let i = 0; i < icon.childrenCount; i++) {
            if (i < stars) {
                icon.children[i].spriteAsync(url + "2");
            } else {
                icon.children[i].spriteAsync(url + "1");
            }
        }
    }

    /**
     * 设置背饰icon
     * @param sprite
     * @param res
     * @param cb
     */
    public static setWingIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: (sprite: cc.SpriteFrame) => void): void {
        const url = "texture/wing/iconWings" + res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置神器icon
     * @param sprite
     * @param res
     * @param cb
     */
    public static setWeaponIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: (sprite: cc.SpriteFrame) => void): void {
        const url = "texture/weapon/iconWeapon" + res;
        CocosExt.setSpriteFrameAsync(sprite, url, cb);
    }

    /**
     * 设置技能特效
     * @param sprite
     * @param res
     * @param cb
     */
    public static setSkillEffect(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/skillEffect/spSkillPic${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置战车图标
     * @param sprite
     * @param res
     * @param cb
     */
    public static setTankIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/tank/chariot/iconChariot${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置锻造台图标-未升阶
     * @param sprite
     * @param res
     * @param cb
     */
    public static setForgeIconA(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/forge/iconForgeA${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置锻造台图标-升阶
     * @param sprite
     * @param res
     * @param cb
     */
    public static setForgeIconB(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/forge/iconForgeB${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置宝箱名字图标
     * @param sprite
     * @param res
     * @param cb
     */
    public static setBoxNameIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/syncUI/box/iconDiaoBoxName${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置宝箱图标
     * @param sprite
     * @param res
     * @param cb
     */
    public static setBoxIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/box/iconDiaoBox${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置收藏品大图标
     * @param sprite
     * @param res
     * @param cb
     */
    public static setCollectionIcon(sprite: cc.Node | cc.Sprite, res: string, cb?: () => void): void {
        const path = `texture/collection/iconTreasures${res}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb, res);
    }

    /**
     * 设置公会对决星星
     * @param sprite
     * @param flag true:满星 false:非满星
     * @param cb
     */
    public static setUnionDefenseStar(sprite: cc.Node | cc.Sprite, flag: boolean, cb?: () => void): void {
        const path = `texture/activity/union/defense/spUBStar${flag ? "2" : "1"}`;
        CocosExt.setSpriteFrameAsync(sprite, path, cb);
    }
}
