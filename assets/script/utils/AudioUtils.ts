/*
 * @Author: chenx
 * @Date: 2024-07-22 11:14:12
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:19:01
 */
import Audio from "../../nsn/audio/Audio";
import { AudioEffectName, AudioEffectPath } from "../../nsn/audio/AudioType";
import UI from "../../nsn/ui/UI";
import { UIType } from "../../nsn/ui/UIType";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import { DungeonType } from "../game/Combat";

export default class AudioUtils {
    private static lastArcherAudioName: string = "";
    private static lastLeadAudioName: string = "";
    private static lastMallAudioName: string = "";
    private static lastUnionShopAudioName: string = "";

    /**
     * 播放战斗音效
     * @param name 名称
     * @param path 路径
     * @param dungeonType 副本类型
     * @param playCb 播放cb
     * @param finishCb 结束cb
     */
    public static playCombatEffect(
        name: AudioEffectName,
        path: AudioEffectPath,
        dungeonType: DungeonType = DungeonType.Main,
        playCb: (audioId: number) => void = null,
        finishCb: (audioId: number) => void = null
    ): void {
        if (dungeonType === DungeonType.Main && UI.getInstance().checkHaveUIType(UIType.UI)) {
            return;
        }

        Audio.getInstance().playEffect(name, path, false, playCb, finishCb);
    }

    /**
     * 播放伙伴配音
     * @param archerId
     */
    public static playArcherEffect(archerId: number, voiceId?: number): void {
        if (this.lastArcherAudioName) {
            Audio.getInstance().stopEffectByName(this.lastArcherAudioName);
        }
        voiceId = voiceId || Math.random() > 0.5 ? 1 : 2;
        const audioName = `archer${archerId}Voice${voiceId}`;
        this.lastArcherAudioName = audioName;
        Audio.getInstance().playEffect(audioName, AUDIO_EFFECT_PATH.ARCHER);
    }

    /**
     * 播放主角配音
     * @param leadId
     */
    public static playLeadEffect(leadId: number): void {
        if (this.lastLeadAudioName) {
            Audio.getInstance().stopEffectByName(this.lastLeadAudioName);
        }
        const audioName = `lead${leadId}Voice${Math.random() > 0.5 ? "1" : "2"}`;
        this.lastLeadAudioName = audioName;
        Audio.getInstance().playEffect(audioName, AUDIO_EFFECT_PATH.LEAD);
    }

    /**
     * 播放商城配音
     */
    public static playMallEffect(): void {
        if (this.lastMallAudioName) {
            Audio.getInstance().stopEffectByName(this.lastMallAudioName);
        }
        const audioName =
            Math.random() > 0.5 ? AUDIO_EFFECT_TYPE.BUSINESS_GIRL_VOICE_1 : AUDIO_EFFECT_TYPE.BUSINESS_GIRL_VOICE_2;
        this.lastMallAudioName = audioName;
        Audio.getInstance().playEffect(audioName, AUDIO_EFFECT_PATH.MALL);
    }

    /**
     * 播放联盟砍价商店配音
     * @param audioName
     */
    public static playUnionTreasureShopEffect(audioName: string): void {
        if (this.lastUnionShopAudioName) {
            Audio.getInstance().stopEffectByName(this.lastUnionShopAudioName);
        }
        this.lastUnionShopAudioName = audioName;
        Audio.getInstance().playEffect(audioName, AUDIO_EFFECT_PATH.UNION);
    }
}
