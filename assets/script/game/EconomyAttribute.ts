/*
 * @Author: chenx
 * @Date: 2025-04-08 10:27:49
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-17 12:01:58
 */
import Logic from "../../nsn/core/Logic";
import MathUtils from "../../nsn/util/MathUtils";
import Utils from "../../nsn/util/Utils";
import {
    ArrowGroupSyncRet,
    BagUpdateRet,
    ForgeCompleteRet,
    ForgeCreateSkinNoticeRet,
    ForgeCultivateUpgradeRet,
    PrivilegeUpdateNoticeRet,
    RechargeNoticeRet,
    TalentTreeCompleteRet,
    TalentTreeUpgradeRet,
} from "../../protobuf/proto";
import Reset, { ResetEvent } from "../core/Reset";
import { EnumEconomyAttributeNature } from "../data/base/BaseEconomyAttribute";
import { EnumFundMarkupType } from "../data/base/BaseFundMarkup";
import { EnumPrivilegeConfigType } from "../data/base/BasePrivilegeConfig";
import { EnumTalentLeafNature } from "../data/base/BaseTalentLeaf";
import DataEconomyAttribute from "../data/extend/DataEconomyAttribute";
import DataItemAttribute from "../data/extend/DataItemAttribute";
import DataPrivilegeConfig from "../data/extend/DataPrivilegeConfig";
import TBEconomyAttribute from "../data/parser/TBEconomyAttribute";
import TBForgeCultivateLevel from "../data/parser/TBForgeCultivateLevel";
import TBForgeLevel from "../data/parser/TBForgeLevel";
import TBForgeSkin from "../data/parser/TBForgeSkin";
import TBFundMarkup from "../data/parser/TBFundMarkup";
import TBItemAttribute from "../data/parser/TBItemAttribute";
import TBPrivilegeConfig from "../data/parser/TBPrivilegeConfig";
import { RECHARGE_ID } from "../data/parser/TBRecharge";
import TBTalentLeaf from "../data/parser/TBTalentLeaf";
import TBTalentLeafGroup from "../data/parser/TBTalentLeafGroup";
import { ICacheAttrData } from "./Attribute";
import Bag from "./Bag";
import CombatLog, { CombatLogId } from "./CombatLog";
import Forge from "./Forge";
import MakeArrow from "./MakeArrow";
import Privilege, { PrivilegeEvent } from "./Privilege";
import Recharge from "./Recharge";
import Talent from "./Talent";

/**
 * 经济属性
 */
export interface IEconomyAttr {
    [attrId: number]: IEconomyAttrData; // 经济属性数据
}

/**
 * 经济属性数据
 */
export interface IEconomyAttrData {
    id: number; // 属性id
    value: number; // 值
    initValue: number; // 初始值
    culValue: number; // 培养值
    info: DataEconomyAttribute; // 信息
    initLog: string; // 初始log
    culLog: string; // 培养log
}

/**
 * 经济属性事件
 */
export enum EconomyAttributeEvent {
    Update = "update", // 更新
}

/**
 * 经济属性
 */
export default class EconomyAttribute extends Logic {
    private makeArrowAttr: IEconomyAttr = null; // 制作箭矢属性
    private miningAttr: IEconomyAttr = null; // 挖矿属性
    private otherAttr: IEconomyAttr = null; // 其他属性

    private forgeAttr: ICacheAttrData = null; // 锻造台
    private forgeLevelAttr: ICacheAttrData = null; // 锻造台等级
    private itemAttr: ICacheAttrData = null; // 道具
    private privilegeAttr: ICacheAttrData = null; // 特权
    private talentAttr: ICacheAttrData = null; // 天赋
    private fundAttr: ICacheAttrData = null; // 基金

    public clear(): void {
        this.makeArrowAttr = null;
        this.miningAttr = null;
        this.otherAttr = null;

        this.forgeAttr = null;
        this.forgeLevelAttr = null;
        this.itemAttr = null;
        this.privilegeAttr = null;
        this.talentAttr = null;
        this.fundAttr = null;
    }

    protected registerHandler(): void {
        // 重置-玩家数据
        Reset.getInstance().on(
            ResetEvent.ResetPlayerData,
            () => {
                this.emit(EconomyAttributeEvent.Update);
            },
            this
        );
        // 背包-更新
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            () => {
                if (this.isUpdateAttrByItem()) {
                    this.emit(EconomyAttributeEvent.Update);
                }
            },
            this
        );
        // 充值-通知
        Recharge.getInstance().on(
            RechargeNoticeRet.prototype.clazzName,
            () => {
                if (this.isUpdateAttrByFund()) {
                    this.emit(EconomyAttributeEvent.Update);
                }
            },
            this
        );
        // 锻造台-获得/升级/培养升级
        Forge.getInstance().on(
            [
                ForgeCreateSkinNoticeRet.prototype.clazzName,
                ForgeCompleteRet.prototype.clazzName,
                ForgeCultivateUpgradeRet.prototype.clazzName,
            ],
            () => {
                this.emit(EconomyAttributeEvent.Update);
            },
            this
        );
        // 特权-更新通知/更新生效状态
        Privilege.getInstance().on(
            [PrivilegeUpdateNoticeRet.prototype.clazzName, PrivilegeEvent.UpdateEffectState],
            () => {
                this.emit(EconomyAttributeEvent.Update);
            },
            this
        );
        // 天赋-升级/完成
        Talent.getInstance().on(
            [TalentTreeUpgradeRet.prototype.clazzName, TalentTreeCompleteRet.prototype.clazzName],
            () => {
                this.emit(EconomyAttributeEvent.Update);
            },
            this
        );
        // 制作箭矢-同步数据
        MakeArrow.getInstance().on(
            ArrowGroupSyncRet.prototype.clazzName,
            () => {
                if (this.isUpdateAttrByFund()) {
                    this.emit(EconomyAttributeEvent.Update);
                }
            },
            this
        );
    }

    /**
     * 初始化经济属性
     * @param type 类型
     * @returns
     */
    private initEconomyAttr(type: EnumEconomyAttributeNature): IEconomyAttr {
        const attr: IEconomyAttr = {};
        const attrInfo = TBEconomyAttribute.getInstance().getDataByNature(type);
        attrInfo.forEach((e) => {
            attr[e.id] = {
                id: e.id,
                value: 0,
                initValue: e.initial,
                culValue: 0,
                info: e,
                initLog: "",
                culLog: "",
            };
            attr[e.id].initLog = `初始-${attr[e.id].initValue}`;
        });

        return attr;
    }

    /**
     * 获取制作箭矢属性
     * @returns
     */
    public getMakeArrowAttr(): IEconomyAttr {
        !this.makeArrowAttr && (this.makeArrowAttr = this.initEconomyAttr(EnumEconomyAttributeNature.ArrowAttribute));

        this.updateAttrByCul(this.makeArrowAttr);

        this.sumAttr(this.makeArrowAttr);

        this.logSystemAttr(CombatLogId.MakeArrowEconomyAttr, this.makeArrowAttr);

        return this.makeArrowAttr;
    }

    /**
     * 获取挖矿属性
     * @returns
     */
    public getMiningAttr(): IEconomyAttr {
        !this.miningAttr && (this.miningAttr = this.initEconomyAttr(EnumEconomyAttributeNature.InstituteAttribute));

        this.updateAttrByCul(this.miningAttr);

        this.sumAttr(this.miningAttr);

        this.logSystemAttr(CombatLogId.MiningEconomyAttr, this.miningAttr);

        return this.miningAttr;
    }

    /**
     * 获取其他属性
     * @returns
     */
    public getOtherAttr(): IEconomyAttr {
        !this.otherAttr && (this.otherAttr = this.initEconomyAttr(EnumEconomyAttributeNature.OtherAttribute));

        this.updateAttrByCul(this.otherAttr);

        this.sumAttr(this.otherAttr);

        this.logSystemAttr(CombatLogId.OtherEconomyAttr, this.otherAttr);

        return this.otherAttr;
    }

    /**
     * 更新属性-培养
     * @param attr 属性
     */
    private updateAttrByCul(attr: IEconomyAttr): void {
        for (const e in attr) {
            attr[e].culValue = 0;
            attr[e].culLog = "";
        }
        // 锻造台
        this.updateAttrByForge(attr);
        // 锻造台等级
        this.updateAttrByForgeLevel(attr);
        // 道具
        this.updateAttrByItem(attr);
        // 特权
        this.updateAttrByPrivilege(attr);
        // 天赋
        this.updateAttrByTalent(attr);
        // 基金
        this.updateAttrByFund(attr);
    }

    /**
     * 汇总属性
     * @param attr 属性
     */
    private sumAttr(attr: IEconomyAttr): void {
        for (const key in attr) {
            attr[key].value = attr[key].initValue + attr[key].culValue;
            attr[key].value = MathUtils.round(attr[key].value, 4);
        }
    }

    /**
     * 更新属性-锻造台
     * @param attr 属性
     */
    private updateAttrByForge(attr: IEconomyAttr): void {
        const forgeId = Forge.getInstance().getSkinInfos();
        let isReset = !this.forgeAttr;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (!this.forgeAttr) {
            forgeId.sort((a, b) => a - b);
            cacheAttrData.para = forgeId.concat();
        }
        if (!isReset) {
            cacheAttrData = this.forgeAttr;
            forgeId.sort((a, b) => a - b);
            const tempPara = forgeId.concat();
            isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
            isReset && (cacheAttrData.para = tempPara);
        }

        if (isReset) {
            cacheAttrData.attr = {};

            forgeId.forEach((e) => {
                const forgeInfo = TBForgeSkin.getInstance().getDataById(e);
                forgeInfo.economyAttribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            this.forgeAttr = cacheAttrData;

            this.logModuleAttr(CombatLogId.ForgeEconomyAttr, cacheAttrData);
        }

        for (const key in cacheAttrData.attr) {
            if (attr[key]) {
                attr[key].culValue += cacheAttrData.attr[key];
                attr[key].culLog += `锻造台-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-锻造台等级
     * @param attr 属性
     */
    private updateAttrByForgeLevel(attr: IEconomyAttr): void {
        const levelId = Forge.getInstance().getForgeLevel();
        const culData = Forge.getInstance().getCultivateInfos();
        let isReset = !this.forgeLevelAttr;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (!this.forgeLevelAttr) {
            cacheAttrData.para.push(levelId);
            culData.sort((a, b) => a.cultivateLevelId - b.cultivateLevelId);
            culData.forEach((e) => {
                cacheAttrData.para.push(e.cultivateLevelId, e.num);
            });
        }
        if (!isReset) {
            cacheAttrData = this.forgeLevelAttr;
            const tempPara: number[] = [];
            tempPara.push(levelId);
            culData.sort((a, b) => a.cultivateLevelId - b.cultivateLevelId);
            culData.forEach((e) => {
                tempPara.push(e.cultivateLevelId, e.num);
            });
            isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
            isReset && (cacheAttrData.para = tempPara);
        }

        if (isReset) {
            cacheAttrData.attr = {};

            const levelInfo = TBForgeLevel.getInstance().getDataById(levelId);
            levelInfo.economyAttribute.forEach(([attrId, init]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init;
            });

            culData.forEach((e) => {
                const culInfo = TBForgeCultivateLevel.getInstance().getDataById(e.cultivateLevelId);
                culInfo.stepEconomyAttribute.forEach(([attrId, init, step]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init + step * e.num;
                });
            });

            this.forgeLevelAttr = cacheAttrData;

            this.logModuleAttr(CombatLogId.ForgeLevelEconomyAttr, cacheAttrData);
        }

        for (const key in cacheAttrData.attr) {
            if (attr[key]) {
                attr[key].culValue += cacheAttrData.attr[key];
                attr[key].culLog += `锻造台等级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-道具
     * @param attr 属性
     */
    private updateAttrByItem(attr: IEconomyAttr): void {
        const itemAttrInfo: DataItemAttribute[] = [];
        const allItemAttrInfo = TBItemAttribute.getInstance().getList();
        allItemAttrInfo.forEach((e) => {
            const { isOwn } = Bag.getInstance().getLimitTimeItemState(e.id);
            isOwn && itemAttrInfo.push(e);
        });
        itemAttrInfo.sort((a, b) => a.id - b.id);
        let isReset = !this.itemAttr;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (!this.itemAttr) {
            itemAttrInfo.forEach((e) => cacheAttrData.para.push(e.id));
        }
        if (!isReset) {
            cacheAttrData = this.itemAttr;
            const tempPara: number[] = [];
            itemAttrInfo.forEach((e) => tempPara.push(e.id));
            isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
            isReset && (cacheAttrData.para = tempPara);
        }

        if (isReset) {
            cacheAttrData.attr = {};

            itemAttrInfo.forEach((e) => {
                e.economyAttribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            this.itemAttr = cacheAttrData;

            this.logModuleAttr(CombatLogId.ItemEconomyAttr, cacheAttrData);
        }

        for (const key in cacheAttrData.attr) {
            if (attr[key]) {
                attr[key].culValue += cacheAttrData.attr[key];
                attr[key].culLog += `道具-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 是否更新属性-道具
     * @returns
     */
    private isUpdateAttrByItem(): boolean {
        if (!this.itemAttr) {
            return false;
        }

        const itemAttrInfo: DataItemAttribute[] = [];
        const allItemAttrInfo = TBItemAttribute.getInstance().getList();
        allItemAttrInfo.forEach((e) => {
            const { isOwn } = Bag.getInstance().getLimitTimeItemState(e.id);
            isOwn && itemAttrInfo.push(e);
        });
        itemAttrInfo.sort((a, b) => a.id - b.id);
        const tempPara: number[] = [];
        itemAttrInfo.forEach((e) => tempPara.push(e.id));
        const isReset = !this.isEqualArray(this.itemAttr.para, tempPara);

        return isReset;
    }

    /**
     * 更新属性-特权
     * @param attr 属性
     */
    private updateAttrByPrivilege(attr: IEconomyAttr): void {
        const privilegeInfo: DataPrivilegeConfig[] = [];
        const allPrivilegeInfo = TBPrivilegeConfig.getInstance().getDataListByType(
            EnumPrivilegeConfigType.EconomyAttribute
        );
        allPrivilegeInfo.forEach((e) => {
            Privilege.getInstance().hasPrivilege(e.id) && privilegeInfo.push(e);
        });
        privilegeInfo.sort((a, b) => a.id - b.id);
        let isReset = !this.privilegeAttr;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (!this.privilegeAttr) {
            privilegeInfo.forEach((e) => cacheAttrData.para.push(e.id));
        }
        if (!isReset) {
            cacheAttrData = this.privilegeAttr;
            const tempPara: number[] = [];
            privilegeInfo.forEach((e) => tempPara.push(e.id));
            isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
            isReset && (cacheAttrData.para = tempPara);
        }

        if (isReset) {
            cacheAttrData.attr = {};

            privilegeInfo.forEach((e) => {
                e.para.attr.forEach((e2: { economyAttribute: number; value: number }) => {
                    cacheAttrData.attr[e2.economyAttribute] = cacheAttrData.attr[e2.economyAttribute] || 0;
                    cacheAttrData.attr[e2.economyAttribute] += e2.value;
                });
            });

            this.privilegeAttr = cacheAttrData;

            this.logModuleAttr(CombatLogId.PrivilegeEconomyAttr, cacheAttrData);
        }

        for (const key in cacheAttrData.attr) {
            if (attr[key]) {
                attr[key].culValue += cacheAttrData.attr[key];
                attr[key].culLog += `特权-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-天赋
     * @param attr 属性
     */
    private updateAttrByTalent(attr: IEconomyAttr): void {
        const talentData = Talent.getInstance().getAllTreeData();
        talentData.sort((a, b) => a.treeType - b.treeType);
        talentData.forEach((e) => e.leafInfos.sort((a, b) => a.leafId - b.leafId));
        let isReset = !this.talentAttr;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (!this.talentAttr) {
            talentData.forEach((e) => {
                e.leafInfos.forEach((e2) => cacheAttrData.para.push(e2.leafId, e2.level));
            });
        }
        if (!isReset) {
            cacheAttrData = this.talentAttr;
            const tempPara: number[] = [];
            talentData.forEach((e) => {
                e.leafInfos.forEach((e2) => tempPara.push(e2.leafId, e2.level));
            });
            isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
            isReset && (cacheAttrData.para = tempPara);
        }

        if (isReset) {
            cacheAttrData.attr = {};

            talentData.forEach((e) => {
                e.leafInfos.forEach((e2) => {
                    const leafInfo = TBTalentLeaf.getInstance().getDataById(e2.leafId);
                    if (leafInfo.nature === EnumTalentLeafNature.EconomyAttribute) {
                        const leafGroupInfo = TBTalentLeafGroup.getInstance().getDataByGroupAndLevel(
                            leafInfo.group,
                            e2.level
                        );
                        const [attrId, init] = leafGroupInfo.attribute[0];
                        cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                        cacheAttrData.attr[attrId] += init;
                    }
                });
            });

            this.talentAttr = cacheAttrData;

            this.logModuleAttr(CombatLogId.TalentEconomyAttr, cacheAttrData);
        }

        for (const key in cacheAttrData.attr) {
            if (attr[key]) {
                attr[key].culValue += cacheAttrData.attr[key];
                attr[key].culLog += `天赋-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-基金
     * @param attr 属性
     */
    private updateAttrByFund(attr: IEconomyAttr): void {
        const allFundAttrInfo = TBFundMarkup.getInstance().getDataByType(EnumFundMarkupType.ReputationPackage);
        allFundAttrInfo.sort((a, b) => b.cost - a.cost);
        const makeArrowStoneNum = MakeArrow.getInstance().getForgeIronCostCounts();
        const fundAttrInfo = allFundAttrInfo.find((e) => e.cost <= makeArrowStoneNum);
        const isRecharged = Recharge.getInstance().isRecharged(RECHARGE_ID.POWER_FUND);
        let isReset = !this.fundAttr;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (!this.fundAttr) {
            cacheAttrData.para.push(fundAttrInfo && isRecharged ? fundAttrInfo.id : -1);
        }
        if (!isReset) {
            cacheAttrData = this.fundAttr;
            const tempPara: number[] = [];
            tempPara.push(fundAttrInfo && isRecharged ? fundAttrInfo.id : -1);
            isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
            isReset && (cacheAttrData.para = tempPara);
        }

        if (isReset) {
            cacheAttrData.attr = {};

            if (fundAttrInfo && isRecharged) {
                fundAttrInfo.economyAttribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            }

            this.fundAttr = cacheAttrData;

            this.logModuleAttr(CombatLogId.FundEconomyAttr, cacheAttrData);
        }

        for (const key in cacheAttrData.attr) {
            if (attr[key]) {
                attr[key].culValue += cacheAttrData.attr[key];
                attr[key].culLog += `基金-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 是否更新属性-基金
     * @returns
     */
    private isUpdateAttrByFund(): boolean {
        if (!this.fundAttr) {
            return false;
        }

        const allFundAttrInfo = TBFundMarkup.getInstance().getDataByType(EnumFundMarkupType.ReputationPackage);
        allFundAttrInfo.sort((a, b) => b.cost - a.cost);
        const makeArrowStoneNum = MakeArrow.getInstance().getForgeIronCostCounts();
        const fundAttrInfo = allFundAttrInfo.find((e) => e.cost <= makeArrowStoneNum);
        const isRecharged = Recharge.getInstance().isRecharged(RECHARGE_ID.POWER_FUND);
        const tempPara: number[] = [];
        tempPara.push(fundAttrInfo && isRecharged ? fundAttrInfo.id : -1);
        const isReset = !this.isEqualArray(this.fundAttr.para, tempPara);

        return isReset;
    }

    /**
     * 数组是否相等
     * @param arr
     * @param arr2
     * @returns
     */
    private isEqualArray(arr: number[], arr2: number[]): boolean {
        if (arr.length !== arr2.length) {
            return false;
        }
        for (let i = 0; i < arr.length; i++) {
            if (arr[i] !== arr2[i]) {
                return false;
            }
        }

        return true;
    }

    /**
     * 打印日志-系统属性
     * @param logId 日志id
     * @param attr 属性
     */
    public logSystemAttr(logId: number, attr: IEconomyAttr): void {
        if (!CombatLog.getInstance().getShowState(logId)) {
            return;
        }

        CombatLog.getInstance().logTitle(logId);

        cc.log(Utils.clone(attr));
    }

    /**
     * 打印日志-模块属性
     * @param logId 日志id
     * @param attrData 属性数据
     */
    public logModuleAttr(logId: number, attrData: ICacheAttrData): void {
        if (!CombatLog.getInstance().getShowState(logId)) {
            return;
        }

        CombatLog.getInstance().logTitle(logId);

        const logData: { attr: { [attrName: string]: number }; para: number[] } = { attr: {}, para: attrData.para };
        for (const attrId in attrData.attr) {
            const attrInfo = TBEconomyAttribute.getInstance().getDataById(parseInt(attrId));
            logData.attr[`${attrId}#${attrInfo.name}`] = attrData.attr[attrId];
        }
        cc.log(logData);
    }
}
