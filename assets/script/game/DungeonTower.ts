/*
 * @Author: chenx
 * @Date: 2025-01-16 10:44:02
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 11:57:57
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import Tips from "../../nsn/util/Tips";
import {
    DungeonTowerBoxReward,
    DungeonTowerBoxRewardRet,
    DungeonTowerFail,
    DungeonTowerFailRet,
    DungeonTowerInitRet,
    DungeonTowerReward,
    DungeonTowerRewardRet,
    IDungeonTowerObj,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import Combat, { CombatEvent, DungeonType } from "./Combat";

/**
 * 爬塔副本
 */
export default class DungeonTower extends Logic {
    private data: IDungeonTowerObj = null; // 爬塔副本数据

    protected registerHandler(): void {
        Socket.getInstance().on(DungeonTowerInitRet.prototype.clazzName, this.dungeonTowerInitRet, this);
        Socket.getInstance().on(DungeonTowerRewardRet.prototype.clazzName, this.dungeonTowerRewardRet, this);
        Socket.getInstance().on(DungeonTowerFailRet.prototype.clazzName, this.dungeonTowerFailRet, this);
        Socket.getInstance().on(DungeonTowerBoxRewardRet.prototype.clazzName, this.dungeonTowerBoxRewardRet, this);
    }

    public clear(): void {
        this.data = null;
    }

    /**
     * 初始化数据
     * @param data
     */
    private dungeonTowerInitRet(data: DungeonTowerInitRet): void {
        const { errMsg, dungeonTowerInfo } = data;
        if (errMsg !== DungeonTowerInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonTowerInitRet[errMsg]);
            return;
        }

        this.data = dungeonTowerInfo;
    }

    /**
     * 领取天虹之塔过关奖励
     * @param data
     * @returns
     */
    private dungeonTowerRewardRet(data: DungeonTowerRewardRet): void {
        const { errMsg, dungeonTowerInfo } = data;
        if (errMsg !== DungeonTowerRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonTowerRewardRet[errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.Tower);
            return;
        }
        this.data = dungeonTowerInfo;
        this.emit(DungeonTowerRewardRet.prototype.clazzName, data);
    }

    /**
     * 战斗失败
     * @param data
     */
    private dungeonTowerFailRet(data: DungeonTowerFailRet): void {
        const { errMsg } = data;
        if (errMsg !== DungeonTowerFailRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonTowerFailRet[errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.Tower);
            return;
        }

        this.emit(DungeonTowerFailRet.prototype.clazzName);
    }

    /**
     * 领取宝箱奖励
     * @param data
     * @returns
     */
    private dungeonTowerBoxRewardRet(data: DungeonTowerBoxRewardRet): void {
        const { errMsg } = data;
        if (errMsg !== DungeonTowerBoxRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonTowerBoxRewardRet[errMsg]);
            return;
        }
        this.data.isReward = false;
        this.emit(DungeonTowerBoxRewardRet.prototype.clazzName);
    }

    /**
     * 获取当前关卡id
     */
    public getCurDungeonId(): number {
        return this.data.curDungeonId;
    }

    /**
     * 获取上次恢复时间
     * @returns
     */
    public getLastRecoverTime(): number {
        return this.data.lastRecoverTime;
    }

    /**
     * 更新上次恢复时间
     * @param value
     */
    public setLastRecoverTime(value: number): void {
        this.data.lastRecoverTime = value;
    }

    /**
     * 是否有宝箱奖励未领取
     * @returns
     */
    public hasReward(): boolean {
        return this.data.isReward;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 领取天虹之塔过关奖励
     * @param isSkip
     * @param count
     */
    public sendDungeonTowerReward(isSkip: boolean = false, count: number = 1): void {
        const data = DungeonTowerReward.create({ isSkip, count });
        Socket.getInstance().send(data);
    }

    /**
     * 战斗失败
     */
    public sendCombatFailure(): void {
        const data = DungeonTowerFail.create();
        Socket.getInstance().send(data);
    }

    /**
     * 领取宝箱奖励
     */
    public sendDungeonTowerBoxReward(): void {
        const data = DungeonTowerBoxReward.create();
        Socket.getInstance().send(data);
    }
}
