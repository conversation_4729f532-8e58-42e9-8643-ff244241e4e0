/*
 * @Author: chenx
 * @Date: 2024-09-26 17:47:01
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-11-29 16:45:48
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Time, { MINUTE_TO_SECOND } from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    ChildDealType,
    ChildSearchType,
    ConfidantChildBatchRejectApply,
    ConfidantChildBatchRejectApplyRet,
    ConfidantChildCancelPropose,
    ConfidantChildCancelProposeRet,
    ConfidantChildDealApply,
    ConfidantChildDealApplyNotice,
    ConfidantChildDealApplyRet,
    ConfidantChildMarryApply,
    ConfidantChildMarryApplyNotice,
    ConfidantChildMarryApplyRet,
    ConfidantChildModifyName,
    ConfidantChildModifyNameRet,
    ConfidantChildPublish,
    ConfidantChildPublishRet,
    ConfidantChildSearch,
    ConfidantChildSearchRet,
    ConfidantChildUpgrade,
    ConfidantChildUpgradeRet,
    ConfidantCreatePrincessNotice,
    ConfidantExchangeStrength,
    ConfidantExchangeStrengthRet,
    ConfidantFavorType,
    ConfidantInitRet,
    ConfidantPrincessFavor,
    ConfidantPrincessFavorRet,
    ConfidantPrincessMarry,
    ConfidantPrincessMarryRet,
    ConfidantPrincessUnlock,
    ConfidantPrincessUnlockRet,
    ConfidantPrincessUpgrade,
    ConfidantPrincessUpgradeRet,
    ConfidantTraveling,
    ConfidantTravelingRet,
    IChildInfo,
    IConfidantInfo,
    IPrincessInfo,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointId } from "../core/redPoint/RedPointId";
import { EnumPrincessTotalPara } from "../data/base/BasePrincessTotal";
import DataPrincessChild from "../data/extend/DataPrincessChild";
import TBPrincess from "../data/parser/TBPrincess";
import TBPrincessChild from "../data/parser/TBPrincessChild";
import TBPrincessTotal from "../data/parser/TBPrincessTotal";
import { IFloatConfidantUpgradeArgs } from "../ui/FloatConfidantUpgrade";

/**
 * 知己-王储数据
 */
export interface IConfidantChildData {
    id: number; // 王储id
    quality: number; // 品质
    score: number; // 评分
    info: DataPrincessChild; // 王储信息
    data: IChildInfo; // 王储数据
    isSelected: boolean; // 是否已选择
}

/**
 * 知己事件
 */
export enum ConfidantEvent {
    SelectUnmarriedChild = "select-unmarried-child", // 选择未婚王储
    SelectMarriedChild = "select-married-child", // 选择已婚王储
    UpdateListUnmarried = "update-list-unmarried", // 更新未婚王储列表
    UpdateListApply = "update-list-apply", // 更新王储申请列表
    RecoverTravelPower = "recover-travel-power", // 恢复游历体力
}

/**
 * 知己
 */
export default class Confidant extends Logic {
    private data: IConfidantInfo = null; // 知己数据

    protected registerHandler(): void {
        Socket.getInstance().on(ConfidantInitRet.prototype.clazzName, this.confidantInitRet, this);

        Socket.getInstance().on(ConfidantPrincessUnlockRet.prototype.clazzName, this.confidantPrincessUnlockRet, this);
        Socket.getInstance().on(
            ConfidantCreatePrincessNotice.prototype.clazzName,
            this.confidantCreatePrincessNotice,
            this
        );
        Socket.getInstance().on(
            ConfidantPrincessUpgradeRet.prototype.clazzName,
            this.confidantPrincessUpgradeRet,
            this
        );
        Socket.getInstance().on(ConfidantPrincessFavorRet.prototype.clazzName, this.confidantPrincessFavorRet, this);
        Socket.getInstance().on(ConfidantPrincessMarryRet.prototype.clazzName, this.confidantPrincessMarryRet, this);
        Socket.getInstance().on(ConfidantTravelingRet.prototype.clazzName, this.confidantTravelingRet, this);
        Socket.getInstance().on(
            ConfidantExchangeStrengthRet.prototype.clazzName,
            this.confidantExchangeStrengthRet,
            this
        );

        Socket.getInstance().on(
            ConfidantChildModifyNameRet.prototype.clazzName,
            this.confidantChildModifyNameRet,
            this
        );
        Socket.getInstance().on(ConfidantChildUpgradeRet.prototype.clazzName, this.confidantChildUpgradeRet, this);
        Socket.getInstance().on(ConfidantChildPublishRet.prototype.clazzName, this.confidantChildPublishRet, this);
        Socket.getInstance().on(
            ConfidantChildCancelProposeRet.prototype.clazzName,
            this.confidantChildCancelProposeRet,
            this
        );
        Socket.getInstance().on(ConfidantChildSearchRet.prototype.clazzName, this.confidantChildSearchRet, this);
        Socket.getInstance().on(
            ConfidantChildMarryApplyRet.prototype.clazzName,
            this.confidantChildMarryApplyRet,
            this
        );
        Socket.getInstance().on(
            ConfidantChildMarryApplyNotice.prototype.clazzName,
            this.confidantChildMarryApplyNotice,
            this
        );
        Socket.getInstance().on(ConfidantChildDealApplyRet.prototype.clazzName, this.confidantChildDealApplyRet, this);
        Socket.getInstance().on(
            ConfidantChildDealApplyNotice.prototype.clazzName,
            this.confidantChildDealApplyNotice,
            this
        );
        Socket.getInstance().on(
            ConfidantChildBatchRejectApplyRet.prototype.clazzName,
            this.confidantChildBatchRejectApplyRet,
            this
        );
    }

    public clear(): void {
        this.data = null;
    }

    /**
     * 初始化数据
     * @param data
     */
    private confidantInitRet(data: ConfidantInitRet): void {
        const { errMsg, confidantInfo } = data;
        if (errMsg !== ConfidantInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantInitRet[errMsg]);
            return;
        }

        this.data = confidantInfo;
        const powerLimit = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.StrengthLimit);
        const recoverDuration =
            TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.StrengthRecoveryTime) * 1000;
        const nowTime = Time.getInstance().now();
        while (this.data.remainStrength < powerLimit && nowTime - this.data.recoverTime >= recoverDuration) {
            this.data.remainStrength += 1;
            this.data.recoverTime += recoverDuration;
        }

        this.emit(ConfidantInitRet.prototype.clazzName);
    }

    /**
     * 知己-解锁
     * @param data
     */
    private confidantPrincessUnlockRet(data: ConfidantPrincessUnlockRet): void {
        const { errMsg } = data;
        if (errMsg !== ConfidantPrincessUnlockRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantPrincessUnlockRet[errMsg]);
            return;
        }
    }

    /**
     * 知己-新增
     * @param data
     */
    private confidantCreatePrincessNotice(data: ConfidantCreatePrincessNotice): void {
        const { errMsg, princessInfo } = data;
        if (errMsg !== ConfidantCreatePrincessNotice.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantCreatePrincessNotice[errMsg]);
            return;
        }

        this.data.princessInfos.push(princessInfo);

        this.emit(ConfidantCreatePrincessNotice.prototype.clazzName, data);
    }

    /**
     * 知己-升级
     * @param data
     */
    private confidantPrincessUpgradeRet(data: ConfidantPrincessUpgradeRet): void {
        const { errMsg, princessInfo } = data;
        if (errMsg !== ConfidantPrincessUpgradeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantPrincessUpgradeRet[errMsg]);
            return;
        }

        const index = this.data.princessInfos.findIndex((e) => e.princessId === princessInfo.princessId);
        this.data.princessInfos[index] = princessInfo;

        this.emit(ConfidantPrincessUpgradeRet.prototype.clazzName, data);
    }

    /**
     * 知己-宠幸
     * @param data
     */
    private confidantPrincessFavorRet(data: ConfidantPrincessFavorRet): void {
        const { errMsg, childInfos, favorType } = data;
        if (errMsg !== ConfidantPrincessFavorRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantPrincessFavorRet[errMsg]);
            return;
        }

        this.data.childInfos.push(...childInfos);
        favorType === ConfidantFavorType.Random && this.data.randomFavorRemainTimes--;

        this.emit(ConfidantPrincessFavorRet.prototype.clazzName, data);
    }

    /**
     * 知己-结婚
     * @param data
     */
    private confidantPrincessMarryRet(data: ConfidantPrincessMarryRet): void {
        const { errMsg, princessId } = data;
        if (errMsg !== ConfidantPrincessMarryRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantPrincessMarryRet[errMsg]);
            return;
        }

        const confidantData = this.data.princessInfos.find((e) => e.princessId === princessId);
        confidantData.isMarry = true;

        this.emit(ConfidantPrincessMarryRet.prototype.clazzName, data);
    }

    /**
     * 知己-游历
     * @param data
     */
    private confidantTravelingRet(data: ConfidantTravelingRet): void {
        const { errMsg, confidantEventInfos, recoverTime, remainStrength } = data;
        if (errMsg !== ConfidantTravelingRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantTravelingRet[errMsg]);
            return;
        }

        confidantEventInfos.forEach((e) => {
            if (e.princessInfos) {
                const index = this.data.princessInfos.findIndex((e2) => e2.princessId === e.princessInfos.princessId);
                this.data.princessInfos[index] = e.princessInfos;

                if (e.princessInfos.progress === 0) {
                    const initData: IFloatConfidantUpgradeArgs = {
                        confidantId: e.princessInfos.princessId,
                        childId: -1,
                        level: e.princessInfos.level,
                    };
                    UI.getInstance().pushBuffer("FloatConfidantUpgrade", initData);
                }
            }
        });
        this.data.remainStrength = remainStrength;
        this.data.recoverTime = recoverTime;

        this.emit(ConfidantTravelingRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.ConfidantTravel);
    }

    /**
     * 知己-获取游历体力
     * @param data
     */
    private confidantExchangeStrengthRet(data: ConfidantExchangeStrengthRet): void {
        const { errMsg, recoverTime, remainStrength } = data;
        if (errMsg !== ConfidantExchangeStrengthRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantExchangeStrengthRet[errMsg]);
            return;
        }

        this.data.remainStrength = remainStrength;
        this.data.recoverTime = recoverTime;

        this.emit(ConfidantExchangeStrengthRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.ConfidantTravel);
    }

    /**
     * 王储-重命名
     * @param data
     */
    private confidantChildModifyNameRet(data: ConfidantChildModifyNameRet): void {
        const { errMsg, childUuid, name } = data;
        if (errMsg !== ConfidantChildModifyNameRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildModifyNameRet[errMsg]);
            return;
        }

        const childData = this.data.childInfos.find((e) => e.childUuid === childUuid);
        childData.name = name;

        this.emit(ConfidantChildModifyNameRet.prototype.clazzName, data);
    }

    /**
     * 王储-升级
     * @param data
     */
    private confidantChildUpgradeRet(data: ConfidantChildUpgradeRet): void {
        const { errMsg, childUuid } = data;
        if (errMsg !== ConfidantChildUpgradeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildUpgradeRet[errMsg]);
            return;
        }

        const childData = this.data.childInfos.find((e) => e.childUuid === childUuid);
        childData.level++;
        const childInfo = TBPrincessChild.getInstance().getDataById(childData.childId);
        childData.isMaxLevel = childData.level === childInfo.levelLimit;
        if (childData.isMaxLevel) {
            RedPoint.getInstance().cancelRecord(RedPointId.ChildMarriageUnmarriedNew);
            RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedNew);
        }

        this.emit(ConfidantChildUpgradeRet.prototype.clazzName, data);
    }

    /**
     * 王储-招亲
     * @param data
     */
    private confidantChildPublishRet(data: ConfidantChildPublishRet): void {
        const { errMsg, childUuid, proposeExpireTime } = data;
        if (errMsg !== ConfidantChildPublishRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildPublishRet[errMsg]);
            return;
        }

        const childData = this.data.childInfos.find((e) => e.childUuid === childUuid);
        childData.proposeExpireTime = proposeExpireTime;

        this.emit(ConfidantChildPublishRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedNew);
    }

    /**
     * 王储-取消招亲
     * @param data
     */
    private confidantChildCancelProposeRet(data: ConfidantChildCancelProposeRet): void {
        const { errMsg, childUuid } = data;
        if (errMsg !== ConfidantChildCancelProposeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildCancelProposeRet[errMsg]);
            return;
        }

        const childData = this.data.childInfos.find((e) => e.childUuid === childUuid);
        childData.proposeExpireTime = 0;

        this.emit(ConfidantChildCancelProposeRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedNew);
    }

    /**
     * 王储-搜索
     * @param data
     */
    private confidantChildSearchRet(data: ConfidantChildSearchRet): void {
        const { errMsg } = data;
        if (errMsg !== ConfidantChildSearchRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildSearchRet[errMsg]);
            return;
        }

        this.emit(ConfidantChildSearchRet.prototype.clazzName, data);
    }

    /**
     * 王储-申请
     * @param data
     */
    private confidantChildMarryApplyRet(data: ConfidantChildMarryApplyRet): void {
        const { errMsg, myChildUuid, companionInfo, appointPlayerInfo, proposeExpireTime } = data;
        if (errMsg !== ConfidantChildMarryApplyRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildMarryApplyRet[errMsg]);
            return;
        }

        const childData = this.data.childInfos.find((e) => e.childUuid === myChildUuid);
        childData.companionInfo = companionInfo;
        childData.appointPlayerInfo = appointPlayerInfo;
        childData.proposeExpireTime = proposeExpireTime;

        this.emit(ConfidantChildMarryApplyRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedApply);
        RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedNew);
    }

    /**
     * 王储-申请通知
     * @param data
     */
    private confidantChildMarryApplyNotice(data: ConfidantChildMarryApplyNotice): void {
        const { errMsg, childUuid, applyInfo } = data;
        if (errMsg !== ConfidantChildMarryApplyNotice.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildMarryApplyNotice[errMsg]);
            return;
        }

        const childData = this.data.childInfos.find((e) => e.childUuid === childUuid);
        childData.marryApplyInfos.push(applyInfo);

        this.emit(ConfidantChildMarryApplyNotice.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedApply);
    }

    /**
     * 王储-申请处理
     * @param data
     */
    private confidantChildDealApplyRet(data: ConfidantChildDealApplyRet): void {
        const { errMsg, childUuid, applyUuid, companionInfo } = data;
        if (errMsg !== ConfidantChildDealApplyRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildDealApplyRet[errMsg]);
            return;
        }

        const childData = this.data.childInfos.find((e) => e.childUuid === childUuid);
        const index = childData.marryApplyInfos.findIndex((e) => e.applyUuid === applyUuid);
        index !== -1 && childData.marryApplyInfos.splice(index, 1);
        childData.companionInfo = companionInfo;

        this.emit(ConfidantChildDealApplyRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedApply);
    }

    /**
     * 王储-申请处理通知
     * @param data
     */
    private confidantChildDealApplyNotice(data: ConfidantChildDealApplyNotice): void {
        const { errMsg, childUuid, companionInfo } = data;
        if (errMsg !== ConfidantChildDealApplyNotice.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildDealApplyNotice[errMsg]);
            return;
        }

        const childData = this.data.childInfos.find((e) => e.childUuid === childUuid);
        childData.appointPlayerInfo = null;
        childData.proposeExpireTime = 0;
        childData.companionInfo = companionInfo;

        this.emit(ConfidantChildDealApplyNotice.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedApply);
        RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedNew);
    }

    /**
     * 王储-申请批量拒绝
     * @param data
     */
    private confidantChildBatchRejectApplyRet(data: ConfidantChildBatchRejectApplyRet): void {
        const { errMsg, childUuid } = data;
        if (errMsg !== ConfidantChildBatchRejectApplyRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.confidantChildBatchRejectApplyRet[errMsg]);
            return;
        }

        const childData = this.data.childInfos.find((e) => e.childUuid === childUuid);
        childData.marryApplyInfos = [];

        this.emit(ConfidantChildBatchRejectApplyRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.ChildMarriageUnmarriedApply);
    }

    /**
     * 获取知己数据
     * @returns
     */
    public getAllConfidantData(): IPrincessInfo[] {
        return this.data.princessInfos;
    }

    /**
     * 获取知己数据
     * @param confidantId 知己id
     * @returns
     */
    public getConfidantData(confidantId: number): IPrincessInfo {
        return this.data.princessInfos.find((e) => e.princessId === confidantId);
    }

    /**
     * 获取随机宠幸的免费次数
     * @returns
     */
    public getRandomFavorFreeTimes(): number {
        return this.data.randomFavorRemainTimes;
    }

    /**
     * 获取王储数据
     * @returns
     */
    public getAllChildData(): IChildInfo[] {
        return this.data.childInfos;
    }

    /**
     * 获取未婚王储数据-满级
     * @returns
     */
    public getUnmarriedChildData(): IChildInfo[] {
        return this.data.childInfos.filter((e) => !e.companionInfo && e.isMaxLevel);
    }

    /**
     * 获取未婚王储数据-非满级
     * @returns
     */
    public getUnmarriedChildData2(): IChildInfo[] {
        return this.data.childInfos.filter((e) => !e.companionInfo && !e.isMaxLevel);
    }

    /**
     * 获取已婚王储数据
     * @returns
     */
    public getMarriedChildData(): IChildInfo[] {
        return this.data.childInfos.filter((e) => e.companionInfo);
    }

    /**
     * 获取王储数据
     * @param childUuid 王储uuid
     * @returns
     */
    public getChildData(childUuid: string): IChildInfo {
        return this.data.childInfos.find((e) => e.childUuid === childUuid);
    }

    /**
     * 获取游历体力
     * @returns
     */
    public getTravelPower(): number {
        return this.data.remainStrength;
    }

    /**
     * 获取体力恢复时间-游历
     * @returns
     */
    public getPowerRecoverTimeByTravel(): number {
        return this.data.recoverTime;
    }

    /**
     * 更新体力状态
     */
    public updateTravelPowerState(): void {
        const powerLimit = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.StrengthLimit);
        if (this.data.remainStrength >= powerLimit) {
            return;
        }

        const nowTime = Time.getInstance().now();
        const recoverDuration =
            TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.StrengthRecoveryTime) * 1000;
        if (nowTime - this.data.recoverTime >= recoverDuration) {
            this.data.remainStrength += 1;
            this.data.recoverTime += recoverDuration;

            this.emit(ConfidantEvent.RecoverTravelPower);
            RedPoint.getInstance().checkRelative(RedPointId.ConfidantTravel);
        }
    }

    /**
     * 获取属性-知己
     * @returns
     */
    public getAttrByConfidant(): number[][] {
        const attr: number[][] = [];
        this.data.princessInfos.forEach((e) => {
            const confidantInfo = TBPrincess.getInstance().getDataById(e.princessId);
            confidantInfo.attribute.forEach(([attrId, init, step]) => {
                const index = attr.findIndex(([attrId2]) => attrId2 === attrId);
                if (index !== -1) {
                    attr[index][1] += init + step * (e.level - 1);
                } else {
                    attr.push([attrId, init + step * (e.level - 1)]);
                }
            });
        });

        return attr;
    }

    /**
     * 获取属性-王储
     * @returns
     */
    public getAttrByChild(): number[][] {
        const attr: number[][] = [];
        this.data.childInfos.forEach((e) => {
            const childInfo = TBPrincessChild.getInstance().getDataById(e.childId);
            childInfo.attribute.forEach(([attrId, init, step]) => {
                const index = attr.findIndex(([attrId2]) => attrId2 === attrId);
                if (index !== -1) {
                    attr[index][1] += init + step * (e.level - 1);
                } else {
                    attr.push([attrId, init + step * (e.level - 1)]);
                }
            });
            if (e.companionInfo) {
                const childInfo2 = TBPrincessChild.getInstance().getDataById(e.companionInfo.childId);
                childInfo2.attribute.forEach(([attrId, init, step]) => {
                    const index = attr.findIndex(([attrId2]) => attrId2 === attrId);
                    if (index !== -1) {
                        attr[index][1] += init + step * (childInfo2.levelLimit - 1);
                    } else {
                        attr.push([attrId, init + step * (childInfo2.levelLimit - 1)]);
                    }
                });
            }
        });

        return attr;
    }

    /**
     * 获取未婚王储的申请红点
     * @returns
     */
    public getChildUnMarriedApplyRed(): boolean {
        let isRed = false;
        const childData = this.getUnmarriedChildData();
        const nowTime = Time.getInstance().now();
        for (const e of childData) {
            isRed = e.marryApplyInfos.findIndex((e2) => e2.applyExpireTime > nowTime) !== -1;
            if (isRed) {
                break;
            }
        }

        return isRed;
    }

    /**
     * 获取未婚王储的新红点
     * @returns
     */
    public getChildUnmarriedNewRed(): boolean {
        const childData = this.getUnmarriedChildData();
        const nowTime = Time.getInstance().now();
        const isRecord = RedPoint.getInstance().isRecord(RedPointId.ChildMarriageUnmarriedNew);
        if (isRecord) {
            for (const e of childData) {
                if (e.proposeExpireTime <= nowTime && e.proposeExpireTime > nowTime - MINUTE_TO_SECOND * 1000) {
                    RedPoint.getInstance().cancelRecord(RedPointId.ChildMarriageUnmarriedNew);
                    break;
                }
            }
        }

        let isRed = false;
        for (const e of childData) {
            isRed = e.proposeExpireTime <= nowTime;
            if (isRed) {
                break;
            }
        }
        if (isRed) {
            isRed = !RedPoint.getInstance().isRecord(RedPointId.ChildMarriageUnmarriedNew);
        }

        return isRed;
    }

    /**
     * 获取游历红点
     * @returns
     */
    public getTravelRed(): boolean {
        const power = this.getTravelPower();
        const costPower = TBPrincessTotal.getInstance().getValueByPara(EnumPrincessTotalPara.TravelingConsumeStrength);
        const isRed = power >= costPower;

        return isRed;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 知己-解锁
     * @param princessId 知己id
     */
    public sendUnlockByConfidant(princessId: number): void {
        const data = ConfidantPrincessUnlock.create({ princessId });
        Socket.getInstance().send(data);
    }

    /**
     * 知己-升级
     * @param princessId 知己id
     * @param count 消耗数量
     */
    public sendUpgradeByConfidant(princessId: number, count: number): void {
        const data = ConfidantPrincessUpgrade.create({ princessId, count });
        Socket.getInstance().send(data);
    }

    /**
     * 知己-宠幸
     * @param princessId 知己id
     * @param favorType 宠幸类型
     */
    public sendFavorByConfidant(princessId: number, favorType: ConfidantFavorType): void {
        const data = ConfidantPrincessFavor.create({ princessId, favorType });
        Socket.getInstance().send(data);
    }

    /**
     * 知己-结婚
     * @param princessId 知己id
     */
    public sendMarryByConfidant(princessId: number): void {
        const data = ConfidantPrincessMarry.create({ princessId });
        Socket.getInstance().send(data);
    }

    /**
     * 知己-游历
     * @param times 次数
     */
    public sendTravelByConfidant(times: number): void {
        const data = ConfidantTraveling.create({ times });
        Socket.getInstance().send(data);
    }

    /**
     * 知己-获取游历体力
     * @param counts 消耗数量
     */
    public sendGetTravelPowerByConfidant(counts: number): void {
        const data = ConfidantExchangeStrength.create({ counts });
        Socket.getInstance().send(data);
    }

    /**
     * 王储-重命名
     * @param childUuid 王储uuid
     * @param name 名称
     */
    public sendRenameByChild(childUuid: string, name: string): void {
        const data = ConfidantChildModifyName.create({ childUuid, name });
        Socket.getInstance().send(data);
    }

    /**
     * 王储-升级
     * @param childUuid 王储uuid
     */
    public sendUpgradeByChild(childUuid: string): void {
        const data = ConfidantChildUpgrade.create({ childUuid });
        Socket.getInstance().send(data);
    }

    /**
     * 王储-招亲
     * @param childUuid 王储uuid
     */
    public sendChildShare(childUuid: string): void {
        const data = ConfidantChildPublish.create({ childUuid });
        Socket.getInstance().send(data);
    }

    /**
     * 王储-取消招亲
     * @param childUuid 王储uuid
     */
    public sendChildCancelShare(childUuid: string): void {
        const data = ConfidantChildCancelPropose.create({ childUuid });
        Socket.getInstance().send(data);
    }

    /**
     * 王储-搜索
     * @param sexType 性别
     * @param childSearchType 搜索类型
     * @param gameId 游戏id
     */
    public sendChildSearch(
        sexType: ConfidantChildSearch.SexType,
        childSearchType: ChildSearchType,
        gameId: number = 0
    ): void {
        const data = ConfidantChildSearch.create({ sexType, childSearchType, gameId });
        Socket.getInstance().send(data);
    }

    /**
     * 王储-申请
     * @param myChildUuid 王储uuid
     * @param otherChildUuid 王储uuid
     */
    public sendChildApply(myChildUuid: string, otherChildUuid: string): void {
        const data = ConfidantChildMarryApply.create({ myChildUuid, otherChildUuid });
        Socket.getInstance().send(data);
    }

    /**
     * 王储-申请处理
     * @param childUuid 王储uuid
     * @param applyUuid 申请uuid
     * @param dealType 处理类型
     */
    public sendChildApplyProcess(childUuid: string, applyUuid: string, dealType: ChildDealType): void {
        const data = ConfidantChildDealApply.create({ childUuid, applyUuid, dealType });
        Socket.getInstance().send(data);
    }

    /**
     * 王储-申请批量拒绝
     * @param childUuid 王储uuid
     */
    public sendChildBatchRefuse(childUuid: string): void {
        const data = ConfidantChildBatchRejectApply.create({ childUuid });
        Socket.getInstance().send(data);
    }
}
