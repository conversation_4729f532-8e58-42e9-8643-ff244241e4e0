/*
 * @Author: chenx
 * @Date: 2025-01-16 10:44:16
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 11:57:52
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import Tips from "../../nsn/util/Tips";
import {
    DungeonThiefInitRet,
    DungeonThiefReward,
    DungeonThiefRewardRet,
    DungeonThiefSweetReward,
    DungeonThiefSweetRewardRet,
    IDungeonThiefObj,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import Combat, { CombatEvent, DungeonType } from "./Combat";

/**
 * 怪盗副本
 */
export default class DungeonThief extends Logic {
    private data: IDungeonThiefObj = null; // 怪盗副本数据

    protected registerHandler(): void {
        Socket.getInstance().on(DungeonThiefInitRet.prototype.clazzName, this.dungeonThiefInitRet, this);
        Socket.getInstance().on(DungeonThiefRewardRet.prototype.clazzName, this.dungeonThiefRewardRet, this);
        Socket.getInstance().on(DungeonThiefSweetRewardRet.prototype.clazzName, this.dungeonThiefSweetRewardRet, this);
    }

    public clear(): void {
        this.data = null;
    }

    /**
     * 初始化数据
     * @param data
     */
    private dungeonThiefInitRet(data: DungeonThiefInitRet): void {
        const { errMsg, dungeonThiefInfo } = data;
        if (errMsg !== DungeonThiefInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonThiefInitRet[errMsg]);
            return;
        }
        this.data = dungeonThiefInfo;
    }

    /**
     * 初始化数据
     * @param data
     */
    private dungeonThiefRewardRet(data: DungeonThiefRewardRet): void {
        const { errMsg, dungeonThiefInfo } = data;
        if (errMsg !== DungeonThiefRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonThiefRewardRet[errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.Thief);
            return;
        }
        this.data = dungeonThiefInfo;
        this.emit(DungeonThiefRewardRet.prototype.clazzName);
    }

    /**
     * 扫荡
     * @param data
     */
    private dungeonThiefSweetRewardRet(data: DungeonThiefSweetRewardRet): void {
        const { errMsg, dungeonThiefInfo } = data;
        if (errMsg !== DungeonThiefSweetRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonThiefSweetRewardRet[errMsg]);
            return;
        }

        this.data = dungeonThiefInfo;

        this.emit(DungeonThiefSweetRewardRet.prototype.clazzName);
    }

    /**
     * 获取当前关卡id
     */
    public getCurDungeonId(): number {
        return this.data.curDungeonId;
    }

    /**
     * 获取历史最高分数
     * @returns
     */
    public getHistoryMaxScore(): number {
        return this.data.hisMaxScore;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 领取怪盗奖励
     * @param score
     */
    public sendDungeonThiefReward(score: number): void {
        const data = DungeonThiefReward.create({ score });
        Socket.getInstance().send(data);
    }

    /**
     * 扫荡
     */
    public sendSweep(): void {
        const data = DungeonThiefSweetReward.create();
        Socket.getInstance().send(data);
    }
}
