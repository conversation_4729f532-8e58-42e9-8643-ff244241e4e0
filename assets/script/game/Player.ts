/*
 * @Author: <PERSON>y<PERSON><PERSON>
 * @Date: 2023-07-25 11:09:35
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-22 11:43:11
 */

import Loading, { LoadingEvent } from "../../nsn/core/Loading";
import LocalStorage from "../../nsn/core/LocalStorage";
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Time from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    BagUpdateRet,
    IItemInfo,
    IPlayerInfo,
    IServerInfo,
    PlayerBindReward,
    PlayerBindRewardRet,
    PlayerInfoQuery,
    PlayerInfoQueryRet,
    PlayerInfoReplace,
    PlayerInfoReplaceRet,
    PlayerInit,
    PlayerInitRet,
    PlayerLoginOutRet,
    PlayerRename,
    PlayerRenameRet,
    PlayerUpgradeRet,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import PlayerData from "../core/PlayerData";
import RedPoint from "../core/redPoint/RedPoint";
import { EnumItemEffectType, EnumItemType } from "../data/base/BaseItem";
import TBItem from "../data/parser/TBItem";
import GameData from "../sdk/GameData";
import SDKReporter from "../sdk/reporter/SDKReporter";
import SceneUtils, { SceneType } from "../utils/SceneUtils";
import Bag from "./Bag";
import Task from "./Task";

/**
 * 人物事件枚举
 */
export enum PlayerEvent {
    PlayerSelectHeadIcon = "player-select-head-icon", // 个人信息选中头像
    PlayerUpdateCombatScore = "player-update-combat-score", // 战力变更
    PlayerFirstLoginToday = "player-first-login-today", // 玩家今日首次登录
    PlayerZeroReset = "player-zero-reset", // 0点重置
    SelectedItem = "selected-item", // 选择个性化物品
}

/**
 * 玩家数据初始化类型
 */
export const PLAYER_INIT_TYPE = {
    NONE: "", // 初始化、断线重连
    SYNC: "sync", // 同步
    ZERO: "zero", // 零点重置
};

export default class Player extends Logic {
    private playerInfo: IPlayerInfo = null;
    private serverInfo: IServerInfo = null;
    private logoutResult: string = null;

    /**
     * 清理数据
     */
    public clear(): void {
        this.playerInfo = null;
        this.serverInfo = null;
    }

    protected registerHandler(): void {
        Socket.getInstance().on(PlayerInitRet.prototype.clazzName, this.playerInitRet, this);
        Socket.getInstance().on(PlayerUpgradeRet.prototype.clazzName, this.playerUpgradeRet, this);
        Socket.getInstance().on(PlayerInfoReplaceRet.prototype.clazzName, this.playerInfoReplaceRet, this);
        Socket.getInstance().on(PlayerInfoQueryRet.prototype.clazzName, this.playerInfoQueryRet, this);
        Socket.getInstance().on(PlayerRenameRet.prototype.clazzName, this.playerRenameRet, this);
        Socket.getInstance().on(PlayerLoginOutRet.prototype.clazzName, this.playerLoginOutRet, this);
        Socket.getInstance().on(PlayerBindRewardRet.prototype.clazzName, this.playerBindRewardRet, this);

        // 道具数量变更
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            (data: BagUpdateRet) => {
                this.bagUpdate(data.gotItem);
            },
            this
        );
    }

    /**
     * 初始化玩家数据
     * @param data
     * @returns
     */
    private playerInitRet(data: PlayerInitRet): void {
        const { errMsg, playerInfo, serverInfo, timeZone, timestamp, payload } = data;
        if (errMsg !== PlayerInitRet.ErrorResult.None) {
            const curScene = SceneUtils.getCurrentScene();
            switch (curScene) {
                case SceneType.Login:
                    Loading.getInstance().emit(LoadingEvent.PlayerDataInitFailed, i18n.playerInitRet[errMsg]);
                    break;
                case SceneType.Game:
                    SceneUtils.exitInGameScene();
                    break;
                default:
                    break;
            }

            return;
        }
        this.playerInfo = playerInfo;
        this.serverInfo = serverInfo;

        Time.getInstance().clear();
        Time.getInstance().setTimeZone(timeZone);
        Time.getInstance().syncTime(timestamp);

        GameData.getInstance().init();
        LocalStorage.getInstance().init(this.playerInfo.playerId);
        RedPoint.getInstance().reset();
        PlayerData.getInstance().requestBaseInfo();
        PlayerData.getInstance().requestExtraInfo();

        this.emit(PlayerInitRet.prototype.clazzName, payload);

        switch (payload) {
            case PLAYER_INIT_TYPE.ZERO:
                Task.getInstance().reset();
                this.emit(PlayerEvent.PlayerZeroReset);
                break;
            case PLAYER_INIT_TYPE.NONE:
                // 今日首次登录
                const logout = playerInfo.logoutTime;
                if (logout && Time.getInstance().isBefore(logout, Time.getInstance().getTodayZero())) {
                    this.emit(PlayerEvent.PlayerFirstLoginToday);
                }
                break;
            default:
                break;
        }

        if (playerInfo.isNewPlayer) {
            SDKReporter.getInstance().createRole(playerInfo.name);
        }
    }

    /**
     * 角色升级返回
     * @param data
     * @returns
     */
    private playerUpgradeRet(data: PlayerUpgradeRet): void {
        if (data.errMsg !== PlayerUpgradeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.playerUpgradeRet[data.errMsg]);
            return;
        }
        this.playerInfo.level = data.level;
        this.emit(PlayerUpgradeRet.prototype.clazzName, data.level);
        this.emit(PlayerEvent.PlayerUpdateCombatScore);
    }

    /**
     * 个人信息替换返回
     * @param data
     * @returns
     */
    private playerInfoReplaceRet(data: PlayerInfoReplaceRet): void {
        if (data.errMsg !== PlayerInfoReplaceRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.playerInfoReplaceRet[data.errMsg]);
            return;
        }
        const itemConfig = TBItem.getInstance().getDataById(data.propId);
        switch (itemConfig.type) {
            case EnumItemType.AvatarFrame:
                this.playerInfo.avatarFrame = data.propId;
                break;
            case EnumItemType.HeadSculpture:
                this.playerInfo.avatar = data.propId;
                break;
            case EnumItemType.Title:
                this.playerInfo.title = data.propId;
                break;
            default:
                break;
        }
        this.emit(PlayerInfoReplaceRet.prototype.clazzName);
    }

    /**
     * 个人信息查询返回
     * @param data
     */
    private playerInfoQueryRet(data: PlayerInfoQueryRet): void {
        UI.getInstance().hideSoftLoading();
        if (data.errMsg !== PlayerInfoQueryRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.playerInfoQueryRet[data.errMsg]);
            return;
        }
        this.emit(PlayerInfoQueryRet.prototype.clazzName, data);
    }

    /**
     * 道具数量变更
     */
    private bagUpdate(gotItem: IItemInfo[]): void {
        let changeCombatScore = false;
        for (const item of gotItem) {
            const itemConfig = TBItem.getInstance().getDataById(item.itemInfoId);
            if (itemConfig && itemConfig.effectType === EnumItemEffectType.AttributeBonus) {
                changeCombatScore = true;
                break;
            }
        }
        if (changeCombatScore) {
            this.emit(PlayerEvent.PlayerUpdateCombatScore);
        }
    }

    /**
     * 角色改名返回
     */
    private playerRenameRet(data: PlayerRenameRet): void {
        if (data.errMsg !== PlayerRenameRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.playerRenameRet[data.errMsg]);
            return;
        }
        this.playerInfo.name = data.name;
        this.emit(PlayerRenameRet.prototype.clazzName);
    }

    /**
     * 玩家退出游戏
     */
    private playerLoginOutRet(data: PlayerLoginOutRet): void {
        this.logoutResult = i18n.playerLoginOutRet[data.result];
        if (SceneUtils.getCurrentScene() === SceneType.Game) {
            SceneUtils.exitInGameScene();
        }
    }

    /**
     * 获取绑定奖励
     * @param data
     * @returns
     */
    private playerBindRewardRet(data: PlayerBindRewardRet): void {
        if (data.errMsg !== PlayerBindRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.playerBindRewardRet[data.errMsg]);
            return;
        }
    }

    /**
     * 获得玩家信息
     */
    public getInfo(): IPlayerInfo {
        return this.playerInfo;
    }

    /**
     * 获取id
     * @returns
     */
    public getId(): string {
        return this.playerInfo ? this.playerInfo.playerId : "";
    }

    /**
     * 获取游戏编号
     * @returns
     */
    public getGameId(): number {
        return this.playerInfo?.gameId;
    }

    /**
     * 获取注册时间
     * @returns
     */
    public getRegisterDate(): number {
        return this.playerInfo.registerDate;
    }

    /**
     * 设置登出原因
     * @param result
     */
    public setLogoutResult(result: string): void {
        this.logoutResult = result;
    }

    /**
     * 获取登出原因
     * @returns
     */
    public getLogoutResult(): string {
        return this.logoutResult;
    }

    /**
     * 获取等级
     * @returns
     */
    public getLevel(): number {
        return this.playerInfo.level;
    }

    /**
     * 设置头像
     * @param avatarId 头像id
     */
    public setAvatar(avatarId: number): void {
        this.playerInfo.avatar = avatarId;
    }

    /**
     * 获取头像
     * @returns
     */
    public getAvatar(): number {
        return this.playerInfo.avatar;
    }

    /**
     * 获取头像框
     * @returns
     */
    public getAvatarFrame(): number {
        return this.playerInfo.avatarFrame;
    }

    /**
     * 获取昵称
     * @returns
     */
    public getName(): string {
        return this.playerInfo.name;
    }

    /**
     * 获得服务器信息
     */
    public getServerInfo(): IServerInfo {
        return this.serverInfo;
    }

    /**
     * 获取称号
     * @returns
     */
    public getTitle(): number {
        return this.playerInfo.title;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 初始化用户数据
     */
    public sendPlayerInit(payload: string): void {
        const data = PlayerInit.create({ payload });
        Socket.getInstance().send(data);
    }

    /**
     * 个人信息替换
     */
    public sendPlayerInfoReplace(propId: number): void {
        const data = PlayerInfoReplace.create({ propId });
        Socket.getInstance().send(data);
    }

    /**
     * 获取个人信息
     * @param playerId
     */
    public sendPlayerInfoQuery(playerId: string): void {
        const data = PlayerInfoQuery.create({ playerId });
        Socket.getInstance().send(data);
    }

    /**
     * 发送角色改名协议
     */
    public sendPlayerRename(name: string): void {
        const data = PlayerRename.create({ name });
        Socket.getInstance().send(data);
    }

    /**
     * 获取绑定奖励
     */
    public sendPlayerBindReward(bindRewardToken: string): void {
        const data = PlayerBindReward.create({ bindRewardToken });
        Socket.getInstance().send(data);
    }
}
