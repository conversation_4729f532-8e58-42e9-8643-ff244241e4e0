/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-02-28 15:41:08
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-22 11:43:15
 */

import Audio from "../../nsn/audio/Audio";
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Tips from "../../nsn/util/Tips";
import {
    DrawCardDraw,
    ILeadSkinInfo,
    LeadSkinActivateReward,
    LeadSkinActivateRewardRet,
    LeadSkinCreateRet,
    LeadSkinDress,
    LeadSkinDressRet,
    LeadSkinIncrStar,
    LeadSkinIncrStarRet,
    LeadSkinInitRet,
    LeadSkinMagical,
    LeadSkinMagicalRet,
    LeadSkinReset,
    LeadSkinResetRet,
    LeadSkinSelect,
    LeadSkinSelectRet,
    LeadSkinUpgrade,
    LeadSkinUpgradeRet,
} from "../../protobuf/proto";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointId } from "../core/redPoint/RedPointId";
import Player from "./Player";

export enum SkinEvent {
    SelectedSkin = "selected-skin", // 选择皮肤
}

export default class LeadSkin extends Logic {
    private data: Map<number, ILeadSkinInfo> = new Map<number, ILeadSkinInfo>();
    private skinId: number = 0;
    private magicalId: number = 0;
    private noDrawNewLead: number[] = [];
    private isSelectedLead: boolean = false; // 是否已选择主角

    protected registerHandler(): void {
        Socket.getInstance().on(LeadSkinInitRet.prototype.clazzName, this.leadSkinInitRet, this);
        Socket.getInstance().on(LeadSkinDressRet.prototype.clazzName, this.leadSkinDressRet, this);
        Socket.getInstance().on(LeadSkinUpgradeRet.prototype.clazzName, this.leadSkinUpgradeRet, this);
        Socket.getInstance().on(LeadSkinResetRet.prototype.clazzName, this.leadSkinResetRet, this);
        Socket.getInstance().on(LeadSkinIncrStarRet.prototype.clazzName, this.leadSkinIncrStarRet, this);
        Socket.getInstance().on(LeadSkinActivateRewardRet.prototype.clazzName, this.leadSkinActivateRewardRet, this);
        Socket.getInstance().on(LeadSkinCreateRet.prototype.clazzName, this.leadSkinCreateRet, this);
        Socket.getInstance().on(LeadSkinMagicalRet.prototype.clazzName, this.leadSkinMagicalRet, this);
        Socket.getInstance().on(LeadSkinSelectRet.prototype.clazzName, this.leadSkinSelectRet, this);
    }

    /**
     * 清理数据
     */
    public clear(): void {
        this.data.clear();
        this.skinId = 0;
        this.magicalId = 0;
        this.noDrawNewLead = [];
        this.isSelectedLead = false;
    }

    /**
     * 初始化
     * @param data
     * @returns
     */
    private leadSkinInitRet(data: LeadSkinInitRet): void {
        const { errMsg, leadSkinGroupInfo } = data;
        if (errMsg !== LeadSkinInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.leadSkinInitRet[errMsg]);
            return;
        }
        this.clear();
        this.skinId = leadSkinGroupInfo.dressSkinId;
        this.magicalId = leadSkinGroupInfo.magicalId;
        for (const e of leadSkinGroupInfo.leadSkinInfos) {
            this.data.set(e.leadSkinId, e);
        }
        this.isSelectedLead = leadSkinGroupInfo.isSelect;
    }

    /**
     * 穿戴
     * @param data
     * @returns
     */
    private leadSkinDressRet(data: LeadSkinDressRet): void {
        const { errMsg, dressSkinId } = data;
        if (errMsg !== LeadSkinDressRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.leadSkinDressRet[errMsg]);
            return;
        }
        this.skinId = dressSkinId;
        this.emit(LeadSkinDressRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.SkinAll);
    }

    /**
     * 幻化
     * @param data
     * @returns
     */
    private leadSkinMagicalRet(data: LeadSkinMagicalRet): void {
        const { errMsg, magicalId } = data;
        if (errMsg !== LeadSkinMagicalRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.leadSkinMagicalRet[errMsg]);
            return;
        }
        this.magicalId = magicalId;
        this.emit(LeadSkinMagicalRet.prototype.clazzName);
    }

    /**
     * 升级
     * @param data
     * @returns
     */
    private leadSkinUpgradeRet(data: LeadSkinUpgradeRet): void {
        const { errMsg, leadSkinId, times } = data;
        if (errMsg !== LeadSkinUpgradeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.leadSkinUpgradeRet[errMsg]);
            return;
        }
        const info = this.data.get(leadSkinId);
        info.level += times;
        this.emit(LeadSkinUpgradeRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.SkinAll);
    }

    /**
     * 重置
     * @param data
     * @returns
     */
    private leadSkinResetRet(data: LeadSkinResetRet): void {
        const { errMsg, leadSkinId } = data;
        if (errMsg !== LeadSkinResetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.leadSkinResetRet[errMsg]);
            return;
        }
        const info = this.data.get(leadSkinId);
        info.level = 1;
        this.emit(LeadSkinResetRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.SkinAll);
    }

    /**
     * 升星
     * @param data
     * @returns
     */
    private leadSkinIncrStarRet(data: LeadSkinIncrStarRet): void {
        const { errMsg, leadSkinId } = data;
        if (errMsg !== LeadSkinIncrStarRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.leadSkinIncrStarRet[errMsg]);
            return;
        }
        const info = this.data.get(leadSkinId);
        info.star += 1;
        this.emit(LeadSkinIncrStarRet.prototype.clazzName, data);
        UI.getInstance().open("FloatSkinUpgrade", leadSkinId);
        RedPoint.getInstance().checkRelative(RedPointId.SkinAll);
        Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.UPGRADE_SUCCESS, AUDIO_EFFECT_PATH.SYSTEM);
    }

    /**
     * 激活
     * @param data
     * @returns
     */
    private leadSkinActivateRewardRet(data: LeadSkinActivateRewardRet): void {
        const { errMsg, leadSkinId } = data;
        if (errMsg !== LeadSkinActivateRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.leadSkinActivateRewardRet[errMsg]);
            return;
        }
        const info = this.data.get(leadSkinId);
        info.isActivate = true;
        this.emit(LeadSkinActivateRewardRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.SkinAll);
    }

    /**
     * 获得
     * @param data
     * @returns
     */
    private leadSkinCreateRet(data: LeadSkinCreateRet): void {
        const { errMsg, leadSkinInfo, src } = data;
        if (errMsg !== LeadSkinCreateRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.leadSkinCreateRet[errMsg]);
            return;
        }
        this.data.set(leadSkinInfo.leadSkinId, leadSkinInfo);
        this.emit(LeadSkinCreateRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.SkinAll);
        RedPoint.getInstance().record(RedPointId.LeadPersonalizedLead, leadSkinInfo.leadSkinId);

        if (src !== DrawCardDraw.prototype.clazzName) {
            this.noDrawNewLead.push(leadSkinInfo.leadSkinId);
            if (this.noDrawNewLead.length === 1) {
                UI.getInstance().open("FloatNewLeadSkin", {
                    leadIds: [leadSkinInfo.leadSkinId],
                    showItems: [],
                    isDraw: false,
                });
            }
        }

        Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.GET_RESOURCE, AUDIO_EFFECT_PATH.SYSTEM);
    }

    /**
     * 选择主角
     * @param data
     */
    private leadSkinSelectRet(data: LeadSkinSelectRet): void {
        const { errMsg, leadSkinId, leadSkinInfo, avatarId } = data;
        if (errMsg !== LeadSkinSelectRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.leadSkinSelectRet[errMsg]);
            return;
        }

        this.isSelectedLead = true;
        this.data.set(leadSkinId, leadSkinInfo);
        this.skinId = leadSkinId;
        this.magicalId = leadSkinId;
        Player.getInstance().setAvatar(avatarId);

        this.emit(LeadSkinSelectRet.prototype.clazzName);
    }

    /**
     * 获取全部皮肤数据
     * @returns
     */
    public getAllData(): ILeadSkinInfo[] {
        return Array.from(this.data.values());
    }

    /**
     * 获取皮肤信息
     * @param id
     * @returns
     */
    public getDataById(id: number): ILeadSkinInfo {
        return this.data.get(id);
    }

    /**
     * 获取当前皮肤id
     * @returns
     */
    public getId(): number {
        return this.skinId;
    }

    /**
     * 获取所有皮肤id
     * @returns
     */
    public getAllIds(): number[] {
        return Array.from(this.data.keys());
    }

    /**
     * 获取幻化id
     * @returns
     */
    public getMagicalId(): number {
        return this.magicalId;
    }

    /**
     * 删除非抽奖渠道新主角
     */
    public deleteNoDrawNewLeadSkin(leadSkinId: number): void {
        const findIndex = this.noDrawNewLead.findIndex((e) => e === leadSkinId);
        if (findIndex !== -1) {
            this.noDrawNewLead.splice(findIndex, 1);
        }
    }

    /**
     * 获取非抽奖渠道新主角
     * @returns
     */
    public getNoDrawNewLeadSkin(): number[] {
        return this.noDrawNewLead;
    }

    /**
     * 删除非抽奖渠道所有新主角
     * @returns
     */
    public deleteAllNoDrawNewLeadSkin(): void {
        this.noDrawNewLead = [];
    }

    /**
     * 获取主角选择状态
     * @returns
     */
    public getLeadSelectState(): boolean {
        return this.isSelectedLead;
    }

    /** ***************************************** 发送协议 *****************************************************/

    /**
     * 穿戴
     * @param leadSkinId
     */
    public sendLeadSkinDress(leadSkinId: number): void {
        const data = LeadSkinDress.create({ leadSkinId });
        Socket.getInstance().send(data);
    }

    /**
     * 升级
     * @param leadSkinId
     * @param times
     */
    public sendLeadSkinUpgrade(leadSkinId: number, times: number = 1): void {
        const data = LeadSkinUpgrade.create({ leadSkinId, times });
        Socket.getInstance().send(data);
    }

    /**
     * 重置
     * @param leadSkinId
     */
    public sendLeadSkinReset(leadSkinId: number): void {
        const data = LeadSkinReset.create({ leadSkinId });
        Socket.getInstance().send(data);
    }

    /**
     * 升星
     * @param leadSkinId
     */
    public sendLeadSkinIncrStar(leadSkinId: number): void {
        const data = LeadSkinIncrStar.create({ leadSkinId });
        Socket.getInstance().send(data);
    }

    /**
     * 激活
     * @param leadSkinId
     */
    public sendLeadSkinActivateReward(leadSkinId: number): void {
        const data = LeadSkinActivateReward.create({ leadSkinId });
        Socket.getInstance().send(data);
    }

    /**
     * 幻化
     * @param magicalId
     */
    public sendLeadSkinMagical(magicalId: number): void {
        const data = LeadSkinMagical.create({ magicalId });
        Socket.getInstance().send(data);
    }

    /**
     * 选择主角
     * @param skinLeadId
     */
    public sendLeadSkinSelect(skinLeadId: number): void {
        const data = LeadSkinSelect.create({ skinLeadId });
        Socket.getInstance().send(data);
    }
}
