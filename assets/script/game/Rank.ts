/*
 * @Author: JackyF<PERSON>
 * @Date: 2023-08-08 11:21:10
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-11 18:04:33
 */

import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Logger from "../../nsn/util/Logger";
import Time from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    IRankItem,
    IRankReceiveRet,
    IRankUnionItem,
    RankGet,
    RankGetRet,
    RankReceive,
    RankReceiveRet,
    RankUnionGet,
    RankUnionGetRet,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";

export interface IRankInfo {
    ranks: IRankItem[];
    myRank: IRankItem;
    reqTime: number;
    limit?: number;
}

export interface IRankUnionInfo {
    ranks: IRankUnionItem[];
    myRank: IRankUnionItem;
    reqTime: number;
    limit?: number;
}

export default class Rank extends Logic {
    private data: Map<number, IRankInfo> = new Map<number, IRankInfo>();
    private unionRankData: Map<number, IRankInfo> = new Map<number, IRankInfo>();

    /**
     * 清理数据
     */
    public clear(): void {
        this.data.clear();
        this.unionRankData.clear();
    }

    /**
     * 注册消息
     */
    protected registerHandler(): void {
        Socket.getInstance().on(RankGetRet.prototype.clazzName, this.rankGetRet, this);
        Socket.getInstance().on(RankReceiveRet.prototype.clazzName, this.rankReceiveRet, this);
        Socket.getInstance().on(RankUnionGetRet.prototype.clazzName, this.rankUnionGetRet, this);
    }

    /**
     * 排行榜数据返回
     * @param data
     */
    private rankGetRet(data: RankGetRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, rankId, reqTime, items, myItem, limit } = data;
        if (errMsg !== RankGetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.rankGetRet[errMsg]);
            return;
        }
        const info: IRankInfo = {
            ranks: items,
            myRank: myItem,
            reqTime,
            limit,
        };
        this.data.set(rankId, info);
        this.emit(RankGetRet.prototype.clazzName, rankId, limit);
    }

    /**
     * 排行榜手动领奖返回
     */
    private rankReceiveRet(data: IRankReceiveRet): void {
        if (data.errMsg !== RankReceiveRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.rankReceiveRet[data.errMsg]);
            return;
        }
        const rank = this.data.get(data.rankId);
        if (!rank) {
            Logger.error("排行榜", "排行榜不存在：" + data.rankId);
            return;
        }
        rank.myRank.isReceived = true;
        this.emit(RankReceiveRet.prototype.clazzName);
    }

    /**
     * 联盟排行榜数据返回
     * @param data
     */
    private rankUnionGetRet(data: RankUnionGetRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, rankId, reqTime, items, myItem, limit } = data;
        if (errMsg !== RankUnionGetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.rankUnionGetRet[errMsg]);
            return;
        }
        const info: IRankUnionInfo = {
            ranks: items,
            myRank: myItem,
            reqTime,
            limit,
        };
        this.unionRankData.set(rankId, info);
        this.emit(RankUnionGetRet.prototype.clazzName);
    }

    /**
     * 获取排行榜信息
     * @param id
     * @returns
     */
    public getDataById(id: number): IRankInfo {
        return this.data.get(id);
    }

    /**
     * 获取公会排行榜信息
     * @param id
     * @returns
     */
    public getUnionRankDataById(id: number): IRankUnionInfo {
        return this.unionRankData.get(id);
    }

    /**
     * 排行榜奖励是否已领取
     */
    public rankRewardIsReceived(rankId: number): boolean {
        const data = this.data.get(rankId);
        return data.myRank.isReceived;
    }

    /**
     * 排行榜是否过期
     * @param rankId
     * @returns
     */
    public isOutdated(rankId: number): boolean {
        const RANK_UPDATE_INTERVAL = 10;
        const data = this.data.get(rankId);
        if (!data) {
            return true;
        }
        return Time.getInstance().now() - data.reqTime > RANK_UPDATE_INTERVAL * 1000;
    }

    /**
     * 公会排行榜是否过期
     * @param rankId
     * @returns
     */
    public isUnionRankOutdated(rankId: number): boolean {
        const RANK_UPDATE_INTERVAL = 10;
        const data = this.unionRankData.get(rankId);
        if (!data) {
            return true;
        }
        return Time.getInstance().now() - data.reqTime > RANK_UPDATE_INTERVAL * 1000;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////
    /**
     * 初始化数值
     */
    public sendRankGet(rankId: number, isMyself: boolean = false): void {
        const data = RankGet.create({ rankId, isMyself });
        Socket.getInstance().send(data);
    }

    /**
     * 排行榜手动领奖请求
     */
    public sendRankReceive(rankId: number): void {
        const data = RankReceive.create({ rankId });
        Socket.getInstance().send(data);
    }

    /**
     * 请求联盟排行榜数据
     */
    public sendRankUnionGetRet(rankId: number): void {
        const data = RankUnionGet.create({ rankId });
        Socket.getInstance().send(data);
    }
}
