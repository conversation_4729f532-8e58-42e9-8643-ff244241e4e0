/*
 * @Author: chenx
 * @Date: 2024-08-05 09:44:12
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-24 17:36:09
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import Time, { HOUR_TO_SECOND, MINUTE_TO_SECOND } from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    CollectType,
    DressEnum,
    IPlotDressInfo,
    IPlotDressLevelInfo,
    IPlotInfo,
    IPlotManageInfo,
    IPlotOrderInfo,
    IPlotOrderSpeedUpInfo,
    IPlotTankAccTimeInfo,
    IPlotTankOrderInfo,
    PlotAutoReceive,
    PlotAutoReceiveOther,
    PlotAutoReceiveOtherRet,
    PlotAutoReceiveRet,
    PlotBatchAcceptOrder,
    PlotBatchAcceptOrderRet,
    PlotBatchReceiveOrder,
    PlotBatchReceiveOrderRet,
    PlotBatchSpeedUpOrder,
    PlotBatchSpeedUpOrderRet,
    PlotCollect,
    PlotCollectRet,
    PlotEndSnatch,
    PlotEndSnatchRet,
    PlotGet,
    PlotGetLog,
    PlotGetLogRet,
    PlotGetRet,
    PlotIncrExpNoticeRet,
    PlotInitRet,
    PlotModifyDress,
    PlotModifyDressRet,
    PlotModifyManage,
    PlotModifyManageRet,
    PlotModifyName,
    PlotModifyNameRet,
    PlotModifyShow,
    PlotModifyShowRet,
    PlotReceive,
    PlotReceiveNoticeRet,
    PlotReceiveOther,
    PlotReceiveOtherNoticeRet,
    PlotReceiveOtherRet,
    PlotReceiveRet,
    PlotRefreshOrder,
    PlotRefreshOrderRet,
    PlotSearch,
    PlotSearchRet,
    PlotSnatchShare,
    PlotSnatchShareRet,
    PlotStartSnatch,
    PlotStartSnatchNoticeRet,
    PlotStartSnatchRet,
    PlotStop,
    PlotStopOther,
    PlotStopOtherNoticeRet,
    PlotStopOtherRet,
    PlotStopRet,
    PlotUnlockDress,
    PlotUnlockDressRet,
    PlotUpgradeDress,
    PlotUpgradeDressRet,
    SearchType,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointId } from "../core/redPoint/RedPointId";
import { EnumEconomyAttributeType } from "../data/base/BaseEconomyAttribute";
import { EnumParkingLotPara } from "../data/base/BaseParkingLot";
import { EnumParkingLotSkinType } from "../data/base/BaseParkingLotSkin";
import { IAttrInfo } from "../data/parser/TBAttribute";
import TBParkingLot from "../data/parser/TBParkingLot";
import TBParkingLotLevel from "../data/parser/TBParkingLotLevel";
import TBParkingLotSkin from "../data/parser/TBParkingLotSkin";
import TBParkingSkinLevel from "../data/parser/TBParkingSkinLevel";
import Bag from "./Bag";
import Combat, { CombatEvent, DungeonType } from "./Combat";
import Player from "./Player";

/**
 * 停车场事件
 */
export enum ParkEvent {
    Upgrade = "upgrade", // 升级
    SpaceParked = "space-parked", // 车位已停车
    UpdateSpaceState = "update-space-state", // 更新车位状态
    UpdateTankState = "update-tank-state", // 更新战车状态
    Jump = "jump", // 跳转
    OrderRefreshed = "order-refreshed", // 订单已刷新
}

/**
 * 停车场
 */
export default class Park extends Logic {
    private data: IPlotInfo = null; // 停车场数据
    private otherData: IPlotInfo = null; // 他人停车场数据
    private increaseExp: number = 0; // 增长经验
    private tankIncomeData: IPlotTankAccTimeInfo[] = []; // 战车收益数据
    private snatchedTimes: number = 0; // 已抢夺次数

    private orderData: IPlotOrderInfo[] = []; // 订单数据
    private orderReceivedTimes: number = 0; // 订单已接取次数

    protected registerHandler(): void {
        Socket.getInstance().on(PlotInitRet.prototype.clazzName, this.plotInitRet, this);
        Socket.getInstance().on(PlotGetRet.prototype.clazzName, this.plotGetRet, this);
        Socket.getInstance().on(PlotIncrExpNoticeRet.prototype.clazzName, this.plotIncrExpNoticeRet, this);
        Socket.getInstance().on(PlotModifyNameRet.prototype.clazzName, this.plotModifyNameRet, this);
        Socket.getInstance().on(PlotModifyShowRet.prototype.clazzName, this.plotModifyShowRet, this);
        Socket.getInstance().on(PlotModifyManageRet.prototype.clazzName, this.plotModifyManageRet, this);
        Socket.getInstance().on(PlotSearchRet.prototype.clazzName, this.plotSearchRet, this);
        Socket.getInstance().on(PlotCollectRet.prototype.clazzName, this.plotCollectRet, this);
        Socket.getInstance().on(PlotStopRet.prototype.clazzName, this.plotStopRet, this);
        Socket.getInstance().on(PlotStopOtherRet.prototype.clazzName, this.plotStopOtherRet, this);
        Socket.getInstance().on(PlotReceiveRet.prototype.clazzName, this.plotReceiveRet, this);
        Socket.getInstance().on(PlotReceiveOtherRet.prototype.clazzName, this.plotReceiveOtherRet, this);
        Socket.getInstance().on(PlotAutoReceiveRet.prototype.clazzName, this.plotAutoReceiveRet, this);
        Socket.getInstance().on(PlotAutoReceiveOtherRet.prototype.clazzName, this.plotAutoReceiveOtherRet, this);
        Socket.getInstance().on(PlotStartSnatchRet.prototype.clazzName, this.plotStartSnatchRet, this);
        Socket.getInstance().on(PlotEndSnatchRet.prototype.clazzName, this.plotEndSnatchRet, this);
        Socket.getInstance().on(PlotSnatchShareRet.prototype.clazzName, this.plotSnatchShareRet, this);
        Socket.getInstance().on(PlotStopOtherNoticeRet.prototype.clazzName, this.plotStopOtherNoticeRet, this);
        Socket.getInstance().on(PlotReceiveNoticeRet.prototype.clazzName, this.plotReceiveNoticeRet, this);
        Socket.getInstance().on(PlotReceiveOtherNoticeRet.prototype.clazzName, this.plotReceiveOtherNoticeRet, this);
        Socket.getInstance().on(PlotStartSnatchNoticeRet.prototype.clazzName, this.plotStartSnatchNoticeRet, this);
        Socket.getInstance().on(PlotGetLogRet.prototype.clazzName, this.plotGetLogRet, this);

        Socket.getInstance().on(PlotUnlockDressRet.prototype.clazzName, this.plotUnlockDressRet, this);
        Socket.getInstance().on(PlotUpgradeDressRet.prototype.clazzName, this.plotUpgradeDressRet, this);
        Socket.getInstance().on(PlotModifyDressRet.prototype.clazzName, this.plotModifyDressRet, this);

        Socket.getInstance().on(PlotRefreshOrderRet.prototype.clazzName, this.plotRefreshOrderRet, this);
        Socket.getInstance().on(PlotBatchAcceptOrderRet.prototype.clazzName, this.plotBatchAcceptOrderRet, this);
        Socket.getInstance().on(PlotBatchSpeedUpOrderRet.prototype.clazzName, this.plotBatchSpeedUpOrderRet, this);
        Socket.getInstance().on(PlotBatchReceiveOrderRet.prototype.clazzName, this.plotBatchReceiveOrderRet, this);
    }

    public clear(): void {
        this.data = null;
        this.otherData = null;
        this.increaseExp = 0;
        this.tankIncomeData = [];
        this.snatchedTimes = 0;

        this.orderData = [];
        this.orderReceivedTimes = 0;
    }

    /**
     * 初始化数据
     * @param data
     */
    private plotInitRet(data: PlotInitRet): void {
        const { errMsg, plotInfo, tankAccTimeInfos, snatchTimes, plotDispatchInfo } = data;
        if (errMsg !== PlotInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotInitRet[errMsg]);
            return;
        }

        this.data = plotInfo;
        this.tankIncomeData = tankAccTimeInfos;
        this.snatchedTimes = snatchTimes;
        this.orderData = plotDispatchInfo.orderInfos;
        this.orderReceivedTimes = plotDispatchInfo.receiveTimes;
        this.checkUpgrade();
        this.checkAutoFinishPark();

        this.emit(PlotInitRet.prototype.clazzName);
    }

    /**
     * 获取他人数据
     * @param data
     */
    private plotGetRet(data: PlotGetRet): void {
        const { errMsg, plotInfo } = data;
        if (errMsg !== PlotGetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotGetRet[errMsg]);
            return;
        }

        this.otherData = plotInfo;

        this.emit(PlotGetRet.prototype.clazzName);
    }

    /**
     * 获得经验通知
     * @param data
     */
    private plotIncrExpNoticeRet(data: PlotIncrExpNoticeRet): void {
        const { errMsg, incrExp } = data;
        if (errMsg !== PlotIncrExpNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotIncrExpNoticeRet[errMsg]);
            return;
        }

        this.increaseExp += incrExp;

        this.emit(PlotIncrExpNoticeRet.prototype.clazzName);
    }

    /**
     * 重命名
     * @param data
     */
    private plotModifyNameRet(data: PlotModifyNameRet): void {
        const { errMsg, name, nameUpdateTime } = data;
        if (errMsg !== PlotModifyNameRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotModifyNameRet[errMsg]);
            return;
        }

        this.data.name = name;
        this.data.nameUpdateTime = nameUpdateTime;

        this.emit(PlotModifyNameRet.prototype.clazzName);
    }

    /**
     * 展示战车
     * @param data
     */
    private plotModifyShowRet(data: PlotModifyShowRet): void {
        const { errMsg, tankIds } = data;
        if (errMsg !== PlotModifyShowRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotModifyShowRet[errMsg]);
            return;
        }

        this.data.showTankIds = tankIds;

        this.emit(PlotModifyShowRet.prototype.clazzName);
    }

    /**
     * 设置
     * @param data
     */
    private plotModifyManageRet(data: PlotModifyManageRet): void {
        const { errMsg, plotManageInfo } = data;
        if (errMsg !== PlotModifyManageRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotModifyManageRet[errMsg]);
            return;
        }

        this.data.plotManageInfo = plotManageInfo;

        this.emit(PlotModifyManageRet.prototype.clazzName);
    }

    /**
     * 搜索
     * @param data
     */
    private plotSearchRet(data: PlotSearchRet): void {
        const { errMsg, searchType, plotSearchInfos } = data;
        if (errMsg !== PlotSearchRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotSearchRet[errMsg]);
            return;
        }

        this.emit(PlotSearchRet.prototype.clazzName, searchType, plotSearchInfos);
    }

    /**
     * 收藏
     * @param data
     */
    private plotCollectRet(data: PlotCollectRet): void {
        const { errMsg, collectRoleId, collectType } = data;
        if (errMsg !== PlotCollectRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotCollectRet[errMsg]);
            return;
        }

        switch (collectType) {
            case CollectType.Collect:
                if (!this.data.collectPlotIds.includes(collectRoleId)) {
                    this.data.collectPlotIds.push(collectRoleId);
                }
                break;
            case CollectType.CancelCollect:
                const index = this.data.collectPlotIds.findIndex((v) => v === collectRoleId);
                if (index !== -1) {
                    this.data.collectPlotIds.splice(index, 1);
                }
                break;
            default:
                break;
        }

        this.emit(PlotCollectRet.prototype.clazzName);
    }

    /**
     * 停车
     * @param data
     */
    private plotStopRet(data: PlotStopRet): void {
        const { errMsg, plotSpaceInfo } = data;
        if (errMsg !== PlotStopRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotStopRet[errMsg]);
            return;
        }

        const index = this.data.plotSpaceInfos.findIndex((v) => v.spaceId === plotSpaceInfo.spaceId);
        if (index !== -1) {
            this.data.plotSpaceInfos[index] = plotSpaceInfo;
        } else {
            this.data.plotSpaceInfos.push(plotSpaceInfo);
        }

        const index2 = this.data.ownerTankSpaceInfos.findIndex((v) => v.tankId === plotSpaceInfo.tankId);
        if (index2 !== -1) {
            this.data.ownerTankSpaceInfos[index2] = plotSpaceInfo;
        } else {
            this.data.ownerTankSpaceInfos.push(plotSpaceInfo);
        }

        this.emit(PlotStopRet.prototype.clazzName);
        this.emit(ParkEvent.UpdateSpaceState, plotSpaceInfo.ownerRoleId, plotSpaceInfo.spaceId);
        this.emit(ParkEvent.UpdateTankState, plotSpaceInfo.tankId);
    }

    /**
     * 停车-他人
     * @param data
     */
    private plotStopOtherRet(data: PlotStopOtherRet): void {
        const { errMsg, plotSpaceInfo } = data;
        if (errMsg !== PlotStopOtherRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotStopOtherRet[errMsg]);
            if (errMsg === PlotStopOtherRet.ErrorResult.SpaceAlreadyStop) {
                this.emit(ParkEvent.SpaceParked);
            }
            return;
        }

        if (this.otherData && this.otherData.ownerRoleId === plotSpaceInfo.ownerRoleId) {
            const index = this.otherData.plotSpaceInfos.findIndex((v) => v.spaceId === plotSpaceInfo.spaceId);
            if (index !== -1) {
                this.otherData.plotSpaceInfos[index] = plotSpaceInfo;
            } else {
                this.otherData.plotSpaceInfos.push(plotSpaceInfo);
            }
        }

        const index2 = this.data.ownerTankSpaceInfos.findIndex((v) => v.tankId === plotSpaceInfo.tankId);
        if (index2 !== -1) {
            this.data.ownerTankSpaceInfos[index2] = plotSpaceInfo;
        } else {
            this.data.ownerTankSpaceInfos.push(plotSpaceInfo);
        }

        this.emit(PlotStopOtherRet.prototype.clazzName);
        this.emit(ParkEvent.UpdateSpaceState, plotSpaceInfo.ownerRoleId, plotSpaceInfo.spaceId);
        this.emit(ParkEvent.UpdateTankState, plotSpaceInfo.tankId);
    }

    /**
     * 收车
     * @param data
     */
    private plotReceiveRet(data: PlotReceiveRet): void {
        const { errMsg, spaceId, tankAccTimeInfo } = data;
        if (errMsg !== PlotReceiveRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotReceiveRet[errMsg]);
            return;
        }

        const index = this.data.plotSpaceInfos.findIndex((v) => v.spaceId === spaceId);
        index !== -1 && this.data.plotSpaceInfos.splice(index, 1);

        const index2 = this.data.ownerTankSpaceInfos.findIndex(
            (v) => v.ownerRoleId === this.data.ownerRoleId && v.spaceId === spaceId
        );
        if (index2 !== -1) {
            this.data.ownerTankSpaceInfos.splice(index2, 1);
            this.emit(ParkEvent.UpdateTankState, -1);
        }

        if (tankAccTimeInfo) {
            const index3 = this.tankIncomeData.findIndex((v) => v.tankId === tankAccTimeInfo.tankId);
            if (index3 !== -1) {
                this.tankIncomeData[index3] = tankAccTimeInfo;
            } else {
                this.tankIncomeData.push(tankAccTimeInfo);
            }
        }

        this.emit(ParkEvent.UpdateSpaceState, this.data.ownerRoleId, spaceId, true);

        RedPoint.getInstance().checkRelative(RedPointId.ParkSpace);
        RedPoint.getInstance().checkRelative(RedPointId.ParkMyTank);
    }

    /**
     * 收车-他人
     * @param data
     */
    private plotReceiveOtherRet(data: PlotReceiveOtherRet): void {
        const { errMsg, ownerRoleId, spaceId, tankAccTimeInfo } = data;
        if (errMsg !== PlotReceiveOtherRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotReceiveOtherRet[errMsg]);
            return;
        }

        if (this.otherData && this.otherData.ownerRoleId === ownerRoleId) {
            const index = this.otherData.plotSpaceInfos.findIndex((v) => v.spaceId === spaceId);
            index !== -1 && this.otherData.plotSpaceInfos.splice(index, 1);
        }

        const index2 = this.data.ownerTankSpaceInfos.findIndex(
            (v) => v.ownerRoleId === ownerRoleId && v.spaceId === spaceId
        );
        if (index2 !== -1) {
            this.data.ownerTankSpaceInfos.splice(index2, 1);
            this.emit(ParkEvent.UpdateTankState, -1);
        }

        if (tankAccTimeInfo) {
            const index3 = this.tankIncomeData.findIndex((v) => v.tankId === tankAccTimeInfo.tankId);
            if (index3 !== -1) {
                this.tankIncomeData[index3] = tankAccTimeInfo;
            } else {
                this.tankIncomeData.push(tankAccTimeInfo);
            }
        }

        this.emit(ParkEvent.UpdateSpaceState, ownerRoleId, spaceId, true);

        RedPoint.getInstance().checkRelative(RedPointId.ParkSpace);
        RedPoint.getInstance().checkRelative(RedPointId.ParkMyTank);
    }

    /**
     * 自动收车
     * @param data
     */
    private plotAutoReceiveRet(data: PlotAutoReceiveRet): void {
        const { errMsg, spaceId, tankAccTimeInfo } = data;
        if (errMsg !== PlotAutoReceiveRet.ErrorResult.None) {
            return;
        }

        const index = this.data.plotSpaceInfos.findIndex((v) => v.spaceId === spaceId);
        index !== -1 && this.data.plotSpaceInfos.splice(index, 1);

        const index2 = this.data.ownerTankSpaceInfos.findIndex(
            (v) => v.ownerRoleId === this.data.ownerRoleId && v.spaceId === spaceId
        );
        if (index2 !== -1) {
            this.data.ownerTankSpaceInfos.splice(index2, 1);
            this.emit(ParkEvent.UpdateTankState, -1);
        }

        if (tankAccTimeInfo) {
            const index3 = this.tankIncomeData.findIndex((v) => v.tankId === tankAccTimeInfo.tankId);
            if (index3 !== -1) {
                this.tankIncomeData[index3] = tankAccTimeInfo;
            } else {
                this.tankIncomeData.push(tankAccTimeInfo);
            }
        }

        this.emit(ParkEvent.UpdateSpaceState, this.data.ownerRoleId, spaceId, true);

        RedPoint.getInstance().checkRelative(RedPointId.ParkSpace);
        RedPoint.getInstance().checkRelative(RedPointId.ParkMyTank);
    }

    /**
     * 自动收车-他人
     * @param data
     */
    private plotAutoReceiveOtherRet(data: PlotAutoReceiveOtherRet): void {
        const { errMsg, ownerRoleId, spaceId, tankAccTimeInfo } = data;
        if (errMsg !== PlotAutoReceiveOtherRet.ErrorResult.None) {
            return;
        }

        if (this.otherData && this.otherData.ownerRoleId === ownerRoleId) {
            const index = this.otherData.plotSpaceInfos.findIndex((v) => v.spaceId === spaceId);
            index !== -1 && this.otherData.plotSpaceInfos.splice(index, 1);
        }

        const index2 = this.data.ownerTankSpaceInfos.findIndex(
            (v) => v.ownerRoleId === ownerRoleId && v.spaceId === spaceId
        );
        if (index2 !== -1) {
            this.data.ownerTankSpaceInfos.splice(index2, 1);
            this.emit(ParkEvent.UpdateTankState, -1);
        }

        if (tankAccTimeInfo) {
            const index3 = this.tankIncomeData.findIndex((v) => v.tankId === tankAccTimeInfo.tankId);
            if (index3 !== -1) {
                this.tankIncomeData[index3] = tankAccTimeInfo;
            } else {
                this.tankIncomeData.push(tankAccTimeInfo);
            }
        }

        this.emit(ParkEvent.UpdateSpaceState, ownerRoleId, spaceId, true);

        RedPoint.getInstance().checkRelative(RedPointId.ParkSpace);
        RedPoint.getInstance().checkRelative(RedPointId.ParkMyTank);
    }

    /**
     * 开始抢车位
     * @param data
     */
    private plotStartSnatchRet(data: PlotStartSnatchRet): void {
        const { errMsg } = data;
        if (errMsg !== PlotStartSnatchRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotStartSnatchRet[errMsg]);
            return;
        }

        this.emit(PlotStartSnatchRet.prototype.clazzName, data);
    }

    /**
     * 结束抢车位
     * @param data
     */
    private plotEndSnatchRet(data: PlotEndSnatchRet): void {
        const { errMsg, ownerRoleId, spaceId, isWin } = data;
        if (errMsg !== PlotEndSnatchRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotEndSnatchRet[errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.Park);
            return;
        }

        if (isWin) {
            let updatePlayerId = "";
            let updateSpaceId = -1;
            if (this.data.ownerRoleId === ownerRoleId) {
                const index = this.data.plotSpaceInfos.findIndex((v) => v.spaceId === spaceId);
                if (index !== -1) {
                    updatePlayerId = this.data.ownerRoleId;
                    updateSpaceId = this.data.plotSpaceInfos[index].spaceId;

                    this.data.plotSpaceInfos.splice(index, 1);
                }
            }

            if (this.otherData && this.otherData.ownerRoleId === ownerRoleId) {
                const index2 = this.otherData.plotSpaceInfos.findIndex((v) => v.spaceId === spaceId);
                if (index2 !== -1) {
                    updatePlayerId = this.otherData.ownerRoleId;
                    updateSpaceId = this.otherData.plotSpaceInfos[index2].spaceId;

                    this.otherData.plotSpaceInfos.splice(index2, 1);
                }
            }

            this.snatchedTimes++;

            if (updatePlayerId !== "") {
                this.emit(ParkEvent.UpdateSpaceState, updatePlayerId, updateSpaceId);
            }
        } else {
            this.emit(PlotEndSnatchRet.prototype.clazzName, data);
        }
    }

    /**
     * 抢车位分享
     * @param data
     */
    private plotSnatchShareRet(data: PlotSnatchShareRet): void {
        const { errMsg } = data;
        if (errMsg !== PlotSnatchShareRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotSnatchShareRet[errMsg]);
            return;
        }

        this.emit(PlotSnatchShareRet.prototype.clazzName);
    }

    /**
     * 停车通知
     * @param data
     */
    private plotStopOtherNoticeRet(data: PlotStopOtherNoticeRet): void {
        const { errMsg, plotSpaceInfo } = data;
        if (errMsg !== PlotStopOtherNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotStopOtherNoticeRet[errMsg]);
            return;
        }

        const index = this.data.plotSpaceInfos.findIndex((v) => v.spaceId === plotSpaceInfo.spaceId);
        if (index !== -1) {
            this.data.plotSpaceInfos[index] = plotSpaceInfo;
        } else {
            this.data.plotSpaceInfos.push(plotSpaceInfo);
        }

        this.emit(ParkEvent.UpdateSpaceState, plotSpaceInfo.ownerRoleId, plotSpaceInfo.spaceId);
    }

    /**
     * 被收车通知
     * @param data
     */
    private plotReceiveNoticeRet(data: PlotReceiveNoticeRet): void {
        const { errMsg, tankId, tankAccTimeInfo } = data;
        if (errMsg !== PlotReceiveNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotReceiveNoticeRet[errMsg]);
            return;
        }

        let updatePlayerId = "";
        let updateSpaceId = -1;
        const playerId = Player.getInstance().getId();
        const index = this.data.plotSpaceInfos.findIndex((v) => v.pRoleId === playerId && v.tankId === tankId);
        if (index !== -1) {
            updatePlayerId = this.data.ownerRoleId;
            updateSpaceId = this.data.plotSpaceInfos[index].spaceId;

            this.data.plotSpaceInfos.splice(index, 1);
        }

        if (this.otherData) {
            const index2 = this.otherData.plotSpaceInfos.findIndex(
                (v) => v.pRoleId === playerId && v.tankId === tankId
            );
            if (index2 !== -1) {
                updatePlayerId = this.otherData.ownerRoleId;
                updateSpaceId = this.otherData.plotSpaceInfos[index2].spaceId;

                this.otherData.plotSpaceInfos.splice(index2, 1);
            }
        }

        const index3 = this.data.ownerTankSpaceInfos.findIndex((v) => v.tankId === tankId);
        if (index3 !== -1) {
            this.data.ownerTankSpaceInfos.splice(index3, 1);
            this.emit(ParkEvent.UpdateTankState, -1);
        }

        const index4 = this.tankIncomeData.findIndex((v) => v.tankId === tankId);
        if (index4 !== -1) {
            this.tankIncomeData[index4] = tankAccTimeInfo;
        } else {
            this.tankIncomeData.push(tankAccTimeInfo);
        }

        if (updatePlayerId !== "") {
            this.emit(ParkEvent.UpdateSpaceState, updatePlayerId, updateSpaceId);
        }

        RedPoint.getInstance().checkRelative(RedPointId.ParkSpace);
        RedPoint.getInstance().checkRelative(RedPointId.ParkMyTank);
    }

    /**
     * 收车通知
     * @param data
     */
    private plotReceiveOtherNoticeRet(data: PlotReceiveOtherNoticeRet): void {
        const { errMsg, spaceId } = data;
        if (errMsg !== PlotReceiveOtherNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotReceiveOtherNoticeRet[errMsg]);
            return;
        }

        const index = this.data.plotSpaceInfos.findIndex((v) => v.spaceId === spaceId);
        index !== -1 && this.data.plotSpaceInfos.splice(index, 1);

        this.emit(ParkEvent.UpdateSpaceState, this.data.ownerRoleId, spaceId);
    }

    /**
     * 开始抢车位通知
     * @param data
     */
    private plotStartSnatchNoticeRet(data: PlotStartSnatchNoticeRet): void {
        const { errMsg, tankId, snatchRoleId, snatchTime } = data;
        if (errMsg !== PlotStartSnatchNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotStartSnatchNoticeRet[errMsg]);
            return;
        }

        let updatePlayerId = "";
        let updateSpaceId = -1;
        const playerId = Player.getInstance().getId();
        const index = this.data.plotSpaceInfos.findIndex((v) => v.pRoleId === playerId && v.tankId === tankId);
        if (index !== -1) {
            updatePlayerId = this.data.ownerRoleId;
            updateSpaceId = this.data.plotSpaceInfos[index].spaceId;

            this.data.plotSpaceInfos[index].snatchRoleId = snatchRoleId;
            this.data.plotSpaceInfos[index].snatchTime = snatchTime;
        }

        if (this.otherData) {
            const index2 = this.otherData.plotSpaceInfos.findIndex(
                (v) => v.pRoleId === playerId && v.tankId === tankId
            );
            if (index2 !== -1) {
                updatePlayerId = this.otherData.ownerRoleId;
                updateSpaceId = this.otherData.plotSpaceInfos[index2].spaceId;

                this.otherData.plotSpaceInfos[index2].snatchRoleId = snatchRoleId;
                this.otherData.plotSpaceInfos[index2].snatchTime = snatchTime;
            }
        }

        const index3 = this.data.ownerTankSpaceInfos.findIndex((v) => v.pRoleId === playerId && v.tankId === tankId);
        if (index3 !== -1) {
            this.data.ownerTankSpaceInfos[index3].snatchRoleId = snatchRoleId;
            this.data.ownerTankSpaceInfos[index3].snatchTime = snatchTime;
        }

        if (updatePlayerId !== "") {
            this.emit(ParkEvent.UpdateSpaceState, updatePlayerId, updateSpaceId);
        }
    }

    /**
     * 获取记录
     * @param data
     */
    private plotGetLogRet(data: PlotGetLogRet): void {
        const { errMsg } = data;
        if (errMsg !== PlotGetLogRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotGetLogRet[errMsg]);
            return;
        }

        this.emit(PlotGetLogRet.prototype.clazzName, data);
    }

    /**
     * 解锁皮肤
     * @param data
     */
    private plotUnlockDressRet(data: PlotUnlockDressRet): void {
        const { errMsg, dressType, skinId, dressLevelInfo } = data;
        if (errMsg !== PlotUnlockDressRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotUnlockDressRet[errMsg]);
            return;
        }

        const skinTypeData = this.data.dressInfos.find((v) => v.dressType === dressType);
        if (skinTypeData) {
            skinTypeData.dressLevelInfos.push(dressLevelInfo);
        } else {
            this.data.dressInfos.push({ dressType, dressId: 0, dressLevelInfos: [dressLevelInfo] });
        }

        this.emit(PlotUnlockDressRet.prototype.clazzName, dressType, skinId);
    }

    /**
     * 升级皮肤
     * @param data
     */
    private plotUpgradeDressRet(data: PlotUpgradeDressRet): void {
        const { errMsg, dressType, skinId, dressLevelInfo } = data;
        if (errMsg !== PlotUpgradeDressRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotUpgradeDressRet[errMsg]);
            return;
        }

        const skinTypeData = this.data.dressInfos.find((v) => v.dressType === dressType);
        const index = skinTypeData.dressLevelInfos.findIndex((v) => v.skinId === skinId);
        skinTypeData.dressLevelInfos[index] = dressLevelInfo;

        this.emit(PlotUpgradeDressRet.prototype.clazzName, dressType, skinId);
    }

    /**
     * 使用皮肤
     * @param data
     */
    private plotModifyDressRet(data: PlotModifyDressRet): void {
        const { errMsg, dressType, dressId } = data;
        if (errMsg !== PlotModifyDressRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotModifyDressRet[errMsg]);
            return;
        }

        const skinTypeData = this.data.dressInfos.find((v) => v.dressType === dressType);
        skinTypeData.dressId = dressId;

        this.emit(PlotModifyDressRet.prototype.clazzName, dressType, dressId);
    }

    /**
     * 刷新订单
     * @param data
     */
    private plotRefreshOrderRet(data: PlotRefreshOrderRet): void {
        const { errMsg, orderInfos } = data;
        if (errMsg !== PlotRefreshOrderRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotRefreshOrderRet[errMsg]);
            return;
        }

        this.orderData = orderInfos;

        this.emit(PlotRefreshOrderRet.prototype.clazzName);
    }

    /**
     * 接取订单
     * @param data
     */
    private plotBatchAcceptOrderRet(data: PlotBatchAcceptOrderRet): void {
        const { errMsg, orderInfos, tankOrderInfos } = data;
        if (errMsg !== PlotBatchAcceptOrderRet.ErrorResult.None) {
            if (errMsg === PlotBatchAcceptOrderRet.ErrorResult.OrderNotExist) {
                this.emit(ParkEvent.OrderRefreshed);
            } else {
                Tips.getInstance().show(i18n.plotBatchAcceptOrderRet[errMsg]);
            }
            return;
        }

        this.orderData = orderInfos;
        this.orderReceivedTimes += tankOrderInfos.length;

        this.emit(PlotBatchAcceptOrderRet.prototype.clazzName);
    }

    /**
     * 加速订单
     * @param data
     */
    private plotBatchSpeedUpOrderRet(data: PlotBatchSpeedUpOrderRet): void {
        const { errMsg, orderInfos } = data;
        if (errMsg !== PlotBatchSpeedUpOrderRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotBatchSpeedUpOrderRet[errMsg]);
            return;
        }

        this.orderData = orderInfos;

        this.emit(PlotBatchSpeedUpOrderRet.prototype.clazzName);

        RedPoint.getInstance().checkRelative(RedPointId.ParkOrder);
    }

    /**
     * 领取订单奖励
     * @param data
     */
    private plotBatchReceiveOrderRet(data: PlotBatchReceiveOrderRet): void {
        const { errMsg, orderInfos } = data;
        if (errMsg !== PlotBatchReceiveOrderRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.plotBatchReceiveOrderRet[errMsg]);
            return;
        }

        this.orderData = orderInfos;

        this.emit(PlotBatchReceiveOrderRet.prototype.clazzName);

        RedPoint.getInstance().checkRelative(RedPointId.ParkOrder);
    }

    /**
     * 获取停车场数据
     * @returns
     */
    public getData(): IPlotInfo {
        return this.data;
    }

    /**
     * 获取他人停车场数据
     * @returns
     */
    public getOtherData(): IPlotInfo {
        return this.otherData;
    }

    /**
     * 获取等级数据
     * @param exp 经验
     * @returns
     */
    public getLevelData(exp: number): { level: number; exp: number; totalExp: number; isMax: boolean } {
        const levelInfo = TBParkingLotLevel.getInstance().getList();
        levelInfo.sort((a, b) => a.id - b.id);
        let level = 0;
        let totalExp = 0;
        for (const e of levelInfo) {
            level = e.id;
            totalExp = e.exp;
            if (exp < totalExp) {
                break;
            }

            exp -= totalExp;
        }

        return { level, exp, totalExp, isMax: level === levelInfo[levelInfo.length - 1].id && exp >= totalExp };
    }

    /**
     * 获取属性
     * @param level 等级
     * @param skinTypeData 皮肤类型数据
     * @returns
     */
    public getAttr(level: number, skinTypeData: IPlotDressInfo[]): IAttrInfo[] {
        const attrId = [
            EnumEconomyAttributeType.ParkingLotGoldEarnings,
            EnumEconomyAttributeType.ParkingLotExpEarnings,
            EnumEconomyAttributeType.ParkingLotOrderEarnings,
            EnumEconomyAttributeType.DispatchSpeed,
        ];
        const attr: IAttrInfo[] = [];
        attrId.forEach((v) => attr.push({ type: v, value: 0 }));

        // 基础等级属性
        const levelInfo = TBParkingLotLevel.getInstance().getDataById(level);
        levelInfo.attribute.forEach(([attrId, init]) => {
            const tempAttr = attr.find((v) => v.type === attrId);
            if (tempAttr) {
                tempAttr.value = tempAttr.value + init;
            }
        });

        // 皮肤暂时不提供属性
        // skinTypeData.forEach((v) => {
        //     v.dressLevelInfos.forEach((v2) => {
        //         const skinInfo = TBParkingLotSkin.getInstance().getDataById(v2.skinId);
        //         skinInfo.skinOverallBonus.forEach(([attrId, init, step]) => {
        //             const tempAttr = attr.find((v) => v.type === attrId);
        //             if (tempAttr) {
        //                 const value = Math.round((init + step * (v2.level - 1)) * 1000) / 1000;
        //                 tempAttr.value = tempAttr.value + value;
        //             }
        //         });
        //     });
        // });

        return attr;
    }

    /**
     * 获取战车收益数据
     * @param tankId 战车id
     * @returns
     */
    public getTankIncomeData(tankId: number): IPlotTankAccTimeInfo {
        return this.tankIncomeData.find((v) => v.tankId === tankId);
    }

    /**
     * 获取已抢夺次数
     * @returns
     */
    public getSnatchedTimes(): number {
        return this.snatchedTimes;
    }

    /**
     * 获取皮肤类型数据
     * @param type 类型
     * @returns
     */
    public getSkinTypeData(type: EnumParkingLotSkinType): IPlotDressInfo {
        let tempType: DressEnum = null;
        switch (type) {
            case EnumParkingLotSkinType.Stall:
                tempType = DressEnum.Scene;
                break;
            case EnumParkingLotSkinType.Gate:
                tempType = DressEnum.Gate;
                break;
            case EnumParkingLotSkinType.Fence:
                tempType = DressEnum.Fence;
                break;
            case EnumParkingLotSkinType.StreetLamp:
                tempType = DressEnum.StreetLight;
                break;
            default:
                break;
        }
        return this.data.dressInfos.find((v) => v.dressType === tempType);
    }

    /**
     * 获取皮肤数据
     * @param type 类型
     * @param skinId 皮肤id
     * @returns
     */
    public getSkinData(type: EnumParkingLotSkinType, skinId: number): IPlotDressLevelInfo {
        const skinTypeData = this.getSkinTypeData(type);
        return skinTypeData && skinTypeData.dressLevelInfos.find((v) => v.skinId === skinId);
    }

    /**
     * 获取使用中的皮肤id
     * @param type 类型
     * @param skinData 皮肤数据
     * @returns
     */
    public getUsingSkinId(type: EnumParkingLotSkinType, skinData: IPlotDressInfo[]): number {
        let tempType: DressEnum = null;
        switch (type) {
            case EnumParkingLotSkinType.Stall:
                tempType = DressEnum.Scene;
                break;
            case EnumParkingLotSkinType.Gate:
                tempType = DressEnum.Gate;
                break;
            case EnumParkingLotSkinType.Fence:
                tempType = DressEnum.Fence;
                break;
            case EnumParkingLotSkinType.StreetLamp:
                tempType = DressEnum.StreetLight;
                break;
            default:
                break;
        }
        const tempSkinData = skinData.find((v) => v.dressType === tempType);
        if (tempSkinData && tempSkinData.dressId !== 0) {
            return tempSkinData.dressId;
        }

        const skinInfo = TBParkingLotSkin.getInstance().getDataByType(type);
        return skinInfo.find((v) => v.unlock === 1).id;
    }

    /**
     * 获取皮肤类型
     * @param type 类型
     * @returns
     */
    public getServerSkinType(type: EnumParkingLotSkinType): DressEnum {
        let tempType: DressEnum = null;
        switch (type) {
            case EnumParkingLotSkinType.Stall:
                tempType = DressEnum.Scene;
                break;
            case EnumParkingLotSkinType.Gate:
                tempType = DressEnum.Gate;
                break;
            case EnumParkingLotSkinType.Fence:
                tempType = DressEnum.Fence;
                break;
            case EnumParkingLotSkinType.StreetLamp:
                tempType = DressEnum.StreetLight;
                break;
            default:
                break;
        }

        return tempType;
    }

    /**
     * 获取皮肤类型
     * @param type 类型
     * @returns
     */
    public getConfigSkinType(type: DressEnum): EnumParkingLotSkinType {
        let tempType: EnumParkingLotSkinType = null;
        switch (type) {
            case DressEnum.Scene:
                tempType = EnumParkingLotSkinType.Stall;
                break;
            case DressEnum.Gate:
                tempType = EnumParkingLotSkinType.Gate;
                break;
            case DressEnum.Fence:
                tempType = EnumParkingLotSkinType.Fence;
                break;
            case DressEnum.StreetLight:
                tempType = EnumParkingLotSkinType.StreetLamp;
                break;
            default:
                break;
        }

        return tempType;
    }

    /**
     * 获取订单数据
     * @returns
     */
    public getOrderData(): IPlotOrderInfo[] {
        return this.orderData;
    }

    /**
     * 获取订单已接取次数
     * @returns
     */
    public getOrderReceivedTimes(): number {
        return this.orderReceivedTimes;
    }

    /**
     * 获取订单加速道具的消耗数据
     * @param orderUuid 订单uuid
     * @returns
     */
    public getOrderSpeedUpItemCostData(orderUuid: string[]): IPlotOrderSpeedUpInfo[] {
        const costData: IPlotOrderSpeedUpInfo[] = [];
        const nowTime = Time.getInstance().now();
        const speedUpDuration: number = TBParkingLot.getInstance().getValueByPara(EnumParkingLotPara.AccelerationTime);
        for (const e of orderUuid) {
            const orderData = this.orderData.find((e2) => e2.uuid === e);
            if (!orderData) {
                continue;
            }
            if (orderData.endTime === 0 || orderData.endTime <= nowTime) {
                continue;
            }

            const costCount = Math.ceil((orderData.endTime - nowTime) / (speedUpDuration * MINUTE_TO_SECOND * 1000));
            costData.push({ uuid: e, count: costCount });
        }

        return costData;
    }

    /**
     * 是否已收藏
     * @param playerId 玩家id
     * @returns
     */
    public isCollected(playerId: string): boolean {
        return this.data.collectPlotIds.includes(playerId);
    }

    /**
     * 是否已接取订单
     * @param tankId 战车id
     * @returns
     */
    public isReceivedOrder(tankId: number): boolean {
        return this.orderData.findIndex((v) => v.tankId === tankId) !== -1;
    }

    /**
     * 是否显示皮肤红点
     * @param skinId 皮肤id
     * @returns
     */
    public isSkinRedById(skinId: number): boolean {
        const skinInfo = TBParkingLotSkin.getInstance().getDataById(skinId);
        const skinData = Park.getInstance().getSkinData(skinInfo.type, skinId);
        let level = skinInfo.levelLimit;
        if (skinInfo.unlock === 0) {
            level = skinData ? skinData.level : 0;
        }
        if (level !== skinInfo.levelLimit) {
            const haveCount = Bag.getInstance().getItemCountById(skinInfo.upgradeID);
            const levelInfo = TBParkingSkinLevel.getInstance().getDataById(level + 1);
            return haveCount >= levelInfo.upgradeCost;
        }

        return false;
    }

    /**
     * 是否显示皮肤红点
     * @param type 类型
     * @returns
     */
    public isSkinRedByType(type: EnumParkingLotSkinType): boolean {
        const skinInfo = TBParkingLotSkin.getInstance().getDataByType(type);
        for (const e of skinInfo) {
            if (this.isSkinRedById(e.id)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否显示订单红点
     * @returns
     */
    public isOrderRed(): boolean {
        const nowTime = Time.getInstance().now();
        for (const e of this.orderData) {
            if (e.endTime !== 0 && e.endTime <= nowTime) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否显示车位红点
     * @returns
     */
    public isSpaceRed(): boolean {
        if (this.getRedStateByMyTank()) {
            return true;
        }
        const nowTime = Time.getInstance().now();
        const maxDuration: number = TBParkingLot.getInstance().getValueByPara(EnumParkingLotPara.ParkingTimeLimit);
        for (const e of this.data.plotSpaceInfos) {
            if (nowTime - e.startTime > maxDuration * HOUR_TO_SECOND * 1000 && e.isManage) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取红点状态-我的战车
     * @returns
     */
    public getRedStateByMyTank(): boolean {
        const nowTime = Time.getInstance().now();
        const maxDuration: number = TBParkingLot.getInstance().getValueByPara(EnumParkingLotPara.ParkingTimeLimit);
        for (const e of this.data.ownerTankSpaceInfos) {
            if (nowTime - e.startTime > maxDuration * HOUR_TO_SECOND * 1000) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检测升级
     * @returns
     */
    public checkUpgrade(): { level: number; oldLevel: number; isIncreaseExp: boolean } {
        const oldLevelData = this.getLevelData(this.data.exp);
        const isIncreaseExp = this.increaseExp !== 0;
        this.data.exp += this.increaseExp;
        this.increaseExp = 0;
        const levelData = this.getLevelData(this.data.exp);

        return { level: levelData.level, oldLevel: oldLevelData.level, isIncreaseExp };
    }

    /**
     * 检测自动收车
     */
    public checkAutoFinishPark(): void {
        if (!this.data) {
            return;
        }

        const maxDuration = TBParkingLot.getInstance().getValueByPara(EnumParkingLotPara.AutomaticWindingDown);
        const nowTime = Time.getInstance().now();
        const playerId = Player.getInstance().getId();
        this.data.ownerTankSpaceInfos.forEach((v) => {
            if (nowTime - v.startTime > maxDuration * HOUR_TO_SECOND * 1000) {
                if (v.ownerRoleId === playerId) {
                    this.sendAutoFinish(v.spaceId);
                } else {
                    this.sendAutoFinishOther(v.ownerRoleId, v.spaceId);
                }
            }
        });
        this.data.plotSpaceInfos.forEach((v) => {
            if (v.pRoleId !== playerId && nowTime - v.startTime > maxDuration * HOUR_TO_SECOND * 1000) {
                this.sendAutoFinish(v.spaceId);
            }
        });
    }

    /**
     * 皮肤是否解锁
     * @param skinId 皮肤id
     * @returns
     */
    public isUnlockSkinById(skinId: number): boolean {
        let isUnlock = true;
        const skinInfo = TBParkingLotSkin.getInstance().getDataById(skinId);
        if (skinInfo.unlock === 0) {
            const skinTypeData = this.getSkinTypeData(skinInfo.type);
            isUnlock = skinTypeData && skinTypeData.dressLevelInfos.findIndex((v) => v.skinId === skinId) !== -1;
        }

        return isUnlock;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 获取他人数据
     * @param playerId 玩家id
     */
    public sendGetOtherData(playerId: string): void {
        const data = PlotGet.create({ ownerRoleId: playerId });
        Socket.getInstance().send(data);
    }

    /**
     * 重命名
     * @param name
     */
    public sendRename(name: string): void {
        const data = PlotModifyName.create({ name });
        Socket.getInstance().send(data);
    }

    /**
     * 展示战车
     * @param tankId 战车id
     */
    public sendShowTank(tankId: number): void {
        const data = PlotModifyShow.create({ tankIds: tankId === -1 ? [] : [tankId] });
        Socket.getInstance().send(data);
    }

    /**
     * 设置
     * @param plotManageInfo 设置数据
     */
    public sendSet(plotManageInfo: IPlotManageInfo): void {
        const data = PlotModifyManage.create({ plotManageInfo });
        Socket.getInstance().send(data);
    }

    /**
     * 搜索
     * @param searchType 搜索类型
     */
    public sendSearch(searchType: SearchType): void {
        const data = PlotSearch.create({ searchType });
        Socket.getInstance().send(data);
    }

    /**
     * 收藏
     * @param collectRoleId 玩家id
     * @param collectType 收藏类型
     */
    public sendCollect(collectRoleId: string, collectType: CollectType): void {
        const data = PlotCollect.create({ collectRoleId, collectType });
        Socket.getInstance().send(data);
    }

    /**
     * 停车
     * @param spaceId 车位id
     * @param tankId 战车id
     */
    public sendPark(spaceId: number, tankId: number): void {
        const data = PlotStop.create({ spaceId, tankId });
        Socket.getInstance().send(data);
    }

    /**
     * 停车-他人
     * @param ownerRoleId 玩家id
     * @param spaceId 车位id
     * @param tankId 战车id
     * @param isManage 是否缴纳管理费
     * @param isProtect 是否代替驻守
     */
    public sendParkOther(
        ownerRoleId: string,
        spaceId: number,
        tankId: number,
        isManage: boolean,
        isProtect: boolean
    ): void {
        const data = PlotStopOther.create({ ownerRoleId, spaceId, tankId, isManage, isProtect });
        Socket.getInstance().send(data);
    }

    /**
     * 收车
     * @param spaceId 车位id
     */
    public sendFinishPark(spaceId: number): void {
        const data = PlotReceive.create({ spaceId });
        Socket.getInstance().send(data);
    }

    /**
     * 收车-他人
     * @param ownerRoleId 玩家id
     * @param spaceId 车位id
     */
    public sendFinishParkOther(ownerRoleId: string, spaceId: number): void {
        const data = PlotReceiveOther.create({ ownerRoleId, spaceId });
        Socket.getInstance().send(data);
    }

    /**
     * 自动收车
     * @param spaceId 车位id
     */
    public sendAutoFinish(spaceId: number): void {
        const data = PlotAutoReceive.create({ spaceId });
        Socket.getInstance().send(data);
    }

    /**
     * 自动收车-他人
     * @param ownerRoleId 玩家id
     * @param spaceId 车位id
     */
    public sendAutoFinishOther(ownerRoleId: string, spaceId: number): void {
        const data = PlotAutoReceiveOther.create({ ownerRoleId, spaceId });
        Socket.getInstance().send(data);
    }

    /**
     * 开始抢车位
     * @param ownerRoleId 玩家id
     * @param spaceId 车位id
     */
    public sendStartSnatchSpace(ownerRoleId: string, spaceId: number): void {
        const data = PlotStartSnatch.create({ ownerRoleId, spaceId });
        Socket.getInstance().send(data);
    }

    /**
     * 结束抢车位
     * @param ownerRoleId 玩家id
     * @param spaceId 车位id
     * @param isWin 是否成功
     */
    public sendEndSnatchSpace(ownerRoleId: string, spaceId: number, isWin: boolean): void {
        const data = PlotEndSnatch.create({ ownerRoleId, spaceId, isWin });
        Socket.getInstance().send(data);
    }

    /**
     * 抢车位分享
     * @param ownerRoleId 玩家id
     * @param spaceId 车位id
     */
    public sendSnatchSpaceShare(ownerRoleId: string, spaceId: number): void {
        const data = PlotSnatchShare.create({ ownerRoleId, spaceId });
        Socket.getInstance().send(data);
    }

    /**
     * 获取记录
     */
    public sendGetRecord(): void {
        const data = PlotGetLog.create({});
        Socket.getInstance().send(data);
    }

    /**
     * 解锁皮肤
     * @param dressType 类型
     * @param skinId 皮肤id
     */
    public sendUnlockSkin(dressType: DressEnum, skinId: number): void {
        const data = PlotUnlockDress.create({ dressType, skinId });
        Socket.getInstance().send(data);
    }

    /**
     * 升级皮肤
     * @param dressType 类型
     * @param skinId 皮肤id
     */
    public sendUpgradeSkin(dressType: DressEnum, skinId: number): void {
        const data = PlotUpgradeDress.create({ dressType, skinId });
        Socket.getInstance().send(data);
    }

    /**
     * 使用皮肤
     * @param dressType 类型
     * @param dressId 皮肤id
     */
    public sendUseSkin(dressType: DressEnum, dressId: number): void {
        const data = PlotModifyDress.create({ dressType, dressId });
        Socket.getInstance().send(data);
    }

    /**
     * 刷新订单
     * @param isAd 是否为广告调用
     */
    public sendOrderRefresh(isAd: boolean): void {
        const data = PlotRefreshOrder.create({ isAd });
        Socket.getInstance().send(data);
    }

    /**
     * 接取订单
     * @param tankOrderInfos 接取数据
     */
    public sendOrderReceive(tankOrderInfos: IPlotTankOrderInfo[]): void {
        const data = PlotBatchAcceptOrder.create({ tankOrderInfos });
        Socket.getInstance().send(data);
    }

    /**
     * 加速订单
     * @param orderSpeedUpInfos 加速数据
     */
    public sendOrderSpeedUp(orderSpeedUpInfos: IPlotOrderSpeedUpInfo[]): void {
        const data = PlotBatchSpeedUpOrder.create({ orderSpeedUpInfos });
        Socket.getInstance().send(data);
    }

    /**
     * 领取订单奖励
     * @param uuidList 订单id
     */
    public sendOrderGetReward(uuidList: string[]): void {
        const data = PlotBatchReceiveOrder.create({ uuidList });
        Socket.getInstance().send(data);
    }
}
