/*
 * @Author: JackyFu
 * @Date: 2024-04-18 14:58:37
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:26:44
 */

import Socket from "../../../nsn/core/Socket";
import Tips from "../../../nsn/util/Tips";
import {
    ActivityNewTrialFree,
    ActivityNewTrialFreeRet,
    ActivityNewTrialReward,
    ActivityNewTrialRewardRet,
    IActivityInfo,
    IActivityNewTrialObj,
} from "../../../protobuf/proto";
import i18n from "../../config/i18n/I18n";
import { ACTIVITY_ID } from "../../data/parser/TBActivity";
import Combat, { CombatEvent, DungeonType } from "../Combat";
import { ActivityLogic } from "./ActivityLogic";

export default class ActivityNewbieTrial extends ActivityLogic {
    private newTrialInfo: IActivityNewTrialObj = null;

    /**
     * 更新数据
     * @param data
     */
    public update(data: IActivityInfo): void {
        this.newTrialInfo = data.newTrialInfo;
    }

    /**
     * 清理数据
     */
    public clear(): void {
        this.newTrialInfo = null;
    }

    /**
     * 注册消息
     */
    protected registerHandler(): void {
        Socket.getInstance().on(ActivityNewTrialFreeRet.prototype.clazzName, this.activityNewTrialFreeRet, this);
        Socket.getInstance().on(ActivityNewTrialRewardRet.prototype.clazzName, this.activityNewTrialRewardRet, this);
    }

    /**
     * 领取免费奖励
     */
    private activityNewTrialFreeRet(data: ActivityNewTrialFreeRet): void {
        const { newTrialInfo, errMsg } = data;
        if (errMsg !== ActivityNewTrialFreeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.activityNewTrialFreeRet[data.errMsg]);
            return;
        }
        this.newTrialInfo = newTrialInfo;
        this.emit(ActivityNewTrialFreeRet.prototype.clazzName);
    }

    /**
     * 过关领取奖励
     */
    private activityNewTrialRewardRet(data: ActivityNewTrialRewardRet): void {
        const { newTrialInfo, errMsg } = data;
        if (errMsg !== ActivityNewTrialRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.activityNewTrialRewardRet[data.errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.Trial);
            return;
        }
        this.newTrialInfo = newTrialInfo;
        this.emit(ActivityNewTrialRewardRet.prototype.clazzName);
    }

    /**
     * 获取信息
     * @returns
     */
    public getInfo(): IActivityNewTrialObj {
        return this.newTrialInfo;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 领取免费奖励
     */
    public sendActivityNewTrialFree(): void {
        const data = ActivityNewTrialFree.create({ activityId: ACTIVITY_ID.NEWBIE_TRIAL });
        Socket.getInstance().send(data);
    }

    /**
     * 领取通关奖励
     */
    public sendActivityNewTrialReward(): void {
        const data = ActivityNewTrialReward.create({ activityId: ACTIVITY_ID.NEWBIE_TRIAL });
        Socket.getInstance().send(data);
    }
}
