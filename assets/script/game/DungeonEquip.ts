/*
 * @Author: chenx
 * @Date: 2024-08-26 11:37:08
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-02 15:13:42
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Tips from "../../nsn/util/Tips";
import {
    EquipDungeonBatchInvite,
    EquipDungeonBatchInviteRet,
    EquipDungeonBattleEnd,
    EquipDungeonBattleEndRet,
    EquipDungeonBattleStart,
    EquipDungeonBattleStartNoticeRet,
    EquipDungeonBattleStartRet,
    EquipDungeonBattleSync,
    EquipDungeonBattleSyncRet,
    EquipDungeonCreateTeam,
    EquipDungeonCreateTeamRet,
    EquipDungeonHurry,
    EquipDungeonHurryNoticeRet,
    EquipDungeonHurryRet,
    EquipDungeonInitRet,
    EquipDungeonInviteNoticeRet,
    EquipDungeonJoinTeam,
    EquipDungeonJoinTeamNoticeRet,
    EquipDungeonJoinTeamRet,
    EquipDungeonKickPeople,
    EquipDungeonKickPeopleNoticeRet,
    EquipDungeonKickPeopleRet,
    EquipDungeonQuitTeam,
    EquipDungeonQuitTeamNoticeRet,
    EquipDungeonQuitTeamRet,
    EquipDungeonReceiveTicket,
    EquipDungeonReceiveTicketRet,
    EquipDungeonSearch,
    EquipDungeonSearchRet,
    EquipDungeonShareChannel,
    EquipDungeonShareChannelRet,
    EquipSearchType,
    IChallengerInfo,
    IEquipDungeonTeamInfo,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumDungeonEquipmentTotalPara } from "../data/base/BaseDungeonEquipmentTotal";
import { EnumTaskSeqModule } from "../data/base/BaseTaskSeq";
import TBDungeonEquipmentTotal from "../data/parser/TBDungeonEquipmentTotal";
import TBTaskGroup from "../data/parser/TBTaskGroup";
import TBTaskSeq from "../data/parser/TBTaskSeq";
import Bag from "./Bag";
import Task from "./Task";

/**
 * 装备副本事件
 */
export enum DungeonEquipEvent {
    DeleteBeInviteData = "delete-be-invited-data", // 删除被邀请数据
}

/**
 * 装备副本
 */
export default class DungeonEquip extends Logic {
    private teamData: IEquipDungeonTeamInfo = null; // 队伍数据
    private teamDataByCombat: IChallengerInfo = null; // 队友数据-组队战斗

    private keyRecoverTime: number = 0; // 钥匙恢复时间
    private helpedTimes: number = 0; // 已帮助次数
    private inviteTime: { [playerId: string]: number } = {}; // 邀请时间

    protected registerHandler(): void {
        Socket.getInstance().on(EquipDungeonInitRet.prototype.clazzName, this.equipDungeonInitRet, this);

        Socket.getInstance().on(EquipDungeonCreateTeamRet.prototype.clazzName, this.equipDungeonCreateTeamRet, this);
        Socket.getInstance().on(EquipDungeonJoinTeamRet.prototype.clazzName, this.equipDungeonJoinTeamRet, this);
        Socket.getInstance().on(
            EquipDungeonJoinTeamNoticeRet.prototype.clazzName,
            this.equipDungeonJoinTeamNoticeRet,
            this
        );
        Socket.getInstance().on(EquipDungeonHurryRet.prototype.clazzName, this.equipDungeonHurryRet, this);
        Socket.getInstance().on(EquipDungeonHurryNoticeRet.prototype.clazzName, this.equipDungeonHurryNoticeRet, this);
        Socket.getInstance().on(EquipDungeonKickPeopleRet.prototype.clazzName, this.equipDungeonKickPeopleRet, this);
        Socket.getInstance().on(
            EquipDungeonKickPeopleNoticeRet.prototype.clazzName,
            this.equipDungeonKickPeopleNoticeRet,
            this
        );
        Socket.getInstance().on(EquipDungeonQuitTeamRet.prototype.clazzName, this.equipDungeonQuitTeamRet, this);
        Socket.getInstance().on(
            EquipDungeonQuitTeamNoticeRet.prototype.clazzName,
            this.equipDungeonQuitTeamNoticeRet,
            this
        );

        Socket.getInstance().on(EquipDungeonBattleStartRet.prototype.clazzName, this.equipDungeonBattleStartRet, this);
        Socket.getInstance().on(
            EquipDungeonBattleStartNoticeRet.prototype.clazzName,
            this.equipDungeonBattleStartNoticeRet,
            this
        );
        Socket.getInstance().on(EquipDungeonBattleSyncRet.prototype.clazzName, this.equipDungeonBattleSyncRet, this);
        Socket.getInstance().on(EquipDungeonBattleEndRet.prototype.clazzName, this.equipDungeonBattleEndRet, this);

        Socket.getInstance().on(
            EquipDungeonReceiveTicketRet.prototype.clazzName,
            this.equipDungeonReceiveTicketRet,
            this
        );
        Socket.getInstance().on(EquipDungeonSearchRet.prototype.clazzName, this.equipDungeonSearchRet, this);
        Socket.getInstance().on(
            EquipDungeonShareChannelRet.prototype.clazzName,
            this.equipDungeonShareChannelRet,
            this
        );
        Socket.getInstance().on(EquipDungeonBatchInviteRet.prototype.clazzName, this.equipDungeonBatchInviteRet, this);
        Socket.getInstance().on(
            EquipDungeonInviteNoticeRet.prototype.clazzName,
            this.equipDungeonInviteNoticeRet,
            this
        );
    }

    public clear(): void {
        this.teamData = null;
        this.teamDataByCombat = null;

        this.keyRecoverTime = 0;
        this.helpedTimes = 0;
        this.inviteTime = {};
    }

    /**
     * 初始化数据
     * @param data
     */
    private equipDungeonInitRet(data: EquipDungeonInitRet): void {
        const { errMsg, equipDungeonInfo } = data;
        if (errMsg !== EquipDungeonInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonInitRet[errMsg]);
            return;
        }

        this.keyRecoverTime = equipDungeonInfo.recoverTime;
        this.helpedTimes = equipDungeonInfo.helpTimes;
    }

    /**
     * 创建队伍
     * @param data
     */
    private equipDungeonCreateTeamRet(data: EquipDungeonCreateTeamRet): void {
        const { errMsg, teamInfo } = data;
        if (errMsg !== EquipDungeonCreateTeamRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonCreateTeamRet[errMsg]);
            return;
        }

        this.teamData = teamInfo;

        UI.getInstance().open("UIDungeonEntryEquip", -1);
    }

    /**
     * 加入队伍
     * @param data
     */
    private equipDungeonJoinTeamRet(data: EquipDungeonJoinTeamRet): void {
        const { errMsg, teamInfo, dungeonId } = data;
        if (errMsg !== EquipDungeonJoinTeamRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonJoinTeamRet[errMsg]);
            return;
        }

        this.teamData = teamInfo;

        UI.getInstance().open("UIDungeonEntryEquip", dungeonId);
        Tips.getInstance().info(i18n.dungeon0027);
    }

    /**
     * 加入队伍通知
     * @param data
     */
    private equipDungeonJoinTeamNoticeRet(data: EquipDungeonJoinTeamNoticeRet): void {
        const { errMsg, memberInfo } = data;
        if (errMsg !== EquipDungeonJoinTeamNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonJoinTeamNoticeRet[errMsg]);
            return;
        }

        this.teamData.helpRoleId = memberInfo.memberRoleId;
        this.teamData.memberInfos.push(memberInfo);

        this.emit(EquipDungeonJoinTeamNoticeRet.prototype.clazzName, data);
    }

    /**
     * 催促
     * @param data
     */
    private equipDungeonHurryRet(data: EquipDungeonHurryRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonHurryRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonHurryRet[errMsg]);
            return;
        }
    }

    /**
     * 催促通知
     * @param data
     */
    private equipDungeonHurryNoticeRet(data: EquipDungeonHurryNoticeRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonHurryNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonHurryNoticeRet[errMsg]);
            return;
        }

        this.emit(EquipDungeonHurryNoticeRet.prototype.clazzName);
    }

    /**
     * 踢出
     * @param data
     */
    private equipDungeonKickPeopleRet(data: EquipDungeonKickPeopleRet): void {
        const { errMsg, kickRoleId } = data;
        if (errMsg !== EquipDungeonKickPeopleRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonKickPeopleRet[errMsg]);
            return;
        }

        this.teamData.helpRoleId = "";
        const index = this.teamData.memberInfos.findIndex((e) => e.memberRoleId === kickRoleId);
        if (index !== -1) {
            this.teamData.memberInfos.splice(index, 1);
        }

        this.emit(EquipDungeonKickPeopleRet.prototype.clazzName);
    }

    /**
     * 踢出通知
     * @param data
     */
    private equipDungeonKickPeopleNoticeRet(data: EquipDungeonKickPeopleNoticeRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonKickPeopleNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonKickPeopleNoticeRet[errMsg]);
            return;
        }

        this.teamData = null;

        this.emit(EquipDungeonKickPeopleNoticeRet.prototype.clazzName);
    }

    /**
     * 退出队伍
     * @param data
     */
    private equipDungeonQuitTeamRet(data: EquipDungeonQuitTeamRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonQuitTeamRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonQuitTeamRet[errMsg]);
            return;
        }

        this.teamData = null;

        this.emit(EquipDungeonQuitTeamRet.prototype.clazzName);
    }

    /**
     * 退出队伍通知
     * @param data
     */
    private equipDungeonQuitTeamNoticeRet(data: EquipDungeonQuitTeamNoticeRet): void {
        const { errMsg, quitRoleId } = data;
        if (errMsg !== EquipDungeonQuitTeamNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonQuitTeamNoticeRet[errMsg]);
            return;
        }

        if (this.teamData.captionRoleId === quitRoleId) {
            this.teamData = null;
        } else {
            this.teamData.helpRoleId = "";
            const index = this.teamData.memberInfos.findIndex((e) => e.memberRoleId === quitRoleId);
            if (index !== -1) {
                this.teamData.memberInfos.splice(index, 1);
            }
        }

        this.emit(EquipDungeonQuitTeamNoticeRet.prototype.clazzName);
    }

    /**
     * 开始战斗
     * @param data
     */
    private equipDungeonBattleStartRet(data: EquipDungeonBattleStartRet): void {
        const { errMsg, memberInfo, dungeonId } = data;
        if (errMsg !== EquipDungeonBattleStartRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonBattleStartRet[errMsg]);
            return;
        }

        this.teamDataByCombat = memberInfo ? memberInfo.battleInfo : null;

        this.emit(EquipDungeonBattleStartRet.prototype.clazzName, dungeonId);
    }

    /**
     * 开始战斗通知
     * @param data
     */
    private equipDungeonBattleStartNoticeRet(data: EquipDungeonBattleStartNoticeRet): void {
        const { errMsg, captionMemberInfo, dungeonId } = data;
        if (errMsg !== EquipDungeonBattleStartNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonBattleStartNoticeRet[errMsg]);
            return;
        }

        this.teamDataByCombat = captionMemberInfo.battleInfo;

        this.emit(EquipDungeonBattleStartNoticeRet.prototype.clazzName, dungeonId);
    }

    /**
     * 同步战斗
     * @param data
     */
    private equipDungeonBattleSyncRet(data: EquipDungeonBattleSyncRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonBattleSyncRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonBattleSyncRet[errMsg]);
            return;
        }

        //

        this.emit(EquipDungeonBattleSyncRet.prototype.clazzName);
    }

    /**
     * 结束战斗
     * @param data
     */
    private equipDungeonBattleEndRet(data: EquipDungeonBattleEndRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonBattleEndRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonBattleEndRet[errMsg]);
            return;
        }

        //

        this.emit(EquipDungeonBattleEndRet.prototype.clazzName);
    }

    /**
     * 领取钥匙
     * @param data
     */
    private equipDungeonReceiveTicketRet(data: EquipDungeonReceiveTicketRet): void {
        const { errMsg, recoverTime } = data;
        if (errMsg !== EquipDungeonReceiveTicketRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonReceiveTicketRet[errMsg]);
            return;
        }

        this.keyRecoverTime = recoverTime;

        this.emit(EquipDungeonReceiveTicketRet.prototype.clazzName);
    }

    /**
     * 搜索
     * @param data
     */
    private equipDungeonSearchRet(data: EquipDungeonSearchRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonSearchRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonSearchRet[errMsg]);
            return;
        }

        this.emit(EquipDungeonSearchRet.prototype.clazzName, data);
    }

    /**
     * 分享
     * @param data
     */
    private equipDungeonShareChannelRet(data: EquipDungeonShareChannelRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonShareChannelRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonShareChannelRet[errMsg]);
            return;
        }
    }

    /**
     * 邀请
     * @param data
     */
    private equipDungeonBatchInviteRet(data: EquipDungeonBatchInviteRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonBatchInviteRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonBatchInviteRet[errMsg]);
            return;
        }
    }

    /**
     * 邀请通知
     * @param data
     */
    private equipDungeonInviteNoticeRet(data: EquipDungeonInviteNoticeRet): void {
        const { errMsg } = data;
        if (errMsg !== EquipDungeonInviteNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.equipDungeonInviteNoticeRet[errMsg]);
            return;
        }

        this.emit(EquipDungeonInviteNoticeRet.prototype.clazzName, data);
    }

    /**
     * 获取队伍数据
     * @returns
     */
    public getTeamData(): IEquipDungeonTeamInfo {
        return this.teamData;
    }

    /**
     * 获取队友数据-组队战斗
     * @returns
     */
    public getTeamDataByCombat(): IChallengerInfo {
        return this.teamDataByCombat;
    }

    /**
     * 获取钥匙恢复时间
     * @returns
     */
    public getKeyRecoverTime(): number {
        return this.keyRecoverTime;
    }

    /**
     * 获取已帮助次数
     * @returns
     */
    public getHelpedTimes(): number {
        return this.helpedTimes;
    }

    /**
     * 设置邀请时间
     * @param playerId 玩家id
     * @param time 邀请时间
     */
    public setInviteTime(playerId: string, time: number): void {
        this.inviteTime[playerId] = time + 500;
    }

    /**
     * 获取邀请时间
     * @param playerId 玩家id
     * @returns
     */
    public getInviteTime(playerId: string): number {
        return this.inviteTime[playerId];
    }

    /**
     * 是否显示红点-成就
     * @returns
     */
    public isRedByAchieve(): boolean {
        const taskSeqInfo = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.EquipAchievement);
        const taskGroupInfo = TBTaskGroup.getInstance().getDataById(taskSeqInfo.taskGroup);
        for (const e of taskGroupInfo.taskID) {
            if (Task.getInstance().isTaskCanGetReward(e)) {
                return true;
            }
        }
        const taskSeqInfo2 = TBTaskSeq.getInstance().getDataByModule(EnumTaskSeqModule.EquipAchievementLevel);
        const taskGroupInfo2 = TBTaskGroup.getInstance().getDataById(taskSeqInfo2.taskGroup);
        for (const e of taskGroupInfo2.taskID) {
            if (Task.getInstance().isTaskCanGetReward(e)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否可协助
     * @returns
     */
    public isHelp(): boolean {
        const costData: number[][] = TBDungeonEquipmentTotal.getInstance().getValueByPara(
            EnumDungeonEquipmentTotalPara.AssistConsume
        );
        const helpedTimes = this.getHelpedTimes();
        return Bag.getInstance().isEnough(costData[0][0], costData[Math.min(helpedTimes, costData.length - 1)][1]);
    }

    /**
     * 是否已解锁-关卡
     * @param levelId 关卡id
     * @returns
     */
    public isUnlockByLevel(levelId: number): boolean {
        return false;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 创建队伍
     */
    public sendCreateTeam(): void {
        const data = EquipDungeonCreateTeam.create({});
        Socket.getInstance().send(data);
    }

    /**
     * 加入队伍
     * @param teamId 队伍id
     * @param dungeonId 关卡id
     */
    public sendJoinTeam(teamId: string, dungeonId: number): void {
        const data = EquipDungeonJoinTeam.create({ teamId, dungeonId });
        Socket.getInstance().send(data);
    }

    /**
     * 催促
     * @param teamId 队伍id
     */
    public sendUrge(teamId: string): void {
        const data = EquipDungeonHurry.create({ teamId });
        Socket.getInstance().send(data);
    }

    /**
     * 踢出
     * @param teamId 队伍id
     * @param kickRoleId 玩家id
     */
    public sendKick(teamId: string, kickRoleId: string): void {
        const data = EquipDungeonKickPeople.create({ teamId, kickRoleId });
        Socket.getInstance().send(data);
    }

    /**
     * 退出队伍
     * @param teamId 队伍id
     */
    public sendQuitTeam(teamId: string): void {
        const data = EquipDungeonQuitTeam.create({ teamId });
        Socket.getInstance().send(data);
    }

    /**
     * 开始战斗
     * @param teamId 队伍id
     * @param dungeonId 关卡id
     */
    public sendStartCombat(teamId: string, dungeonId: number): void {
        const data = EquipDungeonBattleStart.create({ teamId, dungeonId });
        Socket.getInstance().send(data);
    }

    /**
     * 同步战斗
     * @param teamId 队伍id
     * @param barrier 轮数
     */
    public sendSyncCombat(teamId: string, barrier: number): void {
        const data = EquipDungeonBattleSync.create({ teamId, barrier });
        Socket.getInstance().send(data);
    }

    /**
     * 结束战斗
     * @param teamId 队伍id
     * @param isQuit 是否退出
     */
    public sendEndComabt(teamId: string, isQuit: boolean = false): void {
        const data = EquipDungeonBattleEnd.create({ teamId, isQuit });
        Socket.getInstance().send(data);
    }

    /**
     * 领取钥匙
     */
    public sendGetKey(): void {
        const data = EquipDungeonReceiveTicket.create({});
        Socket.getInstance().send(data);
    }

    /**
     * 搜索
     * @param searchType 搜索类型
     */
    public sendSearch(searchType: EquipSearchType): void {
        const data = EquipDungeonSearch.create({ searchType });
        Socket.getInstance().send(data);
    }

    /**
     * 分享
     * @param teamId 队伍id
     * @param dungeonId 关卡id
     */
    public sendShare(teamId: string, dungeonId: number): void {
        const data = EquipDungeonShareChannel.create({ teamId, dungeonId });
        Socket.getInstance().send(data);
    }

    /**
     * 邀请
     * @param teamId 队伍id
     * @param dungeonId 关卡id
     * @param roleIds 玩家id
     */
    public sendInvite(teamId: string, dungeonId: number, roleIds: string[]): void {
        const data = EquipDungeonBatchInvite.create({ teamId, dungeonId, roleIds });
        Socket.getInstance().send(data);
    }
}
