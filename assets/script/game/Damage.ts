/*
 * @Author: chenx
 * @Date: 2024-12-09 10:46:56
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-16 10:30:40
 */
import Logic from "../../nsn/core/Logic";
import MathUtils from "../../nsn/util/MathUtils";
import Utils from "../../nsn/util/Utils";
import { EnumAttributeType } from "../data/base/BaseAttribute";
import { EnumSkillType } from "../data/base/BaseSkill";
import {
    EnumSkillEffectDamageSourceType,
    EnumSkillEffectDamageType,
    EnumSkillEffectEffectType,
} from "../data/base/BaseSkillEffect";
import DataSkillEffect from "../data/extend/DataSkillEffect";
import { CombatDamageState, ICombatDamageData } from "../prefab/combat/PrefabCombatDamageItem";
import Attribute from "./Attribute";
import Combat, { CombatType } from "./Combat";
import { CombatMember } from "./combat/CombatMember";
import CombatLog, { CombatLogId } from "./CombatLog";
import { ICombatSkillData } from "./Skill";

/**
 * 伤害
 */
export default class Damage extends Logic {
    /**
     * 获取伤害数据
     * @param effectInfo 效果信息
     * @param effectPara 效果参数
     * @param skillData 技能数据
     * @param releaseMember 释放成员
     * @param targetMember 目标成员
     * @returns
     */
    public getDamageData(
        effectInfo: DataSkillEffect,
        effectPara: number[],
        skillData: ICombatSkillData,
        releaseMember: CombatMember,
        targetMember: CombatMember
    ): ICombatDamageData {
        const releaseMemberBaseData = releaseMember.getBaseData();
        const targetMemberBaseData = targetMember.getBaseData();

        const damageData: ICombatDamageData = {
            state: CombatDamageState.Init,

            uuid: -1,
            type: null,
            sourceType: null,
            damage: 0,
            damageBase: 0,

            initPos: null,
        };

        switch (effectInfo.effectType) {
            case EnumSkillEffectEffectType.Buff102:
                Attribute.getInstance().calFinalAttr(releaseMemberBaseData.attr, targetMemberBaseData.attr);
                Attribute.getInstance().calFinalAttr(targetMemberBaseData.attr, releaseMemberBaseData.attr);

                damageData.sourceType = EnumSkillEffectDamageSourceType.Hit;
                this.calDamage(damageData, effectInfo, effectPara, skillData, releaseMember, targetMember);

                this.logDamage(damageData, effectInfo, effectPara, skillData, releaseMember, targetMember);

                return damageData;
            case EnumSkillEffectEffectType.Buff103:
                damageData.sourceType = EnumSkillEffectDamageSourceType.Hit;
                this.calDamage(damageData, effectInfo, effectPara, skillData, releaseMember, targetMember);

                this.logDamage(damageData, effectInfo, effectPara, skillData, releaseMember, targetMember);

                return damageData;
            default:
                Attribute.getInstance().calFinalAttr(releaseMemberBaseData.attr, targetMemberBaseData.attr);
                Attribute.getInstance().calFinalAttr(targetMemberBaseData.attr, releaseMemberBaseData.attr);
                break;
        }

        // 闪避
        if (targetMemberBaseData.attr[EnumAttributeType.Eva].finalValue >= MathUtils.getRandomValue(0, 1)) {
            damageData.sourceType = EnumSkillEffectDamageSourceType.Eva;
            damageData.damage = 0;

            this.logDamage(damageData, effectInfo, effectPara, skillData, releaseMember, targetMember);

            return damageData;
        }

        // 格挡
        if (
            targetMemberBaseData.attr[EnumAttributeType.Block].value >
            releaseMemberBaseData.attr[EnumAttributeType.Block].value
        ) {
            if (releaseMemberBaseData.attr[EnumAttributeType.Block].info.para[0] >= MathUtils.getRandomValue(0, 1)) {
                damageData.sourceType = EnumSkillEffectDamageSourceType.Block;
                damageData.damage = 0;

                this.logDamage(damageData, effectInfo, effectPara, skillData, releaseMember, targetMember);

                return damageData;
            }
        }

        // 命中
        damageData.sourceType = EnumSkillEffectDamageSourceType.Hit;
        this.calDamage(damageData, effectInfo, effectPara, skillData, releaseMember, targetMember);

        // 暴击/会心
        damageData.damageBase = damageData.damage;
        const isCritHit =
            MathUtils.getRandomValue(0, 1) <= releaseMemberBaseData.attr[EnumAttributeType.CritHit].finalValue;
        const isCrit = MathUtils.getRandomValue(0, 1) <= releaseMemberBaseData.attr[EnumAttributeType.Crit].finalValue;
        if (isCritHit && isCrit) {
            damageData.sourceType = EnumSkillEffectDamageSourceType.CritHitAndCrit;
            damageData.damage *=
                releaseMemberBaseData.attr[EnumAttributeType.CritHitDmg].finalValue *
                releaseMemberBaseData.attr[EnumAttributeType.CritDmg].finalValue;
        } else if (isCritHit) {
            damageData.sourceType = EnumSkillEffectDamageSourceType.CritHit;
            damageData.damage *= releaseMemberBaseData.attr[EnumAttributeType.CritHitDmg].finalValue;
        } else if (isCrit) {
            damageData.sourceType = EnumSkillEffectDamageSourceType.Crit;
            damageData.damage *= releaseMemberBaseData.attr[EnumAttributeType.CritDmg].finalValue;
        }

        this.logDamage(damageData, effectInfo, effectPara, skillData, releaseMember, targetMember);

        return damageData;
    }

    /**
     * 计算伤害
     * @param damageData 伤害数据
     * @param effectInfo 效果信息
     * @param effectPara 效果参数
     * @param skillData 技能数据
     * @param releaseMember 释放成员
     * @param targetMember 目标成员
     */
    private calDamage(
        damageData: ICombatDamageData,
        effectInfo: DataSkillEffect,
        effectPara: number[],
        skillData: ICombatSkillData,
        releaseMember: CombatMember,
        targetMember: CombatMember
    ): void {
        const releaseMemberBaseData = releaseMember.getBaseData();
        const targetMemberBaseData = targetMember.getBaseData();

        let fValue = 0;
        let pValue = 0;
        let maxValue = 0;
        switch (effectInfo.effectType) {
            case EnumSkillEffectEffectType.Buff101:
            case EnumSkillEffectEffectType.Buff102:
                {
                    const [damageType, f, p] = effectPara;
                    damageData.type = damageType;
                    fValue = f;
                    pValue = p;

                    switch (damageData.type) {
                        case EnumSkillEffectDamageType.Fire:
                        case EnumSkillEffectDamageType.Thunder:
                        case EnumSkillEffectDamageType.Wind:
                        case EnumSkillEffectDamageType.Physics:
                            damageData.damage =
                                fValue +
                                (releaseMemberBaseData.attr[EnumAttributeType.FinalAtk].finalValue -
                                    targetMemberBaseData.attr[EnumAttributeType.FinalDef].finalValue) *
                                    pValue;
                            break;
                        case EnumSkillEffectDamageType.Real:
                            damageData.damage =
                                fValue +
                                (releaseMemberBaseData.attr[EnumAttributeType.FinalAtk].finalValue -
                                    targetMemberBaseData.attr[EnumAttributeType.FinalDef].finalValue * 0) *
                                    pValue;
                            break;
                        default:
                            break;
                    }

                    switch (skillData.info.type) {
                        case EnumSkillType.LeadASkill:
                            damageData.damage *= Math.max(
                                1 +
                                    releaseMemberBaseData.attr[EnumAttributeType.ESkillB].finalValue +
                                    releaseMemberBaseData.attr[EnumAttributeType.GlobalSkillB].finalValue,
                                0.01
                            );
                            break;
                        case EnumSkillType.LeadAtk:
                        case EnumSkillType.MonsterAtk:
                            damageData.damage *= Math.max(
                                1 + releaseMemberBaseData.attr[EnumAttributeType.AtkB].finalValue,
                                0.01
                            );
                            break;
                        case EnumSkillType.WeaponASkill:
                        case EnumSkillType.WeaponPSkill:
                            break;
                        case EnumSkillType.WingASkill:
                        case EnumSkillType.WingPSkill:
                            break;
                        case EnumSkillType.TankASkill:
                        case EnumSkillType.TankPSkill:
                            break;
                        case EnumSkillType.ArcherASkill:
                        case EnumSkillType.ArcherPSkill:
                            damageData.damage *= Math.max(
                                1 + releaseMemberBaseData.attr[EnumAttributeType.ArcherSkillB].finalValue,
                                0.01
                            );
                            break;
                        case EnumSkillType.ArcherAtk:
                            break;
                        case EnumSkillType.PetASkill:
                        case EnumSkillType.PetPSkill:
                            damageData.damage *= Math.max(
                                1 + releaseMemberBaseData.attr[EnumAttributeType.PetSkillB].finalValue,
                                0.01
                            );
                            break;
                        case EnumSkillType.MagicASkill:
                            damageData.damage *= Math.max(
                                1 +
                                    releaseMemberBaseData.attr[EnumAttributeType.ASkillB].finalValue +
                                    releaseMemberBaseData.attr[EnumAttributeType.GlobalSkillB].finalValue,
                                0.01
                            );
                            break;
                        case EnumSkillType.MagicPSkill:
                            damageData.damage *= Math.max(
                                1 +
                                    releaseMemberBaseData.attr[EnumAttributeType.PSkillB].finalValue +
                                    releaseMemberBaseData.attr[EnumAttributeType.GlobalSkillB].finalValue,
                                0.01
                            );
                            break;
                        default:
                            break;
                    }

                    let damageBonus = 1 + releaseMemberBaseData.attr[EnumAttributeType.GlobalDB].finalValue;
                    const combatType = Combat.getInstance().getCombatType(releaseMemberBaseData.dungeonType);
                    switch (combatType) {
                        case CombatType.Main:
                        case CombatType.Pve:
                            damageBonus += releaseMemberBaseData.attr[EnumAttributeType.MonsterDB].finalValue;
                            break;
                        default:
                            break;
                    }
                    switch (damageData.type) {
                        case EnumSkillEffectDamageType.Fire:
                            damageBonus += releaseMemberBaseData.attr[EnumAttributeType.FireDB].finalValue;
                            break;
                        case EnumSkillEffectDamageType.Thunder:
                            damageBonus += releaseMemberBaseData.attr[EnumAttributeType.ThunderDB].finalValue;
                            break;
                        case EnumSkillEffectDamageType.Wind:
                            damageBonus += releaseMemberBaseData.attr[EnumAttributeType.WindDB].finalValue;
                            break;
                        case EnumSkillEffectDamageType.Physics:
                            damageBonus += releaseMemberBaseData.attr[EnumAttributeType.PhysicsDB].finalValue;
                            break;
                        case EnumSkillEffectDamageType.Real:
                            damageBonus += releaseMemberBaseData.attr[EnumAttributeType.RealDB].finalValue;
                            break;
                        default:
                            break;
                    }
                    damageData.damage *= Math.max(damageBonus, 0.01);

                    let damageBonus2 = 1;
                    switch (combatType) {
                        case CombatType.Pvp:
                            damageBonus2 += releaseMemberBaseData.attr[EnumAttributeType.PVPDB].finalValue;
                            break;
                        default:
                            break;
                    }
                    damageData.damage *= Math.max(damageBonus2, 0.1);

                    damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.GlobalDFB].value;

                    switch (skillData.info.type) {
                        case EnumSkillType.LeadASkill:
                            damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.ESkillFB].value;
                            damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.GlobalSkillDFB].value;
                            break;
                        case EnumSkillType.LeadAtk:
                        case EnumSkillType.MonsterAtk:
                            damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.AtkFB].value;

                            damageData.damage = Math.max(
                                damageData.damage,
                                releaseMemberBaseData.attr[EnumAttributeType.FinalAtk].finalValue *
                                    releaseMemberBaseData.attr[EnumAttributeType.FinalAtk].info.para[0]
                            );
                            break;
                        case EnumSkillType.WeaponASkill:
                        case EnumSkillType.WeaponPSkill:
                            break;
                        case EnumSkillType.WingASkill:
                        case EnumSkillType.WingPSkill:
                            break;
                        case EnumSkillType.TankASkill:
                        case EnumSkillType.TankPSkill:
                            break;
                        case EnumSkillType.ArcherASkill:
                        case EnumSkillType.ArcherPSkill:
                            damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.ArcherSkillFB].value;
                            break;
                        case EnumSkillType.ArcherAtk:
                            break;
                        case EnumSkillType.PetASkill:
                        case EnumSkillType.PetPSkill:
                            damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.PetSkillFB].value;
                            break;
                        case EnumSkillType.MagicASkill:
                            damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.ASkillFB].value;
                            damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.GlobalSkillDFB].value;
                            break;
                        case EnumSkillType.MagicPSkill:
                            damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.PSkillFB].value;
                            damageData.damage += releaseMemberBaseData.attr[EnumAttributeType.GlobalSkillDFB].value;
                            break;
                        default:
                            break;
                    }
                }
                break;
            case EnumSkillEffectEffectType.Buff103:
                {
                    const [p, max] = effectPara;
                    damageData.type = EnumSkillEffectDamageType.Physics;
                    pValue = p;
                    maxValue = max;

                    damageData.damage = targetMemberBaseData.attr[EnumAttributeType.FinalHp].finalValue * pValue;
                    damageData.damage = Math.min(
                        damageData.damage,
                        releaseMemberBaseData.attr[EnumAttributeType.FinalAtk].finalValue * maxValue
                    );
                }
                break;
            default:
                break;
        }
    }

    /**
     * 打印日志-伤害
     * @param damageData 伤害数据
     * @param effectInfo 效果信息
     * @param effectPara 效果参数
     * @param skillData 技能数据
     * @param releaseMember 释放成员
     * @param targetMember 目标成员
     */
    private logDamage(
        damageData: ICombatDamageData,
        effectInfo: DataSkillEffect,
        effectPara: number[],
        skillData: ICombatSkillData,
        releaseMember: CombatMember,
        targetMember: CombatMember
    ): void {
        const releaseMemberBaseData = releaseMember.getBaseData();
        const targetMemberBaseData = targetMember.getBaseData();

        let logId: CombatLogId = null;
        switch (skillData.info.type) {
            case EnumSkillType.LeadASkill:
                logId = CombatLogId.LeadSkillDamage;
                break;
            case EnumSkillType.LeadAtk:
                logId = CombatLogId.LeadAttackDamage;
                break;
            case EnumSkillType.WeaponASkill:
            case EnumSkillType.WeaponPSkill:
                logId = CombatLogId.WeaponSkillDamage;
                break;
            case EnumSkillType.WingASkill:
            case EnumSkillType.WingPSkill:
                logId = CombatLogId.WingSkillDamage;
                break;
            case EnumSkillType.TankASkill:
            case EnumSkillType.TankPSkill:
                logId = CombatLogId.TankSkillDamage;
                break;
            case EnumSkillType.ArcherASkill:
            case EnumSkillType.ArcherPSkill:
                logId = CombatLogId.ArcherSkillDamage;
                break;
            case EnumSkillType.ArcherAtk:
                logId = CombatLogId.ArcherAttackDamage;
                break;
            case EnumSkillType.PetASkill:
            case EnumSkillType.PetPSkill:
                logId = CombatLogId.PetSkillDamage;
                break;
            case EnumSkillType.MagicASkill:
                logId = CombatLogId.MagicASkillDamage;
                break;
            case EnumSkillType.MagicPSkill:
                logId = CombatLogId.MagicPSkillDamage;
                break;
            case EnumSkillType.MonsterAtk:
                logId = CombatLogId.MonsterAttackDamage;
                break;
            default:
                break;
        }
        if (!CombatLog.getInstance().getShowState(logId)) {
            return;
        }
        if (!CombatLog.getInstance().getShowStateByDungeonType(releaseMemberBaseData.dungeonType)) {
            return;
        }

        CombatLog.getInstance().logTitle(logId, releaseMember);

        const logData = {
            damageType: "",
            damageSourceType: "",
            damage: damageData.damage,
            damageBase: damageData.damageBase,
            effectId: effectInfo.id,
            effectPara,
            skillId: skillData.id,
            memberId: skillData.memberId,
            attr: Utils.clone(releaseMemberBaseData.attr),
            attr2: Utils.clone(targetMemberBaseData.attr),
            attrFormat: "",
            attrFormat2: "",
        };
        switch (damageData.type) {
            case EnumSkillEffectDamageType.Real:
                logData.damageType = "真实";
                break;
            case EnumSkillEffectDamageType.Physics:
                logData.damageType = "物理";
                break;
            case EnumSkillEffectDamageType.Thunder:
                logData.damageType = "雷系";
                break;
            case EnumSkillEffectDamageType.Fire:
                logData.damageType = "火系";
                break;
            case EnumSkillEffectDamageType.Wind:
                logData.damageType = "风系";
                break;
            default:
                break;
        }
        switch (damageData.sourceType) {
            case EnumSkillEffectDamageSourceType.Eva:
                logData.damageSourceType = "闪避";
                break;
            case EnumSkillEffectDamageSourceType.Block:
                logData.damageSourceType = "格挡";
                break;
            case EnumSkillEffectDamageSourceType.Hit:
                logData.damageSourceType = "命中";
                break;
            case EnumSkillEffectDamageSourceType.CritHit:
                logData.damageSourceType = "暴击";
                break;
            case EnumSkillEffectDamageSourceType.Crit:
                logData.damageSourceType = "会心";
                break;
            case EnumSkillEffectDamageSourceType.CritHitAndCrit:
                logData.damageSourceType = "会暴";
                break;
            default:
                break;
        }
        for (const e in logData.attr) {
            logData.attrFormat += `${e},`;
            logData.attrFormat += `${logData.attr[e].value}\n`;
        }
        for (const e in logData.attr2) {
            logData.attrFormat2 += `${e},`;
            logData.attrFormat2 += `${logData.attr2[e].value}\n`;
        }
        cc.log(logData);
    }
}
