/*
 * @Author: Jrrend
 * @Date: 2024-04-02 18:07:20
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-17 09:57:44
 */

import LocalStorage from "../../nsn/core/LocalStorage";
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI, { UIEvent } from "../../nsn/ui/UI";
import Time from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import { IPopUpsObj, PopUpsInit, PopUpsInitRet, PopUpsPushNoticeRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { LocalStorageKey } from "../config/LocalStorageConfig";
import Guide from "../core/Guide";

export default class PopUps extends Logic {
    private popUpsInfos: IPopUpsObj[] = null;
    private isShowEntry: boolean = false;
    private time: number = 0;

    /**
     * 清除数据
     */
    public clear(): void {
        this.popUpsInfos = null;
        this.isShowEntry = false;
        this.time = 0;
    }

    /**
     * 注册消息
     */
    protected registerHandler(): void {
        Socket.getInstance().on(PopUpsInitRet.prototype.clazzName, this.popUpsInitRet, this);
        Socket.getInstance().on(PopUpsPushNoticeRet.prototype.clazzName, this.popUpsPushNoticeRet, this);

        UI.getInstance().on(
            UIEvent.Unloaded,
            () => {
                if (!this.popUpsInfos || this.popUpsInfos.length <= 0) {
                    return;
                }
                const isEmpty = UI.getInstance().isEmpty();
                if (!isEmpty) {
                    return;
                }
                const isGuideShowing = Guide.getInstance().isGuideShowing();
                if (isGuideShowing) {
                    return;
                }
                const isShow = this.isPopUpsShown(this.popUpsInfos[0].uuid);
                if (isShow) {
                    return;
                }
                UI.getInstance().open("PopupVipServicePush", this.popUpsInfos[0].content);
                this.setPopUpsShown(this.popUpsInfos[0].uuid);
            },
            this
        );
    }

    /**
     * 弹窗初始化信息
     */
    private popUpsInitRet(data: PopUpsInitRet): void {
        const { errMsg, popUpsInfos } = data;
        if (errMsg !== PopUpsInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.popUpsInitRet[errMsg]);
            return;
        }
        this.popUpsInfos = popUpsInfos;
    }

    /**
     * 弹窗通知
     */
    private popUpsPushNoticeRet(data: PopUpsPushNoticeRet): void {
        const { errMsg, popUpsInfo } = data;
        if (errMsg !== PopUpsPushNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.popUpsPushNoticeRet[errMsg]);
            return;
        }

        this.popUpsInfos = [popUpsInfo];
        this.checkPopUps();
    }

    /**
     * 检测是否进行弹窗
     */
    public checkPopUps(): void {
        if (this.popUpsInfos.length > 0) {
            const isShow = this.isPopUpsShown(this.popUpsInfos[0].uuid);
            const guideIsShowing = Guide.getInstance().isGuideShowing();
            if (!isShow && !guideIsShowing) {
                UI.getInstance().open("PopupVipServicePush", this.popUpsInfos[0].content);
                this.setPopUpsShown(this.popUpsInfos[0].uuid);
            }
        }
    }

    /**
     * 获取客服弹窗信息
     */
    public getPopUpsInfos(): IPopUpsObj[] {
        return this.popUpsInfos;
    }

    /**
     * 判断某个 uuid 的弹窗是否已展示
     */
    public isPopUpsShown(uuid: string): boolean {
        const data = LocalStorage.getInstance().getItem(LocalStorageKey.VipServicePush);
        const shownMap = data ? JSON.parse(data) : {};
        return !!shownMap[uuid];
    }

    /**
     * 标记某个 uuid 的弹窗为已展示
     */
    public setPopUpsShown(uuid: string): void {
        const data = LocalStorage.getInstance().getItem(LocalStorageKey.VipServicePush);
        const shownMap = data ? JSON.parse(data) : {};
        shownMap[uuid] = true;
        LocalStorage.getInstance().setItem(LocalStorageKey.VipServicePush, JSON.stringify(shownMap));
    }

    /**
     * 获取入口是否显示
     */
    public isPopUpsEntryShow(): boolean {
        return this.isShowEntry;
    }

    /**
     * 设置入口是否显示
     */
    public setPopUpsEntryShow(isShow: boolean): void {
        this.isShowEntry = isShow;
    }

    /**
     * 设置入口倒计时
     */
    public setPopUpsEntryTime(): void {
        this.time = Time.getInstance().now();
    }

    /**
     * 获取入口倒计时
     */
    public getPopUpsEntryTime(): number {
        return this.time;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////
    /**
     * 发送弹窗初始化
     */
    public sendPopUpsInit(): void {
        const data = PopUpsInit.create();
        Socket.getInstance().send(data);
    }
    /// /////////////////////////////  get  ////////////////////////////////
}
