/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-11 17:13:12
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:24:36
 */

import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import TextUtils from "../../nsn/util/TextUtils";
import Time, { HOUR_TO_SECOND } from "../../nsn/util/Time";
import TimeFormat, { TimeDurationFormatType } from "../../nsn/util/TimeFormat";
import Tips, { TipsLevel, TipsType } from "../../nsn/util/Tips";
import {
    IRankItem,
    IUnionApplyLog,
    IUnionBossGetRet,
    IUnionBossLogObj,
    IUnionBossObj,
    IUnionBossRewardObj,
    IUnionCurrGetRet,
    IUnionDonateGetRet,
    IUnionGetApplyListRet,
    IUnionInfo,
    IUnionMember,
    IUnionTreasureShopGetRet,
    IUnionTreasureShopInfo,
    UnionActiveReward,
    UnionActiveRewardBroadcastRet,
    UnionActiveRewardGet,
    UnionActiveRewardGetRet,
    UnionActiveRewardRet,
    UnionApplySubmit,
    UnionApplySubmitBroadcastRet,
    UnionApplySubmitRet,
    UnionBossAttack,
    UnionBossAttackRet,
    UnionBossGet,
    UnionBossGetRet,
    UnionBossProgressBroadcastRet,
    UnionBossRank,
    UnionBossRankRet,
    UnionBossReward,
    UnionBossRewardRet,
    UnionChangeName,
    UnionChangeNameRet,
    UnionCreate,
    UnionCreateRet,
    UnionCurrGet,
    UnionCurrGetRet,
    UnionDismiss,
    UnionDismissBroadcastRet,
    UnionDismissRet,
    UnionDonateGet,
    UnionDonateGetRet,
    UnionDonateReward,
    UnionDonateRewardRet,
    UnionGet,
    UnionGetApplyList,
    UnionGetApplyListRet,
    UnionGetRet,
    UnionInitRet,
    UnionKickOut,
    UnionKickOutBroadcastRet,
    UnionKickOutRet,
    UnionLeave,
    UnionLeaveRet,
    UnionModuleGet,
    UnionModuleGetRet,
    UnionOwnerChange,
    UnionOwnerChangeRet,
    UnionPositionSetting,
    UnionPositionSettingRet,
    UnionReview,
    UnionReviewBroadcastRet,
    UnionReviewRet,
    UnionSearch,
    UnionSearchRet,
    UnionSetting,
    UnionSettingRet,
    UnionStatusUpdateBroadcastRet,
    UnionTreasureShopBargain,
    UnionTreasureShopBargainBroadcastRet,
    UnionTreasureShopBargainRet,
    UnionTreasureShopGet,
    UnionTreasureShopGetRet,
    UnionTreasureShopPurchase,
    UnionTreasureShopPurchaseRet,
} from "../../protobuf/proto";
import { AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointId } from "../core/redPoint/RedPointId";
import { EnumUnionPara } from "../data/base/BaseUnion";
import TBRandomShop from "../data/parser/TBRandomShop";
import TBUnion from "../data/parser/TBUnion";
import TBUnionLevel from "../data/parser/TBUnionLevel";
import TBUnionPosition, { UnionPermission } from "../data/parser/TBUnionPosition";
import AudioUtils from "../utils/AudioUtils";
import Combat, { CombatEvent, DungeonType } from "./Combat";
import Player from "./Player";
import UnionHelp from "./UnionHelp";
import UnionSiege from "./UnionSiege";

export enum UnionEvent {
    ChangeFlag = "change-flag", // 修改icon
}

export default class Union extends Logic {
    private unionInfo: IUnionInfo = null; // 公会信息
    private members: IUnionMember[] = []; // 成员
    private leaveTime: number = 0; // 上次离开时间
    private leaveTimes: number = 0; // 已离开次数

    private applyLogs: IUnionApplyLog[] = []; // 申请日志
    private applyNum: number = 0; // 今日入盟人数

    private unionBossObj: IUnionBossObj = null;
    private unionBossRewardObj: IUnionBossRewardObj = null;
    private roleAttackTimes: number = 0; // 已挑战次数
    private bossScore: number = 0; // 伤害
    private bossRank: number = 0; // 排行

    private bossRankItems: IRankItem[] = []; // boss排行榜信息
    private bossRankMyItem: IRankItem = null; // boss排行个人信息

    private activeProgress: number = 0; // 公会活跃进度
    private activeRewardIds: number[] = []; // 已领取的进度奖励ID

    private treasureShop: IUnionTreasureShopInfo = null; // 贸易商人商店信息

    private freeTimes: number = 0; // 免费次数
    private payTimes: number = 0; // 购买次数

    /**
     * 清理数据
     */
    public clear(): void {
        this.clearUnionInfo();
        this.leaveTime = 0;
        this.leaveTimes = 0;
    }

    /**
     * 公会解散，离开公会，被踢出公会后调用
     * 其他情况不得调用
     */
    private clearUnionInfo(): void {
        this.unionInfo = null;
        this.members = [];

        this.leaveTime = Time.getInstance().now();
        this.leaveTimes++;

        this.applyLogs = [];
        this.applyNum = 0;

        this.unionBossObj = null;
        this.unionBossRewardObj = null;
        this.roleAttackTimes = 0;
        this.bossScore = 0;
        this.bossRank = 0;

        this.bossRankItems = [];
        this.bossRankMyItem = null;

        this.activeProgress = 0;
        this.activeRewardIds = [];

        this.treasureShop = null;
    }

    /**
     * 注册消息
     */
    protected registerHandler(): void {
        // 基本功能
        Socket.getInstance().on(UnionInitRet.prototype.clazzName, this.unionInitRet, this);
        Socket.getInstance().on(UnionModuleGetRet.prototype.clazzName, this.unionModuleGetRet, this);
        Socket.getInstance().on(UnionCurrGetRet.prototype.clazzName, this.unionCurrGetRet, this);
        Socket.getInstance().on(
            UnionStatusUpdateBroadcastRet.prototype.clazzName,
            this.unionStatusUpdateBroadcastRet,
            this
        );
        Socket.getInstance().on(
            UnionApplySubmitBroadcastRet.prototype.clazzName,
            this.unionApplySubmitBroadcastRet,
            this
        );
        Socket.getInstance().on(UnionDismissBroadcastRet.prototype.clazzName, this.unionDismissBroadcastRet, this);
        Socket.getInstance().on(UnionKickOutBroadcastRet.prototype.clazzName, this.unionKickOutBroadcastRet, this);
        Socket.getInstance().on(UnionReviewBroadcastRet.prototype.clazzName, this.unionReviewBroadcastRet, this);
        Socket.getInstance().on(UnionGetRet.prototype.clazzName, this.unionGetRet, this);
        Socket.getInstance().on(UnionCreateRet.prototype.clazzName, this.unionCreateRet, this);
        Socket.getInstance().on(UnionSearchRet.prototype.clazzName, this.unionSearchRet, this);
        Socket.getInstance().on(UnionGetApplyListRet.prototype.clazzName, this.unionGetApplyListRet, this);
        Socket.getInstance().on(UnionChangeNameRet.prototype.clazzName, this.unionChangeNameRet, this);
        Socket.getInstance().on(UnionSettingRet.prototype.clazzName, this.unionSettingRet, this);
        Socket.getInstance().on(UnionApplySubmitRet.prototype.clazzName, this.unionApplySubmitRet, this);
        Socket.getInstance().on(UnionReviewRet.prototype.clazzName, this.unionReviewRet, this);
        Socket.getInstance().on(UnionKickOutRet.prototype.clazzName, this.unionKickOutRet, this);
        Socket.getInstance().on(UnionOwnerChangeRet.prototype.clazzName, this.unionOwnerChangeRet, this);
        Socket.getInstance().on(UnionPositionSettingRet.prototype.clazzName, this.unionPositionSettingRet, this);
        Socket.getInstance().on(UnionDismissRet.prototype.clazzName, this.unionDismissRet, this);
        Socket.getInstance().on(UnionLeaveRet.prototype.clazzName, this.unionLeaveRet, this);

        // boss
        Socket.getInstance().on(UnionBossGetRet.prototype.clazzName, this.unionBossGetRet, this);
        Socket.getInstance().on(UnionBossRewardRet.prototype.clazzName, this.unionBossRewardRet, this);
        Socket.getInstance().on(UnionBossAttackRet.prototype.clazzName, this.unionBossAttackRet, this);
        Socket.getInstance().on(UnionBossRankRet.prototype.clazzName, this.unionBossRankRet, this);
        Socket.getInstance().on(
            UnionBossProgressBroadcastRet.prototype.clazzName,
            this.unionBossProgressBroadcastRet,
            this
        );

        // 活跃
        Socket.getInstance().on(UnionActiveRewardGetRet.prototype.clazzName, this.unionActiveRewardGetRet, this);
        Socket.getInstance().on(UnionActiveRewardRet.prototype.clazzName, this.unionActiveRewardRet, this);
        Socket.getInstance().on(
            UnionActiveRewardBroadcastRet.prototype.clazzName,
            this.unionActiveRewardBroadcastRet,
            this
        );

        // 贸易商人
        Socket.getInstance().on(UnionTreasureShopGetRet.prototype.clazzName, this.unionTreasureShopGetRet, this);
        Socket.getInstance().on(
            UnionTreasureShopBargainRet.prototype.clazzName,
            this.unionTreasureShopBargainRet,
            this
        );
        Socket.getInstance().on(
            UnionTreasureShopBargainBroadcastRet.prototype.clazzName,
            this.unionTreasureShopBargainBroadcastRet,
            this
        );
        Socket.getInstance().on(
            UnionTreasureShopPurchaseRet.prototype.clazzName,
            this.unionTreasureShopPurchaseRet,
            this
        );

        // 捐赠
        Socket.getInstance().on(UnionDonateGetRet.prototype.clazzName, this.unionDonateGetRet, this);
        Socket.getInstance().on(UnionDonateRewardRet.prototype.clazzName, this.unionDonateRewardRet, this);
    }

    /**
     * 获取公会信息
     * @param data
     */
    private unionInitRet(data: UnionInitRet): void {
        const { errMsg, unionInfo, members, leaveTime, leaveTimes } = data;
        this.leaveTime = leaveTime;
        this.leaveTimes = leaveTimes;
        if (errMsg !== UnionInitRet.ErrorResult.None) {
            return;
        }
        this.unionInfo = unionInfo;
        this.members = members;
    }

    /**
     * 公会模块关联数据获取
     * @param data
     */
    private unionModuleGetRet(data: UnionModuleGetRet): void {
        UI.getInstance().hideSoftLoading();
        const {
            errMsg,
            unionCurrGetRet,
            unionBossGetRet,
            unionTreasureShopGetRet,
            unionGetApplyListRet,
            unionDonateGetRet,
        } = data;
        if (errMsg !== UnionModuleGetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionModuleGetRet[data.errMsg]);
            return;
        }
        this.unionCurrGetRet(unionCurrGetRet);
        this.unionBossGetRet(unionBossGetRet);
        this.unionTreasureShopGetRet(unionTreasureShopGetRet);
        this.unionGetApplyListRet(unionGetApplyListRet);
        this.unionDonateGetRet(unionDonateGetRet);

        UI.getInstance().open("UIUnion");
    }

    /**
     * 获取自身公会信息
     * @param data
     * @returns
     */
    private unionCurrGetRet(data: IUnionCurrGetRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, unionInfo, members } = data;
        if (errMsg !== UnionCurrGetRet.ErrorResult.None) {
            return;
        }
        this.unionInfo = unionInfo;
        this.members = members;
        this.emit(UnionCurrGet.prototype.clazzName);
    }

    /**
     * 获取某个公会信息
     * @param data
     */
    private unionGetRet(data: UnionGetRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, unionInfo, members, status } = data;
        if (errMsg !== UnionGetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionGetRet[errMsg]);
            return;
        }
        this.emit(UnionGetRet.prototype.clazzName, unionInfo, members, status);
    }

    /**
     * 广播更新公会信息
     * @param data
     */
    private unionStatusUpdateBroadcastRet(data: UnionStatusUpdateBroadcastRet): void {
        const { unionInfo, members } = data;
        this.unionInfo = unionInfo;
        this.members = members;
        this.emit(UnionStatusUpdateBroadcastRet.prototype.clazzName);
    }

    /**
     * 广播更新待审核记录
     * 只有拥有审核权限的人收得到
     * @param data
     */
    private unionApplySubmitBroadcastRet(data: UnionApplySubmitBroadcastRet): void {
        const { applyLogs, applyNum } = data;
        if (applyLogs.length > this.applyLogs.length) {
            Tips.getInstance().show(i18n.union0001);
        }
        this.applyLogs = applyLogs;
        this.applyNum = applyNum;
        this.emit(UnionApplySubmitBroadcastRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionApply);
    }

    /**
     * 广播解散公会
     * @param data
     */
    private unionDismissBroadcastRet(data: UnionDismissBroadcastRet): void {
        if (UI.getInstance().isExist("UIUnion")) {
            UI.getInstance().closeAll();
        }
        this.clearUnionInfo();
        Tips.getInstance().show(i18n.union0002);
        this.emit(UnionDismissBroadcastRet.prototype.clazzName);
        UnionHelp.getInstance().clearHelpData();
        UnionSiege.getInstance().clearUnionSiegeData();
        RedPoint.getInstance().checkRelative(RedPointId.UnionAll);
    }

    /**
     * 广播被逐出公会
     * 只有被逐出的玩家收到
     * @param data
     */
    private unionKickOutBroadcastRet(data: UnionKickOutBroadcastRet): void {
        if (UI.getInstance().isExist("UIUnion")) {
            UI.getInstance().closeAll();
        }
        this.clearUnionInfo();
        Tips.getInstance().show(i18n.union0003);
        this.emit(UnionKickOutBroadcastRet.prototype.clazzName);
        UnionHelp.getInstance().clearHelpData();
        UnionSiege.getInstance().clearUnionSiegeData();
        RedPoint.getInstance().checkRelative(RedPointId.UnionAll);
    }

    /**
     * 广播加入公会
     * 只有加入的玩家收到
     */
    private unionReviewBroadcastRet(data: UnionReviewBroadcastRet): void {
        const { unionInfo, members } = data;
        this.unionInfo = unionInfo;
        this.members = members;
        Tips.getInstance().show(i18n.union0004);
        if (UI.getInstance().isExist("UIUnionList")) {
            UI.getInstance().closeToWindow("UIHomeLand");
            UI.getInstance().open("UIUnion");
        }
        this.emit(UnionReviewBroadcastRet.prototype.clazzName);

        // 进盟后请求所有信息
        this.sendUnionActiveRewardGet();
        this.sendUnionBossGet();
        this.sendUnionGetApplyList();
        this.sendUnionTreasureShopGet();
        this.sendUnionDonateGet();
        UnionHelp.getInstance().sendGetData();
        UnionSiege.getInstance().sendUnionSiegeGet();
    }

    /**
     * 创建公会
     * @param data
     * @returns
     */
    private unionCreateRet(data: UnionCreateRet): void {
        const { errMsg, unionInfo, members } = data;
        if (errMsg !== UnionCreateRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionCreateRet[errMsg]);
            return;
        }
        this.unionInfo = unionInfo;
        this.members = members;
        this.emit(UnionCreateRet.prototype.clazzName);

        // 进盟后请求所有信息
        this.sendUnionActiveRewardGet();
        this.sendUnionBossGet();
        this.sendUnionGetApplyList();
        this.sendUnionTreasureShopGet();
        this.sendUnionDonateGet();
        UnionHelp.getInstance().sendGetData();
        UnionSiege.getInstance().sendUnionSiegeGet();
    }

    /**
     * 搜索公会
     * @param data
     */
    private unionSearchRet(data: UnionSearchRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, items } = data;
        if (errMsg !== UnionSearchRet.ErrorResult.None) {
            return;
        }
        this.emit(UnionSearchRet.prototype.clazzName, items);
    }

    /**
     * 获取申请列表
     * @param data
     * @returns
     */
    private unionGetApplyListRet(data: IUnionGetApplyListRet): void {
        const { errMsg, applyLogs, applyNum } = data;
        if (errMsg !== UnionGetApplyListRet.ErrorResult.None) {
            // 特殊处理，初始化时由于未知权限会请求该数据
            if (errMsg === UnionGetApplyListRet.ErrorResult.PermissionNotEnough) {
                return;
            }
            Tips.getInstance().show(i18n.unionGetApplyListRet[errMsg]);
            return;
        }
        this.applyLogs = applyLogs;
        this.applyNum = applyNum;
        this.emit(UnionGetApplyListRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionApply);
    }

    /**
     * 改名
     * @param data
     * @returns
     */
    private unionChangeNameRet(data: UnionChangeNameRet): void {
        const { errMsg } = data;
        if (errMsg !== UnionChangeNameRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionChangeNameRet[errMsg]);
            return;
        }
        this.emit(UnionChangeNameRet.prototype.clazzName);
    }

    /**
     * 修改公会信息
     * @param data
     */
    private unionSettingRet(data: UnionSettingRet): void {
        const { errMsg } = data;
        if (errMsg !== UnionSettingRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSettingRet[errMsg]);
            return;
        }
        Tips.getInstance().show(i18n.union0020);
        this.emit(UnionSettingRet.prototype.clazzName);
    }

    /**
     * 入盟审批
     * @param data
     */
    private unionReviewRet(data: UnionReviewRet): void {
        const { errMsg, applyLog, applyNum, type } = data;
        if (errMsg !== UnionReviewRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionReviewRet[errMsg]);
        } else {
            switch (type) {
                case UnionReview.ReviewType.Approved:
                    Tips.getInstance().show(i18n.union0057);
                    break;
                case UnionReview.ReviewType.Refuse:
                    Tips.getInstance().show(i18n.union0058);
                    break;
                default:
                    break;
            }
        }
        this.applyLogs = applyLog;
        this.applyNum = applyNum;
        this.emit(UnionReviewRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionApply);
    }

    /**
     * 逐出公会
     * @param data
     * @returns
     */
    private unionKickOutRet(data: UnionKickOutRet): void {
        const { errMsg } = data;
        if (errMsg !== UnionKickOutRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionKickOutRet[errMsg]);
            return;
        }
        this.emit(UnionKickOutRet.prototype.clazzName);
    }

    /**
     * 盟主转让
     * @param data
     */
    private unionOwnerChangeRet(data: UnionOwnerChangeRet): void {
        const { errMsg } = data;
        if (errMsg !== UnionOwnerChangeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionOwnerChangeRet[errMsg]);
            return;
        }
        this.emit(UnionOwnerChangeRet.prototype.clazzName);
    }

    /**
     * 任命
     * @param data
     * @returns
     */
    private unionPositionSettingRet(data: UnionPositionSettingRet): void {
        const { errMsg } = data;
        if (errMsg !== UnionPositionSettingRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionPositionSettingRet[errMsg]);
            return;
        }
        this.emit(UnionPositionSettingRet.prototype.clazzName);
    }

    /**
     * 解散公会
     * @param data
     */
    private unionDismissRet(data: UnionDismissRet): void {
        const { errMsg } = data;
        if (errMsg !== UnionDismissRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionDismissRet[errMsg]);
            return;
        }
        this.emit(UnionDismissRet.prototype.clazzName);
    }

    /**
     * 申请入盟
     * @param data
     * @returns
     */
    private unionApplySubmitRet(data: UnionApplySubmitRet): void {
        const { errMsg, unionId } = data;
        if (errMsg !== UnionApplySubmitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionApplySubmitRet[errMsg]);
            return;
        }
        Tips.getInstance().show(i18n.common0027);
        this.emit(UnionApplySubmitRet.prototype.clazzName, unionId);
    }

    /**
     * 离开公会
     * @param data
     * @returns
     */
    private unionLeaveRet(data: UnionLeaveRet): void {
        const { errMsg } = data;
        if (errMsg !== UnionLeaveRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionLeaveRet[errMsg]);
            return;
        }
        if (UI.getInstance().isExist("UIUnion")) {
            UI.getInstance().closeAll();
        }
        Tips.getInstance().show(i18n.union0076);
        this.clearUnionInfo();
        this.emit(UnionLeaveRet.prototype.clazzName);
        UnionHelp.getInstance().clearHelpData();
        UnionSiege.getInstance().clearUnionSiegeData();
    }

    /**
     * 获取boss信息
     * @param data
     */
    private unionBossGetRet(data: IUnionBossGetRet): void {
        const { errMsg, unionBossObj, roleAttackTimes, unionBossRewardObj } = data;
        if (errMsg !== UnionBossGetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionBossGetRet[errMsg]);
            return;
        }

        this.unionBossObj = unionBossObj;
        this.unionBossRewardObj = unionBossRewardObj;
        this.roleAttackTimes = roleAttackTimes;
        this.bossScore = unionBossRewardObj.score;
        this.bossRank = unionBossRewardObj.rankId;

        this.emit(UnionBossGetRet.prototype.clazzName);
    }

    /**
     * 公会boss领奖
     * @param data
     * @returns
     */
    private unionBossRewardRet(data: UnionBossRewardRet): void {
        const { errMsg, unionBossRewardObj } = data;
        if (errMsg !== UnionBossRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionBossRewardRet[errMsg]);
            return;
        }
        this.unionBossRewardObj = unionBossRewardObj;
        this.emit(UnionBossRewardRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionBossGetReward);
    }

    /**
     * 公会boss攻击
     * @param data
     * @returns
     */
    private unionBossAttackRet(data: UnionBossAttackRet): void {
        const { errMsg, unionBossObj, roleAttackTimes, unionBossRewardObj } = data;
        if (errMsg !== UnionBossAttackRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionBossAttackRet[errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.Union);
            return;
        }

        this.unionBossObj = unionBossObj;
        this.unionBossRewardObj = unionBossRewardObj;
        this.bossScore = unionBossRewardObj.score;
        this.bossRank = unionBossRewardObj.rankId;
        this.roleAttackTimes = roleAttackTimes;

        this.emit(UnionBossAttackRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.UnionBossFight);
    }

    /**
     * 公会boss排行榜
     * @param data
     * @returns
     */
    private unionBossRankRet(data: UnionBossRankRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, items, myItem } = data;
        if (errMsg !== UnionBossRankRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionBossRankRet[errMsg]);
            return;
        }
        this.bossRankItems = items;
        this.bossRankMyItem = myItem;
        this.bossRank = myItem.rankNo;
        this.emit(UnionBossRankRet.prototype.clazzName);
    }

    /**
     * 公会Boss讨伐广播
     * @param data
     * @returns
     */
    private unionBossProgressBroadcastRet(data: UnionBossProgressBroadcastRet): void {
        const { errMsg, unionBossObj } = data;
        if (errMsg !== UnionBossProgressBroadcastRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionBossProgressBroadcastRet[errMsg]);
            return;
        }

        this.unionBossObj = unionBossObj;
        this.emit(UnionBossProgressBroadcastRet.prototype.clazzName);
    }

    /**
     * 公会活跃信息
     * @param data
     */
    private unionActiveRewardGetRet(data: UnionActiveRewardGetRet): void {
        const { errMsg, progress, rewardIds } = data;
        if (errMsg !== UnionActiveRewardGetRet.ErrorResult.None) {
            if (errMsg === UnionActiveRewardGetRet.ErrorResult.NoneUnion) {
                return;
            }
            Tips.getInstance().show(i18n.unionActiveRewardGetRet[errMsg]);
            return;
        }
        this.activeProgress = progress;
        this.activeRewardIds = rewardIds;
        this.emit(UnionActiveRewardGetRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionActiveGetReward);
    }

    /**
     * 公会活跃奖励领取
     * @param data
     */
    private unionActiveRewardRet(data: UnionActiveRewardRet): void {
        const { errMsg, rewardIds } = data;
        if (errMsg !== UnionActiveRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionActiveRewardRet[errMsg]);
            return;
        }
        this.activeRewardIds = rewardIds;
        this.emit(UnionActiveRewardRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionActiveGetReward);
    }

    /**
     * 公会活跃信息广播
     * @param data
     */
    private unionActiveRewardBroadcastRet(data: UnionActiveRewardBroadcastRet): void {
        const { errMsg, progress } = data;
        if (errMsg !== UnionActiveRewardBroadcastRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionActiveRewardBroadcastRet[errMsg]);
            return;
        }
        this.activeProgress = progress;
        this.emit(UnionActiveRewardBroadcastRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionActiveGetReward);
    }

    /**
     * 贸易商人信息获取
     * @param data
     */
    private unionTreasureShopGetRet(data: IUnionTreasureShopGetRet): void {
        const { errMsg, shop } = data;
        if (errMsg !== UnionTreasureShopGetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionTreasureShopGetRet[errMsg]);
            return;
        }
        this.treasureShop = shop;
        this.emit(UnionTreasureShopGetRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionTreasureShop);
    }

    /**
     * 贸易商人砍价
     * @param data
     */
    private unionTreasureShopBargainRet(data: UnionTreasureShopBargainRet): void {
        const { errMsg, shop } = data;
        if (errMsg !== UnionTreasureShopBargainRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionTreasureShopBargainRet[errMsg]);
            return;
        }
        this.treasureShop = shop;
        this.emit(UnionTreasureShopBargainRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionTreasureShop);

        const { randomShopId, logs } = shop;
        const randomShopData = TBRandomShop.getInstance().getDataById(randomShopId);
        const { buyCost } = randomShopData;
        const log = logs.find((v) => v.playerId === Player.getInstance().getId());
        Tips.getInstance().show({ text: "-" + log.bargainNo, id: buyCost[0][0] }, TipsLevel.Normal, TipsType.IconLabel);
        AudioUtils.playUnionTreasureShopEffect(AUDIO_EFFECT_TYPE.UNION_BARGAIN_VOICE_2);
    }

    /**
     * 贸易商人砍价广播
     * @param data
     */
    private unionTreasureShopBargainBroadcastRet(data: UnionTreasureShopBargainBroadcastRet): void {
        const { errMsg, shop } = data;
        if (errMsg !== UnionTreasureShopBargainBroadcastRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionTreasureShopBargainBroadcastRet[errMsg]);
            return;
        }
        const status = this.treasureShop.status;
        this.treasureShop = shop;
        this.treasureShop.status = status;
        this.emit(UnionTreasureShopBargainBroadcastRet.prototype.clazzName);
    }

    /**
     * 贸易商人购买
     * @param data
     */
    private unionTreasureShopPurchaseRet(data: UnionTreasureShopPurchaseRet): void {
        const { errMsg, shop } = data;
        if (errMsg !== UnionTreasureShopPurchaseRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionTreasureShopPurchaseRet[errMsg]);
            return;
        }
        this.treasureShop = shop;
        this.emit(UnionTreasureShopPurchaseRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionTreasureShop);
    }

    /**
     * 获取捐赠信息
     * @param data
     */
    private unionDonateGetRet(data: IUnionDonateGetRet): void {
        const { errMsg, unionDonateObj } = data;
        if (errMsg !== UnionDonateGetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionDonateGetRet[errMsg]);
            return;
        }
        this.freeTimes = unionDonateObj.freeTimes;
        this.payTimes = unionDonateObj.payTimes;
        this.emit(UnionDonateGetRet.prototype.clazzName);
    }

    /**
     * 领取捐赠奖励
     * @param data
     */
    private unionDonateRewardRet(data: UnionDonateRewardRet): void {
        const { errMsg, unionDonateObj } = data;
        if (errMsg !== UnionDonateRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionDonateRewardRet[errMsg]);
            return;
        }
        this.freeTimes = unionDonateObj.freeTimes;
        this.payTimes = unionDonateObj.payTimes;
        this.emit(UnionDonateRewardRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.UnionDonate);
    }

    /**
     * 获取公会信息
     */
    public getInfo(): IUnionInfo {
        return this.unionInfo;
    }

    /**
     * 获取公会成员
     */
    public getMembers(): IUnionMember[] {
        return this.members;
    }

    /**
     * 获取自己公会个人信息
     */
    public getMyInfo(): IUnionMember {
        return this.members.find((v) => v.player.playerId === Player.getInstance().getId());
    }

    /**
     * 是否拥有某个权限
     * @param permission
     */
    public hasPermission(permission: UnionPermission): boolean {
        const info = this.getMyInfo();
        if (!info) {
            return false;
        }
        return TBUnionPosition.getInstance().hasPermission(info.unionPositionId, permission);
    }

    /**
     * 获取申请日志
     * @returns
     */
    public getApplyLogs(): IUnionApplyLog[] {
        return this.applyLogs;
    }

    /**
     * 获取今日入盟人数
     * @returns
     */
    public getApplyNum(): number {
        return this.applyNum;
    }

    /**
     * 返回boss伤害排行榜
     * @returns
     */
    public getBossRankItems(): IRankItem[] {
        return this.bossRankItems;
    }

    /**
     * 返回boss伤害排行榜自己的信息
     * @returns
     */
    public getBossRankMyItem(): IRankItem {
        return this.bossRankMyItem;
    }

    /**
     * 获取已攻击次数
     * @returns
     */
    public getRoleAttackTimes(): number {
        return this.roleAttackTimes;
    }

    /**
     * 获取boss奖励
     * 未领取
     * @returns
     */
    public getUnReceiveBossReward(): { smallBox: number; bigBox: number } {
        return {
            smallBox: Math.max(this.unionBossObj.smallBox - this.unionBossRewardObj.smallBox, 0),
            bigBox: Math.max(this.unionBossObj.bigBox - this.unionBossRewardObj.bigBox, 0),
        };
    }

    /**
     * 获取公会副本奖励
     * @param count 数量
     * @returns
     */
    public getDungeonUnionReward(count: number): { smallBoxCount: number; bigBoxCount: number } {
        let smallBoxCount = 0;
        let bigBoxCount = 0;
        for (const key in this.unionBossRewardObj.preBoxMap) {
            if (parseInt(key) > count) {
                break;
            }

            this.unionBossRewardObj.preBoxMap[key] === 1 && bigBoxCount++;
            this.unionBossRewardObj.preBoxMap[key] === 2 && smallBoxCount++;
        }

        return { smallBoxCount, bigBoxCount };
    }

    /**
     * 获取boss奖励
     * 已领取
     * @returns
     */
    public getReceiveBossReward(): { smallBox: number; bigBox: number } {
        return {
            smallBox: this.unionBossRewardObj.smallBox,
            bigBox: this.unionBossRewardObj.bigBox,
        };
    }

    /**
     * 获取boss奖励
     * 可领取
     * @returns
     */
    public getCanReceiveBossReward(): { smallBox: number; bigBox: number } {
        const unionLevel = TBUnionLevel.getInstance().getLevelByExp(this.unionInfo.exp);
        const unionLevelData = TBUnionLevel.getInstance().getDataById(unionLevel);
        return {
            smallBox: Math.max(
                Math.min(
                    unionLevelData.chestNumber - this.unionBossRewardObj.smallBox,
                    this.unionBossObj.smallBox - this.unionBossRewardObj.smallBox
                ),
                0
            ),
            bigBox: Math.max(
                Math.min(
                    unionLevelData.bigChestNumber - this.unionBossRewardObj.bigBox,
                    this.unionBossObj.bigBox - this.unionBossRewardObj.bigBox
                ),
                0
            ),
        };
    }

    /**
     * 获取boss伤害
     * @returns
     */
    public getBossScore(): number {
        return this.bossScore;
    }

    /**
     * 获取boss伤害排行
     * @returns
     */
    public getBossRank(): number {
        return this.bossRank;
    }

    /**
     * 获取公会boss日志
     * @returns
     */
    public getBossLog(): IUnionBossLogObj[] {
        return this.unionBossObj.unionBossLogInfos;
    }

    /**
     * 获取活跃进度
     * @returns
     */
    public getActiveProgress(): number {
        return this.activeProgress;
    }

    /**
     * 获取已领取的活跃奖励
     * @returns
     */
    public getActiveRewardIds(): number[] {
        return this.activeRewardIds;
    }

    /**
     * 获取贸易商人信息
     * @returns
     */
    public getTreasureShop(): IUnionTreasureShopInfo {
        return this.treasureShop;
    }

    /**
     * 获取捐赠免费次数
     * @returns
     */
    public getDonateFreeTimes(): number {
        return this.freeTimes;
    }

    /**
     * 获取捐赠付费次数
     * @returns
     */
    public getDonatePayTimes(): number {
        return this.payTimes;
    }

    /**
     * 获取公会会长 1表示会长
     */
    public getMembersUnionLead(): IUnionMember {
        return this.members.find((v) => v.unionPositionId === 1);
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 查询当前公会各模块信息
     */
    public sendUnionModuleGet(): void {
        UI.getInstance().showSoftLoading();
        const data = UnionModuleGet.create();
        Socket.getInstance().send(data);
    }

    /**
     * 查询当前公会信息
     */
    public sendUnionCurrGet(): void {
        UI.getInstance().showSoftLoading();
        const data = UnionCurrGet.create();
        Socket.getInstance().send(data);
    }

    /**
     * 查询指定公会
     * @param unionId
     */
    public sendUnionGet(id: number): void {
        const data = UnionGet.create({ id });
        Socket.getInstance().send(data);
    }

    /**
     * 发送创建公会
     * @param name
     * @param publicize
     * @param notice
     * @param flag
     * @param allowAutoJoinIn
     */
    public sendUnionCreate(
        name: string,
        publicize: string,
        flag: number,
        allowAutoJoinIn: boolean,
        lowestLevel: number
    ): void {
        const data = UnionCreate.create({ name, publicize, flag, allowAutoJoinIn, lowestLevel });
        Socket.getInstance().send(data);
    }

    /**
     * 搜索公会列表
     * @param id
     */
    public sendUnionSearch(): void {
        const data = UnionSearch.create();
        Socket.getInstance().send(data);
    }

    /**
     * 入盟申请
     * @param type
     * @param unionId
     */
    public sendUnionApplySubmit(type: UnionApplySubmit.SubmitType, unionId?: number): void {
        if (this.leaveTime) {
            const cdArr = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionCD);
            let cd = cdArr[this.leaveTimes - 1];
            if (isNaN(cd)) {
                cd = cdArr[cdArr.length - 1];
            }
            const gap = this.leaveTime + cd * HOUR_TO_SECOND * 1000 - Time.getInstance().now();
            if (gap > 0) {
                Tips.getInstance().show(
                    TextUtils.format(
                        i18n.union0089,
                        TimeFormat.getInstance().getTextByDuration(gap, TimeDurationFormatType.HH_MM_SS)
                    )
                );
                return;
            }
        }
        const data = UnionApplySubmit.create({ type, unionId });
        Socket.getInstance().send(data);
    }

    /**
     * 获取当前公会请求列表
     */
    public sendUnionGetApplyList(): void {
        const data = UnionGetApplyList.create();
        Socket.getInstance().send(data);
    }

    /**
     * 入盟审批
     * @param id
     * @param type
     */
    public sendUnionReview(ids: string[], type: UnionReview.ReviewType): void {
        const data = UnionReview.create({ ids, type });
        Socket.getInstance().send(data);
    }

    /**
     * 改名
     * @param name
     */
    public sendUnionChangeName(name: string): void {
        const data = UnionChangeName.create({ name });
        Socket.getInstance().send(data);
    }

    /**
     * 逐出公会
     * @param id
     */
    public sendUnionKickOut(id: string): void {
        const data = UnionKickOut.create({ id });
        Socket.getInstance().send(data);
    }

    /**
     * 转让盟主
     * @param id
     */
    public sendUnionOwnerChange(id: string): void {
        const data = UnionOwnerChange.create({ id });
        Socket.getInstance().send(data);
    }

    /**
     * 发起任命
     * @param id
     * @param position
     */
    public sendUnionPositionSetting(id: string, position: number): void {
        const data = UnionPositionSetting.create({ id, position });
        Socket.getInstance().send(data);
    }

    /**
     * 解散公会
     */
    public sendUnionDismiss(): void {
        const data = UnionDismiss.create();
        Socket.getInstance().send(data);
    }

    /**
     * 修改公会信息
     * @param publicize
     * @param notice
     * @param flag
     * @param allowAutoJoinIn
     * @param lowestLevel
     */
    public sendUnionSetting(publicize: string, flag: number, allowAutoJoinIn: boolean, lowestLevel: number): void {
        const data = UnionSetting.create({ publicize, flag, allowAutoJoinIn, lowestLevel });
        Socket.getInstance().send(data);
    }

    /**
     * 离开公会
     */
    public sendUnionLeave(): void {
        const data = UnionLeave.create();
        Socket.getInstance().send(data);
    }

    /**
     * 获取boss信息
     */
    public sendUnionBossGet(): void {
        const data = UnionBossGet.create();
        Socket.getInstance().send(data);
    }

    /**
     * 领取全盟伤害奖励
     * @param type
     */
    public sendUnionBossReward(type: UnionBossReward.ReceiveType): void {
        const data = UnionBossReward.create({ type });
        Socket.getInstance().send(data);
    }

    /**
     * 获取boss排行榜信息
     */
    public sendUnionBossRank(): void {
        const data = UnionBossRank.create();
        Socket.getInstance().send(data);
    }

    /**
     * 挑战boss
     * @param damage
     */
    public sendUnionBossAttack(damage: number): void {
        const data = UnionBossAttack.create({ score: damage });
        Socket.getInstance().send(data);
    }

    /**
     * 获取公会活跃信息
     */
    public sendUnionActiveRewardGet(): void {
        const data = UnionActiveRewardGet.create();
        Socket.getInstance().send(data);
    }

    /**
     * 领取活跃奖励
     * @param rewardIds
     */
    public sendUnionActiveReward(rewardIds: number[]): void {
        const data = UnionActiveReward.create({ rewardIds });
        Socket.getInstance().send(data);
    }

    /**
     * 获取贸易商人信息
     */
    public sendUnionTreasureShopGet(): void {
        const data = UnionTreasureShopGet.create();
        Socket.getInstance().send(data);
    }

    /**
     * 贸易商人砍价
     */
    public sendUnionTreasureShopBargain(): void {
        const data = UnionTreasureShopBargain.create();
        Socket.getInstance().send(data);
    }

    /**
     * 贸易商人购买
     */
    public sendUnionTreasureShopPurchase(): void {
        const data = UnionTreasureShopPurchase.create();
        Socket.getInstance().send(data);
    }

    /**
     * 获取捐赠信息
     */
    public sendUnionDonateGet(): void {
        const data = UnionDonateGet.create();
        Socket.getInstance().send(data);
    }

    /**
     * 领取捐赠奖励
     */
    public sendUnionDonateReward(): void {
        const data = UnionDonateReward.create();
        Socket.getInstance().send(data);
    }
}
