/*
 * @Author: zahngwj
 * @Date: 2023-07-26 14:42:47
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-16 15:25:18
 */

import Audio from "../../nsn/audio/Audio";
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Time from "../../nsn/util/Time";
import Tips, { TipsLevel, TipsType } from "../../nsn/util/Tips";
import {
    BagAddProps,
    BagBatchExchange,
    BagBatchExchangeRet,
    BagInit,
    BagInitRet,
    BagOpenRandomBox,
    BagOpenRandomBoxRet,
    BagOpenSelectBox,
    BagOpenSelectBoxRet,
    BagUpdateRet,
    BagUseProp,
    BagUsePropRet,
    IBagDiyReward,
    IItemInfo,
    ItemInfo,
    NetworkResult,
} from "../../protobuf/proto";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import { REWARD_CONFIG, REWARD_TIPS_CONFIG } from "../config/RewardConfig";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointGroup } from "../core/redPoint/RedPointEnum";
import { EnumItemCountJudgment, EnumItemShowType, EnumItemTimeLimitType } from "../data/base/BaseItem";
import { EnumTaskDetailType } from "../data/base/BaseTaskDetail";
import TBItem, { ITEM_ID } from "../data/parser/TBItem";
import NumberUtils from "../utils/NumberUtils";
import Player from "./Player";
import Task from "./Task";

const { ccclass } = cc._decorator;

/**
 * 背包事件枚举
 */
export enum BagEvent {
    DiyNumChange = "diy-num-change", // 自选数量变更
    HomePlayGetItemEffect = "home-play-get-item-effect", // 播放主界面获得道具特效
}

@ccclass
export default class Bag extends Logic {
    private items: IItemInfo[] = [];

    protected registerHandler(): void {
        Socket.getInstance().on(BagInitRet.prototype.clazzName, this.bagInitRet, this);
        Socket.getInstance().on(BagUpdateRet.prototype.clazzName, this.bagUpdateRet, this);
        Socket.getInstance().on(BagOpenRandomBoxRet.prototype.clazzName, this.bagOpenRandomBoxRet, this);
        Socket.getInstance().on(BagOpenSelectBoxRet.prototype.clazzName, this.bagOpenSelectBoxRet, this);
        Socket.getInstance().on(BagBatchExchangeRet.prototype.clazzName, this.bagBatchExchangeRet, this);
        Socket.getInstance().on(BagUsePropRet.prototype.clazzName, this.bagUsePropRet, this);
    }

    /**
     * 清理数据
     */
    public clear(): void {
        this.items = [];
    }

    /**
     * 获得背包道具
     */
    public getItems(): IItemInfo[] {
        return this.items;
    }

    /**
     * 初始化背包数据
     * @param data
     * @returns
     */
    private bagInitRet(data: BagInitRet): void {
        if (data.errMsg !== BagInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.bagInitRet[data.errMsg]);
            return;
        }
        this.items = data.items;
        this.checkLimitTimeItemState();
    }

    /**
     * 更新背包数据
     * @param data
     * @returns
     */
    public bagUpdateRet(data: BagUpdateRet): void {
        const { result, item, costItem, gotItem, srcReq, showItem } = data;
        if (result === NetworkResult.Failure) {
            return;
        }
        item.forEach((value) => {
            const index = this.items.findIndex((item) => item.itemInfoId === value.itemInfoId);
            if (index === -1) {
                this.items.push(value);
            } else {
                this.items[index] = value;
            }
        });
        costItem.forEach((e) => {
            const itemConfig = TBItem.getInstance().getDataById(e.itemInfoId);
            if (itemConfig.countJudgment === EnumItemCountJudgment.NotCount) {
                return;
            }
            Task.getInstance().updateStatisticCount(EnumTaskDetailType.UseItemTask, e.itemInfoId + "", e.num);
        });
        Bag.getInstance().emit(BagUpdateRet.prototype.clazzName, data);
        RedPoint.getInstance().checkByGroup(RedPointGroup.ItemChanged);
        if (REWARD_CONFIG.includes(srcReq)) {
            if (showItem.length) {
                const list = showItem.filter((item) => {
                    const itemConfig = TBItem.getInstance().getDataById(item.itemInfo.itemInfoId);
                    return itemConfig.showType === EnumItemShowType.GetShow;
                });
                list.length > 0 && UI.getInstance().open("FloatReward", { showItem: list, srcReq });
                return;
            }
            if (gotItem.length) {
                const list = gotItem.filter((item) => {
                    const itemConfig = TBItem.getInstance().getDataById(item.itemInfoId);
                    return itemConfig.showType === EnumItemShowType.GetShow;
                });
                list.length > 0 && UI.getInstance().open("FloatReward", { gotItem: list, srcReq });
                return;
            }
        } else if (REWARD_TIPS_CONFIG.includes(srcReq)) {
            if (gotItem.length) {
                Tips.getInstance().show(
                    { text: [i18n.common0075, "x" + gotItem[0].num], id: gotItem[0].itemInfoId },
                    TipsLevel.Normal,
                    TipsType.LabelIconLabel
                );
            }
        }
    }

    /**
     * 开启随机奖励宝箱
     */
    private bagOpenRandomBoxRet(data: BagOpenRandomBoxRet): void {
        if (data.errMsg !== BagOpenRandomBoxRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.bagOpenRandomBoxRet[data.errMsg]);
            return;
        }
        this.emit(BagOpenRandomBoxRet.prototype.clazzName);
    }

    /**
     * 开启随机奖励宝箱
     */
    private bagOpenSelectBoxRet(data: BagOpenSelectBoxRet): void {
        if (data.errMsg !== BagOpenSelectBoxRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.bagOpenSelectBoxRet[data.errMsg]);
            return;
        }
        UI.getInstance().open("FloatReward", { gotItem: data.gotItem });
        this.emit(BagOpenSelectBoxRet.prototype.clazzName);
    }

    /**
     * 批量兑换道具
     * @param data
     */
    private bagBatchExchangeRet(data: BagBatchExchangeRet): void {
        const { errMsg } = data;
        if (errMsg !== BagBatchExchangeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.bagBatchExchangeRet[errMsg]);
            return;
        }
        Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.EXCHANGE, AUDIO_EFFECT_PATH.SYSTEM);
    }

    /**
     * 批量使用道具
     * @param data
     */
    private bagUsePropRet(data: BagUsePropRet): void {
        const { errMsg } = data;
        if (errMsg !== BagUsePropRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.bagUsePropRet[errMsg]);
            return;
        }
    }

    /**
     * 本地手动更新道具
     * @param item
     */
    public modifyItemCount(item: IItemInfo[], srcReq: string = ""): void {
        item.forEach((value) => {
            const index = this.items.findIndex((item) => item.itemInfoId === value.itemInfoId);
            if (index === -1) {
                const item = ItemInfo.create({ itemInfoId: value.itemInfoId, num: value.num });
                this.items.push(item);
            } else {
                this.items[index].num += value.num;
            }
        });
        this.emit(BagUpdateRet.prototype.clazzName, { gotItem: item, srcReq });
        RedPoint.getInstance().checkByGroup(RedPointGroup.ItemChanged);
    }

    /**
     * 根据id获得道具信息
     */
    public getItemById(id: number): IItemInfo {
        for (const key in this.items) {
            if (this.items[key].itemInfoId === id) {
                return this.items[key];
            }
        }
        return null;
    }

    /**
     * 根据道具ID获取道具数量
     * @param id 道具ID
     * @returns {number}
     */
    public getItemCountById(id: number): number {
        const data = this.items.find((item) => item.itemInfoId === id);
        return data ? data.num : 0;
    }

    /**
     * 根据道具ID获取格式化后道具数量
     * @param id 道具ID
     * @param count 如不传入数量，则使用背包内数量
     * @returns {string}
     */
    public getCountFormatById(id: number, count?: number): string {
        if (count === undefined) {
            const data = this.items.find((item) => item.itemInfoId === id);
            if (!data) {
                count = 0;
            } else {
                count = data.num;
            }
        }
        return NumberUtils.format(count, 1, 0);
    }

    /**
     * 根据道具ID获取道具是否足够
     * @param id 道具ID
     * @param consume 消耗数量
     * @returns {boolean} 是否足够
     */
    public isEnough(id: number, consume: number): boolean {
        const data = this.items.find((item) => item.itemInfoId === id);
        const count = data ? data.num : 0;
        return count >= consume;
    }

    /**
     * 返回道具剩余时间，如果为0则是永久
     * @param itemId 道具id
     * @param itemData 道具数据
     * @returns
     */
    public getRemainTime(itemId: number, itemData?: IItemInfo): number {
        let duration = 0;
        const itemTB = TBItem.getInstance().getDataById(itemId);
        if (itemTB.timeLimitType !== EnumItemTimeLimitType.NoneLimit) {
            const item = itemData || this.getItemById(itemId);
            duration = item.expireTime - Time.getInstance().now();
            duration = !duration ? -1 : duration;
        }

        return duration;
    }

    /**
     * 获取限时道具状态
     * @param itemId 道具id
     * @param itemData 道具数据
     * @returns
     */
    public getLimitTimeItemState(
        itemId: number,
        itemData?: IItemInfo
    ): { isOwn: boolean; isLimitTime: boolean; remainingTime: number } {
        if (!itemData) {
            itemData = Bag.getInstance().getItemById(itemId);
        }
        const state = { isOwn: itemData && itemData.num > 0, isLimitTime: false, remainingTime: 0 };
        const itemInfo = TBItem.getInstance().getDataById(itemId);
        state.isLimitTime = itemInfo.timeLimitType !== EnumItemTimeLimitType.NoneLimit;
        if (state.isOwn && state.isLimitTime) {
            state.remainingTime = this.getRemainTime(itemId, itemData);
            state.isOwn = state.remainingTime > 0;
        }

        return state;
    }

    /**
     * 检测限时道具状态
     */
    public checkLimitTimeItemState(): void {
        const avatarFrameId = Player.getInstance().getAvatarFrame();
        const avatarFrameState = this.getLimitTimeItemState(avatarFrameId);
        if (avatarFrameState.isLimitTime && !avatarFrameState.isOwn) {
            Player.getInstance().sendPlayerInfoReplace(ITEM_ID.DEFAULT_AVATAR_FRAME);
        }

        const titleId = Player.getInstance().getTitle();
        const titleState = this.getLimitTimeItemState(titleId);
        if (titleState.isLimitTime && !titleState.isOwn) {
            Player.getInstance().sendPlayerInfoReplace(ITEM_ID.DEFAULT_TITLE);
        }
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 发送资源获取临时接口协议
     */
    public sendBagAddProps(items: IItemInfo[]): void {
        const data = BagAddProps.create({ items });
        Socket.getInstance().send(data);
    }

    /**
     * 发送初始化背包协议
     */
    public sendBagInit(): void {
        const data = BagInit.create();
        Socket.getInstance().send(data);
    }

    /**
     * 发送背包模块开启随机奖励宝箱协议
     */
    public sendBagOpenRandomBox(boxId: number, openCount: number): void {
        const data = BagOpenRandomBox.create({ boxId, openCount });
        Socket.getInstance().send(data);
    }

    /**
     * 发送背包模块开启自选奖励宝箱协议
     */
    public sendBagOpenSelectBox(boxId: number, diyRewards: IBagDiyReward[]): void {
        const data = BagOpenSelectBox.create({ boxId, diyRewards });
        Socket.getInstance().send(data);
    }

    /**
     * 批量转化道具
     */
    public sendBagBatchExchange(itemInfos: IItemInfo[]): void {
        const data = BagBatchExchange.create({ itemInfos });
        Socket.getInstance().send(data);
    }

    /**
     * 批量使用道具
     */
    public sendBagUseProp(propId: number, useCounts: number): void {
        const data = BagUseProp.create({ propId, useCounts });
        Socket.getInstance().send(data);
    }
}
