/*
 * @Author: chenx
 * @Date: 2024-04-02 13:56:15
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:26:26
 */
import Logic from "../../nsn/core/Logic";
import Time from "../../nsn/util/Time";
import Utils from "../../nsn/util/Utils";
import {
    ArcherIncrStarRet,
    ArcherResetLevelRet,
    ArcherUpgradeRet,
    IChallengerInfo,
    IPetObj,
    LeadSkinIncrStarRet,
    LeadSkinResetRet,
    LeadSkinUpgradeRet,
    MagicalIncrStarRet,
    MagicalResetLevelRet,
    MagicalUpgradeRet,
    PetIncrStarRet,
    PetIncrStarUsePropRet,
    PetResetLevelRet,
    PetUpgradeRet,
    TankUpgradeLevelRet,
    TankUpgradeStarRet,
    WeaponUpgradeLevelRet,
    WeaponUpgradeStarRet,
    WingsBatchUpgradeLevelRet,
    WingsUpgradeStarRet,
} from "../../protobuf/proto";
import { EnumAttributeType } from "../data/base/BaseAttribute";
import { EnumMagicSkillType } from "../data/base/BaseMagicSkill";
import { EnumSkillEffectCondition, EnumSkillEffectEffectType } from "../data/base/BaseSkillEffect";
import { EnumSkillShowFolder, EnumSkillShowPreform, EnumSkillShowTargetType } from "../data/base/BaseSkillShow";
import DataSkill from "../data/extend/DataSkill";
import DataSkillEffect from "../data/extend/DataSkillEffect";
import DataSkillShow from "../data/extend/DataSkillShow";
import TBMagicSkill from "../data/parser/TBMagicSkill";
import TBSkill, { EnumSkillParamLevelType, EnumSkillParamValueType } from "../data/parser/TBSkill";
import TBSkillEffect from "../data/parser/TBSkillEffect";
import TBSkillShow from "../data/parser/TBSkillShow";
import TBTankLevel from "../data/parser/TBTankLevel";
import TBWeaponLevel from "../data/parser/TBWeaponLevel";
import NumberUtils from "../utils/NumberUtils";
import Archer from "./Archer";
import Combat, { DungeonType } from "./Combat";
import { CombatMember, CombatMemberType } from "./combat/CombatMember";
import CombatMemberPlayer, { CombatPlayerType } from "./combat/CombatMemberPlayer";
import CombatLog, { CombatLogId } from "./CombatLog";
import LeadSkin from "./LeadSkin";
import Magical from "./Magical";
import MakeArrow from "./MakeArrow";
import Pet from "./Pet";
import Tank from "./Tank";
import Weapon from "./Weapon";
import Wing from "./Wing";

/**
 * 技能状态
 */
export enum SkillState {
    Init = 1, // 初始化
    Show = 2, // 表现
    Effect = 3, // 效果
    Finish = 4, // 完成
    Clear = 5, // 清理
}

/**
 * 技能效果状态
 */
export enum SkillEffectState {
    Init = 1, // 初始化
    Delay = 2, // 延迟
    Show = 3, // 表现
    Effect = 4, // 效果
    Finish = 5, // 完成
    Clear = 6, // 清理
}

/**
 * 技能表现状态
 */
export enum SkillShowState {
    Init = 1, // 初始化
    Show = 2, // 表现
    Finish = 3, // 完成
    Clear = 4, // 清理
}

/**
 * 表现item状态
 */
export enum CombatShowItemState {
    Init = 1, // 初始化
    Show = 2, // 表现
    Finish = 3, // 完成
    WaitClear = 4, // 待清理
    Clear = 5, // 清理
    Skip = 6, // 跳过
}

/**
 * 技能数据
 */
export interface ICombatSkillData {
    state: SkillState; // 状态

    id: number; // 技能id
    info: DataSkill; // 信息
    memberId: number; // 成员id

    releaseMember: CombatMember; // 释放成员
    targetMember: CombatMember; // 目标成员
    targetPoint: cc.Vec2; // 目标点
    targetPoint2: cc.Vec2; // 目标点

    showData: ICombatShowData[]; // 表现数据
    effectData: ICombatEffectData[]; // 效果数据
}

/**
 * 效果数据
 */
export interface ICombatEffectData {
    state: SkillEffectState; // 状态

    id: number; // 效果id
    info: DataSkillEffect; // 信息
    para: number[]; // 参数
    triggerPara: number[][]; // 触发参数
    delay: number; // 延迟
    nextEffectId: number; // 下一个效果id

    targetMember: CombatMember[]; // 目标成员

    showData: ICombatShowData[]; // 表现数据
}

/**
 * 增益数据
 */
export interface ICombatBuffData {
    id: number; // id
    info: DataSkillEffect; // 信息
    para: number[]; // 参数
    effectTime: number; // 生效时间
    triggerPara: number[][]; // 触发参数
    isTrigger: boolean; // 是否触发

    skillData: ICombatSkillData; // 技能数据
    targetMember: CombatMember; // 目标成员

    layerData: number[][]; // 层数数据
    maxLayerData: number[][]; // 最大层数数据
    addMaxLayerData: number[][]; // 增加最大层数数据

    showData: ICombatShowData[]; // 表现数据
}

/**
 * 表现数据
 */
export interface ICombatShowData {
    state: SkillShowState; // 状态

    id: number; // 表现id
    info: DataSkillShow; // 信息

    itemData: ICombatShowItemData[]; // item数据
}

/**
 * 表现item数据
 */
export interface ICombatShowItemData {
    state: CombatShowItemState; // 状态

    uuid: number; // 表现uuid
    info: DataSkillShow; // 信息
    dungeonType: DungeonType; // 副本类型

    targetType: EnumSkillShowTargetType; // 目标类型
    prefabType: EnumSkillShowPreform; // 预制体类型
    resFolder: EnumSkillShowFolder; // 资源-文件夹
    resPre: string; // 资源-前缀
    resSuf: string; // 资源-后缀
    initPos: cc.Vec2; // 初始pos
    targetPos: cc.Vec2; // 目标pos
    zIndex: number; // 层级
    angle: number; // 角度
    scaleX: number; // 缩放-x

    audioRes: string; // 音效res

    skipTime: number; // 跳过时间
}

/**
 * 技能事件
 */
export enum SkillEvent {
    ClearEffect = "clear-effect", // 清除特效
    UpdateBuff = "update-buff", // 更新增益
    UpdateBuff703 = "update-buff-703", // 更新增益703
    ClearShow = "clear-show", // 清理表现
    ClearSkill = "clear-skill", // 清理技能
    Release = "release", // 释放
    ResetEffectPara = "reset-effect-para", // 重置效果参数
}

/**
 * 技能
 */
export default class Skill extends Logic {
    protected registerHandler(): void {
        // 主角-升级
        LeadSkin.getInstance().on(
            LeadSkinUpgradeRet.prototype.clazzName,
            (data: LeadSkinUpgradeRet) => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.SkinLevel, [data.leadSkinId]);
            },
            this
        );
        // 主角-等级重置
        LeadSkin.getInstance().on(
            LeadSkinResetRet.prototype.clazzName,
            (data: LeadSkinResetRet) => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.SkinLevel, [data.leadSkinId]);
            },
            this
        );
        // 主角-升星
        LeadSkin.getInstance().on(
            LeadSkinIncrStarRet.prototype.clazzName,
            (data: LeadSkinIncrStarRet) => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.SkinStar, [data.leadSkinId]);
            },
            this
        );
        // 神器-升级
        Weapon.getInstance().on(
            WeaponUpgradeLevelRet.prototype.clazzName,
            () => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.WeaponLevel, [
                    Weapon.getInstance().getTeamId(),
                ]);
            },
            this
        );
        // 神器-升星
        Weapon.getInstance().on(
            WeaponUpgradeStarRet.prototype.clazzName,
            (data: WeaponUpgradeStarRet) => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.WeaponStar, [data.weaponId]);
            },
            this
        );
        // 背饰-升级
        Wing.getInstance().on(
            WingsBatchUpgradeLevelRet.prototype.clazzName,
            () => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.WingLevel, [
                    Wing.getInstance().getTeamId(),
                ]);
            },
            this
        );
        // 背饰-升星
        Wing.getInstance().on(
            WingsUpgradeStarRet.prototype.clazzName,
            (data: WingsUpgradeStarRet) => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.WingStar, [data.wingInfo.wingId]);
            },
            this
        );
        // 战车-升级
        Tank.getInstance().on(
            TankUpgradeLevelRet.prototype.clazzName,
            () => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.TankLevel, [
                    Tank.getInstance().getTeamId(),
                ]);
            },
            this
        );
        // 战车-升星
        Tank.getInstance().on(
            TankUpgradeStarRet.prototype.clazzName,
            (data: TankUpgradeStarRet) => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.TankStar, [data.tankId]);
            },
            this
        );
        // 弓箭手-升级
        Archer.getInstance().on(
            ArcherUpgradeRet.prototype.clazzName,
            (data: ArcherUpgradeRet) => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.ArcherLevel, [data.archerId]);
            },
            this
        );
        // 弓箭手-等级重置
        Archer.getInstance().on(
            ArcherResetLevelRet.prototype.clazzName,
            (data: ArcherResetLevelRet) => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.ArcherLevel, data.archerIds);
            },
            this
        );
        // 弓箭手-升星
        Archer.getInstance().on(
            ArcherIncrStarRet.prototype.clazzName,
            (data: ArcherIncrStarRet) => {
                this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.ArcherStar, [data.archerId]);
            },
            this
        );
        // 宠物-升级
        Pet.getInstance().on(
            PetUpgradeRet.prototype.clazzName,
            (data: PetUpgradeRet) => {
                const petBattleUuid = Pet.getInstance().getTeamIds();
                if (petBattleUuid.includes(data.petInfo.uuid)) {
                    this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.PetLevel, [data.petInfo.petId]);
                }
            },
            this
        );
        // 宠物-等级重置
        Pet.getInstance().on(
            PetResetLevelRet.prototype.clazzName,
            (data: PetResetLevelRet) => {
                const petBattleUuid = Pet.getInstance().getTeamIds();
                const index = data.uuids.findIndex((e) => petBattleUuid.includes(e));
                if (index !== -1) {
                    const petData = Pet.getInstance().getDataById(data.uuids[index]);
                    this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.PetLevel, [petData.petId]);
                }
            },
            this
        );
        // 宠物-升星
        Pet.getInstance().on(
            [PetIncrStarRet.prototype.clazzName, PetIncrStarUsePropRet.prototype.clazzName],
            (petData: IPetObj) => {
                const petBattleUuid = Pet.getInstance().getTeamIds();
                if (petBattleUuid.includes(petData.uuid)) {
                    this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.PetStar, [petData.petId]);
                }
            },
            this
        );
        // 魔法-升级
        Magical.getInstance().on(
            MagicalUpgradeRet.prototype.clazzName,
            (data: MagicalUpgradeRet) => {
                const magicInfo = TBMagicSkill.getInstance().getDataById(data.magicalId);
                switch (magicInfo.type) {
                    case EnumMagicSkillType.Initiative:
                        this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.ActiveMagicLevel, [
                            data.magicalId,
                        ]);
                        break;
                    case EnumMagicSkillType.Passive:
                        this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.PassiveMagicLevel, [
                            data.magicalId,
                        ]);
                        break;
                    default:
                        break;
                }
            },
            this
        );
        // 魔法-等级重置
        Magical.getInstance().on(
            MagicalResetLevelRet.prototype.clazzName,
            (data: MagicalResetLevelRet) => {
                const magicInfo = TBMagicSkill.getInstance().getDataById(data.magicalId);
                switch (magicInfo.type) {
                    case EnumMagicSkillType.Initiative:
                        this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.ActiveMagicLevel, [
                            data.magicalId,
                        ]);
                        break;
                    case EnumMagicSkillType.Passive:
                        this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.PassiveMagicLevel, [
                            data.magicalId,
                        ]);
                        break;
                    default:
                        break;
                }
            },
            this
        );
        // 魔法-升星
        Magical.getInstance().on(
            MagicalIncrStarRet.prototype.clazzName,
            (data: MagicalIncrStarRet) => {
                const magicInfo = TBMagicSkill.getInstance().getDataById(data.magicalId);
                switch (magicInfo.type) {
                    case EnumMagicSkillType.Initiative:
                        this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.ActiveMagicStar, [
                            data.magicalId,
                        ]);
                        break;
                    case EnumMagicSkillType.Passive:
                        this.emit(SkillEvent.ResetEffectPara, EnumSkillParamLevelType.PassiveMagicStar, [
                            data.magicalId,
                        ]);
                        break;
                    default:
                        break;
                }
            },
            this
        );
    }

    /**
     * 释放
     * @param skillId 技能id
     * @param memberId 成员id
     * @param releaseMember 释放成员
     * @param targetMember 目标成员
     * @param targetPoint 目标点
     * @param targetPoint2 目标点
     */
    public release(
        skillId: number,
        memberId: number,
        releaseMember: CombatMember,
        targetMember: CombatMember,
        targetPoint: cc.Vec2,
        targetPoint2: cc.Vec2
    ): void {
        const skillData: ICombatSkillData = {
            state: SkillState.Init,

            id: skillId,
            info: TBSkill.getInstance().getDataById(skillId),
            memberId,

            releaseMember,
            targetMember,
            targetPoint,
            targetPoint2,

            showData: [],
            effectData: [],
        };

        if (skillData.info.effectShake.length !== 0) {
            let showId = skillData.info.effectShake.concat();
            let showInfo = TBSkillShow.getInstance().getDataById(showId.shift());
            while (showInfo) {
                const showData: ICombatShowData = {
                    state: SkillShowState.Init,

                    id: showInfo.id,
                    info: showInfo,

                    itemData: [],
                };

                skillData.showData.push(showData);

                showId = showId.concat(showData.info.nextShow);
                if (showId.length !== 0) {
                    showInfo = TBSkillShow.getInstance().getDataById(showId.shift());
                } else {
                    showInfo = null;
                }
            }
        }

        skillData.info.effect.forEach((e) => {
            let effectInfo = TBSkillEffect.getInstance().getDataById(e);
            let waitEffectId = -1;
            while (effectInfo) {
                const effectPara = this.getEffectPara(effectInfo, skillData);
                let effectNum = 1;
                let effectDelay = 0;
                let nextEffectId = -1;
                if (effectInfo.nextEffect !== 0) {
                    nextEffectId = effectInfo.nextEffect;
                } else if (waitEffectId !== -1) {
                    nextEffectId = waitEffectId;
                    waitEffectId = -1;
                }
                switch (effectInfo.effectType) {
                    case EnumSkillEffectEffectType.Buff101:
                        effectNum = effectPara[3];
                        effectDelay = effectPara[4];
                        break;
                    case EnumSkillEffectEffectType.Buff103:
                        effectNum = effectPara[2];
                        effectDelay = effectPara[3];
                        break;
                    case EnumSkillEffectEffectType.Buff602:
                        nextEffectId !== -1 && (waitEffectId = nextEffectId);
                        nextEffectId = effectPara[4];
                        break;
                    case EnumSkillEffectEffectType.Buff702:
                        nextEffectId !== -1 && (waitEffectId = nextEffectId);
                        nextEffectId = effectPara[1];
                        break;
                    default:
                        break;
                }
                for (let i = 0; i < effectNum; i++) {
                    const effectData: ICombatEffectData = {
                        state: SkillEffectState.Init,

                        id: effectInfo.id,
                        info: effectInfo,
                        para: effectPara,
                        triggerPara: Utils.clone(effectInfo.conditionPara),
                        delay: effectDelay * i,
                        nextEffectId,

                        targetMember: [],

                        showData: [],
                    };

                    if (effectData.info.showId.length !== 0) {
                        let showId = effectData.info.showId.concat();
                        let showInfo = TBSkillShow.getInstance().getDataById(showId.shift());
                        while (showInfo) {
                            const showData: ICombatShowData = {
                                state: SkillShowState.Init,

                                id: showInfo.id,
                                info: showInfo,

                                itemData: [],
                            };

                            effectData.showData.push(showData);

                            showId = showId.concat(showData.info.nextShow);
                            if (showId.length !== 0) {
                                showInfo = TBSkillShow.getInstance().getDataById(showId.shift());
                            } else {
                                showInfo = null;
                            }
                        }
                    }

                    skillData.effectData.push(effectData);
                }

                if (nextEffectId !== 0) {
                    effectInfo = TBSkillEffect.getInstance().getDataById(nextEffectId);
                } else {
                    effectInfo = null;
                }
            }
        });

        skillData.releaseMember.getBaseData().skillData.push(skillData);
    }

    /**
     * 获取效果参数
     * @param effectInfo 效果信息
     * @param skillData 技能数据
     * @returns
     */
    private getEffectPara(effectInfo: DataSkillEffect, skillData: ICombatSkillData): number[] {
        const effectPara = Utils.clone(effectInfo.effect);
        effectInfo.effectTarget.forEach(([paraId, effectParaIndex, paraInit], i) => {
            let paraValue = 0;
            const releaseMemberBaseData = skillData.releaseMember.getBaseData();
            if (skillData.releaseMember.getBaseData().type === CombatMemberType.Player) {
                const releasePlayer = skillData.releaseMember as CombatMemberPlayer;
                if (releasePlayer.getData().type !== CombatPlayerType.Self) {
                    const combatPlayerData = Combat.getInstance().getPlayerCombatData(
                        releaseMemberBaseData.dungeonType,
                        releaseMemberBaseData.uuid
                    );
                    paraValue = this.getStepParaValue(paraId, skillData.memberId, combatPlayerData);
                } else {
                    paraValue = this.getStepParaValue(paraId, skillData.memberId);
                }
            } else {
                paraValue = this.getStepParaValue(paraId, skillData.memberId);
            }
            let tempParaValue = paraInit;
            for (const [paraRange, effectStep] of effectInfo.upgrade[i]) {
                effectPara[effectParaIndex - 1] += effectStep * (Math.min(paraValue, paraRange) - tempParaValue);
                tempParaValue = paraRange;

                if (paraValue <= tempParaValue) {
                    break;
                }
            }
        });

        return effectPara;
    }

    /**
     * 获取步长参数值
     * @param paraId 参数id
     * @param memberId 成员id
     * @param combatPlayerData 玩家战斗数据
     * @returns
     */
    public getStepParaValue(paraId: number, memberId: number, combatPlayerData?: IChallengerInfo): number {
        let paraValue = 0;
        switch (paraId) {
            case EnumSkillParamLevelType.SkinLevel:
                if (!combatPlayerData) {
                    const leadData = LeadSkin.getInstance().getDataById(memberId);
                    paraValue = leadData.level;
                } else {
                    const leadData = combatPlayerData.leadSkinGroupInfo.leadSkinInfos.find(
                        (e) => e.leadSkinId === memberId
                    );
                    paraValue = leadData.level;
                }
                break;
            case EnumSkillParamLevelType.SkinStar:
                if (!combatPlayerData) {
                    const leadData = LeadSkin.getInstance().getDataById(memberId);
                    paraValue = leadData.star;
                } else {
                    const leadData = combatPlayerData.leadSkinGroupInfo.leadSkinInfos.find(
                        (e) => e.leadSkinId === memberId
                    );
                    paraValue = leadData.star;
                }
                break;
            case EnumSkillParamLevelType.WeaponLevel:
                if (!combatPlayerData) {
                    const levelId = Weapon.getInstance().getLevel();
                    const levelInfo = TBWeaponLevel.getInstance().getDataById(levelId);
                    paraValue = levelInfo.level;
                } else {
                    const levelInfo = TBWeaponLevel.getInstance().getDataById(combatPlayerData.weaponGroupInfo.levelId);
                    paraValue = levelInfo.level;
                }
                break;
            case EnumSkillParamLevelType.WeaponStar:
                if (!combatPlayerData) {
                    const weaponData = Weapon.getInstance().getDataById(memberId);
                    paraValue = weaponData.star;
                } else {
                    const weaponData = combatPlayerData.weaponGroupInfo.weaponInfos.find(
                        (e) => e.weaponId === memberId
                    );
                    paraValue = weaponData.star;
                }
                break;
            case EnumSkillParamLevelType.WingLevel:
                if (!combatPlayerData) {
                    const featherData = Wing.getInstance().getFeathers();
                    paraValue = Math.min(...featherData.map((e) => e.level));
                } else {
                    const featherData = combatPlayerData.wingsGroupInfo.featherInfos;
                    paraValue = Math.min(...featherData.map((e) => e.level));
                }
                break;
            case EnumSkillParamLevelType.WingStar:
                if (!combatPlayerData) {
                    const wingData = Wing.getInstance().getDataById(memberId);
                    paraValue = wingData.star;
                } else {
                    const wingData = combatPlayerData.wingsGroupInfo.wingInfos.find((e) => e.wingId === memberId);
                    paraValue = wingData.star;
                }
                break;
            case EnumSkillParamLevelType.TankLevel:
                if (!combatPlayerData) {
                    const levelId = Tank.getInstance().getLevelId();
                    const levelInfo = TBTankLevel.getInstance().getDataById(levelId);
                    paraValue = levelInfo.level;
                } else {
                    const levelInfo = TBTankLevel.getInstance().getDataById(combatPlayerData.tanksInfo.level);
                    paraValue = levelInfo.level;
                }
                break;
            case EnumSkillParamLevelType.TankStar:
                if (!combatPlayerData) {
                    const tankData = Tank.getInstance().getInfoById(memberId);
                    paraValue = tankData.star;
                } else {
                    const tankData = combatPlayerData.tanksInfo.tankInfos.find((e) => e.tankId === memberId);
                    paraValue = tankData.star;
                }
                break;
            case EnumSkillParamLevelType.ArcherLevel:
                if (!combatPlayerData) {
                    const archerData = Archer.getInstance().getDataById(memberId);
                    paraValue = archerData.level;
                } else {
                    const archerData = combatPlayerData.archerGroupInfo.archerInfos.find(
                        (e) => e.archerId === memberId
                    );
                    paraValue = archerData.level;
                }
                break;
            case EnumSkillParamLevelType.ArcherStar:
                if (!combatPlayerData) {
                    const archerData = Archer.getInstance().getDataById(memberId);
                    paraValue = archerData.star;
                } else {
                    const archerData = combatPlayerData.archerGroupInfo.archerInfos.find(
                        (e) => e.archerId === memberId
                    );
                    paraValue = archerData.star;
                }
                break;
            case EnumSkillParamLevelType.PetLevel:
                if (!combatPlayerData) {
                    const petBattleUuid = Pet.getInstance().getTeamIds();
                    const petData = Pet.getInstance().getDataById(petBattleUuid[0]);
                    paraValue = petData.level;
                } else {
                    const petBattleUuid = combatPlayerData.petGroupInfo.battleInfos[0].uuid;
                    const petData = combatPlayerData.petGroupInfo.petInfos.find((e) => e.petId === petBattleUuid);
                    paraValue = petData.level;
                }
                break;
            case EnumSkillParamLevelType.PetStar:
                if (!combatPlayerData) {
                    const petBattleUuid = Pet.getInstance().getTeamIds();
                    const petData = Pet.getInstance().getDataById(petBattleUuid[0]);
                    paraValue = petData.star;
                } else {
                    const petBattleUuid = combatPlayerData.petGroupInfo.battleInfos[0].uuid;
                    const petData = combatPlayerData.petGroupInfo.petInfos.find((e) => e.uuid === petBattleUuid);
                    paraValue = petData.star;
                }
                break;
            case EnumSkillParamLevelType.ActiveMagicLevel:
            case EnumSkillParamLevelType.PassiveMagicLevel:
                if (!combatPlayerData) {
                    const magicData = Magical.getInstance().getMagicInfoByMagicId(memberId);
                    paraValue = magicData.level;
                } else {
                    const magicData = combatPlayerData.magicalGroupInfo.magicalInfos.find(
                        (e) => e.magicalId === memberId
                    );
                    paraValue = magicData.level;
                }
                break;
            case EnumSkillParamLevelType.ActiveMagicStar:
            case EnumSkillParamLevelType.PassiveMagicStar:
                if (!combatPlayerData) {
                    const magicData = Magical.getInstance().getMagicInfoByMagicId(memberId);
                    paraValue = magicData.star;
                } else {
                    const magicData = combatPlayerData.magicalGroupInfo.magicalInfos.find(
                        (e) => e.magicalId === memberId
                    );
                    paraValue = magicData.star;
                }
                break;
            case EnumSkillParamLevelType.ArrowLevel:
                if (!combatPlayerData) {
                    const battleData = Archer.getInstance().getTeam();
                    const tempBattleData = battleData.find((e) => e.archerId === memberId);
                    const arrowData = MakeArrow.getInstance().getArcherGridData(tempBattleData.cellId);
                    paraValue = arrowData.level;
                } else {
                    const battleData = combatPlayerData.archerGroupInfo.battleInfos.find(
                        (e) => e.archerId === memberId
                    );
                    const arrowData = combatPlayerData.ArrowGroupInfo.battleCellInfos.find(
                        (e) => e.id === battleData.cellId
                    );
                    paraValue = arrowData.level;
                }
                break;
            default:
                break;
        }

        return paraValue;
    }

    /**
     * 更新效果触发参数
     * @param member 成员
     * @param skillData 技能数据
     * @param triggerType 触发类型
     * @param para 参数
     */
    public updateEffectTriggerPara(
        member: CombatMember,
        skillData: ICombatSkillData[],
        triggerType: EnumSkillEffectCondition,
        para: number
    ): void {
        const effectSkillData = skillData.filter((e) => e.state === SkillState.Effect);
        effectSkillData.forEach((e) => {
            const initEffectData = e.effectData.filter(
                (e2) => e2.state === SkillEffectState.Init && e2.info.condition === triggerType
            );
            initEffectData.forEach((e2) => {
                let isUpdate = false;
                switch (e2.info.condition) {
                    case EnumSkillEffectCondition.Condition2:
                    case EnumSkillEffectCondition.Condition3:
                    case EnumSkillEffectCondition.Condition4:
                        {
                            const index = e2.triggerPara.findIndex(([tempPara]) => tempPara === para);
                            if (index !== -1) {
                                isNaN(e2.triggerPara[index][2]) && (e2.triggerPara[index][2] = 0);
                                e2.triggerPara[index][2]++;

                                isUpdate = true;
                            }
                        }
                        break;
                    case EnumSkillEffectCondition.Condition5:
                    case EnumSkillEffectCondition.Condition9:
                        isNaN(e2.triggerPara[0][1]) && (e2.triggerPara[0][1] = 0);
                        e2.triggerPara[0][1] += para;

                        isUpdate = true;
                        break;
                    case EnumSkillEffectCondition.Condition6:
                    case EnumSkillEffectCondition.Condition10:
                        {
                            const index = e2.triggerPara.findIndex(([tempPara]) => tempPara === para);
                            if (index !== -1) {
                                e2.triggerPara[index][1] = 1;

                                isUpdate = true;
                            }
                        }
                        break;
                    default:
                        break;
                }

                if (isUpdate && CombatLog.getInstance().getShowState(CombatLogId.EffectTriggerTypeUpdate)) {
                    const memberBaseData = member.getBaseData();
                    if (CombatLog.getInstance().getShowStateByDungeonType(memberBaseData.dungeonType)) {
                        CombatLog.getInstance().logTitle(CombatLogId.EffectTriggerTypeUpdate, member);

                        cc.log({
                            triggerType,
                            para,
                            skillId: e.id,
                            effectId: e2.id,
                            triggerPara: Utils.clone(e2.triggerPara),
                        });
                    }
                }
            });
        });
    }

    /**
     * 更新增益触发参数
     * @param member 成员
     * @param buffData 增益数据
     * @param triggerType 触发类型
     * @param para 参数
     */
    public updateBuffTriggerPara(
        member: CombatMember,
        buffData: ICombatBuffData[],
        triggerType: EnumSkillEffectCondition,
        para: number
    ): void {
        for (const e of buffData) {
            if (e.layerData.length <= 0) {
                continue;
            }
            if (!e.targetMember.isAttack()) {
                continue;
            }
            if (e.info.condition !== triggerType) {
                continue;
            }

            let isUpdate = false;
            switch (e.info.condition) {
                case EnumSkillEffectCondition.Condition12:
                case EnumSkillEffectCondition.Condition13:
                case EnumSkillEffectCondition.Condition14:
                    {
                        const index = e.triggerPara.findIndex(([tempPara]) => tempPara === para);
                        if (index !== -1) {
                            isNaN(e.triggerPara[index][2]) && (e.triggerPara[index][2] = 0);
                            e.triggerPara[index][2]++;

                            isUpdate = true;
                        }
                    }
                    break;
                case EnumSkillEffectCondition.Condition15:
                    {
                        const index = e.triggerPara.findIndex(([tempPara]) => tempPara === para);
                        if (index !== -1) {
                            e.triggerPara[index][1] = 1;

                            isUpdate = true;
                        }
                    }
                    break;
                default:
                    break;
            }

            if (isUpdate && CombatLog.getInstance().getShowState(CombatLogId.BuffTriggerTypeUpdate)) {
                if (CombatLog.getInstance().getShowStateByDungeonType(member.getBaseData().dungeonType)) {
                    CombatLog.getInstance().logTitle(CombatLogId.BuffTriggerTypeUpdate, member);

                    cc.log({
                        triggerType,
                        para,
                        buffId: e.id,
                        triggerPara: Utils.clone(e.triggerPara),
                    });
                }
            }
        }
    }

    /**
     * 更新效果参数
     * @param effectData 效果数据
     * @param skillData 技能数据
     */
    public updateEffectPara(effectData: ICombatEffectData, skillData: ICombatSkillData): void {
        const memberBaseData = skillData.releaseMember.getBaseData();
        const buffData = memberBaseData.buffData.filter((e) => {
            if (e.info.effectType === EnumSkillEffectEffectType.Buff302 && e.layerData.length !== 0) {
                const [, , , , , , ...effectId] = e.para;

                return effectId.includes(effectData.id);
            }

            return false;
        });
        buffData.forEach((e) => {
            const [index, init] = e.para;
            effectData.para[index - 1] += init * e.layerData.length;

            this.clearBuffLayer(skillData.releaseMember, e, e.layerData.length, true);
        });
    }

    /**
     * 触发增益
     * @param effectData 效果数据
     * @param skillData 技能数据
     * @param targetMember 目标成员
     */
    public triggerBuff(effectData: ICombatEffectData, skillData: ICombatSkillData, targetMember: CombatMember): void {
        let tempEffectData: ICombatEffectData = null;
        let tempAdd = 0;
        let tempMax = 0;
        let tempAddMax = 0;
        let tempDuration = 0;
        let tempDuration2 = 0;
        switch (effectData.info.effectType) {
            case EnumSkillEffectEffectType.Buff101:
            case EnumSkillEffectEffectType.Buff103:
                tempEffectData = effectData;
                tempAdd = 1;
                tempMax = -1;
                tempAddMax = 0;
                tempDuration = tempEffectData.info.duration;
                break;
            case EnumSkillEffectEffectType.Buff201:
                {
                    const [, , add, max, addMax, duration] = effectData.para;
                    tempEffectData = effectData;
                    tempAdd = add;
                    tempMax = max;
                    tempAddMax = addMax;
                    tempDuration = duration;
                }
                break;
            case EnumSkillEffectEffectType.Buff302:
                {
                    const [, , add, max, addMax, duration] = effectData.para;
                    tempEffectData = effectData;
                    tempAdd = add;
                    tempMax = max;
                    tempAddMax = addMax;
                    tempDuration = duration;
                }
                break;
            case EnumSkillEffectEffectType.Buff601:
                {
                    const [add, max, addMax, effectId, duration] = effectData.para;
                    tempEffectData = skillData.effectData.find((e) => e.id === effectId);
                    tempAdd = add;
                    tempMax = max;
                    tempAddMax = addMax;
                    tempDuration = tempEffectData.info.duration;
                    tempDuration2 = duration;
                }
                break;
            case EnumSkillEffectEffectType.Buff701:
                {
                    const [, , add, max, addMax, duration] = effectData.para;
                    tempEffectData = effectData;
                    tempAdd = add;
                    tempMax = max;
                    tempAddMax = addMax;
                    tempDuration = duration;
                }
                break;
            case EnumSkillEffectEffectType.Buff703:
                {
                    const [, , duration] = effectData.para;
                    tempEffectData = effectData;
                    tempAdd = 1;
                    tempMax = -1;
                    tempAddMax = 0;
                    tempDuration = duration;
                }
                break;
            case EnumSkillEffectEffectType.Buff704:
                {
                    const [add, duration] = effectData.para;
                    tempEffectData = effectData;
                    tempAdd = add;
                    tempMax = -1;
                    tempAddMax = 0;
                    tempDuration = duration;
                }
                break;
            default:
                break;
        }

        let buffData: ICombatBuffData = null;
        const releaseMemberBaseData = skillData.releaseMember.getBaseData();
        buffData = releaseMemberBaseData.buffData.find((e) => e.id === tempEffectData.id);
        if (!buffData) {
            buffData = {
                id: tempEffectData.id,
                info: tempEffectData.info,
                para: Utils.clone(tempEffectData.para),
                effectTime: -1,
                triggerPara: Utils.clone(tempEffectData.info.conditionPara),
                isTrigger: false,

                skillData,
                targetMember: null,

                layerData: [],
                maxLayerData: [],
                addMaxLayerData: [],

                showData: [],
            };
        } else {
            buffData.para = Utils.clone(tempEffectData.para);
            buffData.skillData = skillData;
        }

        tempMax !== 0 &&
            buffData.maxLayerData.push([tempDuration, tempMax, skillData.id, effectData.id, skillData.memberId]);
        if (tempAddMax !== 0) {
            if (tempAdd === 0 && tempMax === 0) {
                buffData.addMaxLayerData.push([
                    tempDuration2,
                    tempAddMax,
                    skillData.id,
                    effectData.id,
                    skillData.memberId,
                ]);
            } else {
                buffData.addMaxLayerData.push([
                    tempDuration,
                    tempAddMax,
                    skillData.id,
                    effectData.id,
                    skillData.memberId,
                ]);
            }
        }

        if (releaseMemberBaseData.buffData.findIndex((e) => e.id === buffData.id) === -1) {
            releaseMemberBaseData.buffData.push(buffData);
        }

        let buffData2: ICombatBuffData = null;
        const targetMemberBaseData = targetMember.getBaseData();
        buffData2 = targetMemberBaseData.buffData.find(
            (e) =>
                e.id === tempEffectData.id &&
                e.skillData.releaseMember.getBaseData().uuid === releaseMemberBaseData.uuid
        );
        if (!buffData2) {
            buffData2 = {
                id: tempEffectData.id,
                info: tempEffectData.info,
                para: Utils.clone(tempEffectData.para),
                effectTime: -1,
                triggerPara: Utils.clone(tempEffectData.info.conditionPara),
                isTrigger: false,

                skillData,
                targetMember,

                layerData: [],
                maxLayerData: [],
                addMaxLayerData: [],

                showData: [],
            };
        } else {
            buffData2.para = Utils.clone(tempEffectData.para);
            buffData2.skillData = skillData;
            buffData2.targetMember = targetMember;
        }

        if (tempAdd !== 0) {
            let max = 0;
            for (const [, e] of buffData.maxLayerData) {
                if (e === -1) {
                    max = -1;
                    break;
                } else {
                    max = Math.max(max, e);
                }
            }
            let addmax = 0;
            buffData.addMaxLayerData.forEach(([, e]) => {
                addmax = Math.max(addmax, e);
            });
            max !== -1 && (tempAdd = Math.min(tempAdd, max + addmax - buffData2.layerData.length));
            for (let i = 0; i < tempAdd; i++) {
                switch (effectData.info.effectType) {
                    case EnumSkillEffectEffectType.Buff703:
                        {
                            const [type, init] = effectData.para;

                            let value = 0;
                            switch (type) {
                                case 1:
                                    value = releaseMemberBaseData.attr[EnumAttributeType.FinalHp].value * init;
                                    break;
                                default:
                                    break;
                            }
                            buffData2.layerData.push([tempDuration, skillData.id, value]);
                        }
                        break;
                    default:
                        buffData2.layerData.push([tempDuration, skillData.id]);
                        break;
                }
            }
        }

        if (tempAdd !== 0 && buffData2.info.buffShowId.length !== 0) {
            if (buffData2.showData.findIndex((e) => e.state === SkillShowState.Show) === -1) {
                buffData2.info.buffShowId.forEach((e) => {
                    const showData: ICombatShowData = {
                        state: SkillShowState.Init,

                        id: e,
                        info: TBSkillShow.getInstance().getDataById(e),

                        itemData: [],
                    };

                    buffData2.showData.push(showData);
                });
            }
        }

        if (
            targetMemberBaseData.buffData.findIndex(
                (e) =>
                    e.id === buffData2.id &&
                    e.skillData.releaseMember.getBaseData().uuid ===
                        buffData2.skillData.releaseMember.getBaseData().uuid
            ) === -1
        ) {
            targetMemberBaseData.buffData.push(buffData2);
        }

        switch (buffData2.info.effectType) {
            case EnumSkillEffectEffectType.Buff201:
                buffData2.targetMember.updateAttrBySkill();
                break;
            case EnumSkillEffectEffectType.Buff703:
                Skill.getInstance().emit(
                    SkillEvent.UpdateBuff703,
                    targetMemberBaseData.dungeonType,
                    targetMemberBaseData.type,
                    targetMemberBaseData.uuid
                );
                break;
            default:
                break;
        }

        if (CombatLog.getInstance().getShowState(CombatLogId.BuffTrigger)) {
            if (CombatLog.getInstance().getShowStateByDungeonType(releaseMemberBaseData.dungeonType)) {
                CombatLog.getInstance().logTitle(CombatLogId.BuffTrigger, skillData.releaseMember);

                cc.log({
                    buffId: buffData.id,
                    buffPara: Utils.clone(buffData.para),
                    add: tempAdd,
                    max: tempMax,
                    addMax: tempAddMax,
                    layerData: Utils.clone(buffData2.layerData),
                    maxLayerData: Utils.clone(buffData.maxLayerData),
                    addMaxLayerData: Utils.clone(buffData.addMaxLayerData),
                    time: Time.getInstance().now(),
                });
            }
        }
    }

    /**
     * 清理增益层数
     * @param member 成员
     * @param buffData 增益数据
     * @param layer 层数
     * @param isOnly 是否只清理单次生效层
     */
    public clearBuffLayer(
        member: CombatMember,
        buffData: ICombatBuffData,
        layer: number,
        isOnly: boolean = false
    ): void {
        if (isOnly) {
            for (let i = buffData.layerData.length - 1; i >= 0; i--) {
                if (buffData.layerData[i][0] === 0) {
                    buffData.layerData.splice(i, 1);
                }
            }
        } else {
            buffData.layerData.sort(([duration], [duration2]) => {
                if (duration === duration2) {
                    return 0;
                }
                if (duration === -1 || duration2 === -1) {
                    return duration === -1 ? 1 : -1;
                }
                return duration - duration2;
            });
            buffData.layerData.splice(0, layer);
        }

        if (CombatLog.getInstance().getShowState(CombatLogId.BuffClear)) {
            const memberBaseData = member.getBaseData();
            if (CombatLog.getInstance().getShowStateByDungeonType(memberBaseData.dungeonType)) {
                CombatLog.getInstance().logTitle(CombatLogId.BuffClear, member);

                cc.log({
                    buffId: buffData.id,
                    clear: layer,
                    layerData: Utils.clone(buffData.layerData),
                    maxLayerData: Utils.clone(buffData.maxLayerData),
                    addMaxLayerData: Utils.clone(buffData.addMaxLayerData),
                    time: Time.getInstance().now(),
                });
            }
        }
    }

    /**
     * 重置效果
     */
    public resetEffect(effectData: ICombatEffectData): void {
        effectData.state = SkillEffectState.Init;
        effectData.targetMember = [];
        effectData.showData = [];

        if (effectData.info.showId.length !== 0) {
            let showId = effectData.info.showId.concat();
            let showInfo = TBSkillShow.getInstance().getDataById(showId.shift());
            while (showInfo) {
                const showData: ICombatShowData = {
                    state: SkillShowState.Init,

                    id: showInfo.id,
                    info: showInfo,

                    itemData: [],
                };

                effectData.showData.push(showData);

                showId = showId.concat(showData.info.nextShow);
                if (showId.length !== 0) {
                    showInfo = TBSkillShow.getInstance().getDataById(showId.shift());
                } else {
                    showInfo = null;
                }
            }
        }
    }

    /**
     * 重置效果参数
     * @param player 玩家
     * @param paraType 参数类型
     * @param memberId 成员id
     */
    public resetEffectPara(player: CombatMemberPlayer, paraType: EnumSkillParamLevelType, memberId: number[]): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        switch (paraType) {
            case EnumSkillParamLevelType.SkinLevel:
            case EnumSkillParamLevelType.SkinStar:
                if (!memberId.includes(playerData.leadData.id)) {
                    return;
                }
                break;
            case EnumSkillParamLevelType.WeaponLevel:
            case EnumSkillParamLevelType.WeaponStar:
                if (!memberId.includes(playerData.weaponData.id)) {
                    return;
                }
                break;
            case EnumSkillParamLevelType.WingLevel:
            case EnumSkillParamLevelType.WingStar:
                if (!memberId.includes(playerData.wingData.id)) {
                    return;
                }
                break;
            case EnumSkillParamLevelType.TankLevel:
            case EnumSkillParamLevelType.TankStar:
                if (!memberId.includes(playerData.tankData.id)) {
                    return;
                }
                break;
            case EnumSkillParamLevelType.ArcherLevel:
            case EnumSkillParamLevelType.ArcherStar:
            case EnumSkillParamLevelType.ArrowLevel:
                if (playerData.archerData.findIndex((e) => memberId.includes(e.id)) === -1) {
                    return;
                }
                break;
            case EnumSkillParamLevelType.PetLevel:
            case EnumSkillParamLevelType.PetStar:
                if (!memberId.includes(playerData.petData.id)) {
                    return;
                }
                break;
            case EnumSkillParamLevelType.ActiveMagicLevel:
            case EnumSkillParamLevelType.PassiveMagicLevel:
            case EnumSkillParamLevelType.ActiveMagicStar:
            case EnumSkillParamLevelType.PassiveMagicStar:
                if (playerData.magicData.findIndex((e) => memberId.includes(e.id)) === -1) {
                    return;
                }
                break;
            default:
                break;
        }

        for (const e of playerBaseData.skillData) {
            if (e.info.cd !== -1) {
                continue;
            }
            if (!memberId.includes(e.memberId)) {
                continue;
            }

            for (const e2 of e.effectData) {
                if (e2.info.condition === EnumSkillEffectCondition.Condition1) {
                    continue;
                }
                if (e2.info.effectTarget.findIndex((e3) => e3[0] === paraType) === -1) {
                    continue;
                }

                const oldEffectPara = Utils.clone(e2.para);
                e2.para = this.getEffectPara(e2.info, e);

                if (CombatLog.getInstance().getShowState(CombatLogId.EffectParaUpdate)) {
                    if (CombatLog.getInstance().getShowStateByDungeonType(playerBaseData.dungeonType)) {
                        CombatLog.getInstance().logTitle(CombatLogId.EffectParaUpdate, player);
                        cc.log({
                            skillId: e.id,
                            effectId: e2.id,
                            effectPara: Utils.clone(e2.para),
                            oldEffectPara,
                        });
                    }
                }
            }
        }

        for (const e of playerBaseData.buffData) {
            if (e.skillData.releaseMember.getBaseData().uuid !== playerBaseData.uuid) {
                continue;
            }

            let isUpdate = false;
            let oldBuffPara: number[] = null;
            if (
                e.skillData.info.cd === -1 &&
                memberId.includes(e.skillData.memberId) &&
                e.info.condition === EnumSkillEffectCondition.Condition1 &&
                e.info.effectTarget.findIndex((e2) => e2[0] === paraType) !== -1
            ) {
                oldBuffPara = Utils.clone(e.para);
                e.para = this.getEffectPara(e.info, e.skillData);

                switch (e.info.effectType) {
                    case EnumSkillEffectEffectType.Buff201:
                        e.targetMember.updateAttrBySkill();
                        break;
                    default:
                        break;
                }

                isUpdate = true;
            }

            for (const e2 of e.addMaxLayerData) {
                const [, , skillId, effectId, tempMemberId] = e2;

                const skillInfo = TBSkill.getInstance().getDataById(skillId);
                const effectInfo = TBSkillEffect.getInstance().getDataById(effectId);
                if (
                    skillInfo.cd === -1 &&
                    memberId.includes(tempMemberId) &&
                    effectInfo.condition === EnumSkillEffectCondition.Condition1 &&
                    effectInfo.effectTarget.findIndex((e3) => e3[0] === paraType) !== -1
                ) {
                    const effectPara = this.getEffectPara(effectInfo, e.skillData);
                    switch (effectInfo.effectType) {
                        case EnumSkillEffectEffectType.Buff201:
                            {
                                const [, , , , addMax] = effectPara;

                                e2[1] = addMax;
                            }
                            break;
                        case EnumSkillEffectEffectType.Buff302:
                            {
                                const [, , , , addMax] = effectPara;

                                e2[1] = addMax;
                            }
                            break;
                        case EnumSkillEffectEffectType.Buff601:
                            {
                                const [, , addMax] = effectPara;

                                e2[1] = addMax;
                            }
                            break;
                        case EnumSkillEffectEffectType.Buff701:
                            {
                                const [, , , , addMax] = effectPara;

                                e2[1] = addMax;
                            }
                            break;
                        default:
                            break;
                    }

                    isUpdate = true;
                }
            }

            if (isUpdate && CombatLog.getInstance().getShowState(CombatLogId.BuffUpdate)) {
                if (CombatLog.getInstance().getShowStateByDungeonType(playerBaseData.dungeonType)) {
                    CombatLog.getInstance().logTitle(CombatLogId.BuffUpdate, player);
                    cc.log({
                        buffId: e.id,
                        buffPara: Utils.clone(e.para),
                        oldBuffPara,
                        addMaxLayerData: Utils.clone(e.addMaxLayerData),
                        time: Time.getInstance().now(),
                    });
                }
            }
        }
    }

    /**
     * 根据id获取技能数值
     * @param id
     */
    public getSkillValueById(id: number, levels: { [key in EnumSkillParamLevelType]?: number }): string[] {
        const values: string[] = [];
        const data = TBSkill.getInstance().getDataById(id);
        for (const e of data.para) {
            const [skillEffectId, effectTargetIndex, valueType] = e;
            const { effect, effectTarget, upgrade } = TBSkillEffect.getInstance().getDataById(skillEffectId);
            const [levelType, effectIndex, initLevel] = effectTarget[effectTargetIndex - 1];
            const upgradeInfo: number[][] = upgrade[effectTargetIndex - 1];
            const level = levels[levelType];
            if (isNaN(level)) {
                continue;
            }
            let index = 0;
            for (let i = 0; i < upgradeInfo.length; i++) {
                const [stepLevel] = upgradeInfo[i];
                if (level <= stepLevel) {
                    index = i;
                    break;
                }
            }
            let value: number = effect[effectIndex - 1];
            for (let i = 0; i <= index; i++) {
                value +=
                    ((i < index ? upgradeInfo[i][0] : level) -
                        (upgradeInfo[i - 1] ? upgradeInfo[i - 1][0] : initLevel)) *
                    upgradeInfo[i][1];
            }
            value = valueType === EnumSkillParamValueType.Percent ? value * 100 : value;
            let valueStr =
                valueType === EnumSkillParamValueType.Percent ? value.toFixed(1) : NumberUtils.format(value, 1, 0);
            valueStr = valueStr + (valueType === EnumSkillParamValueType.Percent ? "%" : "");
            values.push(valueStr);
        }
        return values;
    }
}
