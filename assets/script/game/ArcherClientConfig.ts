/*
 * @Author: chenx
 * @Date: 2025-07-07 16:35:47
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-22 09:45:49
 */
import ClientConfig from "../../nsn/core/ClientConfig";
import Logic from "../../nsn/core/Logic";

/**
 * 客户端配置名称
 */
export enum ClientConfigName {
    SpineData = "spine-data", // 骨骼数据
}

/**
 * 骨骼数据
 */
export interface ISpineData {
    [aniName: string]: // 动画名称
    {
        duration: number; // 持续时间
        event: {
            [name: string]: number[]; // 帧时间
        }; // 事件
    };
}

/**
 * 客户端配置
 */
export default class ArcherClientConfig extends Logic {
    /**
     * 获取骨骼数据
     * @param spineName 骨骼名称
     * @returns
     */
    public getSpineData(spineName: string): ISpineData {
        const spineData: { [spineName: string]: ISpineData } = ClientConfig.getInstance().getConfig(
            ClientConfigName.SpineData
        );
        return spineData[spineName];
    }
}
