/*
 * @Author: chenx
 * @Date: 2025-07-14 15:43:15
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-24 17:43:03
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Time, { HOUR_TO_SECOND, MINUTE_TO_SECOND } from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    IPlayerInfo,
    IRankItem,
    IUnionInfo,
    IUnionSiegeDetailObj,
    IUnionSiegeGetActiveRet,
    IUnionSiegeGetRet,
    IUnionSiegeLogObj,
    IUnionSiegeObj,
    UnionSiegeBoxReward,
    UnionSiegeBoxRewardRet,
    UnionSiegeEndChallenge,
    UnionSiegeEndChallengeNoticeRet,
    UnionSiegeEndChallengeRet,
    UnionSiegeGet,
    UnionSiegeGetActive,
    UnionSiegeGetActiveRet,
    UnionSiegeGetLog,
    UnionSiegeGetLogRet,
    UnionSiegeGetMyLog,
    UnionSiegeGetMyLogRet,
    UnionSiegeGetRet,
    UnionSiegeRank,
    UnionSiegeRankRet,
    UnionSiegeReward,
    UnionSiegeRewardRet,
    UnionSiegeStartChallenge,
    UnionSiegeStartChallengeNoticeRet,
    UnionSiegeStartChallengeRet,
    UnionSiegeSweetChallenge,
    UnionSiegeSweetChallengeRet,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumUnionPara } from "../data/base/BaseUnion";
import TBActivity from "../data/parser/TBActivity";
import TBUnion from "../data/parser/TBUnion";
import Combat, { CombatEvent, DungeonType } from "./Combat";
import { CombatPlayerType } from "./combat/CombatMemberPlayer";
import Player from "./Player";
import Union from "./Union";

/**
 * 公会攻城战状态
 */
export enum EnumUnionSiegeStatus {
    Status1, // 匹配期间
    Status2, // 备战期间
    Status3, // 激战期间
    Status4, // 结算期间
    Status5, // 领奖期间
    Status6, // 公会本轮轮空（需要显示一个未参赛状态旗帜和状态）
    Status7, // 本轮未参赛(个人未参赛：显示敌我双方公会信息, 公会未参赛：需要显示一个未参赛状态旗帜和状态)
    Status8, // 未开放期间1 周一六点前
    Status9, // 未开放期间2 周六周日
}

/**
 * 参战成员类型
 */
export enum EnumUnionSiegeMemberType {
    Leader = 1, // 领袖
    Elite = 2, // 精英
    Other = 3, // 其他
}

/**
 * 挑战难度
 */
export enum EnumChallengeDifficulty {
    Ordinary = 1, // 普通
    Difficulty = 2, // 困难
    Nightmare = 3, // 噩梦
}

/**
 * 成员类型对应难度守护的星级
 * 根据类型和难度获取星级
 */
const UNION_SIEGE_STAR_COUNT_MAP = {
    [EnumUnionSiegeMemberType.Leader]: {
        [EnumChallengeDifficulty.Ordinary]: 2,
        [EnumChallengeDifficulty.Difficulty]: 3,
        [EnumChallengeDifficulty.Nightmare]: 4,
    },
    [EnumUnionSiegeMemberType.Elite]: {
        [EnumChallengeDifficulty.Ordinary]: 1,
        [EnumChallengeDifficulty.Difficulty]: 2,
        [EnumChallengeDifficulty.Nightmare]: 3,
    },
    [EnumUnionSiegeMemberType.Other]: {
        [EnumChallengeDifficulty.Ordinary]: 1,
        [EnumChallengeDifficulty.Difficulty]: 1,
        [EnumChallengeDifficulty.Nightmare]: 1,
    },
};

const LOG_DATA_EXPIRED_TIME = 0.1; // 日志数据过期时间
export const UNION_DEFENSE_EXT_ADD_START = 1; // 扫荡增加星数

export default class UnionSiege extends Logic {
    private unionSiegeInfos: Map<number, IUnionSiegeObj> = new Map<number, IUnionSiegeObj>(); // 公会攻城战信息
    private unionSiegeLogInfos: IUnionSiegeLogObj[] = []; // 公会攻城战日志信息
    private myUnionSiegeLogInfos: IUnionSiegeLogObj[] = []; // 我的公会攻城战日志信息
    private requestLogTime: number = 0; // 请求日志数据时间
    private rankItems: IRankItem[] = []; // 排行信息
    private rankMyItem: IRankItem = null; // 我的排名信息

    public clear(): void {
        this.unionSiegeInfos.clear();
        this.unionSiegeLogInfos = [];
        this.myUnionSiegeLogInfos = [];
        this.requestLogTime = 0;
        this.rankItems = [];
        this.rankMyItem = null;
    }

    protected registerHandler(): void {
        Socket.getInstance().on(UnionSiegeGetRet.prototype.clazzName, this.unionSiegeGetRet, this);
        Socket.getInstance().on(UnionSiegeGetActiveRet.prototype.clazzName, this.unionSiegeGetActiveRet, this);
        Socket.getInstance().on(UnionSiegeGetLogRet.prototype.clazzName, this.unionSiegeGetLogRet, this);
        Socket.getInstance().on(UnionSiegeGetMyLogRet.prototype.clazzName, this.unionSiegeGetMyLogRet, this);
        Socket.getInstance().on(
            UnionSiegeStartChallengeRet.prototype.clazzName,
            this.unionSiegeStartChallengeRet,
            this
        );
        Socket.getInstance().on(
            UnionSiegeStartChallengeNoticeRet.prototype.clazzName,
            this.unionSiegeStartChallengeNoticeRet,
            this
        );
        Socket.getInstance().on(UnionSiegeEndChallengeRet.prototype.clazzName, this.unionSiegeEndChallengeRet, this);
        Socket.getInstance().on(
            UnionSiegeEndChallengeNoticeRet.prototype.clazzName,
            this.unionSiegeEndChallengeNoticeRet,
            this
        );
        Socket.getInstance().on(
            UnionSiegeSweetChallengeRet.prototype.clazzName,
            this.unionSiegeSweetChallengeRet,
            this
        );
        Socket.getInstance().on(UnionSiegeRankRet.prototype.clazzName, this.unionSiegeRankRet, this);
        Socket.getInstance().on(UnionSiegeRewardRet.prototype.clazzName, this.unionSiegeRewardRet, this);
        Socket.getInstance().on(UnionSiegeBoxRewardRet.prototype.clazzName, this.unionSiegeBoxRewardRet, this);
    }

    /**
     * 获取公会GVG信息
     * @param data
     */
    private unionSiegeGetRet(data: IUnionSiegeGetRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, unionSiegeInfos } = data;
        if (errMsg !== UnionSiegeGetRet.ErrorResult.None) {
            return;
        }
        this.clear();
        for (const e of unionSiegeInfos) {
            this.unionSiegeInfos.set(e.unionId, e);
        }
        this.emit(UnionSiegeGetRet.prototype.clazzName);
    }

    /**
     * 获取公会和个人活跃度
     * @param data
     */
    private unionSiegeGetActiveRet(data: IUnionSiegeGetActiveRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, personalActivePoint, sumActivePoint } = data;
        if (errMsg !== UnionSiegeGetActiveRet.ErrorResult.None) {
            return;
        }

        this.emit(UnionSiegeGetActiveRet.prototype.clazzName, personalActivePoint, sumActivePoint);
    }

    /**
     * 获取公会日志信息
     * @param data
     */
    private unionSiegeGetLogRet(data: UnionSiegeGetLogRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, siegeLogInfos } = data;
        if (errMsg !== UnionSiegeGetLogRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeGetLogRet[errMsg]);
            return;
        }
        this.unionSiegeLogInfos = siegeLogInfos;
        this.requestLogTime = Time.getInstance().now();
        this.emit(UnionSiegeGetLogRet.prototype.clazzName);
    }

    /**
     * 获取我的日志信息
     * @param data
     */
    private unionSiegeGetMyLogRet(data: UnionSiegeGetMyLogRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, siegeLogInfos } = data;
        if (errMsg !== UnionSiegeGetMyLogRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeGetMyLogRet[errMsg]);
            return;
        }
        this.myUnionSiegeLogInfos = siegeLogInfos;
        this.requestLogTime = Time.getInstance().now();
        this.emit(UnionSiegeGetMyLogRet.prototype.clazzName);
    }

    /**
     * 开始挑战(我的)
     * @param data
     */
    private unionSiegeStartChallengeRet(data: UnionSiegeStartChallengeRet): void {
        UI.getInstance().hideSoftLoading();
        const {
            errMsg,
            rivalUnionId,
            rivalRoleId,
            challengeDifficulty,
            activityId,
            myChallengerInfo,
            rivalChallengerInfo,
        } = data;
        if (errMsg !== UnionSiegeStartChallengeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeStartChallengeRet[errMsg]);
            return;
        }

        const find = this.getUnionSiegeDetailObjByUnionIdAndPlayerId(rivalUnionId, rivalRoleId); // 对方公会被挑战的人
        if (find) {
            const challengeInfo = find.challengeInfos.find((e) => e.challengeDifficulty === challengeDifficulty);
            if (challengeInfo) {
                challengeInfo.challengerRoleId = Player.getInstance().getId(); // 更新挑战者角色ID(我)
                challengeInfo.challengeTime = Time.getInstance().now(); // 更新挑战时间
            }
        }
        this.emit(UnionSiegeStartChallengeRet.prototype.clazzName);

        Combat.getInstance().setPlayerCombatData(
            [{ combatData: myChallengerInfo, hpAtkDefPer: 1 }],
            CombatPlayerType.Self
        );
        const para: number[] = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseDifficulty);
        Combat.getInstance().setPlayerCombatData(
            [{ combatData: rivalChallengerInfo, hpAtkDefPer: para[challengeDifficulty - 1] }],
            CombatPlayerType.Opponent
        );
        UI.getInstance().open("UIDungeonCombatUnionDefense", {
            type: DungeonType.UnionDefense,
            resultCb: (isWin: boolean) => {
                UnionSiege.getInstance().sendUnionSiegeEndChallenge(
                    rivalUnionId,
                    challengeDifficulty,
                    rivalRoleId,
                    activityId,
                    isWin
                );
            },
        });
    }

    /**
     * 开始挑战通知(通知参赛的人)
     * @param data
     */
    private unionSiegeStartChallengeNoticeRet(data: UnionSiegeStartChallengeNoticeRet): void {
        const { errMsg, challengeDifficulty, roleId, rivalRoleId, rivalUnionId, challengeTime } = data;
        if (errMsg !== UnionSiegeStartChallengeNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeStartChallengeNoticeRet[errMsg]);
            return;
        }

        const find = this.getUnionSiegeDetailObjByUnionIdAndPlayerId(rivalUnionId, rivalRoleId); // 对方公会被挑战的人
        if (find) {
            const challengeInfo = find.challengeInfos.find((e) => e.challengeDifficulty === challengeDifficulty);
            if (challengeInfo) {
                challengeInfo.challengerRoleId = roleId; // 更新挑战者角色ID
                challengeInfo.challengeTime = challengeTime; // 更新挑战时间
            }
        }
        this.emit(UnionSiegeStartChallengeNoticeRet.prototype.clazzName);
    }

    /**
     * 结束挑战
     * @param data
     */
    private unionSiegeEndChallengeRet(data: UnionSiegeEndChallengeRet): void {
        const { errMsg } = data;
        if (errMsg !== UnionSiegeEndChallengeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeEndChallengeRet[errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.UnionDefense);
            return;
        }
        const playerId = Player.getInstance().getId();
        const unionInfo = Union.getInstance().getInfo();
        const find = this.getUnionSiegeDetailObjByUnionIdAndPlayerId(unionInfo.id, playerId); // 我的参赛信息
        if (find) {
            find.todayChallengeCount += 1;
        }

        this.emit(UnionSiegeEndChallengeRet.prototype.clazzName, data);
    }

    /**
     * 结束挑战通知(通知参赛的人)
     * @param data
     */
    private unionSiegeEndChallengeNoticeRet(data: UnionSiegeEndChallengeNoticeRet): void {
        const { errMsg, rivalUnionId, challengeDifficulty, rivalRoleId, isWin } = data;
        if (errMsg !== UnionSiegeEndChallengeNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeEndChallengeNoticeRet[errMsg]);
            return;
        }

        const unionInfo = Union.getInstance().getInfo(); // 公会信息
        const findRivalInfo = this.getUnionSiegeDetailObjByUnionIdAndPlayerId(rivalUnionId, rivalRoleId); // 敌方参赛信息
        const findMyUnion = this.getUnionSiegeInfoByUnionId(unionInfo.id); // 我方公会攻城战信息
        const challengeInfo = findRivalInfo.challengeInfos.find((e) => e.challengeDifficulty === challengeDifficulty); // 敌方挑战信息
        const count = this.getStarCount(findRivalInfo.memberType, challengeDifficulty);

        challengeInfo.challengerRoleId = "";
        challengeInfo.isChallengeSuccess = isWin; // 更新挑战结果
        challengeInfo.challengeTime = 0;
        if (isWin) {
            findMyUnion.totalStarCount += count;
            findMyUnion.starTime = Time.getInstance().now();
        } else {
            findRivalInfo.defendSuccessCount += 1;
        }
        this.emit(UnionSiegeEndChallengeNoticeRet.prototype.clazzName);
    }

    /**
     * 挑战扫荡
     * @param data
     */
    private unionSiegeSweetChallengeRet(data: UnionSiegeSweetChallengeRet): void {
        const { errMsg, rivalUnionId, rivalRoleId } = data;
        if (errMsg !== UnionSiegeSweetChallengeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeSweetChallengeRet[errMsg]);
            return;
        }

        const playerId = Player.getInstance().getId();
        const unionInfo = Union.getInstance().getInfo();
        const find = this.getUnionSiegeDetailObjByUnionIdAndPlayerId(unionInfo.id, playerId); // 我的参赛信息
        const findMyUnion = this.getUnionSiegeInfoByUnionId(unionInfo.id); // 公会参赛信息
        if (find) {
            find.todayChallengeCount++; // 个人挑战次数
        }
        if (findMyUnion) {
            findMyUnion.totalStarCount++; // 公会总星数
        }

        // 对决结算
        UI.getInstance().open("FloatActivityUnionDefenseChallengeResult", {
            type: EnumUnionPara.UnionDefenseChallengeSweeps,
            rivalUnionId,
            rivalRoleId,
        });
        this.emit(UnionSiegeSweetChallengeRet.prototype.clazzName, rivalUnionId, rivalRoleId);
    }

    /**
     * 排行榜信息
     * @param data
     */
    private unionSiegeRankRet(data: UnionSiegeRankRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, items, myItem } = data;
        if (errMsg !== UnionSiegeRankRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeRankRet[errMsg]);
            return;
        }
        this.rankItems = items;
        this.rankMyItem = myItem;
        this.emit(UnionSiegeRankRet.prototype.clazzName);
    }

    /**
     * 领取GVG攻防奖励
     * @param data
     */
    private unionSiegeRewardRet(data: UnionSiegeRewardRet): void {
        const { errMsg } = data;
        if (errMsg !== UnionSiegeRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeRewardRet[errMsg]);
            return;
        }
        const details = this.getMyUnionSingeDetailObj();
        if (details) {
            details.isRewarded = true; // 更新领奖期奖励标识
        }

        UI.getInstance().open("FloatActivityUnionDefenseResult"); // 对决结算
        this.emit(UnionSiegeRewardRet.prototype.clazzName);
    }

    /**
     * 领取GVG宝箱
     * @param data
     */
    private unionSiegeBoxRewardRet(data: UnionSiegeBoxRewardRet): void {
        const { errMsg, showItems } = data;
        if (errMsg !== UnionSiegeBoxRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.unionSiegeBoxRewardRet[errMsg]);
            return;
        }
        const playerId = Player.getInstance().getId();
        const unionInfo = Union.getInstance().getInfo();
        const detailObj = this.getUnionSiegeDetailObjByUnionIdAndPlayerId(unionInfo.id, playerId);
        if (detailObj) {
            detailObj.isBoxRewarded = true;
            detailObj.showProps = showItems;
            detailObj.receiveBoxRewardTime = Time.getInstance().now();
        }
        this.emit(UnionSiegeBoxRewardRet.prototype.clazzName, data);
    }

    /**
     * 根据成员类型和难度获取星数
     * @param memberType
     * @param challengeDifficulty
     * @returns
     */
    public getStarCount(memberType: EnumUnionSiegeMemberType, challengeDifficulty: EnumChallengeDifficulty): number {
        return UNION_SIEGE_STAR_COUNT_MAP[memberType]?.[challengeDifficulty] || 0;
    }

    /**
     * 根据成员类型获取他在三种难度下的总星数
     * @param memberType
     * @returns
     */
    public getTotalStarCountByMemberType(memberType: EnumUnionSiegeMemberType): number {
        const difficulties = [
            EnumChallengeDifficulty.Ordinary,
            EnumChallengeDifficulty.Difficulty,
            EnumChallengeDifficulty.Nightmare,
        ];

        return difficulties.reduce((total, difficulty) => {
            return total + UNION_SIEGE_STAR_COUNT_MAP[memberType]?.[difficulty] || 0;
        }, 0);
    }

    /**
     * 获取攻城战信息
     * @returns
     */
    public getUnionSiegeInfo(): IUnionSiegeObj[] {
        return Array.from(this.unionSiegeInfos.values());
    }

    /**
     * 根据公会id获取攻城战信息
     * @param unionId 公会id
     * @returns
     */
    public getUnionSiegeInfoByUnionId(unionId: number): IUnionSiegeObj {
        return this.unionSiegeInfos.get(unionId);
    }

    /**
     * 根据公会Id获取参战成员信息
     * @param unionId 公会id
     * @returns
     */
    public getUnionSiegeDetails(unionId: number): IUnionSiegeDetailObj[] {
        const allInfo = this.getUnionSiegeInfo();
        if (allInfo.length <= 0) {
            return [];
        }
        const info = this.getUnionSiegeInfoByUnionId(unionId);
        return info.siegeDetailInfos;
    }

    /**
     * 获取所有攻城战日志信息
     * @returns
     */
    public getUnionSiegeLogInfo(): IUnionSiegeLogObj[] {
        return this.unionSiegeLogInfos;
    }

    /**
     * 获取我的攻城战日志信息
     * @returns
     */
    public getMyUnionSiegeLogInfo(): IUnionSiegeLogObj[] {
        return this.myUnionSiegeLogInfos;
    }

    /**
     * 获取公会日志数据是否过期
     * @returns
     */
    public isUnionLogDataExpired(): boolean {
        const now = Time.getInstance().now();
        return now - this.requestLogTime > MINUTE_TO_SECOND * LOG_DATA_EXPIRED_TIME * 1000;
    }

    /**
     * 获取参赛所有公会id
     * @returns
     */
    public getALLUnionId(): number[] {
        const data = this.getUnionSiegeInfo();
        return data ? data.map((e) => e.unionId) : [];
    }

    /**
     * 获取公会输赢结果
     * @returns
     */
    public getUnionWinLose(): { unionId: number; isWin: boolean }[] {
        const data = this.getUnionSiegeInfo();
        data.sort((a, b) => {
            if (b.totalStarCount !== a.totalStarCount) {
                return b.totalStarCount - a.totalStarCount;
            } else {
                return a.starTime - b.starTime;
            }
        });

        // 返回结果
        const result = data.map((item) => ({
            unionId: item.unionId,
            isWin: item.unionId === data[0].unionId,
        }));
        return result;
    }

    /**
     * 根据公会id获取总星星数
     * @param unionId
     * @returns
     */
    public getUnionStarCount(unionId: number): number {
        const data = this.getUnionSiegeInfoByUnionId(unionId);
        return data ? data.totalStarCount : 0;
    }

    /**
     * 根据公会id获取总战力
     * @param unionId
     * @returns
     */
    public getUnionPower(unionId: number): number {
        const data = this.getUnionSiegeInfoByUnionId(unionId);
        return data ? data.sumGVGCombat : 0;
    }

    /**
     * 根据公会id获取公会信息
     * @param unionId
     * @returns
     */
    public getUnionInfoByUnionId(unionId: number): IUnionInfo {
        const data = this.getUnionSiegeInfoByUnionId(unionId);
        return data ? data.unionInfo : null;
    }

    /**
     * 根据公会id获取公会会长信息
     * @param unionId
     * @returns
     */
    public getUnionLeaderInfo(unionId: number): IPlayerInfo {
        const data = this.getUnionSiegeInfoByUnionId(unionId);
        return data ? data.leaderInfo : null;
    }

    /**
     * 清除攻城战数据
     */
    public clearUnionSiegeData(): void {
        this.unionSiegeInfos.clear();
        this.unionSiegeLogInfos = [];
        this.myUnionSiegeLogInfos = [];
        this.requestLogTime = 0;
        this.rankItems = [];
        this.rankMyItem = null;
    }

    /**
     * 根据公会id返回战力前4的成员信息
     * @param unionId
     * @returns
     */
    public getTop4UnionDetailsObj(unionId: number): IUnionSiegeDetailObj[] {
        const data = this.getUnionSiegeDetails(unionId);
        return data.filter((member) => member.rank <= 4);
    }

    /**
     * 根据公会id返回所有参赛成员，按排名降序排列
     * @param unionId
     * @returns
     */
    public getAllUnionMembersSortedByRank(unionId: number): IUnionSiegeDetailObj[] {
        const data = this.getUnionSiegeDetails(unionId);
        return data.sort((a, b) => b.rank - a.rank);
    }

    /**
     * 获取排行信息
     * @returns
     */
    public getRankItems(): IRankItem[] {
        return this.rankItems;
    }

    /**
     * 获取我的排行信息
     * @returns
     */
    public getMyRankItem(): IRankItem {
        return this.rankMyItem;
    }

    /**
     * 获取我的参赛信息
     * @returns
     */
    public getMyUnionSingeDetailObj(): IUnionSiegeDetailObj {
        const unionInfo = Union.getInstance().getInfo();
        const data = this.getUnionSiegeDetails(unionInfo.id);
        const playerId = Player.getInstance().getId();
        return data.find((e) => e.playerInfo.playerId === playerId);
    }

    /**
     * 根据公会id和角色id获取参赛信息
     * @param unionId
     * @param playerId
     * @returns
     */
    public getUnionSiegeDetailObjByUnionIdAndPlayerId(unionId: number, playerId: string): IUnionSiegeDetailObj {
        const details = this.getUnionSiegeDetails(unionId);
        return details.find((e) => e.playerInfo.playerId === playerId);
    }

    /**
     * 获取当前公会对决之星星数
     * @param unionId
     * @returns
     */
    public getUnionSiegeAllStarCount(unionId: number): number {
        const unionSiegeInfo = this.getUnionSiegeInfo();
        if (unionSiegeInfo.length <= 0) {
            return 0;
        }
        const unionSiege = unionSiegeInfo.find((e) => e.unionId === unionId);
        let allCount = 0;
        for (const e of unionSiege.siegeDetailInfos) {
            for (const k of e.challengeInfos) {
                const count = this.getStarCount(e.memberType, k.challengeDifficulty);
                if (!k.isChallengeSuccess) {
                    allCount += count; // 未被挑战成功
                }
            }
        }
        return allCount;
    }

    /**
     * 判断本轮本公会是否已轮空
     * @returns
     */
    public isUnionRoundEmpty(): boolean {
        const unionDefenseInfo = this.getUnionSiegeInfo();
        for (const e of unionDefenseInfo) {
            if (e.rivalUnionId === -1) {
                return true; // -1表示本轮公会轮空
            }
        }
        return false;
    }

    /**
     * 判断本轮本人是否已参战
     * @returns
     */
    public isSelfEmpty(): boolean {
        const unionInfo = Union.getInstance().getInfo();
        if (!unionInfo) {
            return false; // 未加入公会
        }
        const unionDefenseInfo = this.getUnionSiegeDetails(unionInfo.id);
        if (!unionDefenseInfo) {
            return false; // 为空整个公会未参赛
        }
        const playerInfo = Player.getInstance().getInfo();
        for (const defense of unionDefenseInfo) {
            if (defense.playerInfo.playerId === playerInfo.playerId) {
                return true; // 找到匹配记录，说明玩家已参战
            }
        }
        return false; // 找不到匹配记录，说明玩家未参战(公会已参战)
    }

    /**
     * 判断本公会是否已参赛
     * @returns
     */
    public isUnionEmpty(): boolean {
        const unionInfo = Union.getInstance().getInfo();
        if (!unionInfo) {
            return false; // 未加入公会
        }
        const unionDefenseInfo = this.getUnionSiegeInfo();
        if (unionDefenseInfo.length <= 0) {
            return false; // 为空整个公会未参赛
        }
        for (const e of unionDefenseInfo) {
            if (e.rivalUnionId === 0) {
                return false; // 为0表示未参赛
            }
        }

        return true;
    }

    /**
     * 判断是否可进入攻城战
     * @param status
     * @returns
     */
    public isCanEnter(status: EnumUnionSiegeStatus): boolean {
        return status !== EnumUnionSiegeStatus.Status8 && status !== EnumUnionSiegeStatus.Status9;
    }

    /**
     * 红点是否显示
     * @param status
     * @returns
     */
    public isRedPoint(status: EnumUnionSiegeStatus): boolean {
        let isShow = false;
        switch (status) {
            case EnumUnionSiegeStatus.Status7:
            case EnumUnionSiegeStatus.Status8:
            case EnumUnionSiegeStatus.Status9:
                isShow = false;
                break;
            default:
                isShow = true;
                break;
        }
        return isShow;
    }

    /**
     * 获取当前联盟攻城战状态
     * @param activityId 活动id
     * @returns UnionSiegeStatus
     */
    public getStatus(activityId: number): EnumUnionSiegeStatus {
        const weekZeroDay = Time.getInstance().getWeekZero(); // 本周零点
        const unionData = TBUnion.getInstance().getValueByPara(EnumUnionPara.UnionDefenseBattleTime); // 获取对决期配置
        const { prepareTime, totalTime } = TBActivity.getInstance().getDataByActivityId(activityId);
        const now = Time.getInstance().now();
        const time1 = weekZeroDay + prepareTime * HOUR_TO_SECOND * 1000; // 准备时间

        if (now < time1) {
            if (now < weekZeroDay + 6 * HOUR_TO_SECOND * 1000) {
                return EnumUnionSiegeStatus.Status8; // 周一六点前不可进入
            }
        }

        const isRoundEmpty = this.isUnionRoundEmpty();
        if (isRoundEmpty) {
            return EnumUnionSiegeStatus.Status6;
        }

        const isSelfEmpty = this.isSelfEmpty();
        if (!isSelfEmpty) {
            return EnumUnionSiegeStatus.Status7;
        }

        const isUnionEmpty = this.isUnionEmpty();
        if (!isUnionEmpty) {
            return EnumUnionSiegeStatus.Status7;
        }

        if (now < time1) {
            return EnumUnionSiegeStatus.Status1; // 匹配
        }

        // 战斗
        const time3 = weekZeroDay + (totalTime - prepareTime) * HOUR_TO_SECOND * 1000;
        if (now > time1 && now < time3) {
            const currentHour = Math.floor((now - weekZeroDay) / (HOUR_TO_SECOND * 1000)) % 24; // 跨天取余
            const zeroHour = 0; // 0点
            if (currentHour >= zeroHour && currentHour < unionData[0]) {
                return EnumUnionSiegeStatus.Status2;
            } else if (currentHour >= unionData[0] && currentHour < unionData[1]) {
                return EnumUnionSiegeStatus.Status3;
            } else {
                return EnumUnionSiegeStatus.Status4;
            }
        }

        const time4 = weekZeroDay + totalTime * HOUR_TO_SECOND * 1000;
        if (now < time4) {
            return EnumUnionSiegeStatus.Status5; // 领奖
        }

        return EnumUnionSiegeStatus.Status9; // 周六日不可进入
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 查询当前公会GVG信息
     */
    public sendUnionSiegeGet(): void {
        const data = UnionSiegeGet.create();
        Socket.getInstance().send(data);
    }

    /**
     * 查询当前公会和个人活跃度
     */
    public sendUnionSiegeGetActive(): void {
        const data = UnionSiegeGetActive.create();
        Socket.getInstance().send(data);
    }

    /**
     * 查询当前公会日志信息
     */
    public sendUnionSiegeGetLog(): void {
        const data = UnionSiegeGetLog.create();
        Socket.getInstance().send(data);
    }

    /**
     * 查询我的日志信息
     */
    public sendUnionSiegeGetMyLog(): void {
        const data = UnionSiegeGetMyLog.create();
        Socket.getInstance().send(data);
    }

    /**
     * 开始挑战
     * @param rivalUnionId
     * @param challengeDifficulty
     * @param rivalRoleId
     * @param activityId
     */
    public sendUnionSiegeStartChallenge(
        rivalUnionId: number,
        challengeDifficulty: number,
        rivalRoleId: string,
        activityId: number
    ): void {
        const data = UnionSiegeStartChallenge.create({
            rivalUnionId,
            challengeDifficulty,
            rivalRoleId,
            activityId,
        });
        Socket.getInstance().send(data);
    }

    /**
     * 结束挑战
     * @param rivalUnionId
     * @param challengeDifficulty
     * @param rivalRoleId
     * @param activityId
     */
    public sendUnionSiegeEndChallenge(
        rivalUnionId: number,
        challengeDifficulty: number,
        rivalRoleId: string,
        activityId: number,
        isWin: boolean
    ): void {
        const data = UnionSiegeEndChallenge.create({
            rivalUnionId,
            challengeDifficulty,
            rivalRoleId,
            activityId,
            isWin,
        });
        Socket.getInstance().send(data);
    }

    /**
     * 挑战扫荡
     * @param rivalUnionId
     * @param rivalRoleId
     * @param activityId
     */
    public sendUnionSiegeSweetChallenge(rivalUnionId: number, rivalRoleId: string, activityId: number): void {
        const data = UnionSiegeSweetChallenge.create({
            rivalUnionId,
            rivalRoleId,
            activityId,
        });
        Socket.getInstance().send(data);
    }

    /**
     * 获取公会对决排行榜信息
     * @param activityId
     */
    public sendUnionSiegeRank(activityId: number): void {
        const data = UnionSiegeRank.create({
            activityId,
        });
        Socket.getInstance().send(data);
    }

    /**
     * 领取GVG攻防奖励
     * @param activityId
     */
    public sendUnionSiegeReward(activityId: number): void {
        const data = UnionSiegeReward.create({
            activityId,
        });
        Socket.getInstance().send(data);
    }

    /**
     * 领取GVG攻防宝箱奖励
     * @param activityId
     */
    public sendUnionSiegeBoxReward(activityId: number): void {
        const data = UnionSiegeBoxReward.create({
            activityId,
        });
        Socket.getInstance().send(data);
    }
}
