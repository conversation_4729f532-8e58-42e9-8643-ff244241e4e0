/*
 * @Author: chenx
 * @Date: 2024-07-23 09:32:48
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:30:30
 */
import DataMainBarrier from "../../data/extend/DataMainBarrier";
import DataMainChapter from "../../data/extend/DataMainChapter";
import DataMainMap from "../../data/extend/DataMainMap";
import TBMonsterBase from "../../data/parser/TBMonsterBase";
import TBMonsterGroup from "../../data/parser/TBMonsterGroup";
import TBPrivilegeConfig, { PRIVILEGE_ID } from "../../data/parser/TBPrivilegeConfig";
import { DungeonType } from "../Combat";
import Player from "../Player";
import Privilege from "../Privilege";
import Setting, { SettingId } from "../Setting";
import { CombatDungeon } from "./CombatDungeon";
import { CombatMemberType } from "./CombatMember";
import CombatMemberMonster from "./CombatMemberMonster";
import { CombatPlayerType } from "./CombatMemberPlayer";

/**
 * 主线数据
 */
export interface ICombatMainData {
    chapterId: number; // 章节id
    chapterInfo: DataMainChapter; // 章节信息
    mapId: number; // 地图id
    mapInfo: DataMainMap; // 地图信息
    levelId: number; // 关卡id
    levelInfo: DataMainBarrier; // 关卡信息
    totalWave: number; // 总波数
    wave: number; // 波数
    limitTime: number; // 限制时间
    refreshTime: number; // 刷新时间-限制时间
    refreshDuration: number; // 刷新时间段-限制时间
    itemDropUuid: number; // 道具掉落uuid
    itemDropUuid2: number[]; // 道具掉落uuid
    gameSpeedRate: number; // 游戏速度倍率
    isCombated: boolean; // 是否已战斗
    isCheckEquipDrop: boolean; // 是否检测装备掉落
}

/**
 * 主线副本
 */
export default class CombatDungeonMain extends CombatDungeon {
    private data: ICombatMainData = null; // 主线数据

    public clear(): void {
        super.clear();

        this.data = null;
    }

    /**
     * 更新游戏速度
     */
    public updateGameSpeed(): void {
        const baseData = this.getBaseData();
        if (Setting.getInstance().getSwitchState(SettingId.DungeonMainGameSpeed)) {
            const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.GAME_SPEED);
            const isEffect = Privilege.getInstance().hasPrivilege(privilegeInfo.id);
            baseData.gameSpeed = isEffect ? privilegeInfo.para.value : 1;
        } else if (Setting.getInstance().getSwitchState(SettingId.DungeonMainGameSpeed2)) {
            const privilegeInfo = TBPrivilegeConfig.getInstance().getDataById(PRIVILEGE_ID.GAME_SPEED_2);
            const isEffect = Privilege.getInstance().hasPrivilege(privilegeInfo.id);
            baseData.gameSpeed = isEffect ? privilegeInfo.para.value : 1;
        } else {
            baseData.gameSpeed = 1;
        }
        baseData.gameSpeed *= this.data.gameSpeedRate;
    }

    /**
     * 初始化战斗
     * @param dungeonType 副本类型
     */
    public initCombat(dungeonType: DungeonType): void {
        this.initBaseData(dungeonType);
        this.initData();
        this.updateGameSpeed();
        this.initPlayer(dungeonType);
    }

    /**
     * 清理战斗
     */
    public clearCombat(): void {
        this.clearBaseData();
        this.clearData();
        this.clearAllPlayer();
        this.clearAllMonster();
    }

    /**
     * 重置战斗
     */
    public resetCombat(): void {
        this.resetAllPlayer();
        this.resetAllMonster();
    }

    /**
     * 初始化主线数据
     */
    private initData(): void {
        this.data = {
            chapterId: -1,
            chapterInfo: null,
            mapId: -1,
            mapInfo: null,
            levelId: -1,
            levelInfo: null,
            totalWave: 0,
            wave: 0,
            limitTime: 0,
            refreshTime: 0,
            refreshDuration: 0.1,
            itemDropUuid: 1,
            itemDropUuid2: [],
            gameSpeedRate: 1,
            isCombated: false,
            isCheckEquipDrop: false,
        };
    }

    /**
     * 清理主线数据
     */
    public clearData(): void {
        this.data = null;
    }

    /**
     * 获取主线数据
     * @returns
     */
    public getData(): ICombatMainData {
        return this.data;
    }

    /**
     * 初始化玩家
     * @param dungeonType 副本类型
     */
    private initPlayer(dungeonType: DungeonType): void {
        const mainBaseData = this.getBaseData();
        const player = this.getPlayer();
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        playerBaseData.uuid = mainBaseData.playerUuid++;
        playerBaseData.type = CombatMemberType.Player;
        playerBaseData.dungeonType = dungeonType;
        playerBaseData.levelId = -1;
        playerBaseData.isAuto = Setting.getInstance().getSwitchState(SettingId.DungeonMainAutoReleaseSkill);

        playerData.type = CombatPlayerType.Self;
        playerData.roleData = Player.getInstance().getInfo();

        player.initMemberData();
        player.updateMemberData();
        player.initAttr();
        player.updateAttrByCul();
        player.updateAttrBySkill();
        player.resetHp();
        player.initSkillCd();
    }

    /**
     * 初始化全部怪兽
     * @param dungeonType 副本类型
     * @param levelId 关卡id
     */
    public initAllMonster(dungeonType: DungeonType, levelId: number): CombatMemberMonster[] {
        const baseData = this.getBaseData();

        const allMonster: CombatMemberMonster[] = [];
        baseData.monsterTotalNum = 0;
        baseData.monsterKillNum = 0;
        baseData.monsterDropData = [];
        const groupInfo = TBMonsterGroup.getInstance().getDataById(this.data.levelInfo.monsterGrouopId);
        groupInfo.monster[this.data.wave - 1].forEach(([monsterId, num]) => {
            const monsterInfo = TBMonsterBase.getInstance().getDataById(monsterId);
            for (let i = 0; i < num; i++) {
                const monster = this.getMonster();
                const monsterBaseData = monster.getBaseData();
                const monsterData = monster.getData();

                monsterBaseData.uuid = baseData.monsterUuid++;
                monsterBaseData.type = CombatMemberType.Monster;
                monsterBaseData.dungeonType = dungeonType;
                monsterBaseData.levelId = levelId;
                monsterBaseData.isAuto = true;

                monsterData.id = monsterId;
                monsterData.info = monsterInfo;

                monster.initSkill();
                monster.initAttr();
                monster.updateAttrByCul();
                monster.updateAttrBySkill();
                monster.resetHp();
                monster.initSkillCd();

                allMonster.push(monster);
            }

            baseData.monsterTotalNum += num;
        });

        return allMonster;
    }
}
