/*
 * @Author: chenx
 * @Date: 2025-02-05 11:52:05
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-24 19:24:20
 */
import TBDungeonCombat from "../../data/parser/TBDungeonCombat";
import Combat, { DungeonType } from "../Combat";
import Player from "../Player";
import Setting from "../Setting";
import { CombatDungeon } from "./CombatDungeon";
import { CombatMemberType } from "./CombatMember";
import { CombatPlayerType } from "./CombatMemberPlayer";

/**
 * pvp数据
 */
export interface ICombatPvpData {
    totalLimitTime: number; // 总限制时间
    limitTime: number; // 限制时间
    refreshTime: number; // 刷新时间-限制时间
    refreshDuration: number; // 刷新时间段-限制时间
}

/**
 * pvp副本
 */
export default class CombatDungeonPvp extends CombatDungeon {
    private data: ICombatPvpData = null; // pvp数据

    public clear(): void {
        super.clear();

        this.data = null;
    }

    /**
     * 初始化战斗
     * @param dungeonType 副本类型
     */
    public initCombat(dungeonType: DungeonType): void {
        this.initBaseData(dungeonType);
        this.initData();
        this.updateGameSpeed();
        this.initPlayer(dungeonType);
        this.initPlayerByOpponent(dungeonType);
    }

    /**
     * 清理战斗
     */
    public clearCombat(): void {
        this.clearBaseData();
        this.clearData();
        this.clearAllPlayer();
    }

    /**
     * 重置战斗
     */
    public resetCombat(): void {
        this.resetAllPlayer();
    }

    /**
     * 初始化pvp数据
     */
    private initData(): void {
        this.data = {
            totalLimitTime: 0,
            limitTime: 0,
            refreshTime: 0,
            refreshDuration: 0.1,
        };
    }

    /**
     * 清理pvp数据
     */
    public clearData(): void {
        this.data = null;
    }

    /**
     * 获取pvp数据
     * @returns
     */
    public getData(): ICombatPvpData {
        return this.data;
    }

    /**
     * 更新游戏速度
     */
    public updateGameSpeed(): void {
        const baseData = this.getBaseData();
        baseData.gameSpeed = 1;
        const combatInfo = TBDungeonCombat.getInstance().getDataByType(baseData.type);
        if (combatInfo.accelerationSwitch) {
            const settingId = Combat.getInstance().getGameSpeedSettingId(baseData.type);
            if (Setting.getInstance().getSwitchState(settingId)) {
                baseData.gameSpeed = combatInfo.battleAcceleration[1];
            }
        }
    }

    /**
     * 初始化玩家
     * @param dungeonType 副本类型
     */
    private initPlayer(dungeonType: DungeonType): void {
        const pvpBaseData = this.getBaseData();
        const player = this.getPlayer();
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        playerBaseData.uuid = pvpBaseData.playerUuid++;
        playerBaseData.type = CombatMemberType.Player;
        playerBaseData.dungeonType = dungeonType;
        playerBaseData.levelId = -1;
        playerBaseData.isAuto = true;

        playerData.type = CombatPlayerType.Self;
        playerData.roleData = Player.getInstance().getInfo();

        player.initMemberData();
        player.updateMemberData();
        player.initAttr();
        player.updateAttrByCul();
        player.updateAttrBySkill();
        player.resetHp();
        player.initSkillCd();
    }

    /**
     * 初始化玩家-对手
     * @param dungeonType 副本类型
     */
    private initPlayerByOpponent(dungeonType: DungeonType): void {
        const pvpBaseData = this.getBaseData();
        const player = this.getPlayer();
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        playerBaseData.uuid = pvpBaseData.playerUuid++;
        playerBaseData.type = CombatMemberType.Player;
        playerBaseData.dungeonType = dungeonType;
        playerBaseData.levelId = -1;
        playerBaseData.isAuto = true;

        switch (playerBaseData.dungeonType) {
            case DungeonType.UnionDefense:
                playerBaseData.hpAtkDefPer = Combat.getInstance().getPlayerHpAtkDefPer(CombatPlayerType.Opponent);
                break;
            default:
                break;
        }

        playerData.type = CombatPlayerType.Opponent;
        const playerCombatData = Combat.getInstance().getPlayerCombatData(playerData.type);
        playerData.roleData = playerCombatData.playerInfo;

        player.initMemberData();
        player.updateMemberData();
        player.initAttr();
        player.updateAttrByCul();
        player.updateAttrBySkill();
        player.resetHp();
        player.initSkillCd();
    }
}
