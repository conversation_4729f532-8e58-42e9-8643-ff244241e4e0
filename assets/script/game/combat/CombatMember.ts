/*
 * @Author: chenx
 * @Date: 2025-01-03 09:09:48
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:41:33
 */
import { EnumAttributeType } from "../../data/base/BaseAttribute";
import { EnumSkillType } from "../../data/base/BaseSkill";
import { IAttr } from "../Attribute";
import { DungeonType } from "../Combat";
import { ICombatBuffData, ICombatSkillData } from "../Skill";
import CombatSkill, { CombatSkillState } from "./CombatSkill";

/**
 * 成员类型
 */
export enum CombatMemberType {
    Player = 1, // 玩家
    Monster = 2, // 怪兽
}

/**
 * 成员状态
 */
export enum CombatMemberState {
    Init = 1, // 初始化
    Load, // 加载
    WaitEnter, // 待入场
    Enter, // 入场
    Wait, // 待机
    Move, // 移动
    Cast, // 施法
    Attack, // 普攻
    Skill, // 技能
    BeAttacked, // 被攻击
    Die, // 死亡
    Put, // 回收
}

/**
 * 成员数据
 */
export interface ICombatMemberData {
    uuid: number; // 成员uuid
    type: CombatMemberType; // 成员类型
    totalHp: number; // 总血量
    hp: number; // 血量
    dungeonType: DungeonType; // 副本类型
    levelId: number; // 关卡id
    isAuto: boolean; // 是否自动-释放技能

    attr: IAttr; // 属性
    hpAtkDefPer: number; // 血攻防百分比
    skill: CombatSkill[]; // 技能
    skillData: ICombatSkillData[]; // 技能数据
    buffData: ICombatBuffData[]; // 增益数据
}

/**
 * 成员
 */
export abstract class CombatMember {
    private baseData: ICombatMemberData = null; // 成员数据

    public clear(): void {
        this.baseData = {
            uuid: -1,
            type: null,
            totalHp: 0,
            hp: 0,
            dungeonType: null,
            levelId: -1,
            isAuto: false,

            attr: null,
            hpAtkDefPer: 1,
            skill: [],
            skillData: [],
            buffData: [],
        };
    }

    /**
     * 获取成员数据
     * @returns
     */
    public getBaseData(): ICombatMemberData {
        return this.baseData;
    }

    /**
     * 初始化成员技能
     * @param skillId 技能id
     * @param memberUuid 成员uuid
     * @param memberId 成员id
     * @param priority 优先级
     * @param isShow 是否显示
     */
    protected initMemberSkill(
        skillId: number,
        memberUuid: number,
        memberId: number,
        priority: number,
        isShow: boolean = false
    ): void {
        const skill = new CombatSkill();
        skill.initData(skillId, this.baseData.dungeonType, memberUuid, memberId, priority, isShow);

        this.baseData.skill.push(skill);

        const skillData = skill.getData();
        if (skillData.info.attachedSkill !== 0) {
            const tempSkill = new CombatSkill();
            tempSkill.initData(
                skillData.info.attachedSkill,
                this.baseData.dungeonType,
                memberUuid,
                memberId,
                priority,
                isShow
            );

            this.baseData.skill.push(tempSkill);
        }
    }

    /**
     * 初始化属性
     */
    public abstract initAttr(): void;

    /**
     * 更新属性-培养
     */
    public abstract updateAttrByCul(): void;

    /**
     * 更新属性-技能
     */
    public abstract updateAttrBySkill(): void;

    /**
     * 重置血量
     */
    public resetHp(): void {
        this.baseData.totalHp = this.baseData.attr[EnumAttributeType.FinalHp].value;
        this.baseData.hp = this.baseData.totalHp;
    }

    /**
     * 初始化技能cd
     */
    public initSkillCd(): void {
        this.baseData.skill.forEach(
            (e) => e.getData().state === CombatSkillState.Init && e.resetCd(this.baseData.attr)
        );
    }

    /**
     * 更新技能cd
     */
    public updateSkillCd(): void {
        this.baseData.skill.forEach(
            (e) => e.getData().state !== CombatSkillState.Init && e.updateCd(this.baseData.attr)
        );
    }

    /**
     * 重置技能cd
     */
    public resetSkillCd(): void {
        this.baseData.skill.forEach((e) => e.resetCd(this.baseData.attr));
    }

    /**
     * 是否可攻击
     * @returns
     */
    public abstract isAttack(): boolean;

    /**
     * 是否可释放技能
     * @param skillType 技能类型
     * @param memberId 成员id
     * @returns
     */
    public abstract isReleaseSkill(skillType: EnumSkillType, memberId: number): boolean;
}
