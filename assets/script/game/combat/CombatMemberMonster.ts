/*
 * @Author: chenx
 * @Date: 2024-12-05 11:12:12
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-07 17:04:31
 */
import { EnumSkillType } from "../../data/base/BaseSkill";
import DataMonsterBase from "../../data/extend/DataMonsterBase";
import Attribute from "../Attribute";
import { CombatMember, CombatMemberState } from "./CombatMember";

/**
 * 怪兽数据
 */
export interface ICombatMonsterData {
    state: CombatMemberState; // 状态

    id: number; // 怪兽id
    info: DataMonsterBase; // 信息

    initPos: cc.Vec2; // 初始位置
    targetPos: cc.Vec2; // 目标位置
    scaleX: number; // 缩放-x
    angle: number; // 角度
    enterDelay: number; // 入场延迟
}

/**
 * 怪兽
 */
export default class CombatMemberMonster extends CombatMember {
    private data: ICombatMonsterData = null; // 怪兽数据

    public constructor() {
        super();

        this.clear();
    }

    public clear(): void {
        super.clear();

        this.data = {
            state: CombatMemberState.Init,

            id: -1,
            info: null,

            initPos: null,
            targetPos: null,
            scaleX: 1,
            angle: 0,
            enterDelay: 0,
        };
    }

    /**
     * 获取怪兽数据
     * @returns
     */
    public getData(): ICombatMonsterData {
        return this.data;
    }

    /**
     * 初始化技能
     */
    public initSkill(): void {
        const baseData = this.getBaseData();

        this.data.info.skill.forEach((e, i) => this.initMemberSkill(e, baseData.uuid, this.data.id, 1 + i));
        this.data.info.attackId !== 0 && this.initMemberSkill(this.data.info.attackId, baseData.uuid, this.data.id, -1);
    }

    /**
     * 初始化属性
     */
    public initAttr(): void {
        Attribute.getInstance().initMonsterAttr(this);
    }

    /**
     * 更新属性-培养
     */
    public updateAttrByCul(): void {
        Attribute.getInstance().updateMonsterAttrByCul(this);
    }

    /**
     * 更新属性-技能
     */
    public updateAttrBySkill(): void {
        Attribute.getInstance().updateMonsterAttrBySkill(this);
    }

    /**
     * 是否可攻击
     * @returns
     */
    public isAttack(): boolean {
        const baseData = this.getBaseData();

        let isAttack = this.data.id !== -1;
        isAttack =
            isAttack &&
            (this.data.state === CombatMemberState.Move ||
                this.data.state === CombatMemberState.Wait ||
                this.data.state === CombatMemberState.Cast ||
                this.data.state === CombatMemberState.BeAttacked);
        isAttack = isAttack && baseData.hp > 0;

        return isAttack;
    }

    /**
     * 是否可释放技能
     * @param skillType 技能类型
     * @returns
     */
    public isReleaseSkill(skillType: EnumSkillType): boolean {
        let isRelease = true;
        switch (skillType) {
            case EnumSkillType.MonsterASkill:
                isRelease = this.data.state === CombatMemberState.Move || this.data.state === CombatMemberState.Wait;
                break;
            case EnumSkillType.MonsterAtk:
                isRelease = this.data.state === CombatMemberState.Wait;
                break;
            default:
                break;
        }

        return isRelease;
    }
}
