/*
 * @Author: chenx
 * @Date: 2024-07-23 09:31:39
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-29 15:44:18
 */
import Logic from "../../../nsn/core/Logic";
import Combat, { CombatEvent, DungeonType } from "../Combat";
import Skill, { ICombatBuffData, ICombatSkillData, SkillEvent } from "../Skill";
import CombatMemberMonster from "./CombatMemberMonster";
import CombatMemberPlayer from "./CombatMemberPlayer";

/**
 * 副本数据
 */
export interface ICombatDungeonData {
    state: CombatDungeonState; // 状态

    type: DungeonType; // 类型
    gameSpeed: number; // 游戏速度
    skipCombatTime: number; // 跳过战斗帧时间
    resetTime: number; // 重置时间
    playerUuid: number; // 玩家uuid
    monsterUuid: number; // 怪兽uuid
    showUuid: number; // 表现uuid
    damageUuid: number; // 伤害uuid
    monsterTotalNum: number; // 怪兽总数
    monsterKillNum: number; // 怪兽击杀数
    monsterDropData: [number, cc.Vec2, number][]; // 怪兽掉落数据
    isPlayEnterAni: boolean; // 是否播放入场动画
    isSkipCombat: boolean; // 是否可跳过战斗
    isSkipingCombat: boolean; // 是否正在跳过战斗
    combatTime: number; // 战斗时间
}

/**
 * 副本状态
 */
export enum CombatDungeonState {
    Init = 1, // 初始化
    Ready, // 准备
    Load, // 加载
    Enter, // 入场
    Combat, // 战斗
    Pause, // 暂停
    Success, // 成功
    Failure, // 失败
    Reset, // 重置
}

/**
 * 副本
 */
export abstract class CombatDungeon extends Logic {
    private baseData: ICombatDungeonData = null; // 副本数据

    private player: CombatMemberPlayer[] = []; // 玩家
    private playerPool: CombatMemberPlayer[] = []; // 玩家-对象池

    private monster: CombatMemberMonster[] = []; // 怪兽
    private monsterPool: CombatMemberMonster[] = []; // 怪兽-对象池

    public clear(): void {
        this.baseData = null;

        this.player = [];
        this.playerPool = [];

        this.monster = [];
        this.monsterPool = [];
    }

    /**
     * 初始化战斗
     * @param dungeonType 副本类型
     * @param levelId 关卡id
     */
    public abstract initCombat(dungeonType: DungeonType, levelId: number): void;

    /**
     * 清理战斗
     */
    public abstract clearCombat(): void;

    /**
     * 重置战斗
     */
    public abstract resetCombat(): void;

    /**
     * 初始化副本数据
     * @param type 类型
     */
    public initBaseData(type: DungeonType): void {
        this.baseData = {
            state: CombatDungeonState.Init,

            type,
            gameSpeed: 1,
            skipCombatTime: 0.1,
            resetTime: 0,
            playerUuid: 1,
            monsterUuid: 1001,
            showUuid: 1,
            damageUuid: 1,
            monsterTotalNum: 0,
            monsterKillNum: 0,
            monsterDropData: [],
            isPlayEnterAni: false,
            isSkipCombat: false,
            isSkipingCombat: false,
            combatTime: 0,
        };
    }

    /**
     * 清理副本数据
     */
    public clearBaseData(): void {
        this.baseData = null;
    }

    /**
     * 获取副本数据
     * @returns
     */
    public getBaseData(): ICombatDungeonData {
        return this.baseData;
    }

    /**
     * 更新游戏速度
     */
    public abstract updateGameSpeed(): void;

    /**
     * 获取全部玩家
     * @returns
     */
    public getAllPlayer(): CombatMemberPlayer[] {
        return this.player;
    }

    /**
     * 获取玩家
     * @returns
     */
    public getPlayer(): CombatMemberPlayer {
        let player: CombatMemberPlayer = null;
        if (this.playerPool.length !== 0) {
            player = this.playerPool.shift();
        } else {
            player = new CombatMemberPlayer();
        }
        this.player.push(player);

        return player;
    }

    /**
     * 重置全部玩家
     */
    public resetAllPlayer(): void {
        let skillData: ICombatSkillData[] = [];
        let buffData: ICombatBuffData[] = [];
        this.player.forEach((e) => {
            const playerBaseData = e.getBaseData();
            const playerData = e.getData();

            skillData = skillData.concat(playerBaseData.skillData);
            playerBaseData.skillData = [];
            buffData = buffData.concat(playerBaseData.buffData);
            playerBaseData.buffData = [];

            playerData.damage = 0;
            playerData.dps = 0;
            playerData.maxDps = 0;
            playerData.skillCastTime = {};
            playerData.skillReleaseTime = {};
            playerData.skillDamage = {};

            e.updateAttrBySkill();
            e.resetHp();
            e.resetSkillCd();
        });

        if (skillData.length !== 0 || buffData.length !== 0) {
            Skill.getInstance().emit(SkillEvent.ClearShow, this.baseData.type, skillData, buffData);
        }
    }

    /**
     * 清理全部玩家
     */
    public clearAllPlayer(): void {
        this.player.forEach((e) => {
            e.clear();
            this.playerPool.push(e);
        });
        this.player = [];
    }

    /**
     * 设置怪物
     * @param monster 怪物
     */
    public setMonster(monster: CombatMemberMonster): void {
        this.monster.push(monster);
    }

    /**
     * 获取全部怪兽
     * @returns
     */
    public getAllMonster(): CombatMemberMonster[] {
        return this.monster;
    }

    /**
     * 获取怪兽
     * @returns
     */
    public getMonster(): CombatMemberMonster {
        let monster: CombatMemberMonster = null;
        if (this.monsterPool.length !== 0) {
            monster = this.monsterPool.shift();
        } else {
            monster = new CombatMemberMonster();
        }

        return monster;
    }

    /**
     * 重置全部怪兽
     */
    public resetAllMonster(): void {
        const monsterUuid: number[] = [];
        let skillData: ICombatSkillData[] = [];
        let buffData: ICombatBuffData[] = [];
        this.monster.forEach((e) => {
            const monsterBaseData = e.getBaseData();

            monsterUuid.push(monsterBaseData.uuid);
            skillData = skillData.concat(monsterBaseData.skillData);
            buffData = buffData.concat(monsterBaseData.buffData);

            e.clear();
            this.monsterPool.push(e);
        });
        this.monster = [];

        monsterUuid.length !== 0 &&
            Combat.getInstance().emit(CombatEvent.ResetMonster, this.baseData.type, monsterUuid);
        (skillData.length !== 0 || buffData.length !== 0) &&
            Skill.getInstance().emit(SkillEvent.ClearShow, this.baseData.type, skillData, buffData);
    }

    /**
     * 清理全部怪兽
     */
    public clearAllMonster(): void {
        this.monster.forEach((e) => {
            e.clear();
            this.monsterPool.push(e);
        });
        this.monster = [];
    }

    /**
     * 清理怪兽
     * @param monster 怪兽
     */
    public clearMonster(monster: CombatMemberMonster): void {
        monster.clear();
        this.monsterPool.push(monster);
    }
}
