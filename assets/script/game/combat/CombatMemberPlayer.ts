/*
 * @Author: chenx
 * @Date: 2024-07-23 09:47:55
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-29 09:17:11
 */
import Audio from "../../../nsn/audio/Audio";
import {
    BattleCellInfo,
    BattleObj,
    IBattleCellInfo,
    IBattleObj,
    IChallengerInfo,
    IMagicalBattleObj,
    IPlayerInfo,
    MagicalBattleObj,
} from "../../../protobuf/proto";
import { EnumExpandType } from "../../data/base/BaseExpand";
import { EnumMagicSkillType } from "../../data/base/BaseMagicSkill";
import { EnumSkillType } from "../../data/base/BaseSkill";
import DataArcher from "../../data/extend/DataArcher";
import DataArrow from "../../data/extend/DataArrow";
import DataDungeonTest from "../../data/extend/DataDungeonTest";
import DataLeadSkin from "../../data/extend/DataLeadSkin";
import DataMagicSkill from "../../data/extend/DataMagicSkill";
import DataPet from "../../data/extend/DataPet";
import DataTank from "../../data/extend/DataTank";
import DataWeapon from "../../data/extend/DataWeapon";
import DataWing from "../../data/extend/DataWing";
import TBArcher from "../../data/parser/TBArcher";
import TBArrow from "../../data/parser/TBArrow";
import TBDungeonTest from "../../data/parser/TBDungeonTest";
import TBExpand from "../../data/parser/TBExpand";
import TBLeadSkin from "../../data/parser/TBLeadSkin";
import TBMagicSkill from "../../data/parser/TBMagicSkill";
import TBPet from "../../data/parser/TBPet";
import TBTank from "../../data/parser/TBTank";
import TBWeapon from "../../data/parser/TBWeapon";
import TBWing from "../../data/parser/TBWing";
import Archer from "../Archer";
import Attribute from "../Attribute";
import Combat, { DungeonType } from "../Combat";
import CombatLog, { CombatLogId } from "../CombatLog";
import LeadSkin from "../LeadSkin";
import Magical from "../Magical";
import MakeArrow from "../MakeArrow";
import Pet from "../Pet";
import Skill, { ICombatSkillData, SkillEvent } from "../Skill";
import Tank from "../Tank";
import Weapon from "../Weapon";
import Wing from "../Wing";
import { CombatMember, CombatMemberState } from "./CombatMember";

/**
 * 玩家数据
 */
export interface ICombatPlayerData {
    type: CombatPlayerType; // 类型
    roleData: IPlayerInfo; // 角色数据
    damage: number; // 伤害
    dps: number; // 每秒伤害
    maxDps: number; // 最大每秒伤害
    skillCastTime: { [skillId: number]: number[] }; // 技能施法时间
    skillReleaseTime: { [skillId: number]: number[] }; // 技能释放时间
    skillDamage: { [skillId: number]: number }; // 技能伤害

    leadData: ICombatLeadData; // 主角数据
    weaponData: ICombatWeaponData; // 神器数据
    wingData: ICombatWingData; // 背饰数据
    tankData: ICombatTankData; // 战车数据
    archerData: ICombatArcherData[]; // 弓箭手数据
    petData: ICombatPetData; // 宠物数据
    magicData: ICombatMagicData[]; // 魔法数据
}

/**
 * 玩家类型
 */
export enum CombatPlayerType {
    Self = 1, // 自己
    Opponent = 2, // 对手
}

/**
 * 主角数据
 */
export interface ICombatLeadData {
    state: CombatMemberState; // 状态

    id: number; // 主角id
    info: DataLeadSkin; // 信息
    dungeonType: DungeonType; // 副本类型
}

/**
 * 神器数据
 */
export interface ICombatWeaponData {
    state: CombatMemberState; // 状态

    id: number; // 神器id
    info: DataWeapon; // 信息
    showId: number; // 展示神器id
    showInfo: DataWeapon; // 展示信息
}

/**
 * 背饰数据
 */
export interface ICombatWingData {
    state: CombatMemberState; // 状态

    id: number; // 背饰id
    info: DataWing; // 信息
    showId: number; // 展示背饰id
    showInfo: DataWing; // 展示信息
}

/**
 * 战车数据
 */
export interface ICombatTankData {
    state: CombatMemberState; // 状态

    id: number; // 战车id
    info: DataTank; // 信息
    dungeonType: DungeonType; // 副本类型
}

/**
 * 弓箭手数据
 */
export interface ICombatArcherData {
    state: CombatMemberState; // 状态

    id: number; // 弓箭手id
    info: DataArcher; // 信息
    gridId: number; // 弓箭手格id
    arrowId: number; // 弓箭id
    arrowInfo: DataArrow; // 弓箭信息
}

/**
 * 宠物数据
 */
export interface ICombatPetData {
    state: CombatMemberState; // 状态

    id: number; // 宠物id
    info: DataPet; // 信息
}

/**
 * 魔法数据
 */
export interface ICombatMagicData {
    id: number; // 魔法id
    info: DataMagicSkill; // 信息
    gridId: number; // 魔法格id
}

/**
 * 玩家
 */
export default class CombatMemberPlayer extends CombatMember {
    private data: ICombatPlayerData = null; // 玩家数据

    public constructor() {
        super();

        this.clear();
    }

    public clear(): void {
        super.clear();

        this.data = {
            type: null,
            roleData: null,
            damage: 0,
            dps: 0,
            maxDps: 0,
            skillCastTime: {},
            skillReleaseTime: {},
            skillDamage: {},

            leadData: null,
            weaponData: null,
            wingData: null,
            tankData: null,
            archerData: [],
            petData: null,
            magicData: [],
        };
    }

    /**
     * 获取玩家数据
     * @returns
     */
    public getData(): ICombatPlayerData {
        return this.data;
    }

    /**
     * 初始化成员数据
     */
    public initMemberData(): void {
        this.initLeadData();
        this.initWeaponData();
        this.initWingData();
        this.initTankData();
        this.initArcherData();
        this.initPetData();
        this.initMagicData();
    }

    /**
     * 更新成员数据
     */
    public updateMemberData(): void {
        let combatData: IChallengerInfo = null;
        let levelInfo: DataDungeonTest = null;
        const baseData = this.getBaseData();
        switch (this.data.type) {
            case CombatPlayerType.Self:
                switch (baseData.dungeonType) {
                    case DungeonType.UnionDefense:
                        combatData = Combat.getInstance().getPlayerCombatData(this.data.type);
                        break;
                    case DungeonType.Test:
                        levelInfo = TBDungeonTest.getInstance().getDataById(baseData.levelId);
                        break;
                    default:
                        break;
                }
                break;
            case CombatPlayerType.Opponent:
                combatData = Combat.getInstance().getPlayerCombatData(this.data.type);
                break;
            default:
                break;
        }
        if (combatData) {
            this.updateLeadData(combatData.leadSkinGroupInfo.dressSkinId);
            this.updateWeaponData(combatData.weaponGroupInfo.magicalId, combatData.weaponGroupInfo.isShow);
            this.updateWingData(combatData.wingsGroupInfo.lineupWingId, combatData.wingsGroupInfo.isShow);
            this.updateTankData(combatData.tanksInfo.magicalId);
            this.updateArcherData(combatData.archerGroupInfo.battleInfos);
            this.updateArrowData(combatData.ArrowGroupInfo.battleCellInfos);
            this.updatePetData(
                combatData.petGroupInfo.battleInfos.length !== 0
                    ? combatData.petGroupInfo.petInfos.find(
                          (e) => e.uuid === combatData.petGroupInfo.battleInfos[0].uuid
                      ).petId
                    : -1
            );
            this.updateMagicData(combatData.magicalGroupInfo.magicalBattleInfos);
        } else if (levelInfo) {
            this.updateLeadData(levelInfo.lead[0]);
            this.updateWeaponData(levelInfo.weapon[0], levelInfo.weapon[0] !== -1);
            this.updateWingData(levelInfo.wing[0], levelInfo.wing[0] !== -1);
            this.updateTankData(levelInfo.tank[0]);
            const archerData: IBattleObj[] = [];
            const arrowData: IBattleCellInfo[] = [];
            levelInfo.archer.forEach(([archerId, , , arrowId], i) => {
                archerData.push(BattleObj.create({ cellId: i + 1, archerId }));
                arrowData.push(BattleCellInfo.create({ id: i + 1, level: arrowId }));
            });
            this.updateArcherData(archerData);
            this.updateArrowData(arrowData);
            this.updatePetData(levelInfo.pet[0]);
            const magicalData: IMagicalBattleObj[] = [];
            levelInfo.magic.forEach(([magicalId, , , gridId]) => {
                magicalData.push(MagicalBattleObj.create({ cellId: gridId, magicalId, expandId: -1 }));
            });
            this.updateMagicData(magicalData);
        } else {
            this.updateLeadData(LeadSkin.getInstance().getId());
            this.updateWeaponData(Weapon.getInstance().getTeamId(), Weapon.getInstance().isShowWeapon());
            this.updateWingData(Wing.getInstance().getTeamId(), Wing.getInstance().isShowWing());
            this.updateTankData(Tank.getInstance().getTeamId());
            this.updateArcherData(Archer.getInstance().getTeam());
            this.updateArrowData(MakeArrow.getInstance().getArcherGridDataArr());
            this.updatePetData(Pet.getInstance().getBattleId());
            this.updateMagicData(Magical.getInstance().getSkillBattleInfo());
        }
    }

    /**
     * 清理成员技能
     * @param memberId 成员id
     */
    private clearMemberSkill(memberId: number): void {
        const baseData = this.getBaseData();

        const clearSkillData: ICombatSkillData[] = [];
        const clearSkillUuid: string[] = [];
        const clearAudioId: number[] = [];
        for (let i = baseData.skill.length - 1; i >= 0; i--) {
            const skill = baseData.skill[i];
            const skillData = skill.getData();

            if (skillData.memberId !== memberId) {
                continue;
            }

            for (let j = baseData.skillData.length - 1; j >= 0; j--) {
                const tempSkillData = baseData.skillData[j];

                if (tempSkillData.id !== skillData.id) {
                    continue;
                }

                clearSkillData.push(tempSkillData);
                baseData.skillData.splice(j, 1);
            }

            for (let j = baseData.buffData.length - 1; j >= 0; j--) {
                const buffData = baseData.buffData[j];

                for (let k = buffData.layerData.length - 1; k >= 0; k--) {
                    if (buffData.layerData[k][1] === skillData.id) {
                        buffData.layerData.splice(k, 1);
                    }
                }

                for (let k = buffData.maxLayerData.length - 1; k >= 0; k--) {
                    if (buffData.maxLayerData[k][2] === skillData.id) {
                        buffData.maxLayerData.splice(k, 1);
                    }
                }

                for (let k = buffData.addMaxLayerData.length - 1; k >= 0; k--) {
                    if (buffData.addMaxLayerData[k][2] === skillData.id) {
                        buffData.addMaxLayerData.splice(k, 1);
                    }
                }
            }

            skillData.isShow && clearSkillUuid.push(`${baseData.uuid}#${skillData.id}`);
            clearAudioId.push(...skillData.audioId);
            skill.clear();
            baseData.skill.splice(i, 1);
        }
        Skill.getInstance().emit(SkillEvent.ClearShow, baseData.dungeonType, clearSkillData, []);
        Skill.getInstance().emit(SkillEvent.ClearSkill, baseData.dungeonType, clearSkillUuid);
        clearAudioId.forEach((e) => Audio.getInstance().stopEffect(e));
    }

    /**
     * 初始化主角数据
     */
    private initLeadData(): void {
        const baseData = this.getBaseData();

        this.data.leadData = {
            state: CombatMemberState.Init,

            id: -1,
            info: null,
            dungeonType: baseData.dungeonType,
        };
    }

    /**
     * 更新主角数据
     * @param id 主角id
     */
    public updateLeadData(id: number): void {
        const baseData = this.getBaseData();

        this.data.leadData.id !== -1 && this.clearMemberSkill(this.data.leadData.id);

        this.data.leadData.id = id;
        this.data.leadData.info = TBLeadSkin.getInstance().getDataById(this.data.leadData.id);

        let isShow = false;
        switch (this.data.type) {
            case CombatPlayerType.Self:
                isShow = true;
                break;
            case CombatPlayerType.Opponent:
                isShow = false;
                break;
            default:
                break;
        }
        isShow = isShow || CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow);
        this.data.leadData.info.skillId.forEach((e, i) =>
            this.initMemberSkill(e, baseData.uuid, this.data.leadData.id, 1 + i, isShow)
        );
        this.initMemberSkill(
            this.data.leadData.info.attackId,
            baseData.uuid,
            this.data.leadData.id,
            -1,
            CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow)
        );
    }

    /**
     * 初始化神器数据
     */
    private initWeaponData(): void {
        this.data.weaponData = {
            state: CombatMemberState.Init,

            id: -1,
            info: null,
            showId: -1,
            showInfo: null,
        };
    }

    /**
     * 更新神器数据
     * @param weaponId 神器id
     * @param isShow 是否显示
     */
    public updateWeaponData(weaponId: number, isShow: boolean): void {
        const baseData = this.getBaseData();

        if (weaponId !== -1) {
            this.data.weaponData.id !== -1 && this.clearMemberSkill(this.data.weaponData.id);

            this.data.weaponData.id = weaponId;
            this.data.weaponData.info = TBWeapon.getInstance().getDataById(this.data.weaponData.id);

            this.data.weaponData.info.skillId.forEach((e) =>
                this.initMemberSkill(
                    e,
                    baseData.uuid,
                    this.data.weaponData.id,
                    -1,
                    CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow)
                )
            );
        }
        if (isShow) {
            this.data.weaponData.showId = this.data.weaponData.id;
            this.data.weaponData.showInfo = this.data.weaponData.info;
        } else {
            this.data.weaponData.showId = this.data.leadData.info.initialWeapon;
            this.data.weaponData.showInfo = TBWeapon.getInstance().getDataById(this.data.weaponData.showId);
        }
    }

    /**
     * 初始化背饰数据
     */
    private initWingData(): void {
        this.data.wingData = {
            state: CombatMemberState.Init,

            id: -1,
            info: null,
            showId: -1,
            showInfo: null,
        };
    }

    /**
     * 更新背饰数据
     * @param wingId 背饰id
     * @param isShow 是否显示
     */
    public updateWingData(wingId: number, isShow: boolean): void {
        const baseData = this.getBaseData();

        if (wingId !== -1 && wingId !== 0) {
            this.data.wingData.id !== -1 && this.clearMemberSkill(this.data.wingData.id);

            this.data.wingData.id = wingId;
            this.data.wingData.info = TBWing.getInstance().getDataById(this.data.wingData.id);

            this.data.wingData.info.skillId.forEach((e) =>
                this.initMemberSkill(
                    e,
                    baseData.uuid,
                    this.data.wingData.id,
                    -1,
                    CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow)
                )
            );
        }
        if (isShow) {
            this.data.wingData.showId = this.data.wingData.id;
            this.data.wingData.showInfo = this.data.wingData.info;
        } else {
            this.data.wingData.showId = -1;
            this.data.wingData.showInfo = null;
        }
    }

    /**
     * 初始化战车数据
     */
    private initTankData(): void {
        const baseData = this.getBaseData();

        this.data.tankData = {
            state: CombatMemberState.Init,

            id: -1,
            info: null,
            dungeonType: baseData.dungeonType,
        };
    }

    /**
     * 更新战车数据
     * @param tankId 战车id
     */
    public updateTankData(tankId: number): void {
        const baseData = this.getBaseData();

        this.data.tankData.id !== -1 && this.clearMemberSkill(this.data.tankData.id);

        this.data.tankData.id = tankId;
        this.data.tankData.info = TBTank.getInstance().getDataById(this.data.tankData.id);

        this.data.tankData.info.skillId.forEach((e) =>
            this.initMemberSkill(
                e,
                baseData.uuid,
                this.data.tankData.id,
                -1,
                CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow)
            )
        );
    }

    /**
     * 初始化弓箭手数据
     */
    private initArcherData(): void {
        const expandInfo = TBExpand.getInstance().getDataByType(EnumExpandType.ArcherTeamPositionUnlock);
        let gridId = 1;
        expandInfo.forEach((e) => {
            for (let i = 0; i < e.expandCount; i++) {
                const archerData: ICombatArcherData = {
                    state: CombatMemberState.Init,

                    id: -1,
                    info: null,
                    gridId: gridId++,
                    arrowId: -1,
                    arrowInfo: null,
                };

                this.data.archerData.push(archerData);
            }
        });
    }

    /**
     * 更新弓箭手数据
     * @param battleData 上阵数据
     */
    public updateArcherData(battleData: IBattleObj[]): void {
        const baseData = this.getBaseData();

        const archerId: number[] = [];
        this.data.archerData.forEach((e) => e.id !== -1 && archerId.push(e.id));
        this.data.archerData.forEach((e) => {
            if (e.id !== -1 && battleData.findIndex((e2) => e2.archerId === e.id) === -1) {
                this.clearMemberSkill(e.id);
            }

            const tempBattleData = battleData.find((e2) => e2.cellId === e.gridId);
            e.id = tempBattleData ? tempBattleData.archerId : -1;
            e.info = e.id !== -1 ? TBArcher.getInstance().getDataById(e.id) : null;

            if (e.id !== -1 && archerId.findIndex((e2) => e2 === e.id) === -1) {
                e.info.skillId.forEach((e2) =>
                    this.initMemberSkill(
                        e2,
                        baseData.uuid,
                        e.id,
                        -1,
                        CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow)
                    )
                );
                this.initMemberSkill(
                    e.info.attackId,
                    baseData.uuid,
                    e.id,
                    -1,
                    CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow)
                );
            }
        });
    }

    /**
     * 更新弓箭数据
     * @param gridData 弓箭手格数据
     * @returns
     */
    public updateArrowData(gridData: IBattleCellInfo[]): boolean {
        let isUpdate = false;
        this.data.archerData.forEach((e) => {
            const tempArrowId = e.arrowId;
            const tempGridData = gridData.find((e2) => e2.id === e.gridId);
            if (tempGridData) {
                e.arrowId = tempGridData.level !== 0 ? tempGridData.level : -1;
            } else {
                e.arrowId = -1;
            }
            e.arrowId !== tempArrowId && (isUpdate = true);
            e.arrowInfo = e.arrowId !== -1 ? TBArrow.getInstance().getDataById(e.arrowId) : null;
        });

        return isUpdate;
    }

    /**
     * 初始化宠物数据
     */
    private initPetData(): void {
        this.data.petData = {
            state: CombatMemberState.Init,

            id: -1,
            info: null,
        };
    }

    /**
     * 更新宠物数据
     * @param petId 宠物id
     */
    public updatePetData(petId: number): void {
        const baseData = this.getBaseData();

        this.data.petData.id !== -1 && this.clearMemberSkill(this.data.petData.id);

        this.data.petData.id = petId;
        this.data.petData.info = this.data.petData.id ? TBPet.getInstance().getDataById(this.data.petData.id) : null;

        if (this.data.petData.id !== -1 && this.data.petData.info.skillId !== 0) {
            this.initMemberSkill(
                this.data.petData.info.skillId,
                baseData.uuid,
                this.data.petData.id,
                -1,
                CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow)
            );
        }
    }

    /**
     * 初始化魔法数据
     */
    private initMagicData(): void {
        const expandInfo = TBExpand.getInstance().getDataByType(EnumExpandType.InitiativeMagicUnlock);
        let gridId = 1;
        expandInfo.forEach((e) => {
            for (let i = 0; i < e.expandCount; i++) {
                const magicData: ICombatMagicData = {
                    id: -1,
                    info: null,
                    gridId: gridId++,
                };

                this.data.magicData.push(magicData);
            }
        });

        const expandInfo2 = TBExpand.getInstance().getDataByType(EnumExpandType.PassiveMagicUnlock);
        let gridId2 = 11;
        expandInfo2.forEach((e) => {
            for (let i = 0; i < e.expandCount; i++) {
                const magicData: ICombatMagicData = {
                    id: -1,
                    info: null,
                    gridId: gridId2++,
                };

                this.data.magicData.push(magicData);
            }
        });
    }

    /**
     * 更新魔法数据
     * @param battleData 上阵数据
     */
    public updateMagicData(battleData: IMagicalBattleObj[]): void {
        const baseData = this.getBaseData();

        const magicId: number[] = [];
        this.data.magicData.forEach((e) => e.id !== -1 && magicId.push(e.id));
        this.data.magicData.forEach((e) => {
            if (e.id !== -1 && battleData.findIndex((e2) => e2.magicalId === e.id) === -1) {
                this.clearMemberSkill(e.id);
            }

            const tempBattleData = battleData.find((e2) => e2.cellId === e.gridId);
            e.id = tempBattleData ? tempBattleData.magicalId : -1;
            e.info = e.id !== -1 ? TBMagicSkill.getInstance().getDataById(e.id) : null;

            if (e.id !== -1 && magicId.findIndex((e2) => e2 === e.id) === -1) {
                let isShow = false;
                switch (this.data.type) {
                    case CombatPlayerType.Self:
                        isShow = e.info.type === EnumMagicSkillType.Initiative;
                        break;
                    case CombatPlayerType.Opponent:
                        isShow = false;
                        break;
                    default:
                        break;
                }
                isShow = isShow || CombatLog.getInstance().getShowState(CombatLogId.PlayerSkillShow);
                this.initMemberSkill(e.info.skillId, baseData.uuid, e.id, 1000 + e.gridId, isShow);
            }
        });
    }

    /**
     * 初始化属性
     */
    public initAttr(): void {
        Attribute.getInstance().initPlayerAttr(this);
    }

    /**
     * 更新属性-培养
     */
    public updateAttrByCul(): void {
        Attribute.getInstance().updatePlayerAttrByCul(this);
    }

    /**
     * 更新属性-技能
     */
    public updateAttrBySkill(): void {
        Attribute.getInstance().updatePlayerAttrBySkill(this);
    }

    /**
     * 汇总属性-战斗力
     */
    public sumAttrByScore(): void {
        Attribute.getInstance().sumAttrByScore(this.getBaseData().attr);
    }

    /**
     * 是否可攻击
     * @returns
     */
    public isAttack(): boolean {
        return true;
    }

    /**
     * 是否可释放技能
     * @param skillType 技能类型
     * @param memberId 成员id
     * @returns
     */
    public isReleaseSkill(skillType: EnumSkillType, memberId: number): boolean {
        let isRelease = true;
        switch (skillType) {
            case EnumSkillType.LeadASkill:
            case EnumSkillType.LeadAtk:
                isRelease = this.data.leadData.state === CombatMemberState.Wait;
                break;
            case EnumSkillType.WeaponASkill:
                isRelease =
                    this.data.weaponData.showId !== this.data.weaponData.id
                        ? true
                        : this.data.weaponData.state === CombatMemberState.Wait;
                break;
            case EnumSkillType.WingASkill:
                isRelease =
                    this.data.wingData.showId !== this.data.wingData.id
                        ? true
                        : this.data.wingData.state === CombatMemberState.Wait;
                break;
            case EnumSkillType.TankASkill:
                isRelease = this.data.tankData.state === CombatMemberState.Wait;
                break;
            case EnumSkillType.ArcherASkill:
            case EnumSkillType.ArcherAtk:
                isRelease = this.data.archerData.find((e) => e.id === memberId).state === CombatMemberState.Wait;
                break;
            case EnumSkillType.PetASkill:
                isRelease = this.data.petData.state === CombatMemberState.Wait;
                break;
            default:
                break;
        }

        return isRelease;
    }
}
