/*
 * @Author: chenx
 * @Date: 2024-11-28 09:13:23
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:20:23
 */
import MathUtils from "../../../nsn/util/MathUtils";
import { EnumAttributeType } from "../../data/base/BaseAttribute";
import { EnumSkillType } from "../../data/base/BaseSkill";
import DataSkill from "../../data/extend/DataSkill";
import TBSkill from "../../data/parser/TBSkill";
import { IAttr } from "../Attribute";
import { DungeonType } from "../Combat";

/**
 * 技能状态
 */
export enum CombatSkillState {
    Init = 1, // 初始化
    Wait = 2, // 待机
    Cd = 3, // 冷却
}

/**
 * 战斗-成员技能数据
 */
export interface ICombatMemberSkillData {
    state: CombatSkillState; // 状态

    id: number; // 技能id
    info: DataSkill; // 信息
    dungeonType: DungeonType; // 副本类型
    memberUuid: number; // 成员uuid
    memberId: number; // 成员id
    priority: number; // 优先级
    totalCd: number; // 总冷却时间
    cd: number; // 冷却时间
    isShow: boolean; // 是否显示
    isWaitRelease: boolean; // 是否待释放

    audioId: number[]; // 音效id
}

/**
 * 技能
 */
export default class CombatSkill {
    private data: ICombatMemberSkillData = null; // 成员技能数据

    public constructor() {
        this.data = {
            state: CombatSkillState.Init,

            id: -1,
            info: null,
            dungeonType: null,
            memberUuid: -1,
            memberId: -1,
            priority: -1,
            totalCd: 0,
            cd: 0,
            isShow: false,
            isWaitRelease: false,

            audioId: [],
        };
    }

    public clear(): void {
        this.data.state = CombatSkillState.Init;

        this.data.id = -1;
        this.data.info = null;
        this.data.dungeonType = null;
        this.data.memberId = -1;
        this.data.priority = -1;
        this.data.totalCd = 0;
        this.data.cd = 0;
        this.data.isShow = false;
        this.data.isWaitRelease = false;

        this.data.audioId = [];
    }

    /**
     * 获取数据
     * @returns
     */
    public getData(): ICombatMemberSkillData {
        return this.data;
    }

    /**
     * 初始化数据
     * @param skillId 技能id
     * @param dungeonType 副本类型
     * @param memberUuid 成员uuid
     * @param memberId 成员id
     * @param priority 优先级
     * @param isShow 是否显示
     */
    public initData(
        skillId: number,
        dungeonType: DungeonType,
        memberUuid: number,
        memberId: number,
        priority: number,
        isShow: boolean
    ): void {
        this.data.id = skillId;
        this.data.info = TBSkill.getInstance().getDataById(this.data.id);
        this.data.dungeonType = dungeonType;
        this.data.memberUuid = memberUuid;
        this.data.memberId = memberId;
        this.data.priority = priority;
        this.data.isShow = isShow;
    }

    /**
     * 切换状态
     * @param state 状态
     */
    public changeState(state: CombatSkillState): void {
        this.data.state = state;

        switch (this.data.state) {
            case CombatSkillState.Cd:
                this.data.cd = this.data.totalCd;
                break;
            default:
                break;
        }
    }

    /**
     * 更新状态
     * @param dt 时间变化量
     */
    public updateState(dt: number): void {
        switch (this.data.state) {
            case CombatSkillState.Cd:
                if (this.data.cd !== -1) {
                    this.data.cd -= dt;
                    this.data.cd = MathUtils.round(this.data.cd, 3);
                    this.data.cd = Math.max(this.data.cd, 0);

                    this.data.cd <= 0 && this.changeState(CombatSkillState.Wait);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 更新冷却cd
     * @param memberAttr 成员属性
     */
    public updateCd(memberAttr: IAttr): void {
        this.updateTotalCd(memberAttr);
        this.data.cd = Math.min(this.data.cd, this.data.totalCd);
    }

    /**
     * 重置冷却时间
     * @param memberAttr 成员属性
     */
    public resetCd(memberAttr: IAttr): void {
        this.updateTotalCd(memberAttr);
        this.data.cd = 0;
        this.data.isWaitRelease = false;

        if (this.data.info.initialCd === 0) {
            this.changeState(CombatSkillState.Wait);
        } else {
            this.changeState(CombatSkillState.Cd);
        }
    }

    /**
     * 更新总冷却时间
     * @param memberAttr 成员属性
     */
    private updateTotalCd(memberAttr: IAttr): void {
        this.data.totalCd = this.data.info.cd;
        switch (this.data.info.type) {
            case EnumSkillType.LeadASkill:
            case EnumSkillType.MagicASkill:
            case EnumSkillType.MonsterASkill:
                this.data.totalCd *= 1 - memberAttr[EnumAttributeType.SkillHaste].value;
                break;
            case EnumSkillType.ArcherAtk:
                this.data.totalCd = Math.max(
                    this.data.totalCd * (1 - memberAttr[EnumAttributeType.ArcherAtkHaste].value),
                    memberAttr[EnumAttributeType.ArcherAtkHaste].info.para[0]
                );
                break;
            default:
                break;
        }
        this.data.totalCd = MathUtils.floor(this.data.totalCd, 2);
    }

    /**
     * 是否可释放
     * @param isAuto 是否自动-释放技能
     * @returns
     */
    public isRelease(isAuto: boolean): boolean {
        if (this.data.isWaitRelease) {
            return true;
        }

        let isRelease = this.data.state === CombatSkillState.Wait;
        switch (this.data.info.type) {
            case EnumSkillType.LeadASkill:
            case EnumSkillType.MagicASkill:
                isRelease = isRelease && isAuto;
                break;
            default:
                break;
        }

        return isRelease;
    }
}
