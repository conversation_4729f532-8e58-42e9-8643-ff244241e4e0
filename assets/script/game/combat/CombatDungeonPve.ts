/*
 * @Author: chenx
 * @Date: 2025-01-16 11:08:33
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-23 10:05:20
 */
import TBDungeonCombat from "../../data/parser/TBDungeonCombat";
import TBMonsterBase from "../../data/parser/TBMonsterBase";
import Combat, { DungeonType } from "../Combat";
import CombatSetting, { CombatSettingId } from "../CombatSetting";
import Player from "../Player";
import Setting from "../Setting";
import { CombatDungeon } from "./CombatDungeon";
import { CombatMemberType } from "./CombatMember";
import CombatMemberMonster from "./CombatMemberMonster";
import { CombatPlayerType } from "./CombatMemberPlayer";

/**
 * pve数据
 */
export interface ICombatPveData {
    levelId: number; // 关卡id
    totalWave: number; // 总波数
    wave: number; // 波数
    totalLimitTime: number; // 总限制时间
    limitTime: number; // 限制时间
    refreshTime: number; // 刷新时间-限制时间
    refreshDuration: number; // 刷新时间段-限制时间
    monsterShowData: number[][][]; // 怪兽展示数据
    monsterFormationId: number[]; // 怪兽阵型id
    monsterHpData: number[][]; // 怪兽血条数据
    monsterHpIndex: number; // 怪兽血条index
}

/**
 * pve副本
 */
export default class CombatDungeonPve extends CombatDungeon {
    private data: ICombatPveData = null; // pve数据

    public clear(): void {
        super.clear();

        this.data = null;
    }

    /**
     * 初始化战斗
     * @param dungeonType 副本类型
     * @param levelId 关卡id
     */
    public initCombat(dungeonType: DungeonType, levelId: number): void {
        this.initBaseData(dungeonType);
        this.initData(levelId);
        this.updateGameSpeed();
        this.initPlayer(dungeonType);
    }

    /**
     * 清理战斗
     */
    public clearCombat(): void {
        this.clearBaseData();
        this.clearData();
        this.clearAllPlayer();
        this.clearAllMonster();
    }

    /**
     * 重置战斗
     */
    public resetCombat(): void {
        this.resetAllPlayer();
        this.resetAllMonster();
    }

    /**
     * 初始化pve数据
     * @param levelId 关卡id
     */
    private initData(levelId: number): void {
        this.data = {
            levelId,
            totalWave: 0,
            wave: 0,
            totalLimitTime: 0,
            limitTime: 0,
            refreshTime: 0,
            refreshDuration: 0.1,
            monsterShowData: null,
            monsterFormationId: null,
            monsterHpData: [],
            monsterHpIndex: -1,
        };
    }

    /**
     * 清理pve数据
     */
    public clearData(): void {
        this.data = null;
    }

    /**
     * 获取pve数据
     * @returns
     */
    public getData(): ICombatPveData {
        return this.data;
    }

    /**
     * 更新游戏速度
     */
    public updateGameSpeed(): void {
        const baseData = this.getBaseData();
        baseData.gameSpeed = 1;
        const combatInfo = TBDungeonCombat.getInstance().getDataByType(baseData.type);
        if (combatInfo.accelerationSwitch) {
            const settingId = Combat.getInstance().getGameSpeedSettingId(baseData.type);
            if (Setting.getInstance().getSwitchState(settingId)) {
                baseData.gameSpeed = combatInfo.battleAcceleration[1];
            }
        }
    }

    /**
     * 初始化玩家
     * @param dungeonType 副本类型
     */
    private initPlayer(dungeonType: DungeonType): void {
        const pveBaseData = this.getBaseData();
        const player = this.getPlayer();
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        playerBaseData.uuid = pveBaseData.playerUuid++;
        playerBaseData.type = CombatMemberType.Player;
        playerBaseData.dungeonType = dungeonType;
        playerBaseData.levelId = -1;
        switch (playerBaseData.dungeonType) {
            case DungeonType.Boss:
                playerBaseData.isAuto = !CombatSetting.getInstance().getSettingState(
                    CombatSettingId.DungeonBossAutoReleaseSkill
                );
                break;
            case DungeonType.Cloud:
                playerBaseData.isAuto = !CombatSetting.getInstance().getSettingState(
                    CombatSettingId.DungeonCloudAutoReleaseSkill
                );
                break;
            case DungeonType.Thief:
                playerBaseData.isAuto = !CombatSetting.getInstance().getSettingState(
                    CombatSettingId.DungeonThiefAutoReleaseSkill
                );
                break;
            case DungeonType.Tower:
                playerBaseData.isAuto = !CombatSetting.getInstance().getSettingState(
                    CombatSettingId.DungeonTowerAutoReleaseSkill
                );
                break;
            case DungeonType.Union:
                playerBaseData.isAuto = !CombatSetting.getInstance().getSettingState(
                    CombatSettingId.DungeonUnionAutoReleaseSkill
                );
                break;
            case DungeonType.Trial:
                playerBaseData.isAuto = !CombatSetting.getInstance().getSettingState(
                    CombatSettingId.DungeonTrialAutoReleaseSkill
                );
                break;
            case DungeonType.Test:
                playerBaseData.isAuto = !CombatSetting.getInstance().getSettingState(
                    CombatSettingId.DungeonTestAutoReleaseSkill
                );
                break;
            default:
                break;
        }

        playerData.type = CombatPlayerType.Self;
        playerData.roleData = Player.getInstance().getInfo();

        player.initMemberData();
        player.updateMemberData();
        player.initAttr();
        player.updateAttrByCul();
        player.updateAttrBySkill();
        player.resetHp();
        player.initSkillCd();
    }

    /**
     * 初始化全部怪兽
     * @param dungeonType 副本类型
     * @param levelId 关卡id
     */
    public initAllMonster(dungeonType: DungeonType, levelId: number): CombatMemberMonster[] {
        const baseData = this.getBaseData();

        const allMonster: CombatMemberMonster[] = [];
        baseData.monsterTotalNum = 0;
        this.data.monsterShowData[this.data.wave - 1].forEach(([monsterId, num]) => {
            const monsterInfo = TBMonsterBase.getInstance().getDataById(monsterId);
            for (let i = 0; i < num; i++) {
                const monster = this.getMonster();
                const monsterBaseData = monster.getBaseData();
                const monsterData = monster.getData();

                monsterBaseData.uuid = baseData.monsterUuid++;
                monsterBaseData.type = CombatMemberType.Monster;
                monsterBaseData.dungeonType = dungeonType;
                monsterBaseData.levelId = levelId;
                monsterBaseData.isAuto = true;

                monsterData.id = monsterId;
                monsterData.info = monsterInfo;

                monster.initSkill();
                monster.initAttr();
                monster.updateAttrByCul();
                monster.updateAttrBySkill();
                monster.resetHp();
                monster.initSkillCd();

                allMonster.push(monster);
            }

            baseData.monsterTotalNum += num;
        });

        return allMonster;
    }
}
