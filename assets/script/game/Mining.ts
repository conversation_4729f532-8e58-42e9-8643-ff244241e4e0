/* 采矿
 * @Author: wangym
 * @Date: 2024-04-08 10:21:34
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-17 14:38:42
 */
import LocalStorage from "../../nsn/core/LocalStorage";
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import Time, { MINUTE_TO_SECOND } from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    IMapCellInfo,
    IMiningInfo,
    MiningInitRet,
    MiningStart,
    MiningStartRet,
    MiningTakeAward,
    MiningTakeAwardRet,
    MiningUpdateLastRecoverAtRet,
    TalentTreeType,
} from "../../protobuf/proto";
import { LocalStorageKey } from "../config/LocalStorageConfig";
import i18n from "../config/i18n/I18n";
import { EnumEconomyAttributeType } from "../data/base/BaseEconomyAttribute";
import { EnumMiningPropType } from "../data/base/BaseMiningProp";
import { ITEM_ID } from "../data/parser/TBItem";
import TBMiningProp from "../data/parser/TBMiningProp";
import TBMiningSpace from "../data/parser/TBMiningSpace";
import TBTalentLeaf from "../data/parser/TBTalentLeaf";
import TBTalentLeafGroup from "../data/parser/TBTalentLeafGroup";
import Bag from "./Bag";
import EconomyAttribute from "./EconomyAttribute";
import Talent from "./Talent";

/**
 * 自动挖矿规则
 */
export enum EnumMiningAutoExcavateRule {
    SearchRange1Resource = 1, // 自动挖矿（查找1步以内能挖取的所有资源）
    SearchRange2Resource, // 自动挖矿（查找2步以内能挖取的所有资源）
    SearchNoneRangeResource, // 自动挖矿（不限制步数地查找地图中所有资源）
}

/**
 * 自动挖矿设置项（内存中存储设置数据的格式）
 */
export interface IMiningAutoExcavateSettings {
    rule?: EnumMiningAutoExcavateRule; // 自动挖矿规则（寻找范围）
    excludeList?: number[]; // 自动挖矿规则（排除的内容）
}

/**
 * 挖矿事件
 */
export enum MiningEvent {
    MiningFailed = "mining-failed", // 挖矿失败
}

export default class Mining extends Logic {
    private miningInfo: IMiningInfo = null; // 挖矿数据
    private miningAutoSettings: IMiningAutoExcavateSettings = null; // 自动挖矿设置

    /**
     * 清理数据
     */
    public clear(): void {
        this.miningInfo = null;
        this.miningAutoSettings = null;
    }

    /**
     * 注册消息
     */
    protected registerHandler(): void {
        // 矿山
        Socket.getInstance().on(MiningInitRet.prototype.clazzName, this.miningInitRet, this);
        Socket.getInstance().on(MiningStartRet.prototype.clazzName, this.miningStartRet, this);
        Socket.getInstance().on(MiningTakeAwardRet.prototype.clazzName, this.miningTaskAwardRet, this);
        Socket.getInstance().on(
            MiningUpdateLastRecoverAtRet.prototype.clazzName,
            this.miningUpdateLastRecoverAtRet,
            this
        );
    }

    /**
     * 挖矿矿山初始化信息返回
     * @param data
     */
    private miningInitRet(data: MiningInitRet): void {
        const { errMsg, miningInfo } = data;
        if (errMsg !== MiningInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.miningInitRet[errMsg]);
            return;
        }
        this.miningInfo = miningInfo;
        this.emit(MiningInitRet.prototype.clazzName);
    }

    /**
     * 挖矿矿山开始挖矿信息返回
     * @param data
     * @returns
     */
    private miningStartRet(data: MiningStartRet): void {
        const { errMsg, removeRowIndexs, miningInfo } = data;
        if (errMsg !== MiningStartRet.ErrorResult.None) {
            this.emit(MiningEvent.MiningFailed);
            Tips.getInstance().show(i18n.miningStartRet[errMsg]);
            return;
        }
        this.miningInfo = miningInfo;
        this.emit(MiningStartRet.prototype.clazzName, removeRowIndexs);
    }

    /**
     * 领取密室中的道具奖励信息返回
     * @param data
     * @returns
     */
    private miningTaskAwardRet(data: MiningTakeAwardRet): void {
        const { errMsg, row, col } = data;
        if (errMsg !== MiningTakeAwardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.miningTakeAwardRet[errMsg]);
            return;
        }
        const colData = this.getMiningDataByPos(row, col);
        colData.hasTake = true;
        this.emit(MiningTakeAwardRet.prototype.clazzName);
    }

    /**
     * 更新矿镐工具最后一次的同步时间
     * @param data
     */
    private miningUpdateLastRecoverAtRet(data: MiningUpdateLastRecoverAtRet): void {
        const { lastRecoverAt } = data;
        this.miningInfo.lastRecoverAt = lastRecoverAt;
        this.emit(MiningUpdateLastRecoverAtRet.prototype.clazzName);
    }

    /**
     * 获取挖矿矿山数据
     * @returns {IMiningInfo}
     */
    public getMiningData(): IMiningInfo {
        return this.miningInfo;
    }

    /**
     * 获取历史挖掘的最大深度
     * @returns
     */
    public getHistoryDepth(): number {
        return this.getMiningData()?.miningMap?.historyDepth || 0;
    }

    /**
     * 根据行列获取矿物质数据
     * @param row 行序号
     * @param col 列序号
     * @returns
     */
    public getMiningDataByPos(row: number, col: number): IMapCellInfo {
        return this.miningInfo?.miningMap?.rows[row]?.cells[col];
    }

    /**
     * 根据挖掘道具的类型获取道具ID
     * @param toolType 道具类型
     * @returns {number}
     */
    public getMiningToolItemIdByType(toolType: EnumMiningPropType): number {
        let itemInfoId: number = null;
        switch (toolType) {
            case EnumMiningPropType.IronPick:
                itemInfoId = ITEM_ID.MINING_IRON_PICKAXE;
                break;
            case EnumMiningPropType.GoldPick:
                itemInfoId = ITEM_ID.MINING_GOLD_PICKAXE;
                break;
            case EnumMiningPropType.Bit:
                itemInfoId = ITEM_ID.MINING_BIT;
                break;
            case EnumMiningPropType.Explosive:
                itemInfoId = ITEM_ID.MINING_EXPLOSIVE;
                break;
            default:
                break;
        }
        return itemInfoId;
    }

    /**
     * 根据道具ID获取挖掘工具类型
     * @param itemInfoId 道具ID
     * @returns {EnumMiningPropType}
     */
    public getMiningToolTypeByItemId(itemInfoId: number): EnumMiningPropType {
        let tool: EnumMiningPropType = null;
        switch (itemInfoId) {
            case ITEM_ID.MINING_IRON_PICKAXE:
                tool = EnumMiningPropType.IronPick;
                break;
            case ITEM_ID.MINING_GOLD_PICKAXE:
                tool = EnumMiningPropType.GoldPick;
                break;
            case ITEM_ID.MINING_BIT:
                tool = EnumMiningPropType.Bit;
                break;
            case ITEM_ID.MINING_EXPLOSIVE:
                tool = EnumMiningPropType.Explosive;
                break;
            default:
                break;
        }
        return tool;
    }

    /**
     * 获取挖矿道具数量
     * @param toolType 道具类型
     * @returns {number}
     */
    public getMiningToolItemCountByType(toolType: EnumMiningPropType): number {
        const itemInfoId = this.getMiningToolItemIdByType(toolType);
        if (!itemInfoId) {
            // 未知的挖矿工具类型
            return 0;
        }
        const result = Bag.getInstance().getItemCountById(itemInfoId);
        if (!this.miningInfo) {
            // 没有最后一次更新时间，默认使用背包中的道具数量
            return result;
        }
        const propConfig = TBMiningProp.getInstance().getDataById(toolType);
        const recoveryTime = this.getMiningToolRecoveryTime(toolType);
        if (!recoveryTime) {
            // 该道具不会随着时间增加数量，不进行数量再计算
            return result;
        }
        const addCount = Math.floor(
            (Time.getInstance().now() - (this.miningInfo?.lastRecoverAt || 0)) / (recoveryTime * 1000)
        );
        if (addCount <= 0) {
            return result;
        }
        const extraMax = this.getMiningToolExtraMax(toolType);
        return result + Math.min(addCount, Math.max(propConfig.dailyLimit + extraMax - result, 0));
    }

    /**
     * 获取挖矿工具的恢复时间（单位：秒）
     * @param toolType 挖矿工具类型
     * @returns {number}
     */
    public getMiningToolRecoveryTime(toolType: EnumMiningPropType): number {
        // 基础道具数量上限
        const miningPropConfig = TBMiningProp.getInstance().getDataById(toolType);
        if (!miningPropConfig.recoveryTime) {
            // 该道具不会随着时间增加数量，不进行数量再计算
            return 0;
        }
        // 获取所有影响到该值的所有buffID
        let attrTypeList: EnumEconomyAttributeType[] = [];
        switch (toolType) {
            case EnumMiningPropType.IronPick: // 目前只有铁镐可以自动恢复数量
                attrTypeList = [EnumEconomyAttributeType.PickaxeRecoverySpeed];
                break;
            default:
                break;
        }
        // buff列表为空时
        if (attrTypeList.length <= 0) {
            return miningPropConfig.recoveryTime;
        }

        let attrValue = 0;
        const miningAttr = EconomyAttribute.getInstance().getMiningAttr();
        attrTypeList.forEach((e) => {
            switch (e) {
                case EnumEconomyAttributeType.PickaxeRecoverySpeed:
                    attrValue += miningAttr[e].value;
                    break;
                default:
                    break;
            }
        });
        const recoveryTime = miningPropConfig.recoveryTime / (1 + attrValue);

        return recoveryTime;
    }

    /**
     * 获取挖矿道具携带数量的额外上限
     * @param toolType 挖掘道具类型
     * @returns {number}
     */
    public getMiningToolExtraMax(toolType: EnumMiningPropType): number {
        // 基础道具数量上限
        const miningPropConfig = TBMiningProp.getInstance().getDataById(toolType);
        if (!miningPropConfig.recoveryTime) {
            // 该道具不会随着时间增加数量，不进行数量再计算
            return 0;
        }
        // 获取所有影响到该值的所有buffID
        let attrTypeList: EnumEconomyAttributeType[] = [];
        switch (toolType) {
            case EnumMiningPropType.IronPick: // 目前只有铁镐可以自动恢复数量
                attrTypeList = [EnumEconomyAttributeType.PickaxeRecoveryLimit];
                break;
            default:
                break;
        }
        // buff列表为空时
        if (attrTypeList.length <= 0) {
            return 0;
        }

        let attrValue = 0;
        const miningAttr = EconomyAttribute.getInstance().getMiningAttr();
        attrTypeList.forEach((e) => {
            switch (e) {
                case EnumEconomyAttributeType.PickaxeRecoveryLimit:
                    attrValue += miningAttr[e].value;
                    break;
                default:
                    break;
            }
        });

        return attrValue;
    }

    /**
     * 获取自动挖矿规则设置
     * @returns {EnumMiningAutoExcavateRule}
     */
    public getMiningAutoSettingsRule(): EnumMiningAutoExcavateRule {
        if (!this.miningAutoSettings) {
            this.loadSettingsFromLocalStorage();
        }
        return this.miningAutoSettings.rule;
    }

    /**
     * 获取自动挖矿时被排除的资源列表
     * @returns {number[]}
     */
    public getMiningAutoSettingsExcludeList(): number[] {
        if (!this.miningAutoSettings) {
            this.loadSettingsFromLocalStorage();
        }
        return this.miningAutoSettings.excludeList;
    }

    /**
     * 获取自动挖矿时处于挖取目标列表中的资源ID列表
     * @returns
     */
    public getMiningAutoSettingsSearchList(): number[] {
        const excludeList = this.getMiningAutoSettingsExcludeList();
        return TBMiningSpace.getInstance()
            .getAutoExcavateList()
            .map((item) => item.id)
            .filter((spaceId) => {
                return !excludeList.includes(spaceId);
            });
    }

    /**
     * 保存自动挖矿设置
     * @param setting
     */
    public setMiningAutoSettings(setting: IMiningAutoExcavateSettings): void {
        this.miningAutoSettings = setting;
        this.saveSettingsToLocalStorage();
    }

    /**
     * 获取本次研究剩余时间
     * @returns {number} 单位：ms
     */
    public getResearchLeftTime(): number {
        const talentTreeData = Talent.getInstance().getTreeData(TalentTreeType.MiningTree);
        if (!talentTreeData.leafId || talentTreeData.isComplete) {
            return 0;
        }
        const researchConfig = TBTalentLeaf.getInstance().getDataById(talentTreeData.leafId);
        const level = Talent.getInstance().getLevel(TalentTreeType.MiningTree, talentTreeData.leafId);
        const researchAttrConfig = TBTalentLeafGroup.getInstance().getDataByGroupAndLevel(
            researchConfig.group,
            level + 1 // 如果是正在研发，那应该查表格中的下一个等级
        );
        const miningAttr = EconomyAttribute.getInstance().getMiningAttr();
        const time = researchAttrConfig.time / (1 + miningAttr[EnumEconomyAttributeType.AcceleratedResearch].value); // 分钟
        return (
            talentTreeData.startTime +
            (time * MINUTE_TO_SECOND - (talentTreeData.accTime || 0)) * 1000 -
            Time.getInstance().now()
        );
    }

    /**
     * 技能是否已解锁（可研究或升级）
     * @param researchId 研究的技能ID
     * @returns {boolean}
     */
    public isResearchUnlock(researchId: number): boolean {
        const researchConfig = TBTalentLeaf.getInstance().getDataById(researchId);
        if (!researchConfig) {
            return false;
        }
        if (!researchConfig.previous || researchConfig.previous.length <= 0) {
            // 技能没有前置
            return true;
        }
        let isUnlock = true;
        for (const previous of researchConfig.previous) {
            const [researchId, level] = previous;
            if (Talent.getInstance().getLevel(TalentTreeType.MiningTree, researchId) >= level) {
                continue;
            }
            isUnlock = false;
            break;
        }
        return isUnlock;
    }

    /**
     * 从本地加载数据
     */
    private loadSettingsFromLocalStorage(): void {
        const str = LocalStorage.getInstance().getItem(LocalStorageKey.MiningAutoSettings);
        const settings = this.decodeMiningAutoSettingsData(str);
        const autoExcavateSearchList = TBMiningSpace.getInstance().getAutoExcavateList();
        if (!this.miningAutoSettings) {
            this.miningAutoSettings = {};
        }
        this.miningAutoSettings.rule = settings.rule;
        this.miningAutoSettings.excludeList = settings.excludeList.filter((searchId) => {
            return autoExcavateSearchList.findIndex((item) => item.id === searchId) >= 0;
        });
    }

    /**
     * 将设置数据保存到本地
     */
    private saveSettingsToLocalStorage(): void {
        const data = this.encodeMiningAutoSettingsData(this.miningAutoSettings);
        LocalStorage.getInstance().setItem(LocalStorageKey.MiningAutoSettings, data);
    }

    /**
     * 解码挖矿设置数据
     * @param str 设置字符串
     * @returns
     */
    private decodeMiningAutoSettingsData(str: string): IMiningAutoExcavateSettings {
        let localData: [number, number[]] = null;
        try {
            localData = JSON.parse(str);
        } catch (e) {
            // 数据无法解析，标记为丢失
        }
        const result: IMiningAutoExcavateSettings = {
            rule: EnumMiningAutoExcavateRule.SearchRange1Resource,
            excludeList: [],
        };
        const rule: EnumMiningAutoExcavateRule = localData && localData[0];
        switch (rule) {
            case EnumMiningAutoExcavateRule.SearchRange1Resource:
            case EnumMiningAutoExcavateRule.SearchRange2Resource:
            case EnumMiningAutoExcavateRule.SearchNoneRangeResource:
                result.rule = rule;
                break;
            default:
                break;
        }
        if (localData && localData[1] && localData[1].length && localData[1].length >= 0) {
            result.excludeList = localData[1];
        }
        return result;
    }

    /**
     * 编码
     * @param settings 设置对象
     * @returns
     */
    private encodeMiningAutoSettingsData(settings: {
        rule?: EnumMiningAutoExcavateRule;
        searchList?: number[];
    }): string {
        if (!settings) {
            return "[]";
        }
        const rule = settings.rule || EnumMiningAutoExcavateRule.SearchRange1Resource;
        const searchList = settings.searchList || [];
        return JSON.stringify([rule, searchList]);
    }

    /**
     * 开始挖矿
     * @param row 挖掘的行序号
     * @param col 挖掘的列序号
     * @param propType 使用的道具类型
     */
    public sendMiningStart(row: number, col: number, propType: number): void {
        const data = MiningStart.create({ row, col, propType: propType - 1 });
        Socket.getInstance().send(data);
    }

    /**
     * 领取挖矿密室中的奖励
     * @param row 行坐标
     * @param col 列坐标
     */
    public sendMiningTakeAward(row: number, col: number): void {
        const data = MiningTakeAward.create({ row, col });
        Socket.getInstance().send(data);
    }
}
