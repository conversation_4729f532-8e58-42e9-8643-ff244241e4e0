/*
 * @Author: chenx
 * @Date: 2024-03-25 15:07:34
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 14:22:16
 */
import Logic from "../../nsn/core/Logic";
import MathUtils from "../../nsn/util/MathUtils";
import Time from "../../nsn/util/Time";
import Utils from "../../nsn/util/Utils";
import {
    ArcherBatchIncrStarRet,
    ArcherCreateRet,
    ArcherIncrStarRet,
    ArcherResetLevelRet,
    ArcherUpgradeRet,
    BagUpdateRet,
    BlessStartRet,
    CollectionCreateNoticeRet,
    CollectionUpgradeRet,
    CollectionUpgradeStarRet,
    EntryObj,
    EquipBatchStrengthenRet,
    EquipMasterActivateRet,
    EquipReplaceRet,
    EquipStrengthenRet,
    EquipSuitActivateRet,
    ForgeCompleteRet,
    ForgeCreateSkinNoticeRet,
    ForgeCultivateUpgradeRet,
    IArcherObj,
    IBlessInfo,
    ICollectionObj,
    IEquipInfo,
    IEquipPositionInfo,
    IEquipSuitInfo,
    IFeatherInfo,
    IForgeCultivateObj,
    ILeadSkinInfo,
    IMagicalBattleObj,
    IMagicalObj,
    IPetBattleObj,
    IPetObj,
    IPowerInfo,
    ITalentTreeInfo,
    ITankInfo,
    IWeaponObj,
    IWingEnchantObj,
    IWingInfo,
    LeadSkinCreateRet,
    LeadSkinIncrStarRet,
    LeadSkinResetRet,
    LeadSkinUpgradeRet,
    MagicalAnalyzeRet,
    MagicalBatchBattleRet,
    MagicalBatchIncrStarRet,
    MagicalBattleRet,
    MagicalCreateRet,
    MagicalIncrStarRet,
    MagicalResetLevelRet,
    MagicalUpgradeRet,
    PetIncrStarRet,
    PetIncrStarUsePropRet,
    PetRefineRet,
    PetResetLevelRet,
    PetSelectEntryRet,
    PetUpgradeRet,
    PowerBreakOutRet,
    PowerPromotionRet,
    PowerUpgradeRet,
    TalentTreeCompleteRet,
    TalentTreeUpgradeRet,
    TankCreateRet,
    TankUpgradeLevelRet,
    TankUpgradeStarRet,
    TaskStatisticsRet,
    WeaponCreateRet,
    WeaponUpgradeLevelRet,
    WeaponUpgradeStarRet,
    WingsBatchUpgradeLevelRet,
    WingsCreateRet,
    WingsEnchantUpgradeRet,
    WingsUpgradeStarRet,
} from "../../protobuf/proto";
import Reset, { ResetEvent } from "../core/Reset";
import { EnumAttributeType } from "../data/base/BaseAttribute";
import { EnumCollectionUpgradeType } from "../data/base/BaseCollection";
import { EnumMonsterBaseType } from "../data/base/BaseMonsterBase";
import { EnumSkillEffectEffectType } from "../data/base/BaseSkillEffect";
import { EnumTalentLeafNature } from "../data/base/BaseTalentLeaf";
import { EnumTankType } from "../data/base/BaseTank";
import { EnumUnionPara } from "../data/base/BaseUnion";
import { EnumWeaponType } from "../data/base/BaseWeapon";
import { EnumWingType } from "../data/base/BaseWing";
import DataAttribute from "../data/extend/DataAttribute";
import DataEconomyAttribute from "../data/extend/DataEconomyAttribute";
import DataGroupEffect from "../data/extend/DataGroupEffect";
import DataItemAttribute from "../data/extend/DataItemAttribute";
import TBArcher from "../data/parser/TBArcher";
import TBArcherLevel from "../data/parser/TBArcherLevel";
import TBArcherStar from "../data/parser/TBArcherStar";
import TBAttribute from "../data/parser/TBAttribute";
import TBBlessing from "../data/parser/TBBlessing";
import TBBlessingLevel from "../data/parser/TBBlessingLevel";
import TBCollection from "../data/parser/TBCollection";
import TBCollectionStar from "../data/parser/TBCollectionStar";
import TBCollectionTask from "../data/parser/TBCollectionTask";
import TBCollectionUpgrade from "../data/parser/TBCollectionUpgrade";
import TBDungeonBoss from "../data/parser/TBDungeonBoss";
import TBDungeonCloud from "../data/parser/TBDungeonCloud";
import TBDungeonThief from "../data/parser/TBDungeonThief";
import TBDungeonTower from "../data/parser/TBDungeonTower";
import TBEntry from "../data/parser/TBEntry";
import TBForgeCultivateLevel from "../data/parser/TBForgeCultivateLevel";
import TBForgeLevel from "../data/parser/TBForgeLevel";
import TBForgeSkin from "../data/parser/TBForgeSkin";
import TBItemAttribute from "../data/parser/TBItemAttribute";
import TBLeadEquipMaster from "../data/parser/TBLeadEquipMaster";
import TBLeadEquipStrengthen from "../data/parser/TBLeadEquipStrengthen";
import TBLeadEquipSuit from "../data/parser/TBLeadEquipSuit";
import TBLeadSkinLevel from "../data/parser/TBLeadSkinLevel";
import TBLeadSkinStar from "../data/parser/TBLeadSkinStar";
import TBMagicSkill from "../data/parser/TBMagicSkill";
import TBMagicSkillLevel from "../data/parser/TBMagicSkillLevel";
import TBMagicSkillStar from "../data/parser/TBMagicSkillStar";
import TBMainBarrier from "../data/parser/TBMainBarrier";
import TBPetLevel from "../data/parser/TBPetLevel";
import TBPetStar from "../data/parser/TBPetStar";
import TBPowerLevel from "../data/parser/TBPowerLevel";
import TBPowerPromotion from "../data/parser/TBPowerPromotion";
import { RECHARGE_ID } from "../data/parser/TBRecharge";
import TBTalentLeaf from "../data/parser/TBTalentLeaf";
import TBTalentLeafGroup from "../data/parser/TBTalentLeafGroup";
import TBTank from "../data/parser/TBTank";
import TBTankLevel from "../data/parser/TBTankLevel";
import TBTankStar from "../data/parser/TBTankStar";
import TBThiefBloodReward from "../data/parser/TBThiefBloodReward";
import TBTrialBoss from "../data/parser/TBTrialBoss";
import TBUnion from "../data/parser/TBUnion";
import TBUnionBoss from "../data/parser/TBUnionBoss";
import TBWeapon from "../data/parser/TBWeapon";
import TBWeaponLevel from "../data/parser/TBWeaponLevel";
import TBWeaponStar from "../data/parser/TBWeaponStar";
import TBWing from "../data/parser/TBWing";
import TBWingEnchant from "../data/parser/TBWingEnchant";
import TBWingLevel from "../data/parser/TBWingLevel";
import TBWingLevelRewards from "../data/parser/TBWingLevelRewards";
import TBWingStar from "../data/parser/TBWingStar";
import Archer from "./Archer";
import Bag from "./Bag";
import Bless, { BlessEvent } from "./Bless";
import Collection from "./Collection";
import Combat, { DungeonType } from "./Combat";
import CombatDungeonMain from "./combat/CombatDungeonMain";
import { ICombatMemberData } from "./combat/CombatMember";
import CombatMemberMonster from "./combat/CombatMemberMonster";
import CombatMemberPlayer, { CombatPlayerType } from "./combat/CombatMemberPlayer";
import CombatLog, { CombatLogId } from "./CombatLog";
import Equip from "./Equip";
import Forge from "./Forge";
import LeadSkin from "./LeadSkin";
import Magical from "./Magical";
import Pet from "./Pet";
import Power from "./Power";
import Recharge from "./Recharge";
import Skill from "./Skill";
import Talent from "./Talent";
import Tank from "./Tank";
import Task from "./Task";
import Weapon from "./Weapon";
import Wing from "./Wing";

/**
 * 缓存属性
 */
export interface ICacheAttr {
    [attrId: number]: number;
}

/**
 * 缓存属性数据
 */
export interface ICacheAttrData {
    para: number[]; // 参数
    attr: ICacheAttr; // 属性
}

/**
 * 属性
 */
export interface IAttr {
    [attrId: number]: IAttrData; // 属性数据
}

/**
 * 属性数据
 */
export interface IAttrData {
    id: number; // 属性id
    value: number; // 值
    finalValue: number; // 最终值
    scoreValue: number; // 战斗力值
    initValue: number; // 初始值
    culValue: number; // 培养值
    skillValue: number; // 技能值
    info: DataAttribute; // 信息
    initLog: string; // 初始log
    culLog: string; // 培养log
    skillLog: string; // 技能log
}

/**
 * 经济属性
 */
export interface IEconomyAttr {
    [attrId: number]: IEconomyAttrData; // 属性数据
}

/**
 * 经济属性数据
 */
export interface IEconomyAttrData {
    id: number; // 属性id
    value: number; // 值
    initValue: number; // 初始值
    culValue: number; // 培养值
    info: DataEconomyAttribute; // 信息
    initLog: string; // 初始log
    culLog: string; // 培养log
}

/**
 * 属性来源类型
 */
export enum AttrSourceType {
    Total = 1, // 总
    Lead, // 主角
    Weapon, // 神器
    Wing, // 背饰
    Tank, // 战车
    Archer, // 弓箭手
    ArcherGroup, // 弓箭手羁绊
    Pet, // 宠物
    Equip, // 装备
    Magic, // 魔法
    Power, // 王权
    Cul, // 培养
    Bless, // 祝福
    Talent, // 天赋
    Forge, // 锻造台
    Collection, // 藏品
    Other, // 其他
}

/**
 * 属性事件
 */
export enum AttributeEvent {
    Update = "update", // 更新
}

/**
 * 属性
 */
export default class Attribute extends Logic {
    private leadLevelAttr: ICacheAttrData = null; // 主角等级
    private leadStarAttr: ICacheAttrData = null; // 主角星级
    private weaponLevelAttr: ICacheAttrData = null; // 神器等级
    private weaponStarAttr: ICacheAttrData = null; // 神器星级
    private wingLevelAttr: ICacheAttrData = null; // 背饰等级
    private wingStarAttr: ICacheAttrData = null; // 背饰星级
    private wingEnchantment: ICacheAttrData = null; // 背饰附魔
    private tankLevelAttr: ICacheAttrData = null; // 战车等级
    private tankStarAttr: ICacheAttrData = null; // 战车星级
    private archerLevelAttr: ICacheAttrData = null; // 弓箭手等级
    private archerStarAttr: ICacheAttrData = null; // 弓箭手星级
    private archerGroupAttr: ICacheAttrData = null; // 弓箭手羁绊
    private petAttr: ICacheAttrData = null; // 宠物
    private skillAttr: ICacheAttrData = null; // 技能
    private equipAttr: ICacheAttrData = null; // 装备
    private equipStrengthenAttr: ICacheAttrData = null; // 装备强化
    private equipStageAttr: ICacheAttrData = null; // 装备阶段
    private equipSuitAttr: ICacheAttrData = null; // 装备套装
    private magicLevelAttr: ICacheAttrData = null; // 魔法等级
    private magicStarAttr: ICacheAttrData = null; // 魔法星级
    private powerAttr: ICacheAttrData = null; // 王权
    private blessAttr: ICacheAttrData = null; // 祝福
    private talentAttr: ICacheAttrData = null; // 天赋
    private forgeAttr: ICacheAttrData = null; // 锻造台
    private forgeLevelAttr: ICacheAttrData = null; // 锻造台等级
    private collectionAttr: ICacheAttrData = null; // 藏品
    private collectionSuitAttr: ICacheAttrData = null; // 藏品套装
    private itemAttr: ICacheAttrData = null; // 道具

    public clear(): void {
        this.leadLevelAttr = null;
        this.leadStarAttr = null;
        this.weaponLevelAttr = null;
        this.weaponStarAttr = null;
        this.wingLevelAttr = null;
        this.wingStarAttr = null;
        this.wingEnchantment = null;
        this.tankLevelAttr = null;
        this.tankStarAttr = null;
        this.archerLevelAttr = null;
        this.archerStarAttr = null;
        this.archerGroupAttr = null;
        this.petAttr = null;
        this.skillAttr = null;
        this.equipAttr = null;
        this.equipStrengthenAttr = null;
        this.equipStageAttr = null;
        this.equipSuitAttr = null;
        this.magicLevelAttr = null;
        this.magicStarAttr = null;
        this.powerAttr = null;
        this.blessAttr = null;
        this.talentAttr = null;
        this.forgeAttr = null;
        this.forgeLevelAttr = null;
        this.collectionAttr = null;
        this.collectionSuitAttr = null;
        this.itemAttr = null;
    }

    protected registerHandler(): void {
        // 重置-玩家数据
        Reset.getInstance().on(
            ResetEvent.ResetPlayerData,
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 背包-更新
        Bag.getInstance().on(
            BagUpdateRet.prototype.clazzName,
            () => {
                if (this.isUpdateAttrByItem()) {
                    this.emit(AttributeEvent.Update);
                }
            },
            this
        );
        // 任务-全局计数变更通知
        Task.getInstance().on(
            TaskStatisticsRet.prototype.clazzName,
            () => {
                if (this.isUpdateAttrByCollection()) {
                    this.emit(AttributeEvent.Update);
                }
            },
            this
        );
        // 主角-获得/升级/等级重置/升星
        LeadSkin.getInstance().on(
            [
                LeadSkinCreateRet.prototype.clazzName,
                LeadSkinUpgradeRet.prototype.clazzName,
                LeadSkinResetRet.prototype.clazzName,
                LeadSkinIncrStarRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 神器-获得/升级/升星
        Weapon.getInstance().on(
            [
                WeaponCreateRet.prototype.clazzName,
                WeaponUpgradeLevelRet.prototype.clazzName,
                WeaponUpgradeStarRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 背饰-获得/升级/升星/附魔
        Wing.getInstance().on(
            [
                WingsCreateRet.prototype.clazzName,
                WingsBatchUpgradeLevelRet.prototype.clazzName,
                WingsUpgradeStarRet.prototype.clazzName,
                WingsEnchantUpgradeRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 战车-获得/升级/升星
        Tank.getInstance().on(
            [
                TankCreateRet.prototype.clazzName,
                TankUpgradeLevelRet.prototype.clazzName,
                TankUpgradeStarRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 弓箭手-获得/升星/一键升星
        Archer.getInstance().on(
            [
                ArcherCreateRet.prototype.clazzName,
                ArcherIncrStarRet.prototype.clazzName,
                ArcherBatchIncrStarRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 弓箭手-升级
        Archer.getInstance().on(
            ArcherUpgradeRet.prototype.clazzName,
            (data: ArcherUpgradeRet) => {
                const battleData = Archer.getInstance().getTeam();
                if (battleData.findIndex((e) => e.archerId === data.archerId) !== -1) {
                    this.emit(AttributeEvent.Update);
                }
            },
            this
        );
        // 弓箭手-等级重置
        Archer.getInstance().on(
            ArcherResetLevelRet.prototype.clazzName,
            (data: ArcherResetLevelRet) => {
                const battleData = Archer.getInstance().getTeam();
                if (battleData.findIndex((e) => data.archerIds.includes(e.archerId)) !== -1) {
                    this.emit(AttributeEvent.Update);
                }
            },
            this
        );
        // 宠物-升级
        Pet.getInstance().on(
            PetUpgradeRet.prototype.clazzName,
            (data: PetUpgradeRet) => {
                const petBattleUuid = Pet.getInstance().getTeamIds();
                if (petBattleUuid.includes(data.petInfo.uuid)) {
                    this.emit(AttributeEvent.Update);
                }
            },
            this
        );
        // 宠物-等级重置
        Pet.getInstance().on(
            PetResetLevelRet.prototype.clazzName,
            (data: PetResetLevelRet) => {
                const petBattleUuid = Pet.getInstance().getTeamIds();
                const index = data.uuids.findIndex((e) => petBattleUuid.includes(e));
                if (index !== -1) {
                    this.emit(AttributeEvent.Update);
                }
            },
            this
        );
        // 宠物-升星/词条选择/词条洗练
        Pet.getInstance().on(
            [
                PetIncrStarRet.prototype.clazzName,
                PetIncrStarUsePropRet.prototype.clazzName,
                PetSelectEntryRet.prototype.clazzName,
                PetRefineRet.prototype.clazzName,
            ],
            (petData: IPetObj) => {
                const petBattleUuid = Pet.getInstance().getTeamIds();
                if (petBattleUuid.includes(petData.uuid)) {
                    this.emit(AttributeEvent.Update);
                }
            },
            this
        );
        // 装备-穿戴/强化/批量强化/激活阶段/激活套装
        Equip.getInstance().on(
            [
                EquipReplaceRet.prototype.clazzName,
                EquipStrengthenRet.prototype.clazzName,
                EquipBatchStrengthenRet.prototype.clazzName,
                EquipMasterActivateRet.prototype.clazzName,
                EquipSuitActivateRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 魔法-获得/升级/等级重置/升星/一键升星
        Magical.getInstance().on(
            [
                MagicalCreateRet.prototype.clazzName,
                MagicalUpgradeRet.prototype.clazzName,
                MagicalResetLevelRet.prototype.clazzName,
                MagicalIncrStarRet.prototype.clazzName,
                MagicalBattleRet.prototype.clazzName,
                MagicalBatchBattleRet.prototype.clazzName,
                MagicalAnalyzeRet.prototype.clazzName,
                MagicalBatchIncrStarRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 王权-升级/突破/晋升
        Power.getInstance().on(
            [
                PowerUpgradeRet.prototype.clazzName,
                PowerBreakOutRet.prototype.clazzName,
                PowerPromotionRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 祝福-开始/结束/升级
        Bless.getInstance().on(
            [BlessStartRet.prototype.clazzName, BlessEvent.End, BlessEvent.LevelUpdate],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 天赋-升级/完成
        Talent.getInstance().on(
            [TalentTreeUpgradeRet.prototype.clazzName, TalentTreeCompleteRet.prototype.clazzName],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 锻造台-获得/升级/培养升级
        Forge.getInstance().on(
            [
                ForgeCreateSkinNoticeRet.prototype.clazzName,
                ForgeCompleteRet.prototype.clazzName,
                ForgeCultivateUpgradeRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
        // 藏品-获得/升级/升星
        Collection.getInstance().on(
            [
                CollectionCreateNoticeRet.prototype.clazzName,
                CollectionUpgradeRet.prototype.clazzName,
                CollectionUpgradeStarRet.prototype.clazzName,
            ],
            () => {
                this.emit(AttributeEvent.Update);
            },
            this
        );
    }

    /**
     * 初始化玩家属性
     * @param player 玩家
     */
    public initPlayerAttr(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        playerBaseData.attr = {};
        const attrInfo = TBAttribute.getInstance().getList();
        attrInfo.forEach((e) => {
            playerBaseData.attr[e.id] = {
                id: e.id,
                value: 0,
                finalValue: 0,
                scoreValue: 0,
                initValue: e.initial,
                culValue: 0,
                skillValue: 0,
                info: e,
                initLog: "",
                culLog: "",
                skillLog: "",
            };
            playerBaseData.attr[e.id].initLog = `初始-${playerBaseData.attr[e.id].initValue}`;
        });
    }

    /**
     * 更新玩家属性-培养
     * @param player 玩家
     */
    public updatePlayerAttrByCul(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();

        for (const e in playerBaseData.attr) {
            playerBaseData.attr[e].culValue = 0;
            playerBaseData.attr[e].culLog = "";
        }
        // 主角等级
        this.updateAttrByLeadLevel(player);
        // 主角星级
        this.updateAttrByLeadStar(player);
        // 神器等级
        this.updateAttrByWeaponLevel(player);
        // 神器星级
        this.updateAttrByWeaponStar(player);
        // 背饰等级
        this.updateAttrByWingLevel(player);
        // 背饰星级
        this.updateAttrByWingStar(player);
        // 背饰附魔
        this.updateAttrByWingEnchantment(player);
        // 战车等级
        this.updateAttrByTankLevel(player);
        // 战车星级
        this.updateAttrByTankStar(player);
        // 弓箭手等级
        this.updateAttrByArcherLevel(player);
        // 弓箭手星级
        this.updateAttrByArcherStar(player);
        // 弓箭手羁绊
        this.updateAttrByArcherGroup(player);
        // 宠物
        this.updateAttrByPet(player);
        // 技能
        this.updateAttrBySkill(player);
        // 装备
        this.updateAttrByEquip(player);
        // 装备强化
        this.updateAttrByEquipStrengthen(player);
        // 装备阶段
        this.updateAttrByEquipStage(player);
        // 装备套装
        this.updateAttrByEquipSuit(player);
        // 魔法等级
        this.updateAttrByMagicLevel(player);
        // 魔法星级
        this.updateAttrByMagicStar(player);
        // 王权
        this.updateAttrByPower(player);
        // 祝福
        this.updateAttrByBless(player);
        // 天赋
        this.updateAttrByTalent(player);
        // 锻造台
        this.updateAttrByForge(player);
        // 锻造台等级
        this.updateAttrByForgeLevel(player);
        // 藏品
        this.updateAttrByCollection(player);
        // 藏品套装
        this.updateAttrByCollectionSuit(player);
        // 道具
        this.updateAttrByItem(player);
    }

    /**
     * 更新玩家属性-技能
     * @param player 玩家
     */
    public updatePlayerAttrBySkill(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();

        for (const attrId in playerBaseData.attr) {
            const attrData = playerBaseData.attr[attrId];

            attrData.skillValue = 0;
            attrData.skillLog = "";
        }

        const buffData = playerBaseData.buffData.filter(
            (e) => e.info.effectType === EnumSkillEffectEffectType.Buff201 && e.layerData.length !== 0
        );
        buffData.forEach((e) => {
            const [attrId, init] = e.para;
            const value = init * e.layerData.length;
            playerBaseData.attr[attrId].skillValue += value;
            playerBaseData.attr[attrId].skillLog += `增益${e.id}-${value}->`;
        });

        this.sumAttr(playerBaseData);

        this.logPlayerAttr(CombatLogId.PlayerAttr, player);
    }

    /**
     * 初始化怪兽属性
     * @param monster 怪兽
     */
    public initMonsterAttr(monster: CombatMemberMonster): void {
        const monsterBaseData = monster.getBaseData();
        monsterBaseData.attr = {};
        const attrInfo = TBAttribute.getInstance().getList();
        attrInfo.forEach((e) => {
            monsterBaseData.attr[e.id] = {
                id: e.id,
                value: 0,
                finalValue: 0,
                scoreValue: 0,
                initValue: 0,
                culValue: 0,
                skillValue: 0,
                info: e,
                initLog: "",
                culLog: "",
                skillLog: "",
            };
            monsterBaseData.attr[e.id].initLog = `初始-${monsterBaseData.attr[e.id].initValue}`;
        });
    }

    /**
     * 更新怪兽属性-培养
     * @param monster 怪兽
     */
    public updateMonsterAttrByCul(monster: CombatMemberMonster): void {
        const monsterBaseData = monster.getBaseData();

        for (const attrId in monsterBaseData.attr) {
            const attrData = monsterBaseData.attr[attrId];

            attrData.culValue = 0;
            attrData.culLog = "";
        }

        // 上阵属性
        // 怪兽
        this.updateAttrByMonster(monster);

        // 系统属性
        // 副本
        this.updateAttrByDungeon(monster);
    }

    /**
     * 更新怪兽属性-技能
     * @param monster 怪兽
     */
    public updateMonsterAttrBySkill(monster: CombatMemberMonster): void {
        const monsterBaseData = monster.getBaseData();

        for (const attrId in monsterBaseData.attr) {
            const attrData = monsterBaseData.attr[attrId];

            attrData.skillValue = 0;
            attrData.skillLog = "";
        }

        const buffData = monsterBaseData.buffData.filter(
            (e) => e.info.effectType === EnumSkillEffectEffectType.Buff201 && e.layerData.length !== 0
        );
        buffData.forEach((e) => {
            const [attrId, init] = e.para;
            const value = init * e.layerData.length;
            monsterBaseData.attr[attrId].skillValue += value;
            monsterBaseData.attr[attrId].skillLog += `增益${e.id}-${value}->`;
        });

        this.sumAttr(monsterBaseData);

        this.logMonsterAttr(CombatLogId.MonsterAttr, monster);
    }

    /**
     * 汇总属性
     * @param memberBaseData 成员数据
     */
    private sumAttr(memberBaseData: ICombatMemberData): void {
        const attr = memberBaseData.attr;
        for (const key in attr) {
            attr[key].value = attr[key].initValue + attr[key].culValue + attr[key].skillValue;
            attr[key].value = MathUtils.round(attr[key].value, 4);
            attr[key].scoreValue = attr[key].initValue + attr[key].culValue;
            attr[key].scoreValue = MathUtils.round(attr[key].scoreValue, 4);
        }

        [EnumAttributeType.FateHpPer, EnumAttributeType.FateAtkPer, EnumAttributeType.FateDefPer].forEach((attrId) => {
            attr[attrId].value = attr[EnumAttributeType.Fate].value / attr[attrId].info.para[0];
            attr[attrId].value = MathUtils.round(attr[attrId].value, 4);
            attr[attrId].scoreValue = attr[EnumAttributeType.Fate].scoreValue / attr[attrId].info.para[0];
            attr[attrId].scoreValue = MathUtils.round(attr[attrId].scoreValue, 4);
        });

        [
            [
                EnumAttributeType.FinalHp,
                EnumAttributeType.Hp,
                EnumAttributeType.PowerHpPer,
                EnumAttributeType.GlobalHpPer,
                EnumAttributeType.FateHpPer,
            ],
            [
                EnumAttributeType.FinalAtk,
                EnumAttributeType.Atk,
                EnumAttributeType.PowerAtkPer,
                EnumAttributeType.GlobalAtkPer,
                EnumAttributeType.FateAtkPer,
            ],
            [
                EnumAttributeType.FinalDef,
                EnumAttributeType.Def,
                EnumAttributeType.PowerDefPer,
                EnumAttributeType.GlobalDefPer,
                EnumAttributeType.FateDefPer,
            ],
        ].forEach(([final, base, per, per2, per3]) => {
            attr[final].value =
                attr[base].value * (1 + attr[per].value) * (1 + attr[per2].value) * (1 + attr[per3].value);
            attr[final].value = attr[final].value * memberBaseData.hpAtkDefPer;
            attr[final].value = MathUtils.round(attr[final].value, 4);
            attr[final].value = MathUtils.floor(attr[final].value, 0);
            attr[final].scoreValue =
                attr[base].scoreValue *
                (1 + attr[per].scoreValue) *
                (1 + attr[per2].scoreValue) *
                (1 + attr[per3].scoreValue);
            attr[final].scoreValue = MathUtils.round(attr[final].scoreValue, 4);
            attr[final].scoreValue = MathUtils.floor(attr[final].scoreValue, 0);
        });
    }

    /**
     * 汇总属性-战斗力
     * @param attr 属性
     */
    public sumAttrByScore(attr: IAttr): void {
        for (const key in attr) {
            attr[key].scoreValue = attr[key].initValue + attr[key].culValue;
            attr[key].scoreValue = MathUtils.round(attr[key].scoreValue, 4);
        }

        [EnumAttributeType.FateHpPer, EnumAttributeType.FateAtkPer, EnumAttributeType.FateDefPer].forEach((attrId) => {
            attr[attrId].scoreValue = attr[EnumAttributeType.Fate].scoreValue / attr[attrId].info.para[0];
            attr[attrId].scoreValue = MathUtils.round(attr[attrId].scoreValue, 4);
        });

        [
            [
                EnumAttributeType.FinalHp,
                EnumAttributeType.Hp,
                EnumAttributeType.PowerHpPer,
                EnumAttributeType.GlobalHpPer,
                EnumAttributeType.FateHpPer,
            ],
            [
                EnumAttributeType.FinalAtk,
                EnumAttributeType.Atk,
                EnumAttributeType.PowerAtkPer,
                EnumAttributeType.GlobalAtkPer,
                EnumAttributeType.FateAtkPer,
            ],
            [
                EnumAttributeType.FinalDef,
                EnumAttributeType.Def,
                EnumAttributeType.PowerDefPer,
                EnumAttributeType.GlobalDefPer,
                EnumAttributeType.FateDefPer,
            ],
        ].forEach(([final, base, per, per2, per3]) => {
            attr[final].scoreValue =
                attr[base].scoreValue *
                (1 + attr[per].scoreValue) *
                (1 + attr[per2].scoreValue) *
                (1 + attr[per3].scoreValue);
            attr[final].scoreValue = MathUtils.round(attr[final].scoreValue, 4);
            attr[final].scoreValue = MathUtils.floor(attr[final].scoreValue, 0);
        });
    }

    /**
     * 计算最终属性
     * @param attr 属性-我方
     * @param attr2 属性-敌方
     */
    public calFinalAttr(attr: IAttr, attr2: IAttr): void {
        // 最终血量
        attr[EnumAttributeType.FinalHp].finalValue = attr[EnumAttributeType.FinalHp].value;

        // 最终攻击
        attr[EnumAttributeType.FinalAtk].finalValue = attr[EnumAttributeType.FinalAtk].value;

        // 最终防御
        attr2[EnumAttributeType.FinalDef].finalValue = attr2[EnumAttributeType.FinalDef].value;

        // 会心
        const crit = attr[EnumAttributeType.Crit];
        crit.finalValue = Math.max(crit.value - attr2[EnumAttributeType.CritRes].value, crit.info.para[1]);
        crit.finalValue = Math.min(crit.finalValue, crit.info.para[0]);

        // 会心伤害
        const critDmg = attr[EnumAttributeType.CritDmg];
        critDmg.finalValue = Math.max(
            critDmg.info.para[0] + critDmg.value - attr2[EnumAttributeType.CritDmgBlock].value,
            critDmg.info.para[1]
        );

        // 暴击
        const critHit = attr[EnumAttributeType.CritHit];
        critHit.finalValue = Math.max(critHit.value - attr2[EnumAttributeType.CritHitRes].value, critHit.info.para[1]);
        critHit.finalValue = Math.min(critHit.finalValue, critHit.info.para[0]);

        // 暴击伤害
        const critHitDmg = attr[EnumAttributeType.CritHitDmg];
        critHitDmg.finalValue = Math.max(
            critHitDmg.info.para[0] + critHitDmg.value - attr2[EnumAttributeType.CritHitDmgBlock].value,
            critHitDmg.info.para[1]
        );

        // 闪避
        const eva = attr[EnumAttributeType.Eva];
        const evaIgnore2 = attr2[EnumAttributeType.EvaIgnore];
        eva.finalValue = Math.min(eva.value - evaIgnore2.value, eva.info.para[0]);
        eva.finalValue = Math.max(eva.finalValue, eva.info.para[1]);

        // 破防
        const breakDef = attr[EnumAttributeType.BreakDef];
        const breakDef2 = attr2[EnumAttributeType.BreakDef];
        if (breakDef.value > breakDef2.value) {
            breakDef2.finalValue *= 1 - breakDef2.info.para[0];
        }

        // 专属神通加成
        attr[EnumAttributeType.ESkillB].finalValue =
            attr[EnumAttributeType.ESkillB].value - attr2[EnumAttributeType.ESkillR].value;

        // 主动神通加成
        attr[EnumAttributeType.ASkillB].finalValue =
            attr[EnumAttributeType.ASkillB].value - attr2[EnumAttributeType.ASkillR].value;

        // 被动神通加成
        attr[EnumAttributeType.PSkillB].finalValue =
            attr[EnumAttributeType.PSkillB].value - attr2[EnumAttributeType.PSkillR].value;

        // 普攻加成
        attr[EnumAttributeType.AtkB].finalValue =
            attr[EnumAttributeType.AtkB].value - attr2[EnumAttributeType.AtkR].value;

        // 弓箭手加成
        attr[EnumAttributeType.ArcherSkillB].finalValue =
            attr[EnumAttributeType.ArcherSkillB].value - attr2[EnumAttributeType.ArcherSkillR].value;

        // 宠物加成
        attr[EnumAttributeType.PetSkillB].finalValue =
            attr[EnumAttributeType.PetSkillB].value - attr2[EnumAttributeType.PetSkillR].value;

        // 怪兽加成
        attr[EnumAttributeType.MonsterDB].finalValue =
            attr[EnumAttributeType.MonsterDB].value - attr2[EnumAttributeType.MonsterDR].value;

        // 全局神通加成
        attr[EnumAttributeType.GlobalSkillB].finalValue =
            attr[EnumAttributeType.GlobalSkillB].value - attr2[EnumAttributeType.GlobalSkillR].value;

        // 火系伤害加成
        attr[EnumAttributeType.FireDB].finalValue =
            attr[EnumAttributeType.FireDB].value - attr2[EnumAttributeType.FireDR].value;

        // 雷系伤害加成
        attr[EnumAttributeType.ThunderDB].finalValue =
            attr[EnumAttributeType.ThunderDB].value - attr2[EnumAttributeType.ThunderDR].value;

        // 风系伤害加成
        attr[EnumAttributeType.WindDB].finalValue =
            attr[EnumAttributeType.WindDB].value - attr2[EnumAttributeType.WindDR].value;

        // 物理伤害加成
        attr[EnumAttributeType.PhysicsDB].finalValue =
            attr[EnumAttributeType.PhysicsDB].value - attr2[EnumAttributeType.PhysicsDR].value;

        // 真实伤害加成
        attr[EnumAttributeType.RealDB].finalValue =
            attr[EnumAttributeType.RealDB].value - attr2[EnumAttributeType.RealDR].value;

        // 全局伤害加成
        attr[EnumAttributeType.GlobalDB].finalValue =
            attr[EnumAttributeType.GlobalDB].value - attr2[EnumAttributeType.GlobalDR].value;

        // PVP加成
        attr[EnumAttributeType.PVPDB].finalValue =
            attr[EnumAttributeType.PVPDB].value - attr2[EnumAttributeType.PVPDR].value;
    }

    /**
     * 获取展示属性
     * @param sourceType 来源类型
     * @returns
     */
    public getShowAttr(sourceType: AttrSourceType): { [attrId: number]: number } {
        const attr: { [attrId: number]: number } = {};
        switch (sourceType) {
            case AttrSourceType.Total:
                const allPlayer = CombatDungeonMain.getInstance().getAllPlayer();
                const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
                const playerBaseData = player.getBaseData();
                for (const attrId in playerBaseData.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += playerBaseData.attr[attrId].scoreValue;
                }
                break;
            case AttrSourceType.Lead:
                for (const attrId in this.leadLevelAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.leadLevelAttr.attr[attrId];
                }
                for (const attrId in this.leadStarAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.leadStarAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Weapon:
                for (const attrId in this.weaponLevelAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.weaponLevelAttr.attr[attrId];
                }
                for (const attrId in this.weaponStarAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.weaponStarAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Wing:
                for (const attrId in this.wingLevelAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.wingLevelAttr.attr[attrId];
                }
                for (const attrId in this.wingStarAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.wingStarAttr.attr[attrId];
                }
                for (const attrId in this.wingEnchantment.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.wingEnchantment.attr[attrId];
                }
                break;
            case AttrSourceType.Tank:
                for (const attrId in this.tankLevelAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.tankLevelAttr.attr[attrId];
                }
                for (const attrId in this.tankStarAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.tankStarAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Archer:
                for (const attrId in this.archerLevelAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.archerLevelAttr.attr[attrId];
                }
                for (const attrId in this.archerStarAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.archerStarAttr.attr[attrId];
                }
                for (const attrId in this.archerGroupAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.archerGroupAttr.attr[attrId];
                }
                break;
            case AttrSourceType.ArcherGroup:
                for (const attrId in this.archerGroupAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.archerGroupAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Pet:
                for (const attrId in this.petAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.petAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Equip:
                for (const attrId in this.equipAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.equipAttr.attr[attrId];
                }
                for (const attrId in this.equipStrengthenAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.equipStrengthenAttr.attr[attrId];
                }
                for (const attrId in this.equipStageAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.equipStageAttr.attr[attrId];
                }
                for (const attrId in this.equipSuitAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.equipSuitAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Magic:
                for (const attrId in this.magicLevelAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.magicLevelAttr.attr[attrId];
                }
                for (const attrId in this.magicStarAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.magicStarAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Power:
                for (const attrId in this.powerAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.powerAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Bless:
                for (const attrId in this.blessAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.blessAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Talent:
                for (const attrId in this.talentAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.talentAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Forge:
                for (const attrId in this.forgeAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.forgeAttr.attr[attrId];
                }
                for (const attrId in this.forgeLevelAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.forgeLevelAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Collection:
                for (const attrId in this.collectionAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.collectionAttr.attr[attrId];
                }
                for (const attrId in this.collectionSuitAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.collectionSuitAttr.attr[attrId];
                }
                break;
            case AttrSourceType.Other:
                for (const attrId in this.itemAttr.attr) {
                    attr[attrId] = attr[attrId] || 0;
                    attr[attrId] += this.itemAttr.attr[attrId];
                }
                break;
            default:
                break;
        }
        for (const attrId in attr) {
            attr[attrId] = MathUtils.round(attr[attrId], 4);
        }

        return attr;
    }

    /**
     * 更新属性-主角等级
     * @param player 玩家
     */
    private updateAttrByLeadLevel(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();
        let leadData: ILeadSkinInfo[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            leadData = LeadSkin.getInstance().getAllData();

            isReset = !this.leadLevelAttr;
            if (!this.leadLevelAttr) {
                leadData.sort((a, b) => a.leadSkinId - b.leadSkinId);
                leadData.forEach((e) => cacheAttrData.para.push(e.leadSkinId, e.level));
            }
            if (!isReset) {
                cacheAttrData = this.leadLevelAttr;
                const tempPara: number[] = [];
                leadData.sort((a, b) => a.leadSkinId - b.leadSkinId);
                leadData.forEach((e) => tempPara.push(e.leadSkinId, e.level));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            leadData = combatData.leadSkinGroupInfo.leadSkinInfos;

            isReset = true;
            leadData.sort((a, b) => a.leadSkinId - b.leadSkinId);
            leadData.forEach((e) => cacheAttrData.para.push(e.leadSkinId, e.level));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            leadData.forEach((e) => {
                const levelInfo = TBLeadSkinLevel.getInstance().getDataByQualityAndLevel(e.quality, e.level);
                levelInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.leadLevelAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.LeadLevelAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `主角等级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-主角星级
     * @param player 玩家
     */
    private updateAttrByLeadStar(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();
        let leadData: ILeadSkinInfo[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            leadData = LeadSkin.getInstance().getAllData();

            isReset = !this.leadStarAttr;
            if (!this.leadStarAttr) {
                leadData.sort((a, b) => a.leadSkinId - b.leadSkinId);
                leadData.forEach((e) => cacheAttrData.para.push(e.leadSkinId, e.star));
            }
            if (!isReset) {
                cacheAttrData = this.leadStarAttr;
                const tempPara: number[] = [];
                leadData.sort((a, b) => a.leadSkinId - b.leadSkinId);
                leadData.forEach((e) => tempPara.push(e.leadSkinId, e.star));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            leadData = combatData.leadSkinGroupInfo.leadSkinInfos;

            isReset = true;
            leadData.sort((a, b) => a.leadSkinId - b.leadSkinId);
            leadData.forEach((e) => cacheAttrData.para.push(e.leadSkinId, e.star));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            leadData.forEach((e) => {
                const starInfo = TBLeadSkinStar.getInstance().getDataByQualityAndStar(e.quality, e.star);
                starInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.leadStarAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.LeadStarAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `主角星级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-神器等级
     * @param player 玩家
     */
    private updateAttrByWeaponLevel(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let levelId = -1;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            levelId = Weapon.getInstance().getLevel();

            isReset = !this.weaponLevelAttr;
            if (!this.weaponLevelAttr) {
                cacheAttrData.para.push(levelId);
            }
            if (!isReset) {
                cacheAttrData = this.weaponLevelAttr;
                const tempPara = [levelId];
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            levelId = combatData.weaponGroupInfo.levelId;

            isReset = true;
            cacheAttrData.para.push(levelId);
        }

        if (isReset) {
            cacheAttrData.attr = {};

            const levelInfo = TBWeaponLevel.getInstance().getDataById(levelId);
            levelInfo.attribute.forEach(([attrId, init]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init;
            });

            playerData.type === CombatPlayerType.Self && (this.weaponLevelAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.WeaponLevelAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `神器等级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-神器星级
     * @param player 玩家
     */
    private updateAttrByWeaponStar(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let weaponData: IWeaponObj[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            weaponData = Weapon.getInstance().getAllData();
            weaponData = weaponData.filter(
                (e) => TBWeapon.getInstance().getDataById(e.weaponId).type === EnumWeaponType.AdvancedWeapon
            );

            isReset = !this.weaponStarAttr;
            if (!this.weaponStarAttr) {
                weaponData.sort((a, b) => a.weaponId - b.weaponId);
                weaponData.forEach((e) => cacheAttrData.para.push(e.weaponId, e.star));
            }
            if (!isReset) {
                cacheAttrData = this.weaponStarAttr;
                const tempPara: number[] = [];
                weaponData.sort((a, b) => a.weaponId - b.weaponId);
                weaponData.forEach((e) => tempPara.push(e.weaponId, e.star));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            weaponData = combatData.weaponGroupInfo.weaponInfos;
            weaponData = weaponData.filter(
                (e) => TBWeapon.getInstance().getDataById(e.weaponId).type === EnumWeaponType.AdvancedWeapon
            );

            isReset = true;
            weaponData.sort((a, b) => a.weaponId - b.weaponId);
            weaponData.forEach((e) => cacheAttrData.para.push(e.weaponId, e.star));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            weaponData.forEach((e) => {
                const weaponInfo = TBWeapon.getInstance().getDataById(e.weaponId);
                const starInfo = TBWeaponStar.getInstance().getDataByQualityAndStar(weaponInfo.quality, e.star);
                starInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.weaponStarAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.WeaponStarAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `神器星级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-背饰等级
     * @param player 玩家
     */
    private updateAttrByWingLevel(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let featherData: IFeatherInfo[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            featherData = Wing.getInstance().getFeathers();

            isReset = !this.wingLevelAttr;
            if (!this.wingLevelAttr) {
                featherData.sort((a, b) => a.featherType - b.featherType);
                featherData.forEach((e) => cacheAttrData.para.push(e.featherType, e.level));
            }
            if (!isReset) {
                cacheAttrData = this.wingLevelAttr;
                const tempPara: number[] = [];
                featherData.sort((a, b) => a.featherType - b.featherType);
                featherData.forEach((e) => tempPara.push(e.featherType, e.level));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            featherData = combatData.wingsGroupInfo.featherInfos;

            isReset = true;
            featherData.sort((a, b) => a.featherType - b.featherType);
            featherData.forEach((e) => cacheAttrData.para.push(e.featherType, e.level));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            featherData.forEach((e) => {
                const levelInfo = TBWingLevel.getInstance().getDataByTypeAndLevel(e.featherType, e.level);
                levelInfo.stepAttribute.forEach(([attrId, init, step]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init + step * (e.level - levelInfo.levelInterval[0]);
                });
            });

            const minLevel = Math.min(...featherData.map((e) => e.level));
            const levelRewardInfo = TBWingLevelRewards.getInstance().getDataByLevel(minLevel);
            levelRewardInfo.attribute.forEach(([attrId, init]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init;
            });

            playerData.type === CombatPlayerType.Self && (this.wingLevelAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.WingLevelAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `背饰等级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-背饰星级
     * @param player 玩家
     */
    private updateAttrByWingStar(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let wingData: IWingInfo[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            wingData = Wing.getInstance().getAllData();
            wingData = wingData.filter(
                (e) => TBWing.getInstance().getDataById(e.wingId).type === EnumWingType.AdvancedWing
            );

            isReset = !this.wingStarAttr;
            if (!this.wingStarAttr) {
                wingData.sort((a, b) => a.wingId - b.wingId);
                wingData.forEach((e) => cacheAttrData.para.push(e.wingId, e.star));
            }
            if (!isReset) {
                cacheAttrData = this.wingStarAttr;
                const tempPara: number[] = [];
                wingData.sort((a, b) => a.wingId - b.wingId);
                wingData.forEach((e) => tempPara.push(e.wingId, e.star));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            wingData = combatData.wingsGroupInfo.wingInfos;
            wingData = wingData.filter(
                (e) => TBWing.getInstance().getDataById(e.wingId).type === EnumWingType.AdvancedWing
            );

            isReset = true;
            wingData.sort((a, b) => a.wingId - b.wingId);
            wingData.forEach((e) => cacheAttrData.para.push(e.wingId, e.star));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            wingData.forEach((e) => {
                const wingInfo = TBWing.getInstance().getDataById(e.wingId);
                const starInfo = TBWingStar.getInstance().getDataByQualityAndStar(wingInfo.quality, e.star);
                starInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.wingStarAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.WingStarAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `背饰星级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-背饰附魔
     * @param player 玩家
     */
    private updateAttrByWingEnchantment(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let enchantmentData: IWingEnchantObj[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            enchantmentData = Wing.getInstance().getEnchantData();
            enchantmentData.sort((a, b) => a.enchantType - b.enchantType);

            isReset = !this.wingEnchantment;
            if (!this.wingEnchantment) {
                enchantmentData.forEach((e) => cacheAttrData.para.push(e.enchantType, e.level));
            }
            if (!isReset) {
                cacheAttrData = this.wingEnchantment;
                const tempPara: number[] = [];
                enchantmentData.forEach((e) => tempPara.push(e.enchantType, e.level));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            enchantmentData = combatData.wingsGroupInfo.enchantInfos;
            enchantmentData.sort((a, b) => a.enchantType - b.enchantType);

            isReset = true;
            enchantmentData.forEach((e) => cacheAttrData.para.push(e.enchantType, e.level));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            enchantmentData.forEach((e) => {
                const enchantmentInfo = TBWingEnchant.getInstance().getDataByTypeAndLevel(e.enchantType, e.level);
                enchantmentInfo.stepAttribute.forEach(([attrId, init, step]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init + step * (e.level - enchantmentInfo.levelInterval[0]);
                });
            });

            playerData.type === CombatPlayerType.Self && (this.wingEnchantment = cacheAttrData);

            this.logModuleAttr(CombatLogId.WingEnchantmentAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `背饰附魔-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-战车等级
     * @param player 玩家
     */
    private updateAttrByTankLevel(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let levelId = -1;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            levelId = Tank.getInstance().getLevelId();

            isReset = !this.tankLevelAttr;
            if (!this.tankLevelAttr) {
                cacheAttrData.para.push(levelId);
            }
            if (!isReset) {
                cacheAttrData = this.tankLevelAttr;
                const tempPara = [levelId];
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            levelId = combatData.tanksInfo.level;

            isReset = true;
            cacheAttrData.para.push(levelId);
        }

        if (isReset) {
            cacheAttrData.attr = {};

            const levelInfo = TBTankLevel.getInstance().getDataById(levelId);
            levelInfo.attribute.forEach(([attrId, init]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init;
            });

            playerData.type === CombatPlayerType.Self && (this.tankLevelAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.TankLevelAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `战车等级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-战车星级
     * @param player 玩家
     */
    private updateAttrByTankStar(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let tankData: ITankInfo[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            tankData = Tank.getInstance().getAllInfo();
            tankData = tankData.filter(
                (e) => TBTank.getInstance().getDataById(e.tankId).type === EnumTankType.AdvancedTank
            );

            isReset = !this.tankStarAttr;
            if (!this.tankStarAttr) {
                tankData.sort((a, b) => a.tankId - b.tankId);
                tankData.forEach((e) => cacheAttrData.para.push(e.tankId, e.star));
            }
            if (!isReset) {
                cacheAttrData = this.tankStarAttr;
                const tempPara: number[] = [];
                tankData.sort((a, b) => a.tankId - b.tankId);
                tankData.forEach((e) => tempPara.push(e.tankId, e.star));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            tankData = combatData.tanksInfo.tankInfos;
            tankData = tankData.filter(
                (e) => TBTank.getInstance().getDataById(e.tankId).type === EnumTankType.AdvancedTank
            );

            isReset = true;
            tankData.sort((a, b) => a.tankId - b.tankId);
            tankData.forEach((e) => cacheAttrData.para.push(e.tankId, e.star));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            tankData.forEach((e) => {
                const tankInfo = TBTank.getInstance().getDataById(e.tankId);
                const starInfo = TBTankStar.getInstance().getTankStarByQualityAndStar(tankInfo.quality, e.star);
                starInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.tankStarAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.TankStarAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `战车星级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-弓箭手等级
     * @param player
     */
    private updateAttrByArcherLevel(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();
        const archerData: IArcherObj[] = [];
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            const battleData = Archer.getInstance().getTeam();
            battleData.forEach((e) => archerData.push(Archer.getInstance().getDataById(e.archerId)));

            isReset = !this.archerLevelAttr;
            if (!this.archerLevelAttr) {
                archerData.sort((a, b) => a.archerId - b.archerId);
                archerData.forEach((e) => cacheAttrData.para.push(e.archerId, e.level));
            }
            if (!isReset) {
                cacheAttrData = this.archerLevelAttr;
                const tempPara: number[] = [];
                archerData.sort((a, b) => a.archerId - b.archerId);
                archerData.forEach((e) => tempPara.push(e.archerId, e.level));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            combatData.archerGroupInfo.battleInfos.forEach((e) =>
                archerData.push(combatData.archerGroupInfo.archerInfos.find((e2) => e2.archerId === e.archerId))
            );

            isReset = true;
            archerData.sort((a, b) => a.archerId - b.archerId);
            archerData.forEach((e) => cacheAttrData.para.push(e.archerId, e.level));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            archerData.forEach((e) => {
                const levelInfo = TBArcherLevel.getInstance().getDataByQualityAndLevel(e.quality, e.level);
                const archerInfo = TBArcher.getInstance().getDataById(e.archerId);
                levelInfo.attribute.forEach(([attrId, init]) => {
                    const index = archerInfo.factor.findIndex(([tempAttrId]) => tempAttrId === attrId);
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init * (index !== -1 ? archerInfo.factor[index][1] : 1);
                });
            });

            playerData.type === CombatPlayerType.Self && (this.archerLevelAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.ArcherLevelAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `弓箭手等级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-弓箭手星级
     * @param player
     */
    private updateAttrByArcherStar(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();
        const archerData: IArcherObj[] = [];
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            const battleData = Archer.getInstance().getTeam();
            battleData.forEach((e) => archerData.push(Archer.getInstance().getDataById(e.archerId)));

            isReset = !this.archerStarAttr;
            if (!this.archerStarAttr) {
                archerData.sort((a, b) => a.archerId - b.archerId);
                archerData.forEach((e) => cacheAttrData.para.push(e.archerId, e.star));
            }
            if (!isReset) {
                cacheAttrData = this.archerStarAttr;
                const tempPara: number[] = [];
                archerData.sort((a, b) => a.archerId - b.archerId);
                archerData.forEach((e) => tempPara.push(e.archerId, e.star));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            combatData.archerGroupInfo.battleInfos.forEach((e) =>
                archerData.push(combatData.archerGroupInfo.archerInfos.find((e2) => e2.archerId === e.archerId))
            );

            isReset = true;
            archerData.sort((a, b) => a.archerId - b.archerId);
            archerData.forEach((e) => cacheAttrData.para.push(e.archerId, e.star));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            archerData.forEach((e) => {
                const starInfo = TBArcherStar.getInstance().getDataByQualityAndStar(e.quality, e.star);
                starInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.archerStarAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.ArcherStarAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `弓箭手星级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-弓箭手羁绊
     * @param player
     */
    private updateAttrByArcherGroup(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let effectInfo: DataGroupEffect[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            const archerData = Archer.getInstance().getAllArcherInfo();
            effectInfo = Archer.getInstance().getEffectGroupEffectInfo(archerData);

            isReset = !this.archerGroupAttr;
            if (!this.archerGroupAttr) {
                effectInfo.forEach((e) => cacheAttrData.para.push(e.id));
            }
            if (!isReset) {
                cacheAttrData = this.archerGroupAttr;
                const tempPara: number[] = [];
                effectInfo.forEach((e) => tempPara.push(e.id));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            effectInfo = Archer.getInstance().getEffectGroupEffectInfo(combatData.archerGroupInfo.archerInfos);

            isReset = true;
            effectInfo.forEach((e) => cacheAttrData.para.push(e.id));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            effectInfo.forEach((e) => {
                e.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.archerGroupAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.ArcherGroupAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `弓箭手羁绊-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-宠物
     * @param player 玩家
     */
    private updateAttrByPet(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let petBattleData: IPetBattleObj[] = null;
        let petData: IPetObj[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            petBattleData = Pet.getInstance().getTeam();
            petData = Pet.getInstance().getAllPetInfo();

            isReset = !this.petAttr;
            if (!this.petAttr) {
                petBattleData.sort((a, b) => a.uuid - b.uuid);
                petBattleData.forEach((e) => {
                    const tempPetData = petData.find((e2) => e2.uuid === e.uuid);
                    cacheAttrData.para.push(e.uuid, tempPetData.level, tempPetData.star);
                    tempPetData.entryInfos.sort((a, b) => a.position - b.position);
                    tempPetData.entryInfos.forEach(
                        (e2) => e2.entrySrc === EntryObj.EntrySrc.Warn && cacheAttrData.para.push(e2.entryId, e2.level)
                    );
                });
            }
            if (!isReset) {
                cacheAttrData = this.petAttr;
                const tempPara: number[] = [];
                petBattleData.sort((a, b) => a.uuid - b.uuid);
                petBattleData.forEach((e) => {
                    const tempPetData = petData.find((e2) => e2.uuid === e.uuid);
                    tempPara.push(e.uuid, tempPetData.level, tempPetData.star);
                    tempPetData.entryInfos.sort((a, b) => a.position - b.position);
                    tempPetData.entryInfos.forEach(
                        (e2) => e2.entrySrc === EntryObj.EntrySrc.Warn && tempPara.push(e2.entryId, e2.level)
                    );
                });
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            petBattleData = combatData.petGroupInfo.battleInfos;
            petData = combatData.petGroupInfo.petInfos;

            isReset = true;
            petBattleData.sort((a, b) => a.uuid - b.uuid);
            petBattleData.forEach((e) => {
                const tempPetData = petData.find((e2) => e2.uuid === e.uuid);
                cacheAttrData.para.push(e.uuid, tempPetData.level, tempPetData.star);
                tempPetData.entryInfos.sort((a, b) => a.position - b.position);
                tempPetData.entryInfos.forEach(
                    (e2) => e2.entrySrc === EntryObj.EntrySrc.Warn && cacheAttrData.para.push(e2.entryId, e2.level)
                );
            });
        }

        if (isReset) {
            cacheAttrData.attr = {};

            petBattleData.forEach((e) => {
                const tempPetData = petData.find((e2) => e2.uuid === e.uuid);

                const levelInfo = TBPetLevel.getInstance().getDataByQualityAndLevel(
                    tempPetData.quality,
                    tempPetData.level
                );
                levelInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });

                const starInfo = TBPetStar.getInstance().getDataByQualityAndStar(tempPetData.quality, tempPetData.star);
                starInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });

                tempPetData.entryInfos.forEach((e) => {
                    if (e.entrySrc === EntryObj.EntrySrc.Warn) {
                        const entryInfo = TBEntry.getInstance().getDataById(e.entryId);
                        entryInfo.stepAttribute.forEach(([attrId, init, step]) => {
                            cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                            cacheAttrData.attr[attrId] += init + step * (e.level - 1);
                        });
                    }
                });
            });

            playerData.type === CombatPlayerType.Self && (this.petAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.PetAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `宠物-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-技能
     * @param player 玩家
     */
    private updateAttrBySkill(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        if (playerData.type !== CombatPlayerType.Self) {
            return;
        }

        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };

        isReset = !this.skillAttr;
        if (!this.skillAttr) {
            playerBaseData.skill.sort((a, b) => a.getData().id - b.getData().id);
            playerBaseData.skill.forEach((e) => {
                const skillData = e.getData();

                cacheAttrData.para.push(skillData.id);
                skillData.info.skillAttribute.forEach(([paraId]) => {
                    const paraValue = Skill.getInstance().getStepParaValue(paraId, skillData.memberId);
                    cacheAttrData.para.push(paraValue);
                });
            });
        }
        if (!isReset) {
            cacheAttrData = this.skillAttr;
            const tempPara: number[] = [];
            playerBaseData.skill.sort((a, b) => a.getData().id - b.getData().id);
            playerBaseData.skill.forEach((e) => {
                const skillData = e.getData();

                tempPara.push(skillData.id);
                skillData.info.skillAttribute.forEach(([paraId]) => {
                    const paraValue = Skill.getInstance().getStepParaValue(paraId, skillData.memberId);
                    tempPara.push(paraValue);
                });
            });
            isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
            isReset && (cacheAttrData.para = tempPara);
        }

        if (isReset) {
            cacheAttrData.attr = {};

            playerBaseData.skill.forEach((e) => {
                const skillData = e.getData();

                skillData.info.skillAttribute.forEach(([paraId, attrId, paraInit, attrInit], i) => {
                    let attrValue = attrInit;
                    const paraValue = Skill.getInstance().getStepParaValue(paraId, skillData.memberId);
                    let tempParaValue = paraInit;
                    for (const [paraRange, attrStep] of skillData.info.skillAttributeUp[i]) {
                        attrValue += attrStep * (Math.min(paraValue, paraRange) - tempParaValue);
                        tempParaValue = paraRange;

                        if (paraValue <= tempParaValue) {
                            break;
                        }
                    }

                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += attrValue;
                });
            });

            this.skillAttr = cacheAttrData;

            this.logModuleAttr(CombatLogId.SkillAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `技能-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-装备
     * @param player
     */
    private updateAttrByEquip(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();
        let equipData: IEquipInfo[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            const equipUuid = Equip.getInstance().getEquippedList();
            equipData = Equip.getInstance().getInfoByUuids(equipUuid);

            isReset = !this.equipAttr;
            if (!this.equipAttr) {
                cacheAttrData.para = equipUuid.concat();
            }
            if (!isReset) {
                cacheAttrData = this.equipAttr;
                const tempPara = equipUuid.concat();
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            equipData = [];
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            const equipUuid = combatData.equipGroupInfo.equipDressUuids;
            equipUuid.forEach((e) => {
                const tempEquipData = combatData.equipGroupInfo.equipBag.find((e2) => e2.uuid === e);
                tempEquipData && equipData.push(tempEquipData);
            });

            isReset = true;
            cacheAttrData.para = equipUuid.concat();
        }

        if (isReset) {
            cacheAttrData.attr = {};

            equipData.forEach((e) => {
                e.baseAttributeInfos.forEach((e2) => {
                    cacheAttrData.attr[e2.type] = cacheAttrData.attr[e2.type] || 0;
                    cacheAttrData.attr[e2.type] += e2.value;
                });
                e.addAttributeInfos.forEach((e3) => {
                    cacheAttrData.attr[e3.type] = cacheAttrData.attr[e3.type] || 0;
                    cacheAttrData.attr[e3.type] += e3.value;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.equipAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.EquipAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `装备-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-装备强化
     * @param player
     */
    private updateAttrByEquipStrengthen(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();
        let strengthenData: IEquipPositionInfo[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            strengthenData = Equip.getInstance().getAllPositionData();

            isReset = !this.equipStrengthenAttr;
            if (!this.equipStrengthenAttr) {
                strengthenData.sort((a, b) => a.position - b.position);
                strengthenData.forEach((e) => cacheAttrData.para.push(e.position, e.level));
            }
            if (!isReset) {
                cacheAttrData = this.equipStrengthenAttr;
                const tempPara: number[] = [];
                strengthenData.sort((a, b) => a.position - b.position);
                strengthenData.forEach((e) => tempPara.push(e.position, e.level));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            strengthenData = combatData.equipGroupInfo.equipPositionInfos;

            isReset = true;
            strengthenData.sort((a, b) => a.position - b.position);
            strengthenData.forEach((e) => cacheAttrData.para.push(e.position, e.level));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            strengthenData.forEach((e) => {
                const strengthenInfo = TBLeadEquipStrengthen.getInstance().getDataByPositionAndLevel(
                    e.position,
                    e.level
                );
                strengthenInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.equipStrengthenAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.EquipStrengthenAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `装备强化-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-装备阶段
     * @param player
     */
    private updateAttrByEquipStage(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();
        let stageId = -1;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            stageId = Equip.getInstance().getMasterId();

            isReset = !this.equipStageAttr;
            if (!this.equipStageAttr) {
                cacheAttrData.para = [stageId];
            }
            if (!isReset) {
                cacheAttrData = this.equipStageAttr;
                const tempPara = [stageId];
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            stageId = combatData.equipGroupInfo.masterId;

            isReset = true;
            cacheAttrData.para = [stageId];
        }

        if (isReset) {
            cacheAttrData.attr = {};

            const stageInfo = TBLeadEquipMaster.getInstance().getDataById(stageId);
            stageInfo.attribute.forEach(([attrId, init]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init;
            });

            playerData.type === CombatPlayerType.Self && (this.equipStageAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.EquipStageAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `装备强化阶段-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-装备套装
     * @param player
     */
    private updateAttrByEquipSuit(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();
        let suitData: IEquipSuitInfo[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            suitData = Equip.getInstance().getSuitInfos();

            isReset = !this.equipSuitAttr;
            if (!this.equipSuitAttr) {
                suitData.sort((a, b) => a.num - b.num);
                suitData.forEach((e) => cacheAttrData.para.push(e.num, e.suitId));
            }
            if (!isReset) {
                cacheAttrData = this.equipSuitAttr;
                const tempPara: number[] = [];
                suitData.sort((a, b) => a.num - b.num);
                suitData.forEach((e) => tempPara.push(e.num, e.suitId));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            suitData = combatData.equipGroupInfo.equipSuitInfos;

            isReset = true;
            suitData.sort((a, b) => a.num - b.num);
            suitData.forEach((e) => cacheAttrData.para.push(e.num, e.suitId));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            suitData.forEach((e) => {
                const suitInfo = TBLeadEquipSuit.getInstance().getDataById(e.suitId);
                suitInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.equipSuitAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.EquipSuitAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `装备套装-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-魔法等级
     * @param player 玩家
     */
    private updateAttrByMagicLevel(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let magicBattleData: IMagicalBattleObj[] = null;
        let magicData: IMagicalObj[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            magicBattleData = Magical.getInstance().getSkillBattleInfo();
            magicData = Magical.getInstance().getMagicalInfo();

            isReset = !this.magicLevelAttr;
            if (!this.magicLevelAttr) {
                magicBattleData.sort((a, b) => a.magicalId - b.magicalId);
                magicBattleData.forEach((e) => {
                    cacheAttrData.para.push(e.magicalId);
                    const tempMagicData = magicData.find((e2) => e2.magicalId === e.magicalId);
                    cacheAttrData.para.push(tempMagicData.level);
                });
            }
            if (!isReset) {
                cacheAttrData = this.magicLevelAttr;
                const tempPara: number[] = [];
                magicBattleData.sort((a, b) => a.magicalId - b.magicalId);
                magicBattleData.forEach((e) => {
                    tempPara.push(e.magicalId);
                    const tempMagicData = magicData.find((e2) => e2.magicalId === e.magicalId);
                    tempPara.push(tempMagicData.level);
                });
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            magicBattleData = combatData.magicalGroupInfo.magicalBattleInfos;
            magicData = combatData.magicalGroupInfo.magicalInfos;

            isReset = true;
            magicBattleData.sort((a, b) => a.magicalId - b.magicalId);
            magicBattleData.forEach((e) => {
                cacheAttrData.para.push(e.magicalId);
                const tempMagicData = magicData.find((e2) => e2.magicalId === e.magicalId);
                cacheAttrData.para.push(tempMagicData.level);
            });
        }

        if (isReset) {
            cacheAttrData.attr = {};

            magicBattleData.forEach((e) => {
                const magicInfo = TBMagicSkill.getInstance().getDataById(e.magicalId);
                const tempMagicData = magicData.find((e2) => e2.magicalId === e.magicalId);
                const levelInfo = TBMagicSkillLevel.getInstance().getDataByQualityAndTypeAndLevel(
                    magicInfo.quality,
                    magicInfo.type,
                    tempMagicData.level
                );
                levelInfo.attribute.forEach(([attrId, init]) => {
                    const index = magicInfo.factor.findIndex(([tempAttrId]) => tempAttrId === attrId);
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init * (index !== -1 ? magicInfo.factor[index][1] : 1);
                });
            });

            playerData.type === CombatPlayerType.Self && (this.magicLevelAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.MagicLevelAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `魔法等级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-魔法星级
     * @param player 玩家
     */
    private updateAttrByMagicStar(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let magicData: IMagicalObj[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            magicData = Magical.getInstance().getMagicalInfo();

            isReset = !this.magicStarAttr;
            if (!this.magicStarAttr) {
                magicData.sort((a, b) => a.magicalId - b.magicalId);
                magicData.forEach((e) => cacheAttrData.para.push(e.magicalId, e.star));
            }
            if (!isReset) {
                cacheAttrData = this.magicStarAttr;
                const tempPara: number[] = [];
                magicData.sort((a, b) => a.magicalId - b.magicalId);
                magicData.forEach((e) => tempPara.push(e.magicalId, e.star));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            magicData = combatData.magicalGroupInfo.magicalInfos;

            isReset = true;
            magicData.sort((a, b) => a.magicalId - b.magicalId);
            magicData.forEach((e) => cacheAttrData.para.push(e.magicalId, e.star));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            magicData.forEach((e) => {
                const starInfo = TBMagicSkillStar.getInstance().getDataByMagicIdAndStarAndQuality(
                    e.magicalId,
                    e.quality,
                    e.star
                );
                starInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.magicStarAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.MagicStarAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `魔法星级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-王权
     * @param player
     */
    private updateAttrByPower(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let powerData: IPowerInfo = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            powerData = Power.getInstance().getPowerInfo();

            isReset = !this.powerAttr;
            if (!this.powerAttr) {
                cacheAttrData.para = [powerData.kingLevelId, powerData.upgradeTimes];
            }
            if (!isReset) {
                cacheAttrData = this.powerAttr;
                const tempPara = [powerData.kingLevelId, powerData.upgradeTimes];
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            powerData = combatData.powerInfo;

            isReset = true;
            cacheAttrData.para = [powerData.kingLevelId, powerData.upgradeTimes];
        }

        if (isReset) {
            cacheAttrData.attr = {};

            const levelInfo = TBPowerLevel.getInstance().getDataById(powerData.kingLevelId);
            levelInfo.stepAttribute.forEach(([attrId, init, step]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init + step * powerData.upgradeTimes;
            });
            levelInfo.attribute.forEach(([attrId, init]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init;
            });

            const promotionInfo = TBPowerPromotion.getInstance().getDataByTitle(levelInfo.titleId);
            promotionInfo.attribute.forEach(([attrId, init]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init;
            });

            playerData.type === CombatPlayerType.Self && (this.powerAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.PowerAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `王权-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-祝福
     * @param player 玩家
     */
    private updateAttrByBless(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let levelId = -1;
        let blessData: IBlessInfo[] = null;
        let isRecharged = false;
        const nowTime = Time.getInstance().now();
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            levelId = Bless.getInstance().getLevelId();
            blessData = Bless.getInstance().getInfos();
            blessData.sort((a, b) => a.id - b.id);
            isRecharged = Recharge.getInstance().isRecharged(RECHARGE_ID.BLESS);

            isReset = !this.blessAttr;
            if (!this.blessAttr) {
                cacheAttrData.para.push(levelId);
                blessData.forEach((e) => {
                    const isEffect = isRecharged || nowTime < e.updateTime + e.surplusTime * 1000;
                    cacheAttrData.para.push(e.id, e.level, isEffect ? 1 : 0);
                });
            }
            if (!isReset) {
                cacheAttrData = this.blessAttr;
                const tempPara: number[] = [];
                tempPara.push(levelId);
                blessData.forEach((e) => {
                    const isEffect = isRecharged || nowTime < e.updateTime + e.surplusTime * 1000;
                    tempPara.push(e.id, e.level, isEffect ? 1 : 0);
                });
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            levelId = combatData.blessGroupInfo.blessLevel;
            blessData = combatData.blessGroupInfo.blessInfos;
            blessData.sort((a, b) => a.id - b.id);
            isRecharged = combatData.blessGroupInfo.isBuyPack;

            isReset = true;
            cacheAttrData.para.push(levelId);
            blessData.forEach((e) => {
                const isEffect = isRecharged || nowTime < e.updateTime + e.surplusTime * 1000;
                cacheAttrData.para.push(e.id, e.level, isEffect ? 1 : 0);
            });
        }

        if (isReset) {
            cacheAttrData.attr = {};

            const levelInfo = TBBlessingLevel.getInstance().getDataById(levelId);
            levelInfo.attribute.forEach(([attrId, init]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init;
            });

            blessData.forEach((e) => {
                const isEffect = isRecharged || nowTime < e.updateTime + e.surplusTime * 1000;
                if (isEffect) {
                    const blessInfo = TBBlessing.getInstance().getDataById(e.id);
                    blessInfo.stepAttribute.forEach(([attrId, init, step]) => {
                        cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                        cacheAttrData.attr[attrId] += init + step * (e.level - 1);
                    });
                }
            });

            playerData.type === CombatPlayerType.Self && (this.blessAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.Bless, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `祝福-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-天赋
     * @param player 玩家
     */
    private updateAttrByTalent(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let talentData: ITalentTreeInfo[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            talentData = Talent.getInstance().getAllTreeData();

            isReset = !this.talentAttr;
            if (!this.talentAttr) {
                talentData.forEach((e) => {
                    e.leafInfos.sort((a, b) => a.leafId - b.leafId);
                    e.leafInfos.forEach((e2) => cacheAttrData.para.push(e2.leafId, e2.level));
                });
            }
            if (!isReset) {
                cacheAttrData = this.talentAttr;
                const tempPara: number[] = [];
                talentData.forEach((e) => {
                    e.leafInfos.sort((a, b) => a.leafId - b.leafId);
                    e.leafInfos.forEach((e2) => tempPara.push(e2.leafId, e2.level));
                });
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            talentData = combatData.talentTreeInfos;

            isReset = true;
            talentData.forEach((e) => {
                e.leafInfos.sort((a, b) => a.leafId - b.leafId);
                e.leafInfos.forEach((e2) => cacheAttrData.para.push(e2.leafId, e2.level));
            });
        }

        if (isReset) {
            cacheAttrData.attr = {};

            talentData.forEach((e) => {
                e.leafInfos.forEach((e2) => {
                    const leafInfo = TBTalentLeaf.getInstance().getDataById(e2.leafId);
                    if (leafInfo.nature === EnumTalentLeafNature.Attribute) {
                        const leafGroupInfo = TBTalentLeafGroup.getInstance().getDataByGroupAndLevel(
                            leafInfo.group,
                            e2.level
                        );
                        const [attrId, init] = leafGroupInfo.attribute[0];
                        cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                        cacheAttrData.attr[attrId] += init;
                    }
                });
            });

            playerData.type === CombatPlayerType.Self && (this.talentAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.Talent, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `天赋-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-锻造台
     * @param player 玩家
     */
    private updateAttrByForge(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let forgeId: number[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            forgeId = Forge.getInstance().getSkinInfos();

            isReset = !this.forgeAttr;
            if (!this.forgeAttr) {
                forgeId.sort((a, b) => a - b);
                cacheAttrData.para = forgeId.concat();
            }
            if (!isReset) {
                cacheAttrData = this.forgeAttr;
                forgeId.sort((a, b) => a - b);
                const tempPara: number[] = forgeId.concat();
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            forgeId = combatData.forgeGroupInfo.forgeSkinObj.skinIds;

            isReset = true;
            forgeId.sort((a, b) => a - b);
            cacheAttrData.para = forgeId.concat();
        }

        if (isReset) {
            cacheAttrData.attr = {};

            forgeId.forEach((e) => {
                const forgeInfo = TBForgeSkin.getInstance().getDataById(e);
                forgeInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.forgeAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.Forge, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `锻造台-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-锻造台等级
     * @param player 玩家
     */
    private updateAttrByForgeLevel(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let levelId = -1;
        let culData: IForgeCultivateObj[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            levelId = Forge.getInstance().getForgeLevel();
            culData = Forge.getInstance().getCultivateInfos();

            isReset = !this.forgeLevelAttr;
            if (!this.forgeLevelAttr) {
                cacheAttrData.para.push(levelId);
                culData.sort((a, b) => a.cultivateLevelId - b.cultivateLevelId);
                culData.forEach((e) => {
                    cacheAttrData.para.push(e.cultivateLevelId, e.num);
                });
            }
            if (!isReset) {
                cacheAttrData = this.forgeLevelAttr;
                const tempPara: number[] = [];
                tempPara.push(levelId);
                culData.sort((a, b) => a.cultivateLevelId - b.cultivateLevelId);
                culData.forEach((e) => {
                    tempPara.push(e.cultivateLevelId, e.num);
                });
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            levelId = combatData.forgeGroupInfo.forgeUpgradeObj.forgeId;
            culData = combatData.forgeGroupInfo.cultivateInfos;

            isReset = true;
            cacheAttrData.para.push(levelId);
            culData.sort((a, b) => a.cultivateLevelId - b.cultivateLevelId);
            culData.forEach((e) => {
                cacheAttrData.para.push(e.cultivateLevelId, e.num);
            });
        }

        if (isReset) {
            cacheAttrData.attr = {};

            const levelInfo = TBForgeLevel.getInstance().getDataById(levelId);
            levelInfo.attribute.forEach(([attrId, init]) => {
                cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                cacheAttrData.attr[attrId] += init;
            });

            culData.forEach((e) => {
                const culInfo = TBForgeCultivateLevel.getInstance().getDataById(e.cultivateLevelId);
                culInfo.stepAttribute.forEach(([attrId, init, step]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init + step * e.num;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.forgeLevelAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.ForgeLevel, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `锻造台等级-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-藏品
     * @param player 玩家
     */
    private updateAttrByCollection(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let collectionData: ICollectionObj[] = null;
        const collectionEffectNum: { [collectionId: number]: number } = {};
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            collectionData = Collection.getInstance().getCollectionInfos();
            collectionData.sort((a, b) => a.collectionId - b.collectionId);

            isReset = !this.collectionAttr;
            if (!this.collectionAttr) {
                collectionData.forEach((e) => {
                    const collectionInfo = TBCollection.getInstance().getDataById(e.collectionId);
                    switch (collectionInfo.upgradeType) {
                        case EnumCollectionUpgradeType.OrdinaryUpgrade:
                            collectionEffectNum[e.collectionId] = 1;
                            break;
                        case EnumCollectionUpgradeType.TaskUpgrade:
                            const taskInfo = TBCollectionTask.getInstance().getDataById(collectionInfo.taskType);
                            const taskNum = Task.getInstance().getStatisticCountByTypeAndKey(
                                taskInfo.type,
                                taskInfo.taskValue[0] + ""
                            );
                            const levelInfo = TBCollectionUpgrade.getInstance().getDataByTypeAndLevel(
                                collectionInfo.upgradeCall,
                                e.level
                            );
                            collectionEffectNum[e.collectionId] = Math.min(
                                Math.floor(taskNum / collectionInfo.taskPara),
                                levelInfo.taskeffectiveLimit
                            );
                            break;
                        default:
                            collectionEffectNum[e.collectionId] = 0;
                            break;
                    }
                    cacheAttrData.para.push(e.collectionId, e.level, e.star, collectionEffectNum[e.collectionId]);
                });
            }
            if (!isReset) {
                cacheAttrData = this.collectionAttr;
                const tempPara: number[] = [];
                collectionData.forEach((e) => {
                    const collectionInfo = TBCollection.getInstance().getDataById(e.collectionId);
                    switch (collectionInfo.upgradeType) {
                        case EnumCollectionUpgradeType.OrdinaryUpgrade:
                            collectionEffectNum[e.collectionId] = 1;
                            break;
                        case EnumCollectionUpgradeType.TaskUpgrade:
                            const taskInfo = TBCollectionTask.getInstance().getDataById(collectionInfo.taskType);
                            const taskNum = Task.getInstance().getStatisticCountByTypeAndKey(
                                taskInfo.type,
                                taskInfo.taskValue[0] + ""
                            );
                            const levelInfo = TBCollectionUpgrade.getInstance().getDataByTypeAndLevel(
                                collectionInfo.upgradeCall,
                                e.level
                            );
                            collectionEffectNum[e.collectionId] = Math.min(
                                Math.floor(taskNum / collectionInfo.taskPara),
                                levelInfo.taskeffectiveLimit
                            );
                            break;
                        default:
                            collectionEffectNum[e.collectionId] = 0;
                            break;
                    }
                    tempPara.push(e.collectionId, e.level, e.star, collectionEffectNum[e.collectionId]);
                });
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            collectionData = combatData.collectionGroupInfo.collectionInfos;
            collectionData.sort((a, b) => a.collectionId - b.collectionId);

            isReset = true;
            collectionData.forEach((e) => {
                const collectionInfo = TBCollection.getInstance().getDataById(e.collectionId);
                switch (collectionInfo.upgradeType) {
                    case EnumCollectionUpgradeType.OrdinaryUpgrade:
                        collectionEffectNum[e.collectionId] = 1;
                        break;
                    case EnumCollectionUpgradeType.TaskUpgrade:
                        const taskInfo = TBCollectionTask.getInstance().getDataById(collectionInfo.taskType);
                        const taskData = combatData.taskCountInfos.find((e2) => e2.type === taskInfo.type);
                        const taskNum = taskData ? taskData.paraMap[taskInfo.taskValue[0] + ""] || 0 : 0;
                        const levelInfo = TBCollectionUpgrade.getInstance().getDataByTypeAndLevel(
                            collectionInfo.upgradeCall,
                            e.level
                        );
                        collectionEffectNum[e.collectionId] = Math.min(
                            Math.floor(taskNum / collectionInfo.taskPara),
                            levelInfo.taskeffectiveLimit
                        );
                        break;
                    default:
                        collectionEffectNum[e.collectionId] = 0;
                        break;
                }
                cacheAttrData.para.push(e.collectionId, e.level, e.star, collectionEffectNum[e.collectionId]);
            });
        }

        if (isReset) {
            cacheAttrData.attr = {};

            collectionData.forEach((e) => {
                const collectionInfo = TBCollection.getInstance().getDataById(e.collectionId);
                const levelInfo = TBCollectionUpgrade.getInstance().getDataByTypeAndLevel(
                    collectionInfo.upgradeCall,
                    e.level
                );
                const starInfo = TBCollectionStar.getInstance().getDataByStarTypeAndStar(
                    collectionInfo.starType,
                    e.star
                );
                levelInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] +=
                        init * (1 + starInfo.starEffect) * collectionEffectNum[e.collectionId];
                });
                starInfo.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.collectionAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.Collection, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `藏品-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 是否更新属性-藏品
     * @returns
     */
    private isUpdateAttrByCollection(): boolean {
        if (!this.collectionAttr) {
            return false;
        }

        const collectionData = Collection.getInstance().getCollectionInfos();
        collectionData.sort((a, b) => a.collectionId - b.collectionId);
        const collectionEffectNum: { [collectionId: number]: number } = {};
        const tempPara: number[] = [];
        collectionData.forEach((e) => {
            const collectionInfo = TBCollection.getInstance().getDataById(e.collectionId);
            switch (collectionInfo.upgradeType) {
                case EnumCollectionUpgradeType.OrdinaryUpgrade:
                    collectionEffectNum[e.collectionId] = 1;
                    break;
                case EnumCollectionUpgradeType.TaskUpgrade:
                    const taskInfo = TBCollectionTask.getInstance().getDataById(collectionInfo.taskType);
                    const taskNum = Task.getInstance().getStatisticCountByTypeAndKey(
                        taskInfo.type,
                        taskInfo.taskValue[0] + ""
                    );
                    const levelInfo = TBCollectionUpgrade.getInstance().getDataByTypeAndLevel(
                        collectionInfo.upgradeCall,
                        e.level
                    );
                    collectionEffectNum[e.collectionId] = Math.min(
                        Math.floor(taskNum / collectionInfo.taskPara),
                        levelInfo.taskeffectiveLimit
                    );
                    break;
                default:
                    collectionEffectNum[e.collectionId] = 0;
                    break;
            }
            tempPara.push(e.collectionId, e.level, e.star, collectionEffectNum[e.collectionId]);
        });
        const isReset = !this.isEqualArray(this.collectionAttr.para, tempPara);

        return isReset;
    }

    /**
     * 更新属性-藏品套装
     * @param player 玩家
     */
    private updateAttrByCollectionSuit(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        let collectionData: ICollectionObj[] = null;
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            collectionData = Collection.getInstance().getCollectionInfos();
            collectionData.sort((a, b) => a.collectionId - b.collectionId);

            isReset = !this.collectionSuitAttr;
            if (!this.collectionSuitAttr) {
                collectionData.forEach((e) => {
                    cacheAttrData.para.push(e.collectionId, e.star);
                });
            }
            if (!isReset) {
                cacheAttrData = this.collectionSuitAttr;
                const tempPara: number[] = [];
                collectionData.forEach((e) => {
                    tempPara.push(e.collectionId, e.star);
                });
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            collectionData = combatData.collectionGroupInfo.collectionInfos;
            collectionData.sort((a, b) => a.collectionId - b.collectionId);

            isReset = true;
            collectionData.forEach((e) => {
                cacheAttrData.para.push(e.collectionId, e.star);
            });
        }

        if (isReset) {
            cacheAttrData.attr = {};

            const effectInfo = Collection.getInstance().getEffectSuitEffectInfo(collectionData);
            effectInfo.forEach((e) => {
                e.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.collectionSuitAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.CollectionSuit, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `藏品套装-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 更新属性-道具
     * @param player 玩家
     */
    private updateAttrByItem(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const playerData = player.getData();

        const itemAttrInfo: DataItemAttribute[] = [];
        const allItemAttrInfo = TBItemAttribute.getInstance().getList();
        let isReset = false;
        let cacheAttrData: ICacheAttrData = { para: [], attr: null };
        if (playerData.type === CombatPlayerType.Self) {
            allItemAttrInfo.forEach((e) => {
                const { isOwn } = Bag.getInstance().getLimitTimeItemState(e.id);
                isOwn && itemAttrInfo.push(e);
            });
            itemAttrInfo.sort((a, b) => a.id - b.id);

            isReset = !this.itemAttr;
            if (!this.itemAttr) {
                itemAttrInfo.forEach((e) => cacheAttrData.para.push(e.id));
            }
            if (!isReset) {
                cacheAttrData = this.itemAttr;
                const tempPara: number[] = [];
                itemAttrInfo.forEach((e) => tempPara.push(e.id));
                isReset = !this.isEqualArray(cacheAttrData.para, tempPara);
                isReset && (cacheAttrData.para = tempPara);
            }
        } else {
            const combatData = Combat.getInstance().getPlayerCombatData(
                playerBaseData.dungeonType,
                playerBaseData.uuid
            );
            allItemAttrInfo.forEach((e) => {
                const itemData = combatData.itemInfos.find((e2) => e2.itemInfoId === e.id);
                if (itemData) {
                    const { isOwn } = Bag.getInstance().getLimitTimeItemState(e.id, itemData);
                    isOwn && itemAttrInfo.push(e);
                }
            });
            itemAttrInfo.sort((a, b) => a.id - b.id);

            isReset = true;
            itemAttrInfo.forEach((e) => cacheAttrData.para.push(e.id));
        }

        if (isReset) {
            cacheAttrData.attr = {};

            itemAttrInfo.forEach((e) => {
                e.attribute.forEach(([attrId, init]) => {
                    cacheAttrData.attr[attrId] = cacheAttrData.attr[attrId] || 0;
                    cacheAttrData.attr[attrId] += init;
                });
            });

            playerData.type === CombatPlayerType.Self && (this.itemAttr = cacheAttrData);

            this.logModuleAttr(CombatLogId.ItemAttr, cacheAttrData, player);
        }

        for (const key in cacheAttrData.attr) {
            if (playerBaseData.attr[key]) {
                playerBaseData.attr[key].culValue += cacheAttrData.attr[key];
                playerBaseData.attr[key].culLog += `道具-${cacheAttrData.attr[key]}->`;
            }
        }
    }

    /**
     * 是否更新属性-道具
     * @returns
     */
    private isUpdateAttrByItem(): boolean {
        if (!this.itemAttr) {
            return false;
        }

        const itemAttrInfo: DataItemAttribute[] = [];
        const allItemAttrInfo = TBItemAttribute.getInstance().getList();
        allItemAttrInfo.forEach((e) => {
            const { isOwn } = Bag.getInstance().getLimitTimeItemState(e.id);
            isOwn && itemAttrInfo.push(e);
        });
        itemAttrInfo.sort((a, b) => a.id - b.id);
        const tempPara: number[] = [];
        itemAttrInfo.forEach((e) => tempPara.push(e.id));
        const isReset = !this.isEqualArray(this.itemAttr.para, tempPara);

        return isReset;
    }

    /**
     * 更新属性-怪兽
     * @param monster
     */
    private updateAttrByMonster(monster: CombatMemberMonster): void {
        const monsterBaseData = monster.getBaseData();
        const monsterData = monster.getData();
        monsterData.info.attribute.forEach(([attrId, init]) => {
            monsterBaseData.attr[attrId].culValue += init;
            monsterBaseData.attr[attrId].culLog += `怪兽-${init}->`;
        });
    }

    /**
     * 更新属性-副本
     * @param monster
     */
    private updateAttrByDungeon(monster: CombatMemberMonster): void {
        const monsterBaseData = monster.getBaseData();
        const monsterData = monster.getData();

        let attr: number[][] = [];
        let normalAttr: number[][] = null;
        let bossAttr: number[][] = null;
        switch (monsterBaseData.dungeonType) {
            case DungeonType.Main:
                {
                    const levelInfo = TBMainBarrier.getInstance().getDataById(monsterBaseData.levelId);
                    normalAttr = levelInfo.monsterAttribute.concat();
                    bossAttr = levelInfo.bossAttribute.concat();
                }
                break;
            case DungeonType.Boss:
                {
                    const levelInfo = TBDungeonBoss.getInstance().getDataById(monsterBaseData.levelId);
                    bossAttr = levelInfo.bossAttribute.concat();
                }
                break;
            case DungeonType.Cloud:
                {
                    const levelInfo = TBDungeonCloud.getInstance().getDataById(monsterBaseData.levelId);
                    normalAttr = levelInfo.monsterAttribute.concat();
                    bossAttr = levelInfo.bossAttribute.concat();
                }
                break;
            case DungeonType.Thief:
                {
                    const levelInfo = TBDungeonThief.getInstance().getDataById(monsterBaseData.levelId);
                    bossAttr = levelInfo.bossAttribute.concat();

                    const monsterHpInfo = TBThiefBloodReward.getInstance().getList();
                    bossAttr.push([EnumAttributeType.Hp, monsterHpInfo[monsterHpInfo.length - 1].addHp]);
                }
                break;
            case DungeonType.Tower:
                {
                    const levelInfo = TBDungeonTower.getInstance().getDataById(monsterBaseData.levelId);
                    bossAttr = levelInfo.bossAttribute.concat();
                }
                break;
            case DungeonType.Union:
                {
                    bossAttr = TBUnion.getInstance().getValueByPara(EnumUnionPara.BossAttribute);

                    const monsterHpInfo = TBUnionBoss.getInstance().getList();
                    bossAttr.push([EnumAttributeType.Hp, monsterHpInfo[monsterHpInfo.length - 1].addHp]);
                }
                break;
            case DungeonType.Trial:
                {
                    const levelInfo = TBTrialBoss.getInstance().getDataById(monsterBaseData.levelId);
                    bossAttr = levelInfo.bossAttribute.concat();
                }
                break;
            default:
                break;
        }
        switch (monsterData.info.type) {
            case EnumMonsterBaseType.OrdinaryMonster:
                attr = normalAttr;
                break;
            case EnumMonsterBaseType.BossMonster:
                attr = bossAttr;
                break;
            default:
                break;
        }

        attr.forEach(([attrId, init]) => {
            monsterBaseData.attr[attrId].culValue += init;
            monsterBaseData.attr[attrId].culLog += `副本-${init}->`;
        });
    }

    /**
     * 数组是否相等
     * @param arr
     * @param arr2
     * @returns
     */
    private isEqualArray(arr: number[], arr2: number[]): boolean {
        if (arr.length !== arr2.length) {
            return false;
        }
        for (let i = 0; i < arr.length; i++) {
            if (arr[i] !== arr2[i]) {
                return false;
            }
        }

        return true;
    }

    /**
     * 打印日志-玩家属性
     * @param logId 日志id
     * @param player 玩家
     */
    public logPlayerAttr(logId: number, player: CombatMemberPlayer): void {
        if (!CombatLog.getInstance().getShowState(logId)) {
            return;
        }
        const playerBaseData = player.getBaseData();
        if (!CombatLog.getInstance().getShowStateByDungeonType(playerBaseData.dungeonType)) {
            return;
        }

        CombatLog.getInstance().logTitle(logId, player);

        const attr = Utils.clone(playerBaseData.attr);
        cc.log(attr);
    }

    /**
     * 打印日志-怪兽属性
     * @param logId 日志id
     * @param monster 怪兽
     */
    public logMonsterAttr(logId: number, monster: CombatMemberMonster): void {
        if (!CombatLog.getInstance().getShowState(logId)) {
            return;
        }
        const monsterBaseData = monster.getBaseData();
        if (!CombatLog.getInstance().getShowStateByDungeonType(monsterBaseData.dungeonType)) {
            return;
        }

        CombatLog.getInstance().logTitle(logId, monster);

        const attr = Utils.clone(monsterBaseData.attr);
        cc.log(attr);
    }

    /**
     * 打印日志-模块属性
     * @param logId 日志id
     * @param attrData 属性数据
     * @param player 玩家
     */
    public logModuleAttr(logId: number, attrData: ICacheAttrData, player: CombatMemberPlayer): void {
        if (!CombatLog.getInstance().getShowState(logId)) {
            return;
        }
        const playerBaseData = player.getBaseData();
        if (!CombatLog.getInstance().getShowStateByDungeonType(playerBaseData.dungeonType)) {
            return;
        }

        CombatLog.getInstance().logTitle(logId, player);

        const logData: { attr: { [attrName: string]: number }; para: number[] } = { attr: {}, para: attrData.para };
        for (const attrId in attrData.attr) {
            const attrInfo = TBAttribute.getInstance().getDataById(parseInt(attrId));
            logData.attr[`${attrId}#${attrInfo.name}`] = attrData.attr[attrId];
        }
        cc.log(logData);
    }
}
