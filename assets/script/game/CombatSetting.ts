/*
 * @Author: chenx
 * @Date: 2025-05-09 15:01:43
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-23 10:00:35
 */
import LocalStorage from "../../nsn/core/LocalStorage";
import Logic from "../../nsn/core/Logic";
import { LocalStorageKey } from "../config/LocalStorageConfig";

/**
 * 战斗设置id
 */
export enum CombatSettingId {
    EquipScoreImproveShowInfo = 2, // 装备-战斗力提升时显示信息
    EquipHideInfo = 3, // 装备-隐藏信息

    DungeonBossAutoReleaseSkill = 1002, // boss副本-自动释放技能
    DungeonCloudAutoReleaseSkill = 1003, // 云端副本-自动释放技能
    DungeonThiefAutoReleaseSkill = 1004, // 怪盗副本-自动释放技能
    DungeonTowerAutoReleaseSkill = 1005, // 爬塔副本-自动释放技能
    DungeonUnionAutoReleaseSkill = 1006, // 公会副本-自动释放技能
    DungeonTrialAutoReleaseSkill = 1007, // 试炼副本-自动释放技能
    DungeonTestAutoReleaseSkill = 1008, // 测试副本-自动释放技能

    DungeonTowerAutoNextLevel = 2001, // 爬塔副本-自动下一关
}

/**
 * 战斗设置类型
 */
enum CombatSettingType {
    Switch = 1, // 开关
}

/**
 * 战斗设置配置
 */
interface ICombatSettingConfig {
    id: CombatSettingId; // 设置id
    type: CombatSettingType; // 类型
}

/**
 * 战斗设置配置
 */
const COMBAT_SETTING_CONFIG: ICombatSettingConfig[] = [
    { id: CombatSettingId.EquipScoreImproveShowInfo, type: CombatSettingType.Switch },
    { id: CombatSettingId.EquipHideInfo, type: CombatSettingType.Switch },

    { id: CombatSettingId.DungeonBossAutoReleaseSkill, type: CombatSettingType.Switch },
    { id: CombatSettingId.DungeonCloudAutoReleaseSkill, type: CombatSettingType.Switch },
    { id: CombatSettingId.DungeonThiefAutoReleaseSkill, type: CombatSettingType.Switch },
    { id: CombatSettingId.DungeonTowerAutoReleaseSkill, type: CombatSettingType.Switch },
    { id: CombatSettingId.DungeonUnionAutoReleaseSkill, type: CombatSettingType.Switch },
    { id: CombatSettingId.DungeonTrialAutoReleaseSkill, type: CombatSettingType.Switch },
    { id: CombatSettingId.DungeonTestAutoReleaseSkill, type: CombatSettingType.Switch },

    { id: CombatSettingId.DungeonTowerAutoNextLevel, type: CombatSettingType.Switch },
];

/**
 * 战斗设置
 */
export default class CombatSetting extends Logic {
    private settingData: { [id: number]: string } = null; // 设置数据

    public clear(): void {
        this.settingData = null;
    }

    /**
     * 设置设置状态
     * @param settingId 设置id
     */
    public setSettingState(settingId: CombatSettingId): void {
        const settingConfig = COMBAT_SETTING_CONFIG.find((e) => e.id === settingId);
        switch (settingConfig.type) {
            case CombatSettingType.Switch:
                if (parseInt(this.settingData[settingId]) === 0) {
                    this.settingData[settingId] = 1 + "";
                } else {
                    this.settingData[settingId] = 0 + "";
                }
                break;
            default:
                break;
        }

        LocalStorage.getInstance().setItem(LocalStorageKey.CombatSettingData, JSON.stringify(this.settingData));
    }

    /**
     * 获取设置状态
     * @param settingId 设置id
     * @returns
     */
    public getSettingState(settingId: CombatSettingId): boolean {
        if (!this.settingData) {
            const localData = LocalStorage.getInstance().getItem(LocalStorageKey.CombatSettingData);
            this.settingData = localData ? JSON.parse(localData) : {};
        }

        const settingConfig = COMBAT_SETTING_CONFIG.find((e) => e.id === settingId);
        switch (settingConfig.type) {
            case CombatSettingType.Switch:
                if (!this.settingData[settingId]) {
                    this.settingData[settingId] = 0 + "";
                }

                return parseInt(this.settingData[settingId]) === 1;
            default:
                break;
        }
    }
}
