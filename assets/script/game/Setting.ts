/*
 * @Author: chenx
 * @Date: 2025-07-05 11:14:20
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-23 09:52:55
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import Tips from "../../nsn/util/Tips";
import { ISettingInfo, SettingInitRet, SettingSet, SettingSetRet } from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import { EnumUniversalPara } from "../data/base/BaseUniversal";
import TBUniversal from "../data/parser/TBUniversal";

/**
 * 设置场景
 */
export enum SettingScene {
    Switch = 1, // 开关
}

/**
 * 设置id
 */
export enum SettingId {
    DungeonMainAutoReleaseSkill = "dungeon-main-auto-release-skill", // 主线副本-自动释放技能
    DungeonMainSkipLevel = "dungeon-main-skip-level", // 主线副本-跳过关卡
    DungeonMainGameSpeed = "dungeon-main-game-speed", // 主线副本-游戏速度
    DungeonMainGameSpeed2 = "dungeon-main-game-speed2", // 主线副本-游戏速度2
    DungeonBossGameSpeed = "dungeon-boss-game-speed", // boss副本-游戏速度
    DungeonCloudGameSpeed = "dungeon-cloud-game-speed", // 云端副本-游戏速度
    DungeonThiefGameSpeed = "dungeon-thief-game-speed", // 怪盗副本-游戏速度
    DungeonTowerGameSpeed = "dungeon-tower-game-speed", // 爬塔副本-游戏速度
    DungeonUnionGameSpeed = "dungeon-union-game-speed", // 公会副本-游戏速度
    DungeonTrialGameSpeed = "dungeon-trial-game-speed", // 试炼副本-游戏速度
    DungeonTestGameSpeed = "dungeon-test-game-speed", // 测试副本-游戏速度
    DungeonArenaGameSpeed = "dungeon-arena-game-speed", // 竞技场副本-游戏速度
    DungeonParkGameSpeed = "dungeon-park-game-speed", // 停车场副本-游戏速度
    DungeonUnionDefenseGameSpeed = "dungeon-union-defense-game-speed", // 公会副本-游戏速度

    MakeArrowAutoDispose = "make-arrow-auto-dispose", // 制作弓箭-自动处理
    MakeArrowCost = "make-arrow-cost", // 制作弓箭-消耗
    MakeArrowCost2 = "make-arrow-cost2", // 制作弓箭-消耗2

    EquipAutoDispose = "equip-auto-dispose", // 装备-自动处理

    PowerBatchUpgrade = "power-batch-upgrade", // 王权-一键升级
}

/**
 * 设置
 */
export default class Setting extends Logic {
    private data: ISettingInfo[] = null; // 设置数据

    protected registerHandler(): void {
        Socket.getInstance().on(SettingInitRet.prototype.clazzName, this.settingInitRet, this);

        Socket.getInstance().on(SettingSetRet.prototype.clazzName, this.settingSetRet, this);
    }

    public clear(): void {
        this.data = null;
    }

    /**
     * 初始化数据
     * @param data
     */
    private settingInitRet(data: SettingInitRet): void {
        const { errMsg, infos } = data;
        if (errMsg !== SettingInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.settingInitRet[errMsg]);
            return;
        }

        this.data = infos;
    }

    /**
     * 保存数据
     * @param data
     */
    private settingSetRet(data: SettingSetRet): void {
        const { errMsg, info } = data;
        if (errMsg !== SettingSetRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.settingSetRet[errMsg]);
            return;
        }

        const index = this.data.findIndex((e) => e.scene === info.scene);
        if (index !== -1) {
            this.data[index] = info;
        } else {
            this.data.push(info);
        }
    }

    /**
     * 设置开关状态
     * @param settingId 设置id
     * @param isSave 是否保存
     */
    public setSwitchState(settingId: SettingId, isSave: boolean = true): void {
        const isSwitch = this.getSwitchState(settingId);
        const settingData = this.data.find((e) => e.scene === SettingScene.Switch);
        settingData.data[settingId] = JSON.stringify(!isSwitch);
        isSave && this.sendSaveData(settingData);
    }

    /**
     * 获取开关状态
     * @param settingId 设置id
     * @returns
     */
    public getSwitchState(settingId: SettingId): boolean {
        if (this.data.findIndex((e) => e.scene === SettingScene.Switch) === -1) {
            this.data.push({ scene: SettingScene.Switch, data: {} });
        }

        const settingData = this.data.find((e) => e.scene === SettingScene.Switch);
        let isSwitch = false;
        if (!settingData.data[settingId]) {
            switch (settingId) {
                case SettingId.MakeArrowAutoDispose:
                    const para: number = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.AutoForging);
                    isSwitch = para === 1;
                    break;
                default:
                    break;
            }
        } else {
            isSwitch = JSON.parse(settingData.data[settingId]);
        }

        return isSwitch;
    }

    /**
     * 保存数据
     * @param scene 场景
     */
    public saveData(scene: SettingScene): void {
        const settingData = this.data.find((e) => e.scene === scene);
        this.sendSaveData(settingData);
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////
    /**
     * 保存数据
     * @param settingData 设置数据
     */
    public sendSaveData(settingData: ISettingInfo): void {
        const data = SettingSet.create({ info: settingData });
        Socket.getInstance().send(data);
    }
}
