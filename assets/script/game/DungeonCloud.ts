/*
 * @Author: chenx
 * @Date: 2025-01-16 10:44:11
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 11:57:49
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import Tips from "../../nsn/util/Tips";
import {
    DungeonCloudInitRet,
    DungeonCloudReward,
    DungeonCloudRewardRet,
    DungeonCloudSweetReward,
    DungeonCloudSweetRewardRet,
    IDungeonCloudObj,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import Combat, { CombatEvent, DungeonType } from "./Combat";

/**
 * 云端副本
 */
export default class DungeonCloud extends Logic {
    private data: IDungeonCloudObj = null; // 云端副本数据

    protected registerHandler(): void {
        Socket.getInstance().on(DungeonCloudInitRet.prototype.clazzName, this.dungeonCloudInitRet, this);
        Socket.getInstance().on(DungeonCloudRewardRet.prototype.clazzName, this.dungeonCloudRewardRet, this);
        Socket.getInstance().on(DungeonCloudSweetRewardRet.prototype.clazzName, this.dungeonCloudSweetRewardRet, this);
    }

    public clear(): void {
        this.data = null;
    }

    /**
     * 初始化数据
     * @param data
     */
    private dungeonCloudInitRet(data: DungeonCloudInitRet): void {
        const { errMsg, dungeonCloudInfo } = data;
        if (errMsg !== DungeonCloudInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonCloudInitRet[errMsg]);
            return;
        }
        this.data = dungeonCloudInfo;
    }

    /**
     * 领取云端过关奖励
     * @param data
     */
    private dungeonCloudRewardRet(data: DungeonCloudRewardRet): void {
        const { errMsg, dungeonCloudInfo } = data;
        if (errMsg !== DungeonCloudRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonCloudRewardRet[errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.Cloud);
            return;
        }
        this.data = dungeonCloudInfo;
        this.emit(DungeonCloudRewardRet.prototype.clazzName);
    }

    /**
     * 领取云端副本扫荡奖励
     * @param data
     */
    private dungeonCloudSweetRewardRet(data: DungeonCloudSweetRewardRet): void {
        const { errMsg, dungeonCloudInfo } = data;
        if (errMsg !== DungeonCloudSweetRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonCloudSweetRewardRet[errMsg]);
            return;
        }
        this.data = dungeonCloudInfo;
        this.emit(DungeonCloudSweetRewardRet.prototype.clazzName);
    }

    /**
     * 获取当前副本id
     * @returns
     */
    public getCurDungeonId(): number {
        return this.data.curDungeonId;
    }

    /**
     * 获取已通关副本id
     * @returns
     */
    public getUnlockDungeonIds(): number[] {
        return this.data.unlockDungeonIds;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 领取云端过关奖励
     */
    public sendDungeonCloudReward(): void {
        const data = DungeonCloudReward.create();
        Socket.getInstance().send(data);
    }

    /**
     * 领取云端副本扫荡奖励
     * @param dungeonCloudId
     * @param count
     */
    public sendDungeonCloudSweetReward(dungeonCloudId: number, count: number): void {
        const data = DungeonCloudSweetReward.create({ dungeonCloudId, count });
        Socket.getInstance().send(data);
    }
}
