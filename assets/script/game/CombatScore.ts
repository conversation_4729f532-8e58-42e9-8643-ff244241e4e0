/*
 * @Author: JackyF<PERSON>
 * @Date: 2024-08-08 09:54:18
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-16 10:19:31
 */
import Logic from "../../nsn/core/Logic";
import Utils from "../../nsn/util/Utils";
import { EnumAttributeType } from "../data/base/BaseAttribute";
import TBPowerLevel from "../data/parser/TBPowerLevel";
import NumberUtils from "../utils/NumberUtils";
import CombatDungeonMain from "./combat/CombatDungeonMain";
import CombatMemberPlayer, { CombatPlayerType } from "./combat/CombatMemberPlayer";
import CombatLog, { CombatLogId } from "./CombatLog";
import Power from "./Power";

/**
 * 战斗力事件
 */
export enum CombatScoreEvent {
    Update = "update", // 更新
}

/**
 * 战斗力
 */
export default class CombatScore extends Logic {
    private score: number = -1; // 战斗力

    public clear(): void {
        this.score = -1;
    }

    /**
     * 更新战斗力
     * @param isPre 是否预更新
     */
    public updateScore(isPre: boolean = false): void {
        const allPlayer = CombatDungeonMain.getInstance().getAllPlayer();
        const player = allPlayer.find((e) => e.getData().type === CombatPlayerType.Self);
        const oldScore = this.score;
        this.calScore(player);

        !isPre && this.emit(CombatScoreEvent.Update, this.score);

        if (CombatLog.getInstance().getShowState(CombatLogId.CombatScore)) {
            CombatLog.getInstance().logTitle(CombatLogId.CombatScore, player);

            const playerBaseData = player.getBaseData();
            const logData = {
                score: this.score,
                score2: NumberUtils.format(this.score, 1, 0),
                oldScore,
                oldScore2: NumberUtils.format(oldScore, 1, 0),
                changeScore: this.score - oldScore,
                changeScore2: NumberUtils.format(this.score - oldScore, 1, 0),
                attr: Utils.clone(playerBaseData.attr),
                attrFormat: "",
            };
            for (const e in logData.attr) {
                logData.attrFormat += `${e},`;
                logData.attrFormat += `${logData.attr[e].scoreValue}\n`;
            }
            cc.log(logData);
        }
    }

    /**
     * 获取战斗力
     * @returns
     */
    public getScore(): number {
        return this.score;
    }

    /**
     * 计算战斗力
     * @param player 玩家
     */
    private calScore(player: CombatMemberPlayer): void {
        const playerBaseData = player.getBaseData();
        const attr = playerBaseData.attr;

        // 基础战力
        const base =
            attr[EnumAttributeType.FinalHp].scoreValue * attr[EnumAttributeType.FinalHp].info.combat +
            attr[EnumAttributeType.FinalAtk].scoreValue * attr[EnumAttributeType.FinalAtk].info.combat +
            attr[EnumAttributeType.FinalDef].scoreValue * attr[EnumAttributeType.FinalDef].info.combat;

        // 赋值战力
        const assign = attr[EnumAttributeType.EmpowerCombatStrength].scoreValue;

        // 破挡战力
        let breakDefAndBlock =
            attr[EnumAttributeType.BreakDef].scoreValue * attr[EnumAttributeType.BreakDef].info.combat +
            attr[EnumAttributeType.Block].scoreValue * attr[EnumAttributeType.Block].info.combat;
        breakDefAndBlock *= 1 + attr[EnumAttributeType.PowerAtkPer].scoreValue;

        // 固定战力
        let fixed =
            attr[EnumAttributeType.ESkillFB].scoreValue * attr[EnumAttributeType.ESkillFB].info.combat +
            attr[EnumAttributeType.ASkillFB].scoreValue * attr[EnumAttributeType.ASkillFB].info.combat +
            attr[EnumAttributeType.PSkillFB].scoreValue * attr[EnumAttributeType.PSkillFB].info.combat +
            attr[EnumAttributeType.AtkFB].scoreValue * attr[EnumAttributeType.AtkFB].info.combat +
            attr[EnumAttributeType.ArcherSkillFB].scoreValue * attr[EnumAttributeType.ArcherSkillFB].info.combat +
            attr[EnumAttributeType.PetSkillFB].scoreValue * attr[EnumAttributeType.PetSkillFB].info.combat +
            attr[EnumAttributeType.GlobalDFB].scoreValue * attr[EnumAttributeType.GlobalDFB].info.combat;
        fixed *= 1 + attr[EnumAttributeType.PowerAtkPer].scoreValue;

        // 加成百分比
        const addPer =
            attr[EnumAttributeType.Crit].scoreValue / attr[EnumAttributeType.Crit].info.factor +
            attr[EnumAttributeType.CritRes].scoreValue / attr[EnumAttributeType.CritRes].info.factor +
            attr[EnumAttributeType.CritDmg].scoreValue / attr[EnumAttributeType.CritDmg].info.factor +
            attr[EnumAttributeType.CritDmgBlock].scoreValue / attr[EnumAttributeType.CritDmgBlock].info.factor +
            attr[EnumAttributeType.CritHit].scoreValue / attr[EnumAttributeType.CritHit].info.factor +
            attr[EnumAttributeType.CritHitRes].scoreValue / attr[EnumAttributeType.CritHitRes].info.factor +
            attr[EnumAttributeType.CritHitDmg].scoreValue / attr[EnumAttributeType.CritHitDmg].info.factor +
            attr[EnumAttributeType.CritHitDmgBlock].scoreValue / attr[EnumAttributeType.CritHitDmgBlock].info.factor +
            attr[EnumAttributeType.SkillHaste].scoreValue / attr[EnumAttributeType.SkillHaste].info.factor +
            attr[EnumAttributeType.Eva].scoreValue / attr[EnumAttributeType.Eva].info.factor +
            attr[EnumAttributeType.EvaIgnore].scoreValue / attr[EnumAttributeType.EvaIgnore].info.factor +
            attr[EnumAttributeType.ESkillB].scoreValue / attr[EnumAttributeType.ESkillB].info.factor +
            attr[EnumAttributeType.ESkillR].scoreValue / attr[EnumAttributeType.ESkillR].info.factor +
            attr[EnumAttributeType.ASkillB].scoreValue / attr[EnumAttributeType.ASkillB].info.factor +
            attr[EnumAttributeType.ASkillR].scoreValue / attr[EnumAttributeType.ASkillR].info.factor +
            attr[EnumAttributeType.PSkillB].scoreValue / attr[EnumAttributeType.PSkillB].info.factor +
            attr[EnumAttributeType.PSkillR].scoreValue / attr[EnumAttributeType.PSkillR].info.factor +
            attr[EnumAttributeType.AtkB].scoreValue / attr[EnumAttributeType.AtkB].info.factor +
            attr[EnumAttributeType.AtkR].scoreValue / attr[EnumAttributeType.AtkR].info.factor +
            attr[EnumAttributeType.GlobalSkillB].scoreValue / attr[EnumAttributeType.GlobalSkillB].info.factor +
            attr[EnumAttributeType.GlobalSkillR].scoreValue / attr[EnumAttributeType.GlobalSkillR].info.factor +
            attr[EnumAttributeType.ArcherSkillB].scoreValue / attr[EnumAttributeType.ArcherSkillB].info.factor +
            attr[EnumAttributeType.ArcherSkillR].scoreValue / attr[EnumAttributeType.ArcherSkillR].info.factor +
            attr[EnumAttributeType.PetSkillB].scoreValue / attr[EnumAttributeType.PetSkillB].info.factor +
            attr[EnumAttributeType.PetSkillR].scoreValue / attr[EnumAttributeType.PetSkillR].info.factor +
            attr[EnumAttributeType.MonsterDB].scoreValue / attr[EnumAttributeType.MonsterDB].info.factor +
            attr[EnumAttributeType.MonsterDR].scoreValue / attr[EnumAttributeType.MonsterDR].info.factor +
            attr[EnumAttributeType.GlobalDB].scoreValue / attr[EnumAttributeType.GlobalDB].info.factor +
            attr[EnumAttributeType.GlobalDR].scoreValue / attr[EnumAttributeType.GlobalDR].info.factor +
            attr[EnumAttributeType.EmpowerCombatStrengthPer].scoreValue /
                attr[EnumAttributeType.EmpowerCombatStrengthPer].info.factor +
            attr[EnumAttributeType.PVPDB].scoreValue / attr[EnumAttributeType.PVPDB].info.factor +
            attr[EnumAttributeType.PVPDR].scoreValue / attr[EnumAttributeType.PVPDR].info.factor;

        // 总战力
        const powerData = Power.getInstance().getPowerInfo();
        const powerLevelInfo = TBPowerLevel.getInstance().getDataById(powerData.kingLevelId);
        let total = base + assign + breakDefAndBlock + fixed;
        total *= 1 + addPer;
        total *= Math.pow(1.0002, powerLevelInfo.level) + Math.pow(1.0001, powerLevelInfo.level - 1) - 1;

        this.score = total;
    }
}
