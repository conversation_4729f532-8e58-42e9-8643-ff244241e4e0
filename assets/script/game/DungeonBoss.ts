/*
 * @Author: chenx
 * @Date: 2025-01-16 10:44:11
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 11:57:45
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import Tips from "../../nsn/util/Tips";
import {
    DungeonBossInitRet,
    DungeonBossReward,
    DungeonBossRewardRet,
    DungeonBossSweetReward,
    DungeonBossSweetRewardRet,
    IDungeonBossObj,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import Combat, { CombatEvent, DungeonType } from "./Combat";

/**
 * 不竭之境
 */
export default class DungeonBoss extends Logic {
    private data: IDungeonBossObj = null;

    protected registerHandler(): void {
        Socket.getInstance().on(DungeonBossInitRet.prototype.clazzName, this.dungeonBossInitRet, this);
        Socket.getInstance().on(DungeonBossRewardRet.prototype.clazzName, this.dungeonBossRewardRet, this);
        Socket.getInstance().on(DungeonBossSweetRewardRet.prototype.clazzName, this.dungeonBossSweetRewardRet, this);
    }

    public clear(): void {
        this.data = null;
    }

    /**
     * 初始化数据
     * @param data
     */
    private dungeonBossInitRet(data: DungeonBossInitRet): void {
        const { errMsg, dungeonBossInfo } = data;
        if (errMsg !== DungeonBossInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonBossInitRet[errMsg]);
            return;
        }
        this.data = dungeonBossInfo;
    }

    /**
     * 领取不竭之境过关奖励
     * @param data
     */
    private dungeonBossRewardRet(data: DungeonBossRewardRet): void {
        const { errMsg, dungeonBossInfo } = data;
        if (errMsg !== DungeonBossRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonBossRewardRet[errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.Boss);
            return;
        }
        this.data = dungeonBossInfo;
        this.emit(DungeonBossRewardRet.prototype.clazzName);
    }

    /**
     * 领取不竭之境副本扫荡奖励
     * @param data
     */
    private dungeonBossSweetRewardRet(data: DungeonBossSweetRewardRet): void {
        const { errMsg, dungeonBossInfo } = data;
        if (errMsg !== DungeonBossSweetRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.dungeonBossSweetRewardRet[errMsg]);
            return;
        }
        this.data = dungeonBossInfo;
        this.emit(DungeonBossSweetRewardRet.prototype.clazzName);
    }

    /**
     * 获取当前副本id
     * @returns
     */
    public getCurDungeonId(): number {
        return this.data.curDungeonId;
    }

    /**
     * 获取已通关副本id
     * @returns
     */
    public getUnlockDungeonIds(): number[] {
        return this.data.unlockDungeonIds;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 领取不竭之境过关奖励
     */
    public sendDungeonBossReward(): void {
        const data = DungeonBossReward.create();
        Socket.getInstance().send(data);
    }

    /**
     * 领取不竭之境副本扫荡奖励
     * @param dungeonBossId
     * @param count
     */
    public sendDungeonBossSweetReward(dungeonBossId: number, count: number): void {
        const data = DungeonBossSweetReward.create({ dungeonBossId, count });
        Socket.getInstance().send(data);
    }
}
