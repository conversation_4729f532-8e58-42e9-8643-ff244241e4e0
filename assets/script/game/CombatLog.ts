/*
 * @Author: chenx
 * @Date: 2024-10-21 09:20:38
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 10:11:41
 */
import LocalStorage from "../../nsn/core/LocalStorage";
import Logic from "../../nsn/core/Logic";
import Logger from "../../nsn/util/Logger";
import { LocalStorageKey } from "../config/LocalStorageConfig";
import Combat, { CombatType, DungeonType } from "./Combat";
import { CombatMember, CombatMemberType } from "./combat/CombatMember";
import CombatMemberMonster from "./combat/CombatMemberMonster";
import CombatMemberPlayer from "./combat/CombatMemberPlayer";

/**
 * 战斗日志id
 */
export enum CombatLogId {
    // 副本
    MainDungeon = 1, // 主线
    PveDungeon, // pve
    PvpDungeon, // pvp

    // 成员属性
    PlayerAttr, // 玩家
    MonsterAttr, // 怪兽

    // 模块属性
    LeadLevelAttr, // 主角等级
    LeadStarAttr, // 主角星级
    WeaponLevelAttr, // 神器等级
    WeaponStarAttr, // 神器星级
    WingLevelAttr, // 背饰等级
    WingStarAttr, // 背饰星级
    WingEnchantmentAttr, // 背饰附魔
    TankLevelAttr, // 战车等级
    TankStarAttr, // 战车星级
    ArcherLevelAttr, // 弓箭手等级
    ArcherStarAttr, // 弓箭手星级
    ArcherGroupAttr, // 弓箭手羁绊
    PetAttr, // 宠物
    SkillAttr, // 技能
    EquipAttr, // 装备
    EquipStrengthenAttr, // 装备强化
    EquipStageAttr, // 装备阶段
    EquipSuitAttr, // 装备套装
    MagicLevelAttr, // 魔法等级
    MagicStarAttr, // 魔法星级
    PowerAttr, // 王权
    Bless, // 祝福
    Talent, // 天赋
    Forge, // 锻造台
    ForgeLevel, // 锻造台等级
    Collection, // 藏品
    CollectionSuit, // 藏品套装
    ItemAttr, // 道具

    // 系统经济属性
    MakeArrowEconomyAttr, // 制作箭矢
    MiningEconomyAttr, // 挖矿
    OtherEconomyAttr, // 其他

    // 模块经济属性
    ForgeEconomyAttr, // 锻造台
    ForgeLevelEconomyAttr, // 锻造台等级
    ItemEconomyAttr, // 道具
    PrivilegeEconomyAttr, // 特权
    TalentEconomyAttr, // 天赋
    FundEconomyAttr, // 基金

    // 伤害
    LeadSkillDamage, // 主角技能
    LeadAttackDamage, // 主角普攻
    WeaponSkillDamage, // 神器技能
    WingSkillDamage, // 背饰技能
    TankSkillDamage, // 战车技能
    ArcherSkillDamage, // 弓箭手技能
    ArcherAttackDamage, // 弓箭手普攻
    PetSkillDamage, // 宠物技能
    MagicASkillDamage, // 魔法主动技能
    MagicPSkillDamage, // 魔法被动技能
    MonsterAttackDamage, // 怪兽普攻

    // 主线战斗-场景bg移动
    PerWave = 1201, // 每波
    PerLevel = 1202, // 每关

    // 其他
    EffectTriggerTypeUpdate, // 效果触发类型更新
    EffectTriggerTypeTrigger, // 效果触发类型触发
    EffectTriggerTypeProb, // 效果触发类型概率
    EffectParaUpdate, // 效果参数更新
    BuffTriggerTypeUpdate, // 增益触发类型更新
    BuffTriggerTypeTrigger, // 增益触发类型触发
    BuffTrigger, // 增益触发
    BuffUpdate, // 增益更新
    BuffClear, // 增益清理
    Buff602, // 增益602
    Buff702, // 增益702
    PlayerSkillShow, // 玩家技能显示
    PlayerSkillManualRelease, // 玩家技能手动释放
    MonsterSkillManualRelease, // 怪兽技能手动释放
    CombatScore, // 战斗力
    CombatResultData, // 战斗结算数据
    SkipCombatTime, // 跳过战斗帧时间
}

/**
 * 战斗日志类型
 */
export enum CombatLogType {
    Dungeon = 1, // 副本
    MemberAttr, // 成员属性
    ModuleAttr, // 模块属性
    SystemEconomyAttr, // 系统经济属性
    ModuleEconomyAttr, // 模块经济属性
    Damage, // 伤害
    DungeonMainSceneBgMove, // 主线战斗-场景bg移动
    Other, // 其他
}

/**
 * 日志模块配置
 */
export const COMBAT_LOG_MODULE_CONFIG: [CombatLogType, string][] = [
    [CombatLogType.Dungeon, "副本"],
    [CombatLogType.MemberAttr, "成员属性"],
    [CombatLogType.ModuleAttr, "模块属性"],
    [CombatLogType.SystemEconomyAttr, "系统经济属性"],
    [CombatLogType.ModuleEconomyAttr, "模块经济属性"],
    [CombatLogType.Damage, "伤害"],
    [CombatLogType.DungeonMainSceneBgMove, "主线战斗-场景bg移动"],
    [CombatLogType.Other, "其他"],
];

/**
 * 战斗日志配置
 */
export interface ICombatLogConfig {
    id: CombatLogId; // 日志id
    name: string; // 名称
    type: CombatLogType; // 类型
}

/**
 * 战斗日志配置
 */
export const COMBAT_LOG_CONFIG: ICombatLogConfig[] = [
    // 副本
    { id: CombatLogId.MainDungeon, name: "主线", type: CombatLogType.Dungeon },
    { id: CombatLogId.PveDungeon, name: "pve", type: CombatLogType.Dungeon },
    { id: CombatLogId.PvpDungeon, name: "pvp", type: CombatLogType.Dungeon },

    // 成员属性
    { id: CombatLogId.PlayerAttr, name: "玩家", type: CombatLogType.MemberAttr },
    { id: CombatLogId.MonsterAttr, name: "怪兽", type: CombatLogType.MemberAttr },

    // 模块属性
    { id: CombatLogId.LeadLevelAttr, name: "主角等级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.LeadStarAttr, name: "主角星级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.WeaponLevelAttr, name: "神器等级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.WeaponStarAttr, name: "神器星级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.WingLevelAttr, name: "背饰等级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.WingStarAttr, name: "背饰星级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.WingEnchantmentAttr, name: "背饰附魔", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.TankLevelAttr, name: "战车等级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.TankStarAttr, name: "战车星级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.ArcherLevelAttr, name: "弓箭手等级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.ArcherStarAttr, name: "弓箭手星级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.ArcherGroupAttr, name: "弓箭手羁绊", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.PetAttr, name: "宠物", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.SkillAttr, name: "技能", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.EquipAttr, name: "装备", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.EquipStrengthenAttr, name: "装备强化", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.EquipStageAttr, name: "装备阶段", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.EquipSuitAttr, name: "装备套装", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.MagicLevelAttr, name: "魔法等级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.MagicStarAttr, name: "魔法星级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.PowerAttr, name: "王权", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.Bless, name: "祝福", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.Talent, name: "天赋", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.Forge, name: "锻造台", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.ForgeLevel, name: "锻造台等级", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.Collection, name: "藏品", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.CollectionSuit, name: "藏品套装", type: CombatLogType.ModuleAttr },
    { id: CombatLogId.ItemAttr, name: "道具", type: CombatLogType.ModuleAttr },

    // 系统经济属性
    { id: CombatLogId.MakeArrowEconomyAttr, name: "制作箭矢", type: CombatLogType.SystemEconomyAttr },
    { id: CombatLogId.MiningEconomyAttr, name: "挖矿", type: CombatLogType.SystemEconomyAttr },
    { id: CombatLogId.OtherEconomyAttr, name: "其他", type: CombatLogType.SystemEconomyAttr },

    // 模块经济属性
    { id: CombatLogId.ForgeEconomyAttr, name: "锻造台", type: CombatLogType.ModuleEconomyAttr },
    { id: CombatLogId.ForgeLevelEconomyAttr, name: "锻造台等级", type: CombatLogType.ModuleEconomyAttr },
    { id: CombatLogId.ItemEconomyAttr, name: "道具", type: CombatLogType.ModuleEconomyAttr },
    { id: CombatLogId.PrivilegeEconomyAttr, name: "特权", type: CombatLogType.ModuleEconomyAttr },
    { id: CombatLogId.TalentEconomyAttr, name: "天赋", type: CombatLogType.ModuleEconomyAttr },
    { id: CombatLogId.FundEconomyAttr, name: "基金", type: CombatLogType.ModuleEconomyAttr },

    // 伤害
    { id: CombatLogId.LeadSkillDamage, name: "主角技能", type: CombatLogType.Damage },
    { id: CombatLogId.LeadAttackDamage, name: "主角普攻", type: CombatLogType.Damage },
    { id: CombatLogId.WeaponSkillDamage, name: "神器技能", type: CombatLogType.Damage },
    { id: CombatLogId.WingSkillDamage, name: "背饰技能", type: CombatLogType.Damage },
    { id: CombatLogId.TankSkillDamage, name: "战车技能", type: CombatLogType.Damage },
    { id: CombatLogId.ArcherSkillDamage, name: "弓箭手技能", type: CombatLogType.Damage },
    { id: CombatLogId.ArcherAttackDamage, name: "弓箭手普攻", type: CombatLogType.Damage },
    { id: CombatLogId.PetSkillDamage, name: "宠物技能", type: CombatLogType.Damage },
    { id: CombatLogId.MagicASkillDamage, name: "魔法主动技能", type: CombatLogType.Damage },
    { id: CombatLogId.MagicPSkillDamage, name: "魔法被动技能", type: CombatLogType.Damage },
    { id: CombatLogId.MonsterAttackDamage, name: "怪兽普攻", type: CombatLogType.Damage },

    // 主线战斗-场景bg移动
    { id: CombatLogId.PerWave, name: "每波", type: CombatLogType.DungeonMainSceneBgMove },
    { id: CombatLogId.PerLevel, name: "每关", type: CombatLogType.DungeonMainSceneBgMove },

    // 其他
    { id: CombatLogId.EffectTriggerTypeUpdate, name: "效果触发类型更新", type: CombatLogType.Other },
    { id: CombatLogId.EffectTriggerTypeTrigger, name: "效果触发类型触发", type: CombatLogType.Other },
    { id: CombatLogId.EffectTriggerTypeProb, name: "效果触发类型概率", type: CombatLogType.Other },
    { id: CombatLogId.EffectParaUpdate, name: "效果参数更新", type: CombatLogType.Other },
    { id: CombatLogId.BuffTriggerTypeUpdate, name: "增益触发类型更新", type: CombatLogType.Other },
    { id: CombatLogId.BuffTriggerTypeTrigger, name: "增益触发类型触发", type: CombatLogType.Other },
    { id: CombatLogId.BuffTrigger, name: "增益触发", type: CombatLogType.Other },
    { id: CombatLogId.BuffUpdate, name: "增益更新", type: CombatLogType.Other },
    { id: CombatLogId.BuffClear, name: "增益清理", type: CombatLogType.Other },
    { id: CombatLogId.Buff602, name: "增益602", type: CombatLogType.Other },
    { id: CombatLogId.Buff702, name: "增益702", type: CombatLogType.Other },
    { id: CombatLogId.PlayerSkillShow, name: "玩家技能显示", type: CombatLogType.Other },
    { id: CombatLogId.PlayerSkillManualRelease, name: "玩家技能手动释放", type: CombatLogType.Other },
    { id: CombatLogId.MonsterSkillManualRelease, name: "怪物技能手动释放", type: CombatLogType.Other },
    { id: CombatLogId.CombatScore, name: "战斗力", type: CombatLogType.Other },
    { id: CombatLogId.CombatResultData, name: "战斗结算数据", type: CombatLogType.Other },
    { id: CombatLogId.SkipCombatTime, name: "跳过战斗帧时间", type: CombatLogType.Other },
];

/**
 * 战斗日志
 */
export default class CombatLog extends Logic {
    private settingData: { [id: number]: boolean } = null; // 设置数据

    public clear(): void {
        this.settingData = null;
    }

    /**
     * 获取配置
     * @param id 日志id
     * @returns
     */
    public getConfig(id: CombatLogId): ICombatLogConfig {
        return COMBAT_LOG_CONFIG.find((e) => e.id === id);
    }

    /**
     * 设置显示状态
     * @param id 日志id
     */
    public setShowState(id: CombatLogId): void {
        this.settingData[id] = !this.settingData[id];

        LocalStorage.getInstance().setItem(LocalStorageKey.CombatLogSettingData, JSON.stringify(this.settingData));
    }

    /**
     * 获取显示状态
     * @param id 日志id
     * @returns
     */
    public getShowState(id: CombatLogId): boolean {
        if (!this.settingData) {
            const localData = LocalStorage.getInstance().getItem(LocalStorageKey.CombatLogSettingData);
            this.settingData = localData ? JSON.parse(localData) : {};
        }

        return !!this.settingData[id];
    }

    /**
     * 获取显示状态-副本类型
     * @param dungeonType 副本类型
     * @returns
     */
    public getShowStateByDungeonType(dungeonType: DungeonType): boolean {
        let logId: CombatLogId = null;
        const combatType = Combat.getInstance().getCombatType(dungeonType);
        switch (combatType) {
            case CombatType.Main:
                logId = CombatLogId.MainDungeon;
                break;
            case CombatType.Pve:
                logId = CombatLogId.PveDungeon;
                break;
            case CombatType.Pvp:
                logId = CombatLogId.PvpDungeon;
                break;
            default:
                break;
        }
        return CombatLog.getInstance().getShowState(logId);
    }

    /**
     * 打印标题
     * @param logId 日志id
     * @param member 成员
     */
    public logTitle(logId: CombatLogId, member?: CombatMember): void {
        const logConfig = CombatLog.getInstance().getConfig(logId);
        const [, moduleName] = COMBAT_LOG_MODULE_CONFIG.find(([tempType]) => tempType === logConfig.type);
        if (!member) {
            Logger.log(moduleName, logConfig.name);
            return;
        }

        const memberBaseData = member.getBaseData();
        switch (memberBaseData.type) {
            case CombatMemberType.Player:
                const player = member as CombatMemberPlayer;
                Logger.log(
                    moduleName,
                    `${logConfig.name}-玩家类型${player.getData().type}-玩家uuid${memberBaseData.uuid}`
                );
                break;
            case CombatMemberType.Monster:
                const monster = member as CombatMemberMonster;
                Logger.log(
                    moduleName,
                    `${logConfig.name}-怪兽id${monster.getData().id}-怪兽uuid${memberBaseData.uuid}`
                );
                break;
            default:
                break;
        }
    }
}
