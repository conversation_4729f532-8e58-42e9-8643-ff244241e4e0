/*
 * @Author: chenx
 * @Date: 2024-06-19 10:47:38
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-23 09:52:58
 */
import Logic from "../../nsn/core/Logic";
import { IChallengerInfo } from "../../protobuf/proto";
import Arena from "./Arena";
import Park from "./Park";
import { SettingId } from "./Setting";
import UnionSiege from "./UnionSiege";

/**
 * 战斗类型
 */
export enum CombatType {
    Main = 1,
    Pve,
    Pvp,
}

/**
 * 副本类型
 */
export enum DungeonType {
    Main = 1, // 主线
    Boss, // boss
    Cloud, // 云端
    Thief, // 怪盗
    Tower, // 爬塔
    Union, // 公会
    Trial, // 试炼
    Arena, // 竞技场
    Park, // 停车场
    UnionDefense, // 公会攻防战
    Test, // 测试
}

/**
 * 战斗事件
 */
export enum CombatEvent {
    ResetMonster = "reset-monster", // 重置怪兽
    Reset = "reset", // 重置
    CancelPause = "cancel-pause", // 取消暂停
    NextLevel = "next-level", // 下一关
    PlayShakeScreenAni = "play-shake-screen-ani", // 播放震屏动画
    UpdateGameSpeed = "update-game-speed", // 更新游戏速度
    KillMonster = "kill-monster", // 击杀怪兽
    SceneBgMove = "scene-bg-move", // 场景bg移动
    JumpBoss = "jump-boss", // 跳转boss
    TriggerDialog = "trigger-dialog", // 触发对话
    HomeTouch = "home-touch", // 主界面触摸
    CloseDungeon = "close-dungeon", // 关闭副本
}

/**
 * 战斗
 */
export default class Combat extends Logic {
    /**
     * 获取玩家战斗数据
     * @param dungeonType 副本类型
     * @param playerUuid 玩家uuid
     * @returns
     */
    public getPlayerCombatData(dungeonType: DungeonType, playerUuid: number): IChallengerInfo {
        switch (dungeonType) {
            case DungeonType.Arena:
                return Arena.getInstance().getPlayerCombatData();
            case DungeonType.Park:
                return Park.getInstance().getPlayerCombatData();
            case DungeonType.UnionDefense:
                return UnionSiege.getInstance().getPlayerCombatData();
            default:
                break;
        }
    }

    /**
     * 获取战斗类型
     * @param dungeonType 副本类型
     * @returns
     */
    public getCombatType(dungeonType: DungeonType): CombatType {
        switch (dungeonType) {
            case DungeonType.Main:
                return CombatType.Main;
            case DungeonType.Boss:
            case DungeonType.Cloud:
            case DungeonType.Thief:
            case DungeonType.Tower:
            case DungeonType.Union:
            case DungeonType.Trial:
            case DungeonType.Test:
                return CombatType.Pve;
            case DungeonType.Arena:
            case DungeonType.Park:
            case DungeonType.UnionDefense:
                return CombatType.Pvp;
            default:
                break;
        }
    }

    /**
     * 获取游戏速度设置id
     * @param dungeonType 副本类型
     * @returns
     */
    public getGameSpeedSettingId(dungeonType: DungeonType): SettingId {
        switch (dungeonType) {
            case DungeonType.Boss:
                return SettingId.DungeonBossGameSpeed;
            case DungeonType.Cloud:
                return SettingId.DungeonCloudGameSpeed;
            case DungeonType.Thief:
                return SettingId.DungeonThiefGameSpeed;
            case DungeonType.Tower:
                return SettingId.DungeonTowerGameSpeed;
            case DungeonType.Union:
                return SettingId.DungeonUnionGameSpeed;
            case DungeonType.Trial:
                return SettingId.DungeonTrialGameSpeed;
            case DungeonType.Test:
                return SettingId.DungeonTestGameSpeed;
            case DungeonType.Arena:
                return SettingId.DungeonArenaGameSpeed;
            case DungeonType.Park:
                return SettingId.DungeonParkGameSpeed;
            case DungeonType.UnionDefense:
                return SettingId.DungeonUnionDefenseGameSpeed;
            default:
                break;
        }
    }
}
