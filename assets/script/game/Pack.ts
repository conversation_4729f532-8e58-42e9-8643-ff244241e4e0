/*
 * @Author: JackyF<PERSON>
 * @Date: 2024-08-28 20:04:00
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-25 14:39:53
 */
import Singleton from "../../nsn/util/Singleton";
import TextUtils from "../../nsn/util/TextUtils";
import i18n from "../config/i18n/I18n";
import { EnumPackReceiveType } from "../data/base/BasePack";
import { EnumTaskDetailType } from "../data/base/BaseTaskDetail";
import { ITEM_ID } from "../data/parser/TBItem";
import TBMainBarrier from "../data/parser/TBMainBarrier";
import TBMainMap from "../data/parser/TBMainMap";
import TBPack from "../data/parser/TBPack";
import TBPowerLevel from "../data/parser/TBPowerLevel";
import Bag from "./Bag";
import CollectionGame from "./CollectionGame";
import DungeonBoss from "./DungeonBoss";
import DungeonMain from "./DungeonMain";
import <PERSON>Arrow from "./MakeArrow";
import Mining from "./Mining";
import Pet from "./Pet";
import Power from "./Power";
import Task from "./Task";

export default class Pack extends Singleton {
    /**
     * 获得当前条件的完成情况
     * @param packId
     * @param showStr
     * @returns
     */
    public checkCondition(packId: number): { value: string; isLock: boolean } {
        const packTB = TBPack.getInstance().getDataById(packId);
        const checkPara = packTB.receivePara[0];
        const value = checkPara + "";
        let isLock = false;
        switch (packTB.receiveType) {
            case EnumPackReceiveType.Default:
                isLock = false;
                break;
            case EnumPackReceiveType.LevelReachReceive:
                const level = MakeArrow.getInstance().getMaxGridLevel();
                isLock = checkPara > level;
                break;
            case EnumPackReceiveType.BOSSReachReceive:
                const lv1 = DungeonBoss.getInstance().getCurDungeonId();
                isLock = checkPara > lv1;
                break;
            case EnumPackReceiveType.AdventureReachReceive:
                const lv2 = DungeonMain.getInstance().getLevelId();
                isLock = checkPara > lv2;
                break;
            case EnumPackReceiveType.MiningReachReceipt:
                isLock = checkPara > Mining.getInstance().getHistoryDepth();
                break;
            case EnumPackReceiveType.SomeDaysReceive:
                isLock = true;
                break;
            case EnumPackReceiveType.ConsumingDiamondsReceive:
                const count1 = Task.getInstance().getStatisticCountByTypeAndKey(
                    EnumTaskDetailType.UseItemTask,
                    ITEM_ID.DIAMOND + ""
                );
                isLock = checkPara > count1;
                break;
            case EnumPackReceiveType.PowerLevel:
                const lv3 = Power.getInstance().getPowerInfo().kingLevelId;
                isLock = checkPara > lv3;
                break;
            case EnumPackReceiveType.PetSynthesis:
                const times = Pet.getInstance().getSynthesisCount();
                isLock = checkPara > times;
                break;
            case EnumPackReceiveType.CollectGameCostX:
                const powerData = CollectionGame.getInstance().getPowerData();
                isLock = checkPara > powerData.totalPower;
                break;
            case EnumPackReceiveType.ActivityLevelReachesX:
                const count = Bag.getInstance().getItemCountById(ITEM_ID.TASK_ACTIVE_FUND);
                isLock = checkPara > count;
                break;
            default:
                break;
        }
        return { value, isLock };
    }

    /**
     * 获取当前玩家进度文本
     * @param packId
     * @returns
     */
    public getPlayerProgressText(packId: number): string {
        const data = TBPack.getInstance().getDataById(packId);
        let value = "";

        switch (data.receiveType) {
            case EnumPackReceiveType.Default:
                break;
            case EnumPackReceiveType.LevelReachReceive:
                break;
            case EnumPackReceiveType.BOSSReachReceive:
                break;
            case EnumPackReceiveType.AdventureReachReceive:
                const lv2 = DungeonMain.getInstance().getLevelId();
                const barrierData = TBMainBarrier.getInstance().getDataById(lv2);
                const mapData = TBMainMap.getInstance().getDataById(barrierData.map);
                value = TextUtils.format(i18n.fund0001, mapData.name, mapData.reveal, barrierData.reveal);
                break;
            case EnumPackReceiveType.MiningReachReceipt:
                value = TextUtils.format(i18n.fund0002, Mining.getInstance().getHistoryDepth());
                break;
            case EnumPackReceiveType.SomeDaysReceive:
                break;
            case EnumPackReceiveType.ConsumingDiamondsReceive:
                break;
            case EnumPackReceiveType.PowerLevel:
                const lv3 = Power.getInstance().getPowerInfo().kingLevelId;
                const powerData = TBPowerLevel.getInstance().getDataById(lv3);
                value = powerData.name.replace("·", "");
                break;
            case EnumPackReceiveType.PetSynthesis:
                const times = Pet.getInstance().getSynthesisCount();
                value = TextUtils.format(i18n.fund0003, times);
                break;
            case EnumPackReceiveType.CollectGameCostX:
                const powerInfo = CollectionGame.getInstance().getPowerData();
                value = TextUtils.format(i18n.fund0006, powerInfo.totalPower);
                break;
            case EnumPackReceiveType.ActivityLevelReachesX:
                const count = Bag.getInstance().getItemCountById(ITEM_ID.TASK_ACTIVE_FUND);
                value = count + "";
                break;
            default:
                break;
        }

        return value;
    }

    /**
     * 获取当前礼包进度文本
     * @param packId
     * @returns
     */
    public getPackProgressText(packId: number): string {
        const data = TBPack.getInstance().getDataById(packId);
        const conditionText = data.conditionText.split("#");
        let value = "";

        switch (data.receiveType) {
            case EnumPackReceiveType.Default:
                break;
            case EnumPackReceiveType.LevelReachReceive:
                break;
            case EnumPackReceiveType.BOSSReachReceive:
                break;
            case EnumPackReceiveType.AdventureReachReceive:
                value = TextUtils.format(i18n.fund0005, conditionText[0], conditionText[1], conditionText[2]);
                break;
            case EnumPackReceiveType.MiningReachReceipt:
                value = TextUtils.format(i18n.fund0004, conditionText[0], conditionText[1]);
                break;
            case EnumPackReceiveType.SomeDaysReceive:
                break;
            case EnumPackReceiveType.ConsumingDiamondsReceive:
                break;
            case EnumPackReceiveType.PowerLevel:
                if (conditionText.length > 2) {
                    value = TextUtils.format(i18n.fund0005, conditionText[0], conditionText[1], conditionText[2]);
                } else {
                    value = TextUtils.format(i18n.fund0004, conditionText[0], conditionText[1]);
                }
                break;
            case EnumPackReceiveType.PetSynthesis:
                value = TextUtils.format(i18n.fund0004, conditionText[0], conditionText[1]);
                break;
            case EnumPackReceiveType.CollectGameCostX:
                value = TextUtils.format(i18n.fund0004, conditionText[0], conditionText[1]);
                break;
            case EnumPackReceiveType.ActivityLevelReachesX:
                value = TextUtils.format(i18n.fund0004, conditionText[0], conditionText[1]);
                break;
            default:
                break;
        }

        return value;
    }
}
