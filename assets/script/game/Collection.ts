/*
 * @Author: zhangwj
 * @Date: 2023-8-24 15:43:58
 * @Last Modified by: tangtq
 * @Last Modified time: 2025-07-22 10:35:01
 */

import Audio from "../../nsn/audio/Audio";
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Tips from "../../nsn/util/Tips";
import {
    CollectionCreateNoticeRet,
    CollectionGroupReward,
    CollectionGroupRewardRet,
    CollectionInit,
    CollectionInitRet,
    CollectionLevelReward,
    CollectionLevelRewardRet,
    CollectionReceiveExp,
    CollectionReceiveExpRet,
    CollectionUnlock,
    CollectionUpgrade,
    CollectionUpgradeRet,
    CollectionUpgradeStar,
    CollectionUpgradeStarRet,
    DrawCardDraw,
    ICollectionHandbookObj,
    ICollectionObj,
} from "../../protobuf/proto";
import { AUDIO_EFFECT_PATH, AUDIO_EFFECT_TYPE } from "../config/AudioEffectConfig";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointId } from "../core/redPoint/RedPointId";
import { EnumGroupType } from "../data/base/BaseGroup";
import { EnumGroupEffectConditionType } from "../data/base/BaseGroupEffect";
import { EnumUniversalPara } from "../data/base/BaseUniversal";
import DataGroupEffect from "../data/extend/DataGroupEffect";
import TBCollection from "../data/parser/TBCollection";
import TBCollectionStar from "../data/parser/TBCollectionStar";
import TBCollectionUpgrade from "../data/parser/TBCollectionUpgrade";
import TBGroup from "../data/parser/TBGroup";
import TBGroupEffect from "../data/parser/TBGroupEffect";
import { ITEM_ID } from "../data/parser/TBItem";
import TBUniversal from "../data/parser/TBUniversal";
import Bag from "./Bag";

export enum CollectionEvent {
    SelectOnceCollect = "select-once-collect", // 选择被转化道具
    SendPos = "send-pos", // 发送当前位置
}

export enum EnumUICollectionSelectType {
    HasLevel,
    HasQuality,
    All,
    UpStar,
    Group, // 套装页面打开
    NotOpen, // 不打开弹窗
}

export default class Collection extends Logic {
    private collectionInfos: ICollectionObj[] = []; // 藏品信息
    private collectionHandbookInfo: ICollectionHandbookObj = null; // 图鉴信息
    private groupEffectIds: number[] = []; // 已领取组合套装奖励id
    private drawNewCollection: number[] = []; // 抽到新技能信息
    private noDrawNewCollection: number[] = []; // 非抽奖来源新藏品

    /**
     * 清理数据
     */
    public clear(): void {
        this.collectionInfos = [];
        this.collectionHandbookInfo = null;
        this.groupEffectIds = [];
        this.drawNewCollection = [];
        this.noDrawNewCollection = [];
    }

    protected registerHandler(): void {
        Socket.getInstance().on(CollectionInitRet.prototype.clazzName, this.collectionInitRet, this);
        Socket.getInstance().on(CollectionCreateNoticeRet.prototype.clazzName, this.collectionCreateNoticeRet, this);
        Socket.getInstance().on(CollectionUpgradeRet.prototype.clazzName, this.collectionUpgradeRet, this);
        Socket.getInstance().on(CollectionUpgradeStarRet.prototype.clazzName, this.collectionUpgradeStarRet, this);
        Socket.getInstance().on(CollectionReceiveExpRet.prototype.clazzName, this.collectionReceiveExpRet, this);
        Socket.getInstance().on(CollectionLevelRewardRet.prototype.clazzName, this.collectionLevelRewardRet, this);
        Socket.getInstance().on(CollectionGroupRewardRet.prototype.clazzName, this.collectionGroupRewardRet, this);
    }

    /**
     * 藏品初始化
     */
    private collectionInitRet(data: CollectionInitRet): void {
        const { errMsg, collectionGroupInfo } = data;
        if (errMsg !== CollectionInitRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.collectionInitRet[errMsg]);
            return;
        }
        this.collectionInfos = collectionGroupInfo.collectionInfos;
        this.collectionHandbookInfo = collectionGroupInfo.collectionHandbookInfo;
        this.groupEffectIds = collectionGroupInfo.groupEffectIds;

        this.emit(CollectionInitRet.prototype.clazzName);
    }

    /**
     * 生成新的藏品
     */
    private collectionCreateNoticeRet(data: CollectionCreateNoticeRet): void {
        const { errMsg, collectionInfo, src } = data;
        if (errMsg !== CollectionCreateNoticeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.collectionCreateNoticeRet[errMsg]);
            return;
        }
        const find = this.collectionInfos.find((e) => e.collectionId === collectionInfo.collectionId);
        if (!find) {
            this.collectionInfos.push(collectionInfo);
        }
        this.emit(CollectionCreateNoticeRet.prototype.clazzName, collectionInfo.collectionId);
        RedPoint.getInstance().checkRelative(RedPointId.CollectionWaitExp);

        if (src !== DrawCardDraw.prototype.clazzName) {
            this.noDrawNewCollection.push(collectionInfo.collectionId);
            if (this.noDrawNewCollection.length === 1) {
                UI.getInstance().open("FloatNewCollection", {
                    arrArgs: [{ itemInfoId: collectionInfo.collectionId, isDraw: false }],
                });
            }
        }
        Audio.getInstance().playEffect(AUDIO_EFFECT_TYPE.GET_RESOURCE, AUDIO_EFFECT_PATH.SYSTEM);
    }

    /**
     * 藏品升级
     */
    private collectionUpgradeRet(data: CollectionUpgradeRet): void {
        const { errMsg, collectionId } = data;
        if (errMsg !== CollectionUpgradeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.collectionUpgradeRet[errMsg]);
            return;
        }
        const findIndex = this.collectionInfos.findIndex((e) => e.collectionId === collectionId);
        if (findIndex !== -1) {
            this.collectionInfos[findIndex].level += 1;
        }

        this.emit(CollectionUpgradeRet.prototype.clazzName);
    }

    /**
     * 藏品升星
     */
    private collectionUpgradeStarRet(data: CollectionUpgradeStarRet): void {
        const { errMsg, collectionInfo } = data;
        if (errMsg !== CollectionUpgradeStarRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.collectionUpgradeStarRet[errMsg]);
            return;
        }
        const findIndex = this.collectionInfos.findIndex((e) => e.collectionId === collectionInfo.collectionId);
        if (findIndex !== -1) {
            this.collectionInfos[findIndex] = collectionInfo;
        }

        this.emit(CollectionUpgradeStarRet.prototype.clazzName);
    }

    /**
     * 领取藏品图鉴经验
     */
    private collectionReceiveExpRet(data: CollectionReceiveExpRet): void {
        const { errMsg, collectionHandbookInfo, collectionId } = data;
        if (errMsg !== CollectionReceiveExpRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.collectionReceiveExpRet[errMsg]);
            return;
        }

        if (this.collectionHandbookInfo.level !== collectionHandbookInfo.level) {
            Tips.getInstance().show(i18n.collection0053); // 对比等级弹tips
        } else {
            Tips.getInstance().show(i18n.collection0023);
        }

        this.collectionHandbookInfo = collectionHandbookInfo;
        const findIndex = this.collectionInfos.findIndex((e) => e.collectionId === collectionId);
        if (findIndex !== -1) {
            this.collectionInfos[findIndex].waitExp = 0; // 清空经验
        }
        this.emit(CollectionReceiveExpRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.CollectionWaitExp);
        RedPoint.getInstance().checkRelative(RedPointId.CollectionBookInfoReward);
    }

    /**
     * 领取藏品图鉴等级奖励
     */
    private collectionLevelRewardRet(data: CollectionLevelRewardRet): void {
        const { errMsg, collectionHandbookInfo } = data;
        if (errMsg !== CollectionLevelRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.collectionLevelRewardRet[errMsg]);
            return;
        }
        this.collectionHandbookInfo = collectionHandbookInfo;

        this.emit(CollectionLevelRewardRet.prototype.clazzName);
        RedPoint.getInstance().checkRelative(RedPointId.CollectionBookInfoReward);
    }

    /**
     * 领取藏品套装组合奖励
     */
    private collectionGroupRewardRet(data: CollectionGroupRewardRet): void {
        const { errMsg, groupEffectIds } = data;
        if (errMsg !== CollectionGroupRewardRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.collectionGroupRewardRet[errMsg]);
            return;
        }

        for (const id of groupEffectIds) {
            if (!this.groupEffectIds.includes(id)) {
                this.groupEffectIds.push(id);
            }
        }

        RedPoint.getInstance().checkRelative(RedPointId.CollectionGroupReward);
        this.emit(CollectionGroupRewardRet.prototype.clazzName);
    }

    /**
     * 获取当前所有藏品信息
     * @returns
     */
    public getCollectionInfos(): ICollectionObj[] {
        return this.collectionInfos;
    }

    /**
     * 根据id获取当前藏品信息
     * @param collectionId
     * @returns
     */
    public getCollectionDataById(collectionId: number): ICollectionObj {
        return this.collectionInfos.find((e) => e.collectionId === collectionId);
    }

    /**
     * 根据id获取当前藏品是否已拥有
     * @param collectionId
     * @returns
     */
    public getCollectionHasById(collectionId: number): boolean {
        return !!this.collectionInfos.find((e) => e.collectionId === collectionId);
    }

    /**
     * 获取当前图鉴信息
     * @returns
     */
    public getBookInfo(): ICollectionHandbookObj {
        return this.collectionHandbookInfo;
    }

    /**
     * 根据id获取该组合是否已领取
     * @param groupId
     * @returns
     */
    public getGroupRewardReceivedById(groupId: number): boolean {
        return this.groupEffectIds.includes(groupId);
    }

    /**
     * 根据level获取当前图鉴等级奖励是否领取
     * @param level
     * @returns
     */
    public getBookInfoReceivedByLevel(level: number): boolean {
        const bookInfo = this.getBookInfo();
        return bookInfo.receiveLevels.includes(level);
    }

    /**
     * 获取经验未领取的藏品
     * @returns
     */
    public getDataHasExps(): ICollectionObj[] {
        const data = this.getCollectionInfos();
        return data.filter((e) => e.waitExp > 0);
    }

    /**
     * 根据藏品id判断是否可升级
     * @param id
     * @returns
     */
    public getIsUpgradeById(id: number): boolean {
        const data = TBCollection.getInstance().getDataById(id);
        const info = this.getCollectionDataById(id);
        if (!info) {
            return false;
        }
        const nextLevelData = TBCollectionUpgrade.getInstance().getDataByTypeAndLevel(data.upgradeCall, info.level + 1);
        if (!nextLevelData) {
            return false; // 最高等级
        }

        const isEnough = Bag.getInstance().isEnough(nextLevelData.upgradeCost[0][0], nextLevelData.upgradeCost[0][1]);
        if (isEnough) {
            return true;
        }

        const tbUniversalData = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.CollectionItemChange);
        const arr = tbUniversalData.filter((e) => e[2] === nextLevelData.upgradeCost[0][0]);
        if (!isEnough && nextLevelData.upgradeCost[0][0] === ITEM_ID.COLLECTION_MIN_QUALITY_ITEM) {
            return false; // 不可转换并且资源不够
        }

        let result = Bag.getInstance().getItemCountById(nextLevelData.upgradeCost[0][0]); // 当前数量
        for (const e of arr) {
            const count = Math.floor(Bag.getInstance().getItemCountById(e[0]) / e[1]);
            result += count;
            if (result >= nextLevelData.upgradeCost[0][1]) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据id判断该藏品是否可升星
     * @param id
     * @returns
     */
    public isUpStarById(id: number): boolean {
        const data = TBCollection.getInstance().getDataById(id);
        const info = this.getCollectionDataById(id);
        if (!info) {
            return false;
        }
        const starData = TBCollectionStar.getInstance().getDataByStarTypeAndStar(data.starType, info.star + 1);
        if (!starData) {
            return false; // 最高星级
        }

        const universalData = TBUniversal.getInstance().getValueByPara(EnumUniversalPara.CollectionUniversalFragments);
        const find = universalData.find((e) => e[0] === info.quality); // 是否可以用万能碎片转
        const costItemsEnough = starData.costItem.every((item) => Bag.getInstance().isEnough(item[0], item[1]));

        // 处理碎片数量
        let totalFragments = Bag.getInstance().getItemCountById(data.shardId);
        if (find) {
            totalFragments += Bag.getInstance().getItemCountById(find[1]);
        }

        if (starData.costFragment !== 0) {
            if (starData.costItem.length > 0) {
                return totalFragments >= starData.costFragment && costItemsEnough;
            } else {
                return totalFragments >= starData.costFragment;
            }
        } else {
            return starData.costItem.length > 0 ? costItemsEnough : false;
        }
    }

    /**
     * 根据id判断该藏品是否有经验未领取
     * @param collectionId
     * @returns
     */
    public getCollectionHasExp(collectionId: number): boolean {
        const data = this.getCollectionInfos();
        return !!data.find((e) => e.collectionId === collectionId && e.waitExp > 0);
    }

    /**
     * 根据组合数组返回套装数量
     * @param groups
     * @returns
     */
    public getGroupCountByGroups(groups: number[]): number {
        let count = 0;
        for (const e of groups) {
            const find = this.getCollectionDataById(e);
            if (find) {
                count += 1;
            }
        }
        return count;
    }

    /**
     * 获取已领取的组合奖励
     * @returns
     */
    public getEffectsReward(): number[] {
        return this.groupEffectIds;
    }

    /**
     * 根据组合id和达成条件 判断该星级是否解锁
     * @param groupId
     * @param condition
     * @returns
     */
    public getStarLockByGroupIdAndCondition(groupId: number, condition: number): boolean {
        const data = TBGroup.getInstance().getDataById(groupId);
        let result = true;
        for (const e of data.groupItemId) {
            const info = this.getCollectionDataById(e);
            if (!info) {
                result = false;
                break;
            }
            if (info.star < condition) {
                result = false;
                break;
            }
        }
        return result;
    }

    /**
     * 获取组合中拥有的数量
     * @param groupId
     * @returns
     */
    public getGroupCounts(groupId: number): number {
        const data = TBGroup.getInstance().getDataById(groupId);
        let result = 0;
        for (const e of data.groupItemId) {
            const info = this.getCollectionDataById(e);
            if (info) {
                result += 1;
            }
        }
        return result;
    }

    /**
     * 添加新藏品信息 - 抽奖渠道
     * @param collectionId
     */
    public addNewDrawCollection(collectionId: number): void {
        this.drawNewCollection.push(collectionId);
    }

    /**
     * 获取新藏品信息 - 抽奖渠道
     */
    public getNewDrawCollection(): number[] {
        return this.drawNewCollection;
    }

    /**
     * 删除新藏品信息 - 抽奖渠道
     * @param collectionId
     */
    public deleteDrawNewCollection(collectionId: number): void {
        const index = this.drawNewCollection.findIndex((e) => e === collectionId);
        if (index !== -1) {
            this.drawNewCollection.splice(index, 1);
        }
    }

    /**
     * 删除所有新藏品信息 - 抽奖渠道
     */
    public deleteAllDrawNewCollection(): void {
        this.drawNewCollection = [];
    }

    /**
     * 获取是否是新藏品 - 抽奖渠道
     * @param magicalId
     * @returns
     */
    public isNewDrawCollection(collectionId: number): boolean {
        return this.drawNewCollection.includes(collectionId);
    }

    /**
     * 删除非抽奖来源渠道新藏品
     * @param collectionId
     */
    public deleteNoDrawNewCollection(collectionId: number): void {
        const findIndex = this.noDrawNewCollection.findIndex((e) => e === collectionId);
        if (findIndex !== -1) {
            this.noDrawNewCollection.splice(findIndex, 1);
        }
    }

    /**
     * 获取非抽奖渠道新藏品
     * @returns
     */
    public getNoDrawNewCollection(): number[] {
        return this.noDrawNewCollection;
    }

    /**
     * 删除非抽奖渠道新藏品
     * @returns
     */
    public deleteAllNoDrawNewCollection(): void {
        this.noDrawNewCollection = [];
    }

    /*
     * 获取生效套装效果信息
     * @param collectionData 藏品数据
     * @returns
     */
    public getEffectSuitEffectInfo(collectionData: ICollectionObj[]): DataGroupEffect[] {
        const effectInfo: DataGroupEffect[] = [];
        const groupInfo = TBGroup.getInstance().getDataByType(EnumGroupType.Collection);
        groupInfo.forEach((e) => {
            if (
                e.groupItemId.findIndex((e2) => collectionData.findIndex((e3) => e3.collectionId === e2) === -1) === -1
            ) {
                const allEffectInfo = TBGroupEffect.getInstance().getDataByGroupId(e.id);
                const tempEffectInfo = allEffectInfo.filter(
                    (e2) => e2.conditionType === EnumGroupEffectConditionType.CollectionStar
                );
                tempEffectInfo.sort((a, b) => b.condition - a.condition);
                let minStar = -1;
                e.groupItemId.forEach((e2) => {
                    const tempCollectionData = collectionData.find((e3) => e3.collectionId === e2);
                    minStar = minStar === -1 ? tempCollectionData.star : Math.min(tempCollectionData.star, minStar);
                });
                const tempEffectInfo2 = tempEffectInfo.find((e2) => e2.condition <= minStar);
                tempEffectInfo2 && effectInfo.push(tempEffectInfo2);
            }
        });

        return effectInfo;
    }

    /**
     * 套装奖励可领取
     * @param id
     * @returns
     */
    public getRewardEffectCanGet(id: number): boolean {
        const data = TBGroupEffect.getInstance().getDataByGroupId(id);
        const groupData = TBGroup.getInstance().getDataById(id);
        const groupEffectIds = Collection.getInstance().getEffectsReward();
        for (const e of data) {
            if (!groupEffectIds.includes(e.id)) {
                let result = true;
                for (const k of groupData.groupItemId) {
                    const info = Collection.getInstance().getCollectionDataById(k);
                    if (!info || info.star < e.condition) {
                        result = false;
                        break;
                    }
                }

                if (result) {
                    return true; // 找到第一个直接返回
                }
            }
        }
        return false;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 初始化
     */
    public sendCollectionInit(): void {
        const data = CollectionInit.create({});
        Socket.getInstance().send(data);
    }

    /**
     * 解锁藏品
     * @param collectionId
     */
    public sendCollectionUnlock(collectionId: number): void {
        const data = CollectionUnlock.create({ collectionId });
        Socket.getInstance().send(data);
    }

    /**
     * 藏品升级
     * @param collectionId
     * @param isMerge
     */
    public sendCollectionUpgrade(collectionId: number, isMerge: boolean): void {
        const data = CollectionUpgrade.create({ collectionId, isMerge });
        Socket.getInstance().send(data);
    }

    /**
     * 藏品升星
     * @param collectionId
     * @param isUse
     */
    public sendCollectionUpgradeStar(collectionId: number, isUse: boolean): void {
        const data = CollectionUpgradeStar.create({ collectionId, isUse });
        Socket.getInstance().send(data);
    }

    /**
     * 领取藏品图鉴等级奖励
     */
    public sendCollectionLevelReward(): void {
        const data = CollectionLevelReward.create({});
        Socket.getInstance().send(data);
    }

    /**
     * 领取藏品套装组合奖励
     * @param groupEffectIds
     */
    public sendCollectionGroupReward(groupEffectIds: number[]): void {
        const data = CollectionGroupReward.create({ groupEffectIds });
        Socket.getInstance().send(data);
    }

    /**
     * 领取藏品图鉴经验
     * @param collectionId
     */
    public sendCollectionReceiveExp(collectionId: number): void {
        const data = CollectionReceiveExp.create({ collectionId });
        Socket.getInstance().send(data);
    }
}
