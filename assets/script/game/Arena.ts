/*
 * @Author: chenx
 * @Date: 2024-03-18 15:04:09
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-18 11:54:04
 */
import Logic from "../../nsn/core/Logic";
import Socket from "../../nsn/core/Socket";
import UI from "../../nsn/ui/UI";
import Time from "../../nsn/util/Time";
import Tips from "../../nsn/util/Tips";
import {
    ArenaChallenge,
    ArenaChallengeRet,
    ArenaGetLog,
    ArenaGetLogRet,
    ArenaInitRet,
    ArenaRefresh,
    ArenaRefreshRet,
    IChallengerInfo,
} from "../../protobuf/proto";
import i18n from "../config/i18n/I18n";
import RedPoint from "../core/redPoint/RedPoint";
import { RedPointId } from "../core/redPoint/RedPointId";
import Combat, { CombatEvent, DungeonType } from "./Combat";

/**
 * 竞技场
 */
export default class Arena extends Logic {
    private freeTimesUsed: number = 0; // 已使用的免费次数
    private challengerInfos: IChallengerInfo[] = [];
    private requestTime: number = 0;
    private playerCombatData: IChallengerInfo = null; // 玩家战斗数据

    protected registerHandler(): void {
        Socket.getInstance().on(ArenaInitRet.prototype.clazzName, this.arenaInitRet, this);
        Socket.getInstance().on(ArenaRefreshRet.prototype.clazzName, this.arenaRefreshRet, this);
        Socket.getInstance().on(ArenaChallengeRet.prototype.clazzName, this.arenaChallengeRet, this);
        Socket.getInstance().on(ArenaGetLogRet.prototype.clazzName, this.arenaGetLogRet, this);
    }

    public clear(): void {
        this.freeTimesUsed = 0;
        this.challengerInfos = [];
        this.requestTime = 0;
        this.playerCombatData = null;
    }

    /**
     * 初始化
     * @param data
     */
    private arenaInitRet(data: ArenaInitRet): void {
        const { freeTimesUsed } = data;
        this.freeTimesUsed = freeTimesUsed;
    }

    /**
     * 竞技场刷新
     * @param data
     */
    private arenaRefreshRet(data: ArenaRefreshRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, challengerInfos } = data;
        if (errMsg !== ArenaRefreshRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.arenaRefreshRet[errMsg]);
            return;
        }
        this.challengerInfos = challengerInfos;
        this.requestTime = Time.getInstance().now();
        this.emit(ArenaRefreshRet.prototype.clazzName);
    }

    /**
     * 竞技场挑战
     * @param data
     */
    private arenaChallengeRet(data: ArenaChallengeRet): void {
        const { errMsg, freeTimesUsed } = data;
        if (errMsg !== ArenaChallengeRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.arenaChallengeRet[errMsg]);
            Combat.getInstance().emit(CombatEvent.CloseDungeon, DungeonType.Arena);
            return;
        }

        this.freeTimesUsed = freeTimesUsed;
        this.emit(ArenaChallengeRet.prototype.clazzName, data);
        RedPoint.getInstance().checkRelative(RedPointId.Arena);
        this.sendArenaRefresh();
    }

    /**
     * 获取战斗日志
     * @param data
     */
    private arenaGetLogRet(data: ArenaGetLogRet): void {
        UI.getInstance().hideSoftLoading();
        const { errMsg, battleLogInfos } = data;
        if (errMsg !== ArenaGetLogRet.ErrorResult.None) {
            Tips.getInstance().show(i18n.arenaGetLogRet[errMsg]);
            return;
        }
        this.emit(ArenaGetLogRet.prototype.clazzName, battleLogInfos);
    }

    /**
     * 获取已使用的免费挑战次数
     * @returns
     */
    public getUsedFreeChallengeTimes(): number {
        return this.freeTimesUsed;
    }

    /**
     * 获取挑战数据
     * @returns
     */
    public getChallengeInfo(): IChallengerInfo[] {
        return this.challengerInfos;
    }

    /**
     * 获取请求刷新时间
     * @returns
     */
    public getRequestTime(): number {
        return this.requestTime;
    }

    /**
     * 设置玩家战斗数据
     * @param data 玩家战斗数据
     */
    public setPlayerCombatData(data: IChallengerInfo): void {
        this.playerCombatData = data;
    }

    /**
     * 获取玩家战斗数据
     * @returns
     */
    public getPlayerCombatData(): IChallengerInfo {
        return this.playerCombatData;
    }

    /// /////////////////////////////  发送协议  ////////////////////////////////

    /**
     * 刷新对手
     */
    public sendArenaRefresh(): void {
        const data = ArenaRefresh.create({});
        Socket.getInstance().send(data);
    }

    /**
     * 挑战对手
     * @param challengerId
     * @param isWin
     */
    public sendArenaChallenge(challengerId: string, isWin: boolean): void {
        const data = ArenaChallenge.create({ challengerId, isWin });
        Socket.getInstance().send(data);
    }

    /**
     * 获取战斗日志
     */
    public sendArenaGetLog(): void {
        const data = ArenaGetLog.create();
        Socket.getInstance().send(data);
    }
}
