/**
 * 导表工具生成
 * 请勿手动修改
 */

import DataActivity from "./DataActivity";
import DataActivityBoss from "./DataActivityBoss";
import DataActivityGame from "./DataActivityGame";
import DataActivityItem from "./DataActivityItem";
import DataActivityLogin from "./DataActivityLogin";
import DataActivityUniversal from "./DataActivityUniversal";
import DataAdvertisement from "./DataAdvertisement";
import DataArcher from "./DataArcher";
import DataArcherLevel from "./DataArcherLevel";
import DataArcherStar from "./DataArcherStar";
import DataArena from "./DataArena";
import DataArrow from "./DataArrow";
import DataAttribute from "./DataAttribute";
import DataBlessing from "./DataBlessing";
import DataBlessingLevel from "./DataBlessingLevel";
import DataBoxDiy from "./DataBoxDiy";
import DataBoxMagnification from "./DataBoxMagnification";
import DataBoxRandom from "./DataBoxRandom";
import DataBoxSystem from "./DataBoxSystem";
import DataBoxSystemProgress from "./DataBoxSystemProgress";
import DataChat from "./DataChat";
import DataChatText from "./DataChatText";
import DataCollection from "./DataCollection";
import DataCollectionGamePara from "./DataCollectionGamePara";
import DataCollectionHandbook from "./DataCollectionHandbook";
import DataCollectionStar from "./DataCollectionStar";
import DataCollectionTask from "./DataCollectionTask";
import DataCollectionUpgrade from "./DataCollectionUpgrade";
import DataCombatDialogue from "./DataCombatDialogue";
import DataCombatDialogueGroup from "./DataCombatDialogueGroup";
import DataCrossSever from "./DataCrossSever";
import DataDraw from "./DataDraw";
import DataDrawGroup from "./DataDrawGroup";
import DataDungeon from "./DataDungeon";
import DataDungeonBoss from "./DataDungeonBoss";
import DataDungeonBox from "./DataDungeonBox";
import DataDungeonCloud from "./DataDungeonCloud";
import DataDungeonCombat from "./DataDungeonCombat";
import DataDungeonEquipment from "./DataDungeonEquipment";
import DataDungeonEquipmentTotal from "./DataDungeonEquipmentTotal";
import DataDungeonTest from "./DataDungeonTest";
import DataDungeonThief from "./DataDungeonThief";
import DataDungeonTower from "./DataDungeonTower";
import DataEconomyAttribute from "./DataEconomyAttribute";
import DataEntry from "./DataEntry";
import DataEvent from "./DataEvent";
import DataExpand from "./DataExpand";
import DataExpedition from "./DataExpedition";
import DataExpeditionEvent from "./DataExpeditionEvent";
import DataForgeCultivateLevel from "./DataForgeCultivateLevel";
import DataForgeLevel from "./DataForgeLevel";
import DataForgeSkin from "./DataForgeSkin";
import DataFundMarkup from "./DataFundMarkup";
import DataGameLog from "./DataGameLog";
import DataGameSwitch from "./DataGameSwitch";
import DataGiftCenter from "./DataGiftCenter";
import DataGridBigReward from "./DataGridBigReward";
import DataGroup from "./DataGroup";
import DataGroupEffect from "./DataGroupEffect";
import DataGuideGroup from "./DataGuideGroup";
import DataGuideItem from "./DataGuideItem";
import DataHelp from "./DataHelp";
import DataIdleEarningsTotal from "./DataIdleEarningsTotal";
import DataItem from "./DataItem";
import DataItemAttribute from "./DataItemAttribute";
import DataItemChange from "./DataItemChange";
import DataJump from "./DataJump";
import DataLeadEquip from "./DataLeadEquip";
import DataLeadEquipCoefficient from "./DataLeadEquipCoefficient";
import DataLeadEquipMaster from "./DataLeadEquipMaster";
import DataLeadEquipQuality from "./DataLeadEquipQuality";
import DataLeadEquipStrengthen from "./DataLeadEquipStrengthen";
import DataLeadEquipSuit from "./DataLeadEquipSuit";
import DataLeadSkin from "./DataLeadSkin";
import DataLeadSkinLevel from "./DataLeadSkinLevel";
import DataLeadSkinStar from "./DataLeadSkinStar";
import DataLimitedPlay from "./DataLimitedPlay";
import DataMagicSkill from "./DataMagicSkill";
import DataMagicSkillLevel from "./DataMagicSkillLevel";
import DataMagicSkillStar from "./DataMagicSkillStar";
import DataMail from "./DataMail";
import DataMainBarrier from "./DataMainBarrier";
import DataMainChapter from "./DataMainChapter";
import DataMainMap from "./DataMainMap";
import DataMainOtherReward from "./DataMainOtherReward";
import DataMainRandomDrop from "./DataMainRandomDrop";
import DataMainRewardGroup from "./DataMainRewardGroup";
import DataMiniGame from "./DataMiniGame";
import DataMiningGroup from "./DataMiningGroup";
import DataMiningProp from "./DataMiningProp";
import DataMiningSpace from "./DataMiningSpace";
import DataMonsterBase from "./DataMonsterBase";
import DataMonsterCallFormation from "./DataMonsterCallFormation";
import DataMonsterFormation from "./DataMonsterFormation";
import DataMonsterGroup from "./DataMonsterGroup";
import DataOfficialMall from "./DataOfficialMall";
import DataOfficialShop from "./DataOfficialShop";
import DataOutJoin from "./DataOutJoin";
import DataPack from "./DataPack";
import DataParkingDispatch from "./DataParkingDispatch";
import DataParkingLot from "./DataParkingLot";
import DataParkingLotBuff from "./DataParkingLotBuff";
import DataParkingLotLevel from "./DataParkingLotLevel";
import DataParkingLotSkin from "./DataParkingLotSkin";
import DataParkingSkinLevel from "./DataParkingSkinLevel";
import DataPet from "./DataPet";
import DataPetLevel from "./DataPetLevel";
import DataPetStar from "./DataPetStar";
import DataPetSynthesis from "./DataPetSynthesis";
import DataPetTotal from "./DataPetTotal";
import DataPopup from "./DataPopup";
import DataPower from "./DataPower";
import DataPowerLevel from "./DataPowerLevel";
import DataPowerPeakedness from "./DataPowerPeakedness";
import DataPowerPromotion from "./DataPowerPromotion";
import DataPriceTemplate from "./DataPriceTemplate";
import DataPrincess from "./DataPrincess";
import DataPrincessChild from "./DataPrincessChild";
import DataPrincessChildGroup from "./DataPrincessChildGroup";
import DataPrincessTotal from "./DataPrincessTotal";
import DataPrivilegeConfig from "./DataPrivilegeConfig";
import DataPrivilegeGroup from "./DataPrivilegeGroup";
import DataProgressReward from "./DataProgressReward";
import DataRandomNickname from "./DataRandomNickname";
import DataRandomReward from "./DataRandomReward";
import DataRandomShop from "./DataRandomShop";
import DataRandomShopConfig from "./DataRandomShopConfig";
import DataRandomSkill from "./DataRandomSkill";
import DataRank from "./DataRank";
import DataRankReward from "./DataRankReward";
import DataRecharge from "./DataRecharge";
import DataRechargeGift from "./DataRechargeGift";
import DataRechargeTab from "./DataRechargeTab";
import DataRedPacket from "./DataRedPacket";
import DataRefresh from "./DataRefresh";
import DataReset from "./DataReset";
import DataRule from "./DataRule";
import DataShop from "./DataShop";
import DataSkill from "./DataSkill";
import DataSkillEffect from "./DataSkillEffect";
import DataSkillShow from "./DataSkillShow";
import DataSystemEntry from "./DataSystemEntry";
import DataTalentLeaf from "./DataTalentLeaf";
import DataTalentLeafGroup from "./DataTalentLeafGroup";
import DataTalentTree from "./DataTalentTree";
import DataTank from "./DataTank";
import DataTankLevel from "./DataTankLevel";
import DataTankStar from "./DataTankStar";
import DataTaskDetail from "./DataTaskDetail";
import DataTaskGroup from "./DataTaskGroup";
import DataTaskSeq from "./DataTaskSeq";
import DataTextLimit from "./DataTextLimit";
import DataTextSystem from "./DataTextSystem";
import DataThiefBloodReward from "./DataThiefBloodReward";
import DataTotalRecharge from "./DataTotalRecharge";
import DataTraveling from "./DataTraveling";
import DataTrialBoss from "./DataTrialBoss";
import DataUnion from "./DataUnion";
import DataUnionBoss from "./DataUnionBoss";
import DataUnionLevel from "./DataUnionLevel";
import DataUnionPosition from "./DataUnionPosition";
import DataUniversal from "./DataUniversal";
import DataWeapon from "./DataWeapon";
import DataWeaponLevel from "./DataWeaponLevel";
import DataWeaponStar from "./DataWeaponStar";
import DataWing from "./DataWing";
import DataWingEnchant from "./DataWingEnchant";
import DataWingLevel from "./DataWingLevel";
import DataWingLevelRewards from "./DataWingLevelRewards";
import DataWingStar from "./DataWingStar";

export const DATA_MAP = {
    activity: DataActivity,
    activityBoss: DataActivityBoss,
    activityGame: DataActivityGame,
    activityItem: DataActivityItem,
    activityLogin: DataActivityLogin,
    activityUniversal: DataActivityUniversal,
    advertisement: DataAdvertisement,
    archer: DataArcher,
    archerLevel: DataArcherLevel,
    archerStar: DataArcherStar,
    arena: DataArena,
    arrow: DataArrow,
    attribute: DataAttribute,
    blessing: DataBlessing,
    blessingLevel: DataBlessingLevel,
    boxDiy: DataBoxDiy,
    boxMagnification: DataBoxMagnification,
    boxRandom: DataBoxRandom,
    boxSystem: DataBoxSystem,
    boxSystemProgress: DataBoxSystemProgress,
    chat: DataChat,
    chatText: DataChatText,
    collection: DataCollection,
    collectionGamePara: DataCollectionGamePara,
    collectionHandbook: DataCollectionHandbook,
    collectionStar: DataCollectionStar,
    collectionTask: DataCollectionTask,
    collectionUpgrade: DataCollectionUpgrade,
    combatDialogue: DataCombatDialogue,
    combatDialogueGroup: DataCombatDialogueGroup,
    crossSever: DataCrossSever,
    draw: DataDraw,
    drawGroup: DataDrawGroup,
    dungeon: DataDungeon,
    dungeonBoss: DataDungeonBoss,
    dungeonBox: DataDungeonBox,
    dungeonCloud: DataDungeonCloud,
    dungeonCombat: DataDungeonCombat,
    dungeonEquipment: DataDungeonEquipment,
    dungeonEquipmentTotal: DataDungeonEquipmentTotal,
    dungeonTest: DataDungeonTest,
    dungeonThief: DataDungeonThief,
    dungeonTower: DataDungeonTower,
    economyAttribute: DataEconomyAttribute,
    entry: DataEntry,
    event: DataEvent,
    expand: DataExpand,
    expedition: DataExpedition,
    expeditionEvent: DataExpeditionEvent,
    forgeCultivateLevel: DataForgeCultivateLevel,
    forgeLevel: DataForgeLevel,
    forgeSkin: DataForgeSkin,
    fundMarkup: DataFundMarkup,
    gameLog: DataGameLog,
    gameSwitch: DataGameSwitch,
    giftCenter: DataGiftCenter,
    gridBigReward: DataGridBigReward,
    group: DataGroup,
    groupEffect: DataGroupEffect,
    guideGroup: DataGuideGroup,
    guideItem: DataGuideItem,
    help: DataHelp,
    idleEarningsTotal: DataIdleEarningsTotal,
    item: DataItem,
    itemAttribute: DataItemAttribute,
    itemChange: DataItemChange,
    jump: DataJump,
    leadEquip: DataLeadEquip,
    leadEquipCoefficient: DataLeadEquipCoefficient,
    leadEquipMaster: DataLeadEquipMaster,
    leadEquipQuality: DataLeadEquipQuality,
    leadEquipStrengthen: DataLeadEquipStrengthen,
    leadEquipSuit: DataLeadEquipSuit,
    leadSkin: DataLeadSkin,
    leadSkinLevel: DataLeadSkinLevel,
    leadSkinStar: DataLeadSkinStar,
    limitedPlay: DataLimitedPlay,
    magicSkill: DataMagicSkill,
    magicSkillLevel: DataMagicSkillLevel,
    magicSkillStar: DataMagicSkillStar,
    mail: DataMail,
    mainBarrier: DataMainBarrier,
    mainChapter: DataMainChapter,
    mainMap: DataMainMap,
    mainOtherReward: DataMainOtherReward,
    mainRandomDrop: DataMainRandomDrop,
    mainRewardGroup: DataMainRewardGroup,
    miniGame: DataMiniGame,
    miningGroup: DataMiningGroup,
    miningProp: DataMiningProp,
    miningSpace: DataMiningSpace,
    monsterBase: DataMonsterBase,
    monsterCallFormation: DataMonsterCallFormation,
    monsterFormation: DataMonsterFormation,
    monsterGroup: DataMonsterGroup,
    officialMall: DataOfficialMall,
    officialShop: DataOfficialShop,
    outJoin: DataOutJoin,
    pack: DataPack,
    parkingDispatch: DataParkingDispatch,
    parkingLot: DataParkingLot,
    parkingLotBuff: DataParkingLotBuff,
    parkingLotLevel: DataParkingLotLevel,
    parkingLotSkin: DataParkingLotSkin,
    parkingSkinLevel: DataParkingSkinLevel,
    pet: DataPet,
    petLevel: DataPetLevel,
    petStar: DataPetStar,
    petSynthesis: DataPetSynthesis,
    petTotal: DataPetTotal,
    popup: DataPopup,
    power: DataPower,
    powerLevel: DataPowerLevel,
    powerPeakedness: DataPowerPeakedness,
    powerPromotion: DataPowerPromotion,
    priceTemplate: DataPriceTemplate,
    princess: DataPrincess,
    princessChild: DataPrincessChild,
    princessChildGroup: DataPrincessChildGroup,
    princessTotal: DataPrincessTotal,
    privilegeConfig: DataPrivilegeConfig,
    privilegeGroup: DataPrivilegeGroup,
    progressReward: DataProgressReward,
    randomNickname: DataRandomNickname,
    randomReward: DataRandomReward,
    randomShop: DataRandomShop,
    randomShopConfig: DataRandomShopConfig,
    randomSkill: DataRandomSkill,
    rank: DataRank,
    rankReward: DataRankReward,
    recharge: DataRecharge,
    rechargeGift: DataRechargeGift,
    rechargeTab: DataRechargeTab,
    redPacket: DataRedPacket,
    refresh: DataRefresh,
    reset: DataReset,
    rule: DataRule,
    shop: DataShop,
    skill: DataSkill,
    skillEffect: DataSkillEffect,
    skillShow: DataSkillShow,
    systemEntry: DataSystemEntry,
    talentLeaf: DataTalentLeaf,
    talentLeafGroup: DataTalentLeafGroup,
    talentTree: DataTalentTree,
    tank: DataTank,
    tankLevel: DataTankLevel,
    tankStar: DataTankStar,
    taskDetail: DataTaskDetail,
    taskGroup: DataTaskGroup,
    taskSeq: DataTaskSeq,
    textLimit: DataTextLimit,
    textSystem: DataTextSystem,
    thiefBloodReward: DataThiefBloodReward,
    totalRecharge: DataTotalRecharge,
    traveling: DataTraveling,
    trialBoss: DataTrialBoss,
    union: DataUnion,
    unionBoss: DataUnionBoss,
    unionLevel: DataUnionLevel,
    unionPosition: DataUnionPosition,
    universal: DataUniversal,
    weapon: DataWeapon,
    weaponLevel: DataWeaponLevel,
    weaponStar: DataWeaponStar,
    wing: DataWing,
    wingEnchant: DataWingEnchant,
    wingLevel: DataWingLevel,
    wingLevelRewards: DataWingLevelRewards,
    wingStar: DataWingStar,
}
