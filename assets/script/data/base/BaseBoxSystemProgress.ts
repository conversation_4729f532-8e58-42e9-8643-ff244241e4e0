/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 万宝箱进度表
 */
export default class BaseBoxSystemProgress {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 下一个进度id
     */
    get nextProgress(): number {
        return this.data[1];
    }

    /**
     * 进度值
     */
    get progress(): number {
        return this.data[2];
    }

    /**
     * 奖励
     */
    get reward(): any[] {
        return this.data[3];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
