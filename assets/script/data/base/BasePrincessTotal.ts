/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 参数
 */
export enum EnumPrincessTotalPara {
    /**
     * 体力上限
     */
    StrengthLimit = 1,
    /**
     * 体力恢复时间（单位：秒）
     */
    StrengthRecoveryTime = 2,
    /**
     * 结婚等级
     */
    MarriageLevel = 3,
    /**
     * 约会事件
     */
    AppointmentEvent = 4,
    /**
     * 刷新人数
     */
    RandomPeople = 5,
    /**
     * 刷新Cd（单位：秒）
     */
    RandomCd = 6,
    /**
     * 申请Cd（单位：秒）
     */
    ApplyCd = 7,
    /**
     * 结婚消耗
     */
    MarriageExpenses = 8,
    /**
     * 蜜月消耗
     */
    HoneymoonConsumption = 9,
    /**
     * 每次游历消耗体力
     */
    TravelingConsumeStrength = 10,
    /**
     * 结婚台词及奖励
     */
    WeddingLines = 11,
    /**
     * 联姻邮件id
     */
    MarriageMailId = 12,
    /**
     * 知己属性展示
     */
    PrincessAttributeDisplay = 13,
    /**
     * 知己总属性展示
     */
    PrincessOverallAttributeDisplay = 14,
    /**
     * 王储属性展示
     */
    CrownPrinceAttributeDisplay = 15,
    /**
     * 王储总属性展示
     */
    CrownPrinceOverallAttributeDisplay = 16,
    /**
     * 随机宠幸消耗
     */
    RandomFavorCost = 17,
    /**
     * 随机宠幸免费次数
     */
    RandomlyFavorFreeNum = 18,
    /**
     * 初始知己
     */
    InitialPrincess = 19,
    /**
     * 体力恢复道具id#恢复体力数量
     */
    PhysicalRecovery = 20,
    /**
     * 体力恢复道具id#恢复体力数量
     */
    PhysicalRecovery2 = 21,
}
/**
 * 无效表知己总表
 */
export default class BasePrincessTotal {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 参数
     */
    get para(): EnumPrincessTotalPara {
        return this.data[1];
    }

    /**
     * 值
     */
    get value(): string {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
