/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 参数
 */
export enum EnumCollectionGameParaPara {
    /**
     * 随出奖励格子数量
     */
    GridRewardNum = 1,
    /**
     * 每个事件的抽取概率以及保底数值
     */
    GridDraw = 2,
    /**
     * 奖励格子的随机奖励
     */
    GridReward = 3,
    /**
     * 洞洞乐广告奖励
     */
    CollectionGameAd = 4,
    /**
     * 大奖保底次数
     */
    BigRewardMinNum = 5,
}
/**
 * 藏品玩法参数表
 */
export default class BaseCollectionGamePara {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 参数
     */
    get para(): EnumCollectionGameParaPara {
        return this.data[1];
    }

    /**
     * 值
     */
    get value(): string {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
