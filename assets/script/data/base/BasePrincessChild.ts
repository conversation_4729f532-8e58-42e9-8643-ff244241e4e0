/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 品质
 */
export enum EnumPrincessChildQuality {
    /**
     * 入土
     */
    EnteringSoil = 1,
    /**
     * 笨拙
     */
    Clumsy = 2,
    /**
     * 平庸
     */
    Mediocre = 3,
    /**
     * 聪慧
     */
    SmartIntelligent = 4,
    /**
     * 睿智
     */
    Wise = 5,
    /**
     * 神童
     */
    ChildProdigy = 6,
}
/**
 * 性别
 */
export enum EnumPrincessChildSex {
    /**
     * 男
     */
    Boy = 1,
    /**
     * 女
     */
    Girl = 2,
}
/**
 * 无效表王储表
 */
export default class BasePrincessChild {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 品质
     */
    get quality(): EnumPrincessChildQuality {
        return this.data[1];
    }

    /**
     * 性别
     */
    get sex(): EnumPrincessChildSex {
        return this.data[2];
    }

    /**
     * 美术资源
     */
    get res(): any[] {
        return this.data[3];
    }

    /**
     * 名称
     */
    get name(): string {
        return this.data[4];
    }

    /**
     * 等级上限
     */
    get levelLimit(): number {
        return this.data[5];
    }

    /**
     * 升级所需
     */
    get upgradeRequired(): number[] {
        return this.data[6];
    }

    /**
     * 属性
     */
    get attribute(): any[] {
        return this.data[7];
    }

    /**
     * 权重
     */
    get weight(): number {
        return this.data[8];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
