/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 万宝箱系统
 */
export default class BaseBoxSystem {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 宝箱名称
     */
    get name(): string {
        return this.data[1];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[2];
    }

    /**
     * 开启上限
     */
    get openUpperLimit(): number {
        return this.data[3];
    }

    /**
     * 描述
     */
    get desc(): string {
        return this.data[4];
    }

    /**
     * 万宝积分
     */
    get points(): number {
        return this.data[5];
    }

    /**
     * 奖励配置
     */
    get reward(): any[] {
        return this.data[6];
    }

    /**
     * 首次奖励
     */
    get firstReward(): any[] {
        return this.data[7];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
