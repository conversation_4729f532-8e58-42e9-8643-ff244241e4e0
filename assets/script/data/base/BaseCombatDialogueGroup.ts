/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 触发类型
 */
export enum EnumCombatDialogueGroupTriggerType {
    /**
     * 血量≤XX（百分比）时触发
     */
    UnderHp = 1,
    /**
     * 连续通过关卡XX时触发（只针对主线关卡）
     */
    ContinuousBarrier = 2,
    /**
     * 某种怪物类型生成时触发（参数需填怪物类型）
     */
    MonsterType = 3,
    /**
     * 到达地图最后一关卡时触发（只针对主线关卡）
     */
    MapEnding = 4,
}
/**
 * 对象
 */
export enum EnumCombatDialogueGroupTarget {
    /**
     * 战斗中-弓箭手
     */
    Archer = 1,
    /**
     * 战斗中-主角
     */
    Lead = 2,
    /**
     * 战斗中-怪物(boss)
     */
    Monster = 3,
    /**
     * 战斗中-宠物
     */
    Pet = 4,
}
/**
 * 战斗对话组
 */
export default class BaseCombatDialogueGroup {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 场景参数
     */
    get dungeonType(): number[] {
        return this.data[1];
    }

    /**
     * 触发类型
     */
    get triggerType(): EnumCombatDialogueGroupTriggerType {
        return this.data[2];
    }

    /**
     * 触发参数
     */
    get para(): number {
        return this.data[3];
    }

    /**
     * 对象
     */
    get target(): EnumCombatDialogueGroupTarget {
        return this.data[4];
    }

    /**
     * 文本组id
     */
    get textGroupId(): number {
        return this.data[5];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
