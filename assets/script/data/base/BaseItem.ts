/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 道具类型
 */
export enum EnumItemType {
    /**
     * 货币
     */
    Currency = 1,
    /**
     * 资源
     */
    Material = 2,
    /**
     * 伙伴
     */
    Archer = 3,
    /**
     * 其他道具
     */
    Others = 5,
    /**
     * 角色
     */
    GamerFashion = 6,
    /**
     * 背饰
     */
    GamerWing = 7,
    /**
     * 神器
     */
    GamerWeapon = 8,
    /**
     * 战车
     */
    GamerTank = 9,
    /**
     * boss战劵
     */
    BossCoupons = 10,
    /**
     * 记忆石
     */
    MemoryStone = 11,
    /**
     * 宝箱
     */
    Box = 12,
    /**
     * 树枝（随机buff）
     */
    RandomBuff = 13,
    /**
     * 头像
     */
    HeadSculpture = 25,
    /**
     * 称号
     */
    Title = 26,
    /**
     * 旗帜
     */
    Flag = 31,
    /**
     * 头像框
     */
    AvatarFrame = 32,
    /**
     * 人物形象
     */
    Image = 33,
    /**
     * 改名卡
     */
    RenameCard = 34,
    /**
     * 宠物
     */
    Pet = 35,
    /**
     * 挖矿工具
     */
    MiningPror = 36,
    /**
     * 炮台
     */
    Battery = 37,
    /**
     * 主角装备
     */
    LeadEquipment = 38,
    /**
     * 拱门
     */
    ParkGate = 41,
    /**
     * 车位
     */
    ParkStall = 42,
    /**
     * 栅栏
     */
    ParkFence = 43,
    /**
     * 路灯
     */
    ParkStreetLamp = 44,
    /**
     * 特殊装备
     */
    SpecialEquipment = 45,
    /**
     * 魔法
     */
    Magic = 46,
    /**
     * 宠物蛋
     */
    PetEgg = 47,
    /**
     * 锻造台
     */
    ForgeSkin = 48,
    /**
     * 万宝箱
     */
    BoxSystem = 49,
    /**
     * 藏品
     */
    Collection = 50,
    /**
     * 特殊箭矢
     */
    SpecialArrows = 51,
    /**
     * 声望包
     */
    ReputationPackage = 52,
}
/**
 * 限时类型
 */
export enum EnumItemTimeLimitType {
    /**
     * 不限时
     */
    NoneLimit = 1,
    /**
     * 活动限时
     */
    ActivityLimit = 2,
    /**
     * 道具限时
     */
    ItemLimit = 3,
}
/**
 * 道具品质
 */
export enum EnumItemQuality {
    /**
     * 白色
     */
    White = 1,
    /**
     * 绿色
     */
    Green = 2,
    /**
     * 蓝色
     */
    Blue = 3,
    /**
     * 紫色
     */
    Purple = 4,
    /**
     * 橙色
     */
    Orange = 5,
    /**
     * 红色
     */
    Red = 6,
    /**
     * 幻色一
     */
    CyanOne = 7,
    /**
     * 装备品质1
     */
    EquipWhite1 = 101,
    /**
     * 装备品质2
     */
    EquipWhite2 = 102,
    /**
     * 装备品质3
     */
    EquipWhite3 = 103,
    /**
     * 装备品质4
     */
    EquipWhite4 = 104,
    /**
     * 装备品质5
     */
    EquipWhite5 = 105,
    /**
     * 装备品质6
     */
    EquipWhite6 = 106,
    /**
     * 装备品质7
     */
    EquipWhite7 = 107,
    /**
     * 装备品质8
     */
    EquipWhite8 = 108,
    /**
     * 装备品质9
     */
    EquipWhite9 = 109,
    /**
     * 装备品质10
     */
    EquipWhite10 = 110,
    /**
     * 装备品质11
     */
    EquipWhite11 = 111,
    /**
     * 装备品质12
     */
    EquipWhite12 = 112,
    /**
     * 装备品质13
     */
    EquipWhite13 = 113,
    /**
     * 装备品质14
     */
    EquipWhite14 = 114,
    /**
     * 装备品质15
     */
    EquipWhite15 = 115,
    /**
     * 装备品质16
     */
    EquipWhite16 = 116,
    /**
     * 装备品质17
     */
    EquipWhite17 = 117,
    /**
     * 装备品质18
     */
    EquipWhite18 = 118,
    /**
     * 装备品质19
     */
    EquipWhite19 = 119,
    /**
     * 装备品质20
     */
    EquipWhite20 = 120,
    /**
     * 装备品质21
     */
    EquipWhite21 = 121,
    /**
     * 装备品质22
     */
    EquipWhite22 = 122,
    /**
     * 装备品质23
     */
    EquipWhite23 = 123,
    /**
     * 装备品质24
     */
    EquipWhite24 = 124,
}
/**
 * 背包分页
 */
export enum EnumItemTag {
    /**
     * 不显示背包
     */
    NoneShow = 1,
    /**
     * 资源
     */
    Core = 2,
    /**
     * 材料
     */
    Consumable = 3,
}
/**
 * 效果类型
 */
export enum EnumItemEffectType {
    /**
     * 无效果
     */
    NoneType = 1,
    /**
     * 时间效果
     */
    TimeType = 2,
    /**
     * 开启功能效果
     */
    EnableFunction = 3,
    /**
     * 属性加成效果
     */
    AttributeBonus = 4,
    /**
     * 公会经验
     */
    UnionEXP = 5,
    /**
     * 公会活跃
     */
    UnionActive = 6,
    /**
     * 个人贡献
     */
    PersonalContribution = 7,
    /**
     * 装备属性
     */
    EquipAttribute = 8,
    /**
     * 伙伴
     */
    ArcherType = 11,
    /**
     * 随机宝箱
     */
    RandomBox = 12,
    /**
     * 宠物
     */
    PetType = 13,
    /**
     * 主角
     */
    LeadType = 14,
    /**
     * 炮台
     */
    BatteryType = 15,
    /**
     * 战车
     */
    TankType = 16,
    /**
     * 自选宝箱
     */
    DiyBox = 17,
    /**
     * 主角装备
     */
    LeadEquipment = 18,
    /**
     * 家园币
     */
    ParkGold = 19,
    /**
     * 停车场经验
     */
    ParkExp = 20,
    /**
     * 特殊装备
     */
    SpecialEquipmentype = 23,
    /**
     * 圣装
     */
    LeadSkinType = 24,
    /**
     * 魔法
     */
    MagicType = 25,
    /**
     * 魔法碎片
     */
    MagicShardType = 26,
    /**
     * 背饰
     */
    WingType = 27,
    /**
     * 神器
     */
    WeaponType = 28,
    /**
     * 恢复装备疲劳值
     */
    RestoreEquipment = 29,
    /**
     * 锻造台
     */
    ForgeType = 30,
    /**
     * 藏品升级材料
     */
    CollectionUpgrade = 31,
    /**
     * 藏品
     */
    CollectionType = 32,
    /**
     * 宠物万能碎片
     */
    PetFragment = 33,
    /**
     * 声望包
     */
    ReputationPackageType = 34,
}
/**
 * 跳转方式
 */
export enum EnumItemJumpWay {
    /**
     * 无跳转方式
     */
    NoneJump = 1,
    /**
     * 弹窗式跳转
     */
    PopupJump = 2,
    /**
     * 直接跳转
     */
    DirectJump = 3,
}
/**
 * 大数类型
 */
export enum EnumItemDataType {
    /**
     * 非大数道具
     */
    NormalData = 0,
    /**
     * 大数道具
     */
    BigData = 1,
}
/**
 * 获得时是否显示
 */
export enum EnumItemShowType {
    /**
     * 获得时显示
     */
    GetShow = 1,
    /**
     * 获得时不显示
     */
    NotGetShow = 2,
}
/**
 * 是否计数
 */
export enum EnumItemCountJudgment {
    /**
     * 计数
     */
    Count = 1,
    /**
     * 不计数
     */
    NotCount = 0,
}
/**
 * 道具表
 */
export default class BaseItem {
    // data
    private readonly data: any[];

    /**
     * 道具ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 道具名称
     */
    get name(): string {
        return this.data[1];
    }

    /**
     * 道具说明
     */
    get desc(): string {
        return this.data[2];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[3];
    }

    /**
     * 大数类型
     */
    get dataType(): EnumItemDataType {
        return this.data[4];
    }

    /**
     * 道具类型
     */
    get type(): EnumItemType {
        return this.data[5];
    }

    /**
     * 道具品质
     */
    get quality(): EnumItemQuality {
        return this.data[6];
    }

    /**
     * 效果类型
     */
    get effectType(): EnumItemEffectType {
        return this.data[7];
    }

    /**
     * 效果参数
     */
    get effectPara(): number[] {
        return this.data[8];
    }

    /**
     * 使用效果
     */
    get itemEffect(): any[] {
        return this.data[9];
    }

    /**
     * 背包分页
     */
    get tag(): EnumItemTag {
        return this.data[10];
    }

    /**
     * 排序
     */
    get priority(): number {
        return this.data[11];
    }

    /**
     * 背包内出售价格
     */
    get price(): number {
        return this.data[12];
    }

    /**
     * 限时类型
     */
    get timeLimitType(): EnumItemTimeLimitType {
        return this.data[13];
    }

    /**
     * 限时参数
     */
    get timeLimitValue(): number {
        return this.data[14];
    }

    /**
     * 是否计数
     */
    get countJudgment(): EnumItemCountJudgment {
        return this.data[15];
    }

    /**
     * 跳转方式
     */
    get jumpWay(): EnumItemJumpWay {
        return this.data[16];
    }

    /**
     * 跳转ID
     */
    get jumpID(): number[] {
        return this.data[17];
    }

    /**
     * 获得时是否显示
     */
    get showType(): EnumItemShowType {
        return this.data[18];
    }

    /**
     * 获得排行榜id
     */
    get gotRankId(): number {
        return this.data[19];
    }

    /**
     * 排行榜id
     */
    get rankId(): number[] {
        return this.data[20];
    }

    /**
     * 解锁奖励
     */
    get reward(): any[] {
        return this.data[21];
    }

    /**
     * 广播id
     */
    get chatTextId(): number {
        return this.data[22];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
