/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 副本类型
 */
export enum EnumDungeonType {
    /**
     * 不竭之境
     */
    DungeonBoss = 1,
    /**
     * 米德云端
     */
    DungeonCloud = 2,
    /**
     * 怪盗积德
     */
    DungeonThief = 3,
    /**
     * 天虹之塔
     */
    DungeonTower = 4,
    /**
     * 丰裕宝匣
     */
    DungeonBox = 5,
    /**
     * 洞洞乐
     */
    DungeonCollection = 6,
}
/**
 * 副本表
 */
export default class BaseDungeon {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 副本类型
     */
    get type(): EnumDungeonType {
        return this.data[1];
    }

    /**
     * 是否显示
     */
    get show(): number {
        return this.data[2];
    }

    /**
     * 名称
     */
    get name(): string {
        return this.data[3];
    }

    /**
     * 名称资源
     */
    get nameRes(): string {
        return this.data[4];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[5];
    }

    /**
     * 奖励预览
     */
    get rewardPreview(): number[] {
        return this.data[6];
    }

    /**
     * 入场消耗道具展示
     */
    get consumePreview(): number[] {
        return this.data[7];
    }

    /**
     * 入场消耗道具
     */
    get cost(): any[] {
        return this.data[8];
    }

    /**
     * 恢复上限及恢复时间
     */
    get recoveryTime(): number[] {
        return this.data[9];
    }

    /**
     * 广告ID
     */
    get advertisementID(): number {
        return this.data[10];
    }

    /**
     * 系统解锁id
     */
    get switchID(): number {
        return this.data[11];
    }

    /**
     * 规则id
     */
    get rule(): number {
        return this.data[12];
    }

    /**
     * 排序
     */
    get order(): number {
        return this.data[13];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
