/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 领取类型
 */
export enum EnumPackReceiveType {
    /**
     * 购买即可领取
     */
    Default = 1,
    /**
     * 购买且等级达到才可领取
     */
    LevelReachReceive = 2,
    /**
     * 购买且boss副本闯关达到才可领取
     */
    BOSSReachReceive = 3,
    /**
     * 购买且主线冒险通关才可领取
     */
    AdventureReachReceive = 4,
    /**
     * 购买且挖矿深度达到才可领取
     */
    MiningReachReceipt = 5,
    /**
     * 购买且可多日领取
     */
    SomeDaysReceive = 7,
    /**
     * 购买全部指定礼包后才可领取
     */
    GiveGift = 8,
    /**
     * 购买且消耗钻石XX次才可领取
     */
    ConsumingDiamondsReceive = 9,
    /**
     * 王权等级ID达到X
     */
    PowerLevel = 13,
    /**
     * 宠物合成X次
     */
    PetSynthesis = 14,
    /**
     * 购买后第X天领取
     */
    PurchaseDayX = 15,
    /**
     * 魔龙之巢消耗x点体力
     */
    CollectGameCostX = 16,
    /**
     * 消耗锻造石x可领取
     */
    ConsumingForgedStoneX = 17,
    /**
     * 每日任务活跃度达到x可领取
     */
    ActivityLevelReachesX = 18,
}
/**
 * 显示条件类型
 */
export enum EnumPackShowType {
    /**
     * 默认显示
     */
    DefaultDisplay = 1,
    /**
     * 累充金额达到X后显示
     */
    ChargeDisplay = 2,
    /**
     * 角色注册时间达到X日后显示
     */
    RegisterDisplay = 3,
    /**
     * 某个系统解锁后显示
     */
    SystemDisplay = 4,
}
/**
 * 礼包
 */
export default class BasePack {
    // data
    private readonly data: any[];

    /**
     * 礼包id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 礼包描述
     */
    get desc(): string {
        return this.data[1];
    }

    /**
     * 礼包营销语描述
     */
    get res(): string {
        return this.data[2];
    }

    /**
     * 显示条件类型
     */
    get showType(): EnumPackShowType {
        return this.data[3];
    }

    /**
     * 显示条件参数
     */
    get showPara(): number {
        return this.data[4];
    }

    /**
     * 领取类型
     */
    get receiveType(): EnumPackReceiveType {
        return this.data[5];
    }

    /**
     * 领取参数
     */
    get receivePara(): number[] {
        return this.data[6];
    }

    /**
     * 基金条件文本
     */
    get conditionText(): string {
        return this.data[7];
    }

    /**
     * 免费奖励
     */
    get freeReward(): any[] {
        return this.data[8];
    }

    /**
     * 台词文本
     */
    get linelText(): string {
        return this.data[9];
    }

    /**
     * 标签文本
     */
    get labelText(): string {
        return this.data[10];
    }

    /**
     * 自选奖励
     */
    get selectedReward(): any[] {
        return this.data[11];
    }

    /**
     * 购买奖励
     */
    get reward(): any[] {
        return this.data[12];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
