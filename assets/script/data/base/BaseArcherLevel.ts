/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 伙伴品质
 */
export enum EnumArcherLevelQuality {
    /**
     * 普通
     */
    Quality1 = 1,
    /**
     * 精良
     */
    Quality2 = 2,
    /**
     * 优秀
     */
    Quality3 = 3,
    /**
     * 稀有
     */
    Quality4 = 4,
    /**
     * 史诗
     */
    Quality5 = 5,
    /**
     * 传说
     */
    Quality6 = 6,
}
/**
 * 伙伴升级表
 */
export default class BaseArcherLevel {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 伙伴品质
     */
    get quality(): EnumArcherLevelQuality {
        return this.data[1];
    }

    /**
     * 伙伴等级
     */
    get level(): number {
        return this.data[2];
    }

    /**
     * 属性值
     */
    get attribute(): any[] {
        return this.data[3];
    }

    /**
     * 升级消耗
     */
    get upgradeCost(): any[] {
        return this.data[4];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
