/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 参数
 */
export enum EnumActivityUniversalPara {
    /**
     * 开服庆典基金展示挡位
     */
    OpeninCeremonyFundDisplayGear = 10000,
    /**
     * 王权基金展示挡位
     */
    RoyalFundDisplayGear = 10001,
    /**
     * 冒险基金展示挡位
     */
    AdventureFundDisplayGear = 10002,
    /**
     * 宠物基金展示挡位
     */
    PetFundDisplayGear = 10003,
    /**
     * 挖矿基金展示挡位
     */
    MiningFundDisplayGear = 10004,
    /**
     * 免费奖励
     */
    TrialFreeReward = 10005,
    /**
     * 挑战消耗道具
     */
    TrialCostItem = 10006,
    /**
     * 活动大奖展示
     */
    TrialShowReward = 10008,
    /**
     * 战车跑酷玩法的战车ID
     */
    ActivityTankId = 10009,
    /**
     * 藏品基金展示挡位
     */
    CollectFundDisplayGear = 10010,
    /**
     * 游历笔记进入消耗
     */
    PhysicalConsumption = 20001,
    /**
     * 游历笔记倍数选择
     */
    MultipleSelection = 20002,
    /**
     * 游历笔记自动解锁条件，填：进行玩法次数
     */
    AutomaticUnlocking = 20003,
    /**
     * 游历笔记行动倍数解锁条件，填：进行玩法次数
     */
    MultipleSelectionUnlocking = 20004,
    /**
     * 主角资源ID
     */
    LeadRes = 20005,
    /**
     * [[游历组ID，该游历组权重][游历组ID，该游历组权重]...]
     */
    TravelGroup = 20006,
    /**
     * 大吉累计上限
     */
    LimitLuck4 = 20007,
    /**
     * 小吉累计上限
     */
    LimitLuck3 = 20008,
    /**
     * 赠送体力道具
     */
    GetPadkos = 20009,
}
/**
 * 活动万用表
 */
export default class BaseActivityUniversal {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 参数
     */
    get para(): EnumActivityUniversalPara {
        return this.data[1];
    }

    /**
     * 值
     */
    get value(): string {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
