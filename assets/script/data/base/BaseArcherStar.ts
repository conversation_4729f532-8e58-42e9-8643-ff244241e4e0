/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 伙伴品质
 */
export enum EnumArcherStarQuality {
    /**
     * 普通
     */
    Quality1 = 1,
    /**
     * 精良
     */
    Quality2 = 2,
    /**
     * 优秀
     */
    Quality3 = 3,
    /**
     * 稀有
     */
    Quality4 = 4,
    /**
     * 史诗
     */
    Quality5 = 5,
    /**
     * 传说
     */
    Quality6 = 6,
}
/**
 * 伙伴升星表
 */
export default class BaseArcherStar {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 伙伴品质
     */
    get quality(): EnumArcherStarQuality {
        return this.data[1];
    }

    /**
     * 伙伴星级
     */
    get star(): number {
        return this.data[2];
    }

    /**
     * 限制等级
     */
    get levelLimit(): number {
        return this.data[3];
    }

    /**
     * 属性值
     */
    get attribute(): any[] {
        return this.data[4];
    }

    /**
     * 碎片消耗数量
     */
    get cost(): number {
        return this.data[5];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
