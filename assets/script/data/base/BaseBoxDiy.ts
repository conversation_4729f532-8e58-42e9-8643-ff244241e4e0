/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 批量开启
 */
export enum EnumBoxDiyBatchOpen {
    /**
     * 可批量开启
     */
    CanBatchOpen = 1,
    /**
     * 不可批量开启
     */
    NoneBatchOpen = 2,
}
/**
 * 宝箱自选
 */
export default class BaseBoxDiy {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 名称
     */
    get desc(): string {
        return this.data[1];
    }

    /**
     * 批量开启
     */
    get batchOpen(): EnumBoxDiyBatchOpen {
        return this.data[2];
    }

    /**
     * 自选奖励
     */
    get reward(): any[] {
        return this.data[3];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
