/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 活动签到
 */
export default class BaseActivityLogin {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 活动ID
     */
    get activityId(): number {
        return this.data[1];
    }

    /**
     * 天数
     */
    get days(): number {
        return this.data[2];
    }

    /**
     * 奖励
     */
    get reward(): any[] {
        return this.data[3];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
