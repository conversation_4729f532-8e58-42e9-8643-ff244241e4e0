/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 类型
 */
export enum EnumEventType {
    /**
     * 游历奖励事件
     */
    RewardEvent = 1,
    /**
     * 游历好感度事件
     */
    Mediocre = 2,
    /**
     * 游历相识事件
     */
    Pretty = 3,
    /**
     * 结婚台词事件
     */
    WeddingLines = 4,
}
/**
 * 知己事件表
 */
export default class BaseEvent {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 类型
     */
    get type(): EnumEventType {
        return this.data[1];
    }

    /**
     * 知己id
     */
    get princessId(): number {
        return this.data[2];
    }

    /**
     * 组
     */
    get group(): number {
        return this.data[3];
    }

    /**
     * 奖励
     */
    get reward(): any[] {
        return this.data[4];
    }

    /**
     * 对话文本
     */
    get text(): string {
        return this.data[5];
    }

    /**
     * 权重
     */
    get weight(): number {
        return this.data[6];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
