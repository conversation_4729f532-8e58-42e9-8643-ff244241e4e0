/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 消耗类型
 */
export enum EnumActivityGameCostType {
    /**
     * 无消耗
     */
    FreeTickets = 1,
    /**
     * 开始消耗
     */
    BeginTickets = 2,
    /**
     * 结束消耗
     */
    EndTickets = 3,
}
/**
 * undefined
 */
export enum EnumActivityGameEffectType {
    /**
     * 金币
     */
    Gold = 1,
    /**
     * 石头
     */
    Barrier = 2,
    /**
     * 怪物
     */
    BarrierMove = 3,
    /**
     * 加速
     */
    SpeedBuff = 4,
    /**
     * 护盾
     */
    ShieldBuff = 5,
    /**
     * 磁铁
     */
    MagnetBuff = 6,
    /**
     * 坚果
     */
    BloodBuff = 7,
}
/**
 * 活动类型
 */
export enum EnumActivityGameActivityType {
    /**
     * 关卡奖励活动
     */
    ActivityType1 = 1,
    /**
     * 无关卡奖励活动
     */
    ActivityType2 = 2,
    /**
     * 任务进度奖励（参数填任务表ID）
     */
    ActivityType3 = 3,
}
/**
 * 活动玩法
 */
export default class BaseActivityGame {
    // data
    private readonly data: any[];

    /**
     * 活动ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 名称
     */
    get name(): string {
        return this.data[1];
    }

    /**
     * 活动类型
     */
    get activityType(): EnumActivityGameActivityType {
        return this.data[2];
    }

    /**
     * 参考
     */
    get typePara(): number[] {
        return this.data[3];
    }

    /**
     * 关卡地图
     */
    get gameLevel(): number[] {
        return this.data[4];
    }

    /**
     * 参数
     */
    get para(): any {
        return this.data[5];
    }

    /**
     * 关卡奖励
     */
    get rewardPara(): any {
        return this.data[6];
    }

    /**
     * 每天解锁关卡
     */
    get unlockNum(): number {
        return this.data[7];
    }

    /**
     * 消耗类型
     */
    get costType(): EnumActivityGameCostType {
        return this.data[8];
    }

    /**
     * 参与消耗
     */
    get itemCost(): any[] {
        return this.data[9];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
