/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 属性类型
 */
export enum EnumAttributeNature {
    /**
     * 主要属性
     */
    MainAttribute = 1,
    /**
     * 二级属性
     */
    SecondAttribute = 2,
    /**
     * 最终属性
     */
    FinalAttribute = 3,
}
/**
 * 类型
 */
export enum EnumAttributeType {
    /**
     * 总战斗力
     */
    OverallCombatStrength = 1001,
    /**
     * 赋值战力
     */
    EmpowerCombatStrength = 1002,
    /**
     * 最终血量
     */
    FinalHp = 1003,
    /**
     * 最终攻击
     */
    FinalAtk = 1004,
    /**
     * 最终防御
     */
    FinalDef = 1005,
    /**
     * 血量
     */
    Hp = 1006,
    /**
     * 攻击
     */
    Atk = 1007,
    /**
     * 防御
     */
    Def = 1008,
    /**
     * 会心
     */
    Crit = 1009,
    /**
     * 会心抗性
     */
    CritRes = 1010,
    /**
     * 会心伤害
     */
    CritDmg = 1011,
    /**
     * 会心伤害抵挡
     */
    CritDmgBlock = 1012,
    /**
     * 闪避
     */
    Eva = 1013,
    /**
     * 忽闪
     */
    EvaIgnore = 1014,
    /**
     * 技能急速
     */
    SkillHaste = 1015,
    /**
     * 暴击
     */
    CritHit = 1016,
    /**
     * 暴击抗性
     */
    CritHitRes = 1017,
    /**
     * 暴击伤害
     */
    CritHitDmg = 1018,
    /**
     * 暴击伤害抵挡
     */
    CritHitDmgBlock = 1019,
    /**
     * 破防
     */
    BreakDef = 1020,
    /**
     * 格挡
     */
    Block = 1021,
    /**
     * 专属技能加成
     */
    ESkillB = 1022,
    /**
     * 专属技能减免
     */
    ESkillR = 1023,
    /**
     * 专属技能固定加成
     */
    ESkillFB = 1024,
    /**
     * 主动技能加成
     */
    ASkillB = 1025,
    /**
     * 主动技能减免
     */
    ASkillR = 1026,
    /**
     * 主动技能固定加成
     */
    ASkillFB = 1027,
    /**
     * 被动技能加成
     */
    PSkillB = 1028,
    /**
     * 被动技能减免
     */
    PSkillR = 1029,
    /**
     * 被动技能固定加成
     */
    PSkillFB = 1030,
    /**
     * 普攻伤害加成
     */
    AtkB = 1031,
    /**
     * 普攻伤害减免
     */
    AtkR = 1032,
    /**
     * 普攻伤害固定加成
     */
    AtkFB = 1033,
    /**
     * 伙伴伤害加成
     */
    ArcherSkillB = 1034,
    /**
     * 伙伴伤害减免
     */
    ArcherSkillR = 1035,
    /**
     * 伙伴伤害固定加成
     */
    ArcherSkillFB = 1036,
    /**
     * 宠物伤害加成
     */
    PetSkillB = 1037,
    /**
     * 宠物伤害减免
     */
    PetSkillR = 1038,
    /**
     * 宠物伤害固定加成
     */
    PetSkillFB = 1039,
    /**
     * 全局技能加成
     */
    GlobalSkillB = 1040,
    /**
     * 全局技能减免
     */
    GlobalSkillR = 1041,
    /**
     * 全局技能固定加成
     */
    GlobalSkillDFB = 1042,
    /**
     * 怪物伤害加成
     */
    MonsterDB = 1043,
    /**
     * 怪物伤害减免
     */
    MonsterDR = 1044,
    /**
     * 全局伤害加成
     */
    GlobalDB = 1045,
    /**
     * 全局伤害减免
     */
    GlobalDR = 1046,
    /**
     * 全局伤害固定加成
     */
    GlobalDFB = 1047,
    /**
     * 全局攻击百分比
     */
    GlobalAtkPer = 1048,
    /**
     * 全局防御百分比
     */
    GlobalDefPer = 1049,
    /**
     * 全局血量百分比
     */
    GlobalHpPer = 1050,
    /**
     * 王权攻击百分比
     */
    PowerAtkPer = 1051,
    /**
     * 王权防御百分比
     */
    PowerDefPer = 1052,
    /**
     * 王权血量百分比
     */
    PowerHpPer = 1053,
    /**
     * 伙伴普攻急速
     */
    ArcherAtkHaste = 1054,
    /**
     * 火系伤害加成
     */
    FireDB = 1055,
    /**
     * 火系伤害减免
     */
    FireDR = 1056,
    /**
     * 雷系伤害加成
     */
    ThunderDB = 1057,
    /**
     * 雷系伤害减免
     */
    ThunderDR = 1058,
    /**
     * 风系伤害加成
     */
    WindDB = 1059,
    /**
     * 风系伤害减免
     */
    WindDR = 1060,
    /**
     * 物理伤害加成
     */
    PhysicsDB = 1061,
    /**
     * 物理伤害减免
     */
    PhysicsDR = 1062,
    /**
     * 真实伤害加成
     */
    RealDB = 1063,
    /**
     * 真实伤害减免
     */
    RealDR = 1064,
    /**
     * 赋值战力百分比
     */
    EmpowerCombatStrengthPer = 1065,
    /**
     * 天命
     */
    Fate = 1066,
    /**
     * 天命攻击百分比
     */
    FateAtkPer = 1067,
    /**
     * 天命防御百分比
     */
    FateDefPer = 1068,
    /**
     * 天命血量百分比
     */
    FateHpPer = 1069,
    /**
     * PVP增伤
     */
    PVPDB = 1070,
    /**
     * PVP减伤
     */
    PVPDR = 1071,
}
/**
 * 数值类型
 */
export enum EnumAttributeFigureType {
    /**
     * 固定值
     */
    Regular = 1,
    /**
     * 百分比
     */
    Percentage = 2,
}
/**
 * 评分类型
 */
export enum EnumAttributeScoreType {
    /**
     * 无固定值
     */
    NoScore = 1,
    /**
     * 固定值
     */
    FixedValue = 2,
    /**
     * 百分比
     */
    Percentage = 3,
}
/**
 * 属性表
 */
export default class BaseAttribute {
    // data
    private readonly data: any[];

    /**
     * 属性ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[1];
    }

    /**
     * 类型
     */
    get type(): EnumAttributeType {
        return this.data[2];
    }

    /**
     * 名称
     */
    get name(): string {
        return this.data[3];
    }

    /**
     * 描述
     */
    get desc(): string {
        return this.data[4];
    }

    /**
     * 属性类型
     */
    get nature(): EnumAttributeNature {
        return this.data[5];
    }

    /**
     * 数值类型
     */
    get figureType(): EnumAttributeFigureType {
        return this.data[6];
    }

    /**
     * 小数位数
     */
    get decimal(): number {
        return this.data[7];
    }

    /**
     * 战斗力
     */
    get combat(): number {
        return this.data[8];
    }

    /**
     * 系数初值
     */
    get factor(): number {
        return this.data[9];
    }

    /**
     * 初始值
     */
    get initial(): number {
        return this.data[10];
    }

    /**
     * 参数
     */
    get para(): number[] {
        return this.data[11];
    }

    /**
     * 是否显示
     */
    get show(): number {
        return this.data[12];
    }

    /**
     * 显示排序
     */
    get sort(): number {
        return this.data[13];
    }

    /**
     * 评分类型
     */
    get scoreType(): EnumAttributeScoreType {
        return this.data[14];
    }

    /**
     * 评分
     */
    get scoreValue(): number {
        return this.data[15];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
