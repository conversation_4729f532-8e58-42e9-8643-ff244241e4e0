/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 开启类型
 */
export enum EnumBoxRandomType {
    /**
     * 不自动开启
     */
    NoneAutoOpne = 1,
    /**
     * 自动开启
     */
    AutoOpne = 2,
}
/**
 * 箱子随机
 */
export default class BaseBoxRandom {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 开启类型
     */
    get type(): EnumBoxRandomType {
        return this.data[1];
    }

    /**
     * 奖励预览
     */
    get rewardPreview(): any[] {
        return this.data[2];
    }

    /**
     * 固定奖励
     */
    get regularReward(): any[] {
        return this.data[3];
    }

    /**
     * 随机奖励
     */
    get randomReward(): number {
        return this.data[4];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
