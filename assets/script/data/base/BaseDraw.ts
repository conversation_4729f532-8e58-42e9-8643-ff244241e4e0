/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 类型
 */
export enum EnumDrawType {
    /**
     * 伙伴抽卡
     */
    PartnerDrawCard = 1,
    /**
     * 宠物抽卡
     */
    PetDrawCard = 2,
    /**
     * 时空召唤
     */
    TimeDrawCard = 3,
    /**
     * 战车夺宝
     */
    TankDrawCard = 4,
    /**
     * 魔法抽奖
     */
    MagicDrawCard = 5,
    /**
     * 背饰抽奖
     */
    WingDrawCard = 6,
    /**
     * 神器抽奖
     */
    WeaponDrawCard = 7,
    /**
     * 藏品抽奖
     */
    CollectionDrawCard = 8,
}
/**
 * 抽奖表
 */
export default class BaseDraw {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 名称
     */
    get name(): string {
        return this.data[1];
    }

    /**
     * 活动ID
     */
    get activityId(): number {
        return this.data[2];
    }

    /**
     * 排行榜ID
     */
    get rankId(): number[] {
        return this.data[3];
    }

    /**
     * 活动图片资源
     */
    get res(): string {
        return this.data[4];
    }

    /**
     * 概率文本
     */
    get probabilityDesc(): any[] {
        return this.data[5];
    }

    /**
     * 奖励组
     */
    get group(): any[] {
        return this.data[6];
    }

    /**
     * 类型
     */
    get type(): EnumDrawType {
        return this.data[7];
    }

    /**
     * 广告ID
     */
    get advertisementID(): number {
        return this.data[8];
    }

    /**
     * 单抽消耗
     */
    get oneDrawCost1(): any[] {
        return this.data[9];
    }

    /**
     * 十连消耗
     */
    get tenDrawCost1(): any[] {
        return this.data[10];
    }

    /**
     * 单抽消耗2
     */
    get oneDrawCost2(): any[] {
        return this.data[11];
    }

    /**
     * 十连消耗2
     */
    get tenDrawCost2(): any[] {
        return this.data[12];
    }

    /**
     * 首次必出
     */
    get firstReward(): any[] {
        return this.data[13];
    }

    /**
     * 首次十连必出
     */
    get firstTenReward(): number[] {
        return this.data[14];
    }

    /**
     * 奖励规则
     */
    get rewardRule(): string {
        return this.data[15];
    }

    /**
     * 奖励预览
     */
    get rewardShow(): any[] {
        return this.data[16];
    }

    /**
     * 活动大奖展示
     */
    get show(): number[] {
        return this.data[17];
    }

    /**
     * 活动大奖恭喜获得
     */
    get acquireShow(): number[] {
        return this.data[18];
    }

    /**
     * 宣传文本
     */
    get publicityText(): any[] {
        return this.data[19];
    }

    /**
     * 试用主角
     */
    get tryLead(): number {
        return this.data[20];
    }

    /**
     * 展示组
     */
    get groupShow(): number[] {
        return this.data[21];
    }

    /**
     * 每日免费次数
     */
    get dailyFree(): number {
        return this.data[22];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
