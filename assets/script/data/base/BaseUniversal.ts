/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 参数
 */
export enum EnumUniversalPara {
    /**
     * 初始道具
     */
    InitialQuantity = 1,
    /**
     * 点券道具id
     */
    TicketItemId = 3,
    /**
     * 应邀奖励
     */
    InvitedReward = 4,
    /**
     * 合成区域数据同步时间（单位：秒）
     */
    MergeSyncTime = 5,
    /**
     * 装备弹窗界面名
     */
    EquipmentPopup = 6,
    /**
     * 红点提示时间，开服7天内
     */
    GiftCenterRedTip = 61,
    /**
     * 推送弹窗入口存在时间
     */
    PushPopupTime = 65,
    /**
     * 好友系统(好友上限，赠礼上限，赠礼道具id，每次赠礼道具数量，收礼上限)
     */
    FriendSystem = 111,
    /**
     * 每个玩家每日最多可领取的红包数量上限
     */
    RedPacket = 121,
    /**
     * 公会公告正则过滤
     */
    GuildFilter = 131,
    /**
     * 更改名字消耗
     */
    ChangeName = 203,
    /**
     * 副本失败提升引导
     */
    FailureImprovementGuidance = 206,
    /**
     * 科技树观看广告缩短研究时间（单位：秒）
     */
    TechnologyTreeAccelerate = 400,
    /**
     * 装备背包
     */
    LeadEquipmentBag = 603,
    /**
     * 绑定奖励
     */
    BindRewards = 615,
    /**
     * 王权升级属性初始值展示
     */
    PowerLevelAttributeShow = 701,
    /**
     * 王权可一键提升的阶数
     */
    PowerBatchLevel = 702,
    /**
     * 王权之巅膜拜奖励
     */
    PowerWorship = 703,
    /**
     * 伙伴系统初始弓箭手
     */
    ArcherInitial = 801,
    /**
     * 初始化战车id
     */
    TankInitialize = 802,
    /**
     * 初始化神器id
     */
    WeaponInitialize = 803,
    /**
     * 初始化背饰id
     */
    WingInitial = 804,
    /**
     * 初始选角ID
     */
    InitialCastingID = 805,
    /**
     * 魔法系统碎片转化
     */
    MagicChange = 901,
    /**
     * 每掉落1件装备消耗x点体力
     */
    EquipmentCost = 1001,
    /**
     * 击杀第x只小怪，掉落数量，掉落装备id，掉落装备等级
     */
    EquipmentDrops = 2001,
    /**
     * 第x关boss特殊掉落装备
     */
    BossEquipmentDrops = 2002,
    /**
     * 主线掉落时间
     */
    MainDropsTime = 2003,
    /**
     * 疲劳值恢复时间（秒）,恢复疲劳值点数
     */
    EquipmentDropsRestore = 3001,
    /**
     * 广告补充疲劳值点数
     */
    ADEquipmentDropsRestore = 3002,
    /**
     * 道具补充疲劳值点数
     */
    ItemEquipmentDropsRestore = 3003,
    /**
     * 开启宝箱数量上限
     */
    OpenUpperLimit = 11002,
    /**
     * 锻造台观看广告缩短升级时间（单位：分）
     */
    ForgeBedAccelerate = 12001,
    /**
     * 锻造台加速道具ID，锻造台加速券减少时间（单位：分）
     */
    ForgeBedAccelerateItem = 12002,
    /**
     * 锻造台初始皮肤
     */
    ForgeBedSkin = 12003,
    /**
     * 【锻造经验ID，锻造经验数量】
     */
    ForgeExpChange = 12004,
    /**
     * 锻造台可一键升级的阶数
     */
    ForgeBatchLevel = 12005,
    /**
     * 一键升星的数量
     */
    BatchStar = 13001,
    /**
     * 藏品万能碎片
     */
    CollectionUniversalFragments = 13002,
    /**
     * 藏品升级材料转化
     */
    CollectionItemChange = 13003,
    /**
     * 一键升阶的数量
     */
    MagicBatchClass = 13004,
    /**
     * 万宝箱积分进度一键领取的数量（同时可领取数量≥参数值）
     */
    BoxSystemConvenient = 13005,
    /**
     * 湮灭
     */
    Annihilate = 14001,
    /**
     * 主线通过x个关卡,连续通关时间都小于y秒,跳过z关
     */
    PassesXLevels = 15001,
    /**
     * 箭矢校验：限制的箭矢等级===MAX【锻造台的箭矢等级属性+LOG(当前背包的锻造石数量,2)+参数1，参数2】
     */
    InterceptArrow = 16001,
    /**
     * 主线关卡拦截：拦截战力==M-主线关卡表_MainBarrier的resPower字段数字*参数1
     */
    InterceptMainBarrier = 16002,
    /**
     * 天虹之塔拦截：拦截战力==D-天虹之塔副本_DungeonTower的resPower字段数字*参数1
     */
    InterceptDungeonTower = 16003,
    /**
     * 怪盗基德拦截：拦截伤害=玩家的最终攻击[属性ID1004]*参数1*参数2（战斗时间）
     */
    InterceptDungeonThief = 16004,
    /**
     * 联盟副本的伤害拦截：拦截伤害=玩家的最终攻击[属性ID1004]*参数1*参数2（战斗时间）
     */
    InterceptUnionBoss = 16005,
    /**
     * PVP战斗的拦截【竞技场，停车场】：如果我方战力/敌方战力<参数1，则我方战斗必定失败
     */
    InterceptPVP = 16006,
    /**
     * 点券返利比例
     */
    CouponRebate = 17001,
    /**
     * 自动锻造
     */
    AutoForging = 18001,
}
/**
 * 万用表
 */
export default class BaseUniversal {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 参数
     */
    get para(): EnumUniversalPara {
        return this.data[1];
    }

    /**
     * 值
     */
    get value(): string {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
