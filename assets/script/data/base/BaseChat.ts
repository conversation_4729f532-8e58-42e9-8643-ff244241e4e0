/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 频道类型
 */
export enum EnumChatType {
    /**
     * 世界聊天
     */
    WorldChat = 1,
    /**
     * 公会聊天
     */
    UnionChat = 2,
    /**
     * 新闻
     */
    NewsChat = 3,
    /**
     * 无频道
     */
    NotChat = 1001,
    /**
     * 好友私聊
     */
    FriendChat = 5,
}
/**
 * 开启类型
 */
export enum EnumChatOpenType {
    /**
     * 自动开启
     */
    AutoOpen = 1,
    /**
     * 王权等级
     */
    Power = 2,
    /**
     * 完成主线任务数量
     */
    MainLine = 3,
}
/**
 * 聊天
 */
export default class BaseChat {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 频道
     */
    get channel(): string {
        return this.data[1];
    }

    /**
     * 频道类型
     */
    get type(): EnumChatType {
        return this.data[2];
    }

    /**
     * 参数
     */
    get value(): number {
        return this.data[3];
    }

    /**
     * 开启类型
     */
    get openType(): EnumChatOpenType {
        return this.data[4];
    }

    /**
     * 开启参数
     */
    get openValue(): number {
        return this.data[5];
    }

    /**
     * 频道标识
     */
    get channelSign(): string {
        return this.data[6];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
