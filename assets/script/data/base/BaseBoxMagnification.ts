/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 丰裕宝匣倍率
 */
export default class BaseBoxMagnification {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 体力范围
     */
    get physicalRange(): number[] {
        return this.data[1];
    }

    /**
     * 倍率选择
     */
    get multSel(): number[] {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
