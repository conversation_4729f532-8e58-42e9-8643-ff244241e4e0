/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 类型
 */
export enum EnumTravelingType {
    /**
     * 苍澜山
     */
    Mountain = 1,
    /**
     * 荒古城
     */
    City = 2,
    /**
     * 空中庭院
     */
    Courtyard = 3,
    /**
     * 西湖
     */
    Lake = 4,
    /**
     * 青丘
     */
    QingQiu = 5,
    /**
     * 碧海
     */
    BlueSea = 6,
}
/**
 * 无效表知己游历表
 */
export default class BaseTraveling {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 类型
     */
    get type(): EnumTravelingType {
        return this.data[1];
    }

    /**
     * 地名
     */
    get placeName(): string {
        return this.data[2];
    }

    /**
     * 随机事件
     */
    get randomEvent(): any[] {
        return this.data[3];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
