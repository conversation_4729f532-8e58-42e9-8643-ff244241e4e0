/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 条件类型
 */
export enum EnumRankConditionType {
    /**
     * 个人战力
     */
    PersonalPower = 1,
    /**
     * 箭矢等级
     */
    ArrowLevel = 2,
    /**
     * 关卡进度
     */
    LevelProgress = 3,
    /**
     * 地牢进度
     */
    DungeonProgress = 4,
    /**
     * 竞技场积分
     */
    ArenaPoint = 5,
    /**
     * 伙伴召唤竞速
     */
    DrawArcher = 6,
    /**
     * 首领战副本次数（挑战成功或扫荡）
     */
    LeaderBattle = 7,
    /**
     * 竞速关卡进度
     */
    SprintLevelProgress = 8,
    /**
     * 竞速活动-宠物召唤竞速
     */
    PetSummoning = 9,
    /**
     * 竞速活动-符文召唤竞速
     */
    RuneSummoning = 10,
    /**
     * 挖矿进度竞速
     */
    Mining = 11,
    /**
     * 战车养成竞速
     */
    TankSpeed = 12,
    /**
     * 竞速活动-炮台养成竞速
     */
    BatterySpeed = 13,
    /**
     * 钻石消耗竞速
     */
    DiamondConsumption = 14,
    /**
     * 装备副本成就
     */
    EquipmentInstanceAchievement = 15,
    /**
     * 天虹之塔
     */
    DungeonTower = 16,
    /**
     * 怪盗波比伤害榜
     */
    DungeonThief = 17,
    /**
     * 魔法召唤竞速
     */
    MagicSummoning = 101,
    /**
     * 神器养成竞速
     */
    ArtifactSpeed = 111,
    /**
     * 背饰养成竞速
     */
    WingSpeed = 121,
    /**
     * 公会首领伤害
     */
    UnionBossInjured = 18,
    /**
     * 装备养成竞速
     */
    EquipmentSpeed = 131,
    /**
     * 宠物进阶竞速
     */
    PetSpeed = 141,
    /**
     * 藏品抽奖竞速
     */
    CollectionDrawSpeed = 151,
    /**
     * 藏品养成竞速
     */
    CollectionSpeed = 161,
    /**
     * 伙伴召唤竞速（全局计数）
     */
    OverallDrawArcher = 201,
    /**
     * 魔法召唤竞速（全局计数）
     */
    OverallMagicSummoning = 202,
    /**
     * 挖矿进度竞速（全局计数）
     */
    OverallMining = 203,
    /**
     * 战车养成竞速（全局计数）
     */
    OverallTankSpeed = 204,
    /**
     * 神器养成竞速（全局计数）
     */
    OverallArtifactSpeed = 205,
    /**
     * 背饰养成竞速（全局计数）
     */
    OverallWingSpeed = 206,
    /**
     * 钻石消耗竞速（全局计数）
     */
    OverallDiamondConsumption = 207,
    /**
     * 宠物进阶竞速（全局计数）
     */
    OverallPetSpeed = 208,
    /**
     * 装备养成竞速（全局计数）
     */
    OverallEquipmentSpeed = 209,
    /**
     * 藏品抽奖竞速（全局计数）
     */
    OverallCollectionDrawSpeed = 210,
    /**
     * 藏品养成竞速（全局计数）
     */
    OverallCollectionSpeed = 211,
    /**
     * 王权阶级
     */
    PowerLevel = 19,
    /**
     * 公会总战力
     */
    UnionStrengthRank = 20,
    /**
     * 公会攻防战-星星数量
     */
    UnionDefenseRank = 1038,
}
/**
 * 排行类型
 */
export enum EnumRankType {
    /**
     * 个人排行
     */
    PersonalRank = 1,
    /**
     * 联盟排行
     */
    UnionRank = 2,
}
/**
 * 分服
 */
export enum EnumRankSeverDivide {
    /**
     * 跨服
     */
    CrossServer = 1,
    /**
     * 本服
     */
    CurrentServer = 2,
}
/**
 * 重置类型
 */
export enum EnumRankResetType {
    /**
     * 不重置
     */
    NoneReset = 1,
    /**
     * 每日0点重置
     */
    DailyReset = 2,
    /**
     * 每周一0点重置
     */
    WeeklyReset = 3,
    /**
     * 每月1日重置
     */
    MonthlyReset = 4,
    /**
     * 每次活动重置
     */
    ActivityReset = 5,
}
/**
 * 排行榜
 */
export default class BaseRank {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 描述
     */
    get desc(): string {
        return this.data[1];
    }

    /**
     * 排行类型
     */
    get type(): EnumRankType {
        return this.data[2];
    }

    /**
     * 活动ID
     */
    get activityId(): number {
        return this.data[3];
    }

    /**
     * 分服
     */
    get severDivide(): EnumRankSeverDivide {
        return this.data[4];
    }

    /**
     * 显示数量
     */
    get showCount(): number {
        return this.data[5];
    }

    /**
     * 最大排行
     */
    get maxRank(): number {
        return this.data[6];
    }

    /**
     * 标头名称
     */
    get title(): string {
        return this.data[7];
    }

    /**
     * 顺序
     */
    get order(): number {
        return this.data[8];
    }

    /**
     * 重置类型
     */
    get resetType(): EnumRankResetType {
        return this.data[9];
    }

    /**
     * 条件类型
     */
    get conditionType(): EnumRankConditionType {
        return this.data[10];
    }

    /**
     * 上榜条件
     */
    get rankCondition(): number {
        return this.data[11];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
