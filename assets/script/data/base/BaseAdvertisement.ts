/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 类型
 */
export enum EnumAdvertisementType {
    /**
     * 广告礼包/商店
     */
    ADPack = 1,
    /**
     * 广告任务
     */
    ADTask = 2,
    /**
     * 伙伴抽卡
     */
    PartnerDraw = 3,
    /**
     * 科技树加速
     */
    DeviceAccelerate = 15,
    /**
     * 祝福系统广告-增加攻击力
     */
    AddAttack = 19,
    /**
     * 祝福系统广告-增加雷电伤害
     */
    AddThunder = 20,
    /**
     * 祝福系统广告-增加金币获取
     */
    AdCoins = 21,
    /**
     * 祝福系统广告-增加重生石获取
     */
    AddRebirth = 22,
    /**
     * 停车场订单免费刷新广告
     */
    FreeOrderAd = 30,
    /**
     * 魔法抽卡
     */
    MagicDraw = 35,
    /**
     * 锻造台升阶加速
     */
    ForgeBedAccelerate = 42,
    /**
     * 藏品抽卡
     */
    CollectionDraw = 43,
}
/**
 * 奖励类型
 */
export enum EnumAdvertisementRewardType {
    /**
     * 直接使用
     */
    DirectUse = 1,
    /**
     * 获得奖励（道具/次数等）
     */
    ObtainReward = 2,
}
/**
 * 重置类型
 */
export enum EnumAdvertisementResetType {
    /**
     * 不重置
     */
    NotReset = 1,
    /**
     * 每日0点重置
     */
    DailyReset = 2,
    /**
     * 每周一0点重置
     */
    WeeklyReset = 3,
    /**
     * 每月1日0点重置
     */
    MonthlyReset = 4,
}
/**
 * 广告开关
 */
export enum EnumAdvertisementAdSwitch {
    /**
     * 开启
     */
    Open = 1,
    /**
     * 关闭
     */
    Close = 2,
}
/**
 * 是否加领奖次数
 */
export enum EnumAdvertisementRewardFrequency {
    /**
     * 增加
     */
    Increase = 1,
    /**
     * 不增加
     */
    NoIncrease = 0,
}
/**
 * 广告
 */
export default class BaseAdvertisement {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 类型
     */
    get type(): EnumAdvertisementType {
        return this.data[1];
    }

    /**
     * 免看所需特权id
     */
    get skipPrivileges(): number[] {
        return this.data[2];
    }

    /**
     * 奖励类型
     */
    get rewardType(): EnumAdvertisementRewardType {
        return this.data[3];
    }

    /**
     * 重置类型
     */
    get resetType(): EnumAdvertisementResetType {
        return this.data[4];
    }

    /**
     * 广告次数
     */
    get count(): number {
        return this.data[5];
    }

    /**
     * 冷却时间
     */
    get cd(): number {
        return this.data[6];
    }

    /**
     * 免看消耗
     */
    get skipCost(): any[] {
        return this.data[7];
    }

    /**
     * 广告开关
     */
    get adSwitch(): EnumAdvertisementAdSwitch {
        return this.data[8];
    }

    /**
     * 是否加领奖次数
     */
    get rewardFrequency(): EnumAdvertisementRewardFrequency {
        return this.data[9];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
