/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 对话类型
 */
export enum EnumCombatDialogueDialogueType {
    /**
     * 文本对话
     */
    Text = 1,
    /**
     * 表情包对话
     */
    Dynamic = 2,
}
/**
 * 战斗对话
 */
export default class BaseCombatDialogue {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 战斗对话组id
     */
    get dialogueGroup(): number {
        return this.data[1];
    }

    /**
     * 对话类型
     */
    get dialogueType(): EnumCombatDialogueDialogueType {
        return this.data[2];
    }

    /**
     * 描述
     */
    get desc(): string {
        return this.data[3];
    }

    /**
     * 对象参数
     */
    get targetPara(): number {
        return this.data[4];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[5];
    }

    /**
     * 显示时间（秒）
     */
    get showTime(): number {
        return this.data[6];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
