/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 参数
 */
export enum EnumDungeonEquipmentTotalPara {
    /**
     * 门票奖励
     */
    Ticketrestoration = 1,
    /**
     * 门票恢复时间（单位：秒）
     */
    Time = 2,
    /**
     * 装备副本房主消耗
     */
    HomeownerConsume = 3,
    /**
     * 装备副本协助消耗
     */
    AssistConsume = 4,
    /**
     * 邀请Cd（单位：秒）
     */
    InvitationCd = 5,
    /**
     * 名次#人数
     */
    RankingAndPeople = 6,
    /**
     * 邀请消息消失时间（单位：秒）
     */
    InvitationDisappearCd = 7,
    /**
     * 邮件id
     */
    MailId = 8,
}
/**
 * 无效表装备副本总表
 */
export default class BaseDungeonEquipmentTotal {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 参数
     */
    get para(): EnumDungeonEquipmentTotalPara {
        return this.data[1];
    }

    /**
     * 值
     */
    get value(): string {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
