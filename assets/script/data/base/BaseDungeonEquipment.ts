/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 无效表装备副本
 */
export default class BaseDungeonEquipment {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 解锁条件
     */
    get unlockConditions(): number[] {
        return this.data[1];
    }

    /**
     * 小关卡数
     */
    get barrier(): number {
        return this.data[2];
    }

    /**
     * 组别
     */
    get group(): number {
        return this.data[3];
    }

    /**
     * 怪物基础血量系数
     */
    get baseHpCoe(): number[] {
        return this.data[4];
    }

    /**
     * 保底奖励
     */
    get guaranteedReward(): any[] {
        return this.data[5];
    }

    /**
     * 通关奖励
     */
    get randomReward(): any[] {
        return this.data[6];
    }

    /**
     * 波次奖励
     */
    get reward(): any[] {
        return this.data[7];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
