/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 祝福等级
 */
export default class BaseBlessingLevel {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 升级所需经验
     */
    get upgradeCost(): number {
        return this.data[1];
    }

    /**
     * 属性
     */
    get attribute(): any[] {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
