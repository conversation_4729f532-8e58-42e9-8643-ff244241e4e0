/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 弹幕类型
 */
export enum EnumChatTextBulletChat {
    /**
     * 无弹幕类型
     */
    BulletChat1 = 1,
    /**
     * 跑马灯
     */
    BulletChat2 = 2,
}
/**
 * 类型
 */
export enum EnumChatTextType {
    /**
     * 购买月卡触发
     */
    BuyMonthlyCard = 1,
    /**
     * 购买终身卡触发
     */
    BuyLifeCard = 2,
    /**
     * 获得xx道具可触发
     */
    BuySeniorItem = 4,
    /**
     * 车位分享触发
     */
    UnionParkShare = 101,
    /**
     * 购买挖矿卡触发
     */
    BuyMineCard = 102,
    /**
     * 通用红包
     */
    GeneralRedPacket = 103,
    /**
     * 通过第X关主线关卡
     */
    MainLevelsX = 201,
    /**
     * 通关天虹之塔第X关
     */
    DungeonTowerLevelTask = 202,
    /**
     * 矿山深度达到XX米
     */
    MiningTask = 203,
    /**
     * 伙伴招募次数达到XX次
     */
    PartnerRecruit = 204,
    /**
     * 魔法召唤次数达到XX次
     */
    MagicRecruit = 205,
    /**
     * 万宝箱开启数量达到XX次
     */
    OpenBoxTask = 206,
    /**
     * 累计开启X个宠物蛋
     */
    OpenPetEggsTask = 207,
    /**
     * 藏品抽奖次数达到X次
     */
    CollectionRecruit = 208,
    /**
     * 王权达到XX等级
     */
    PowerTask = 209,
    /**
     * 所有的装备强化XX级
     */
    EquipLevelUpTask = 210,
    /**
     * 锻造台升到x阶
     */
    ForgeStageLevelTask = 211,
    /**
     * 箭矢首次等级达到XX等级
     */
    ArrowLevelTask = 212,
    /**
     * 任意魔法达到XX星
     */
    MagicStar = 213,
    /**
     * 神器达到XX级
     */
    WeaponClass = 214,
    /**
     * 背饰的所有羽毛都达到XX级
     */
    WingsLevel = 215,
    /**
     * 任意伙伴达到XX星
     */
    ArcherStar = 216,
    /**
     * 战车XX等级
     */
    TankClass = 217,
    /**
     * 宠物首次等级最高达到XX级
     */
    PetLevel = 218,
    /**
     * 首次获得品质≥6神器
     */
    BuySeniorWeapon1 = 219,
    /**
     * 首次获得品质≥6背饰
     */
    BuySeniorWings1 = 220,
    /**
     * 首次获得品质≥6主角
     */
    BuySeniorLead1 = 221,
    /**
     * 首次获得品质≥6战车
     */
    BuySeniorTank1 = 222,
    /**
     * 首次获得品质≥6的藏品
     */
    BuySeniorCollection = 223,
    /**
     * 首次获得品质≥6的宠物
     */
    BuySeniorPet1 = 224,
    /**
     * 购买王权基金
     */
    BuyPowerFund = 225,
    /**
     * 购买冒险基金
     */
    BuyAdventureFund = 226,
    /**
     * 购买宠物基金
     */
    BuyPetFund = 227,
    /**
     * 购买挖矿基金
     */
    BuyMineFund = 228,
    /**
     * 不竭之镜通关XX关
     */
    DungeonBossLevelTask = 229,
    /**
     * 米德云端通关XX关
     */
    DungeonCloudTask = 230,
    /**
     * 怪盗波比单次击杀血条首次最高达到XX条
     */
    DungeonThief = 231,
    /**
     * 获得品质≥6的伙伴X个
     */
    GetQualityPartnersTask = 232,
    /**
     * 宠物合成X次
     */
    SynthesizePetNumTask = 233,
    /**
     * 获得所有装备品质达到XX
     */
    EquipLevelQuality = 234,
    /**
     * 魔法首次等级最高达到XX级
     */
    MagicFirstLevel = 235,
    /**
     * 伙伴首次等级最高达到XX级
     */
    ArcherFirstLevel = 236,
    /**
     * 任意宠物吞噬等级达到Y级
     */
    DevourLevelTask = 237,
    /**
     * 藏品总升星达到XX
     */
    CollectionAllStar = 238,
    /**
     * 任意神器达到X星
     */
    WeaponStar = 239,
    /**
     * 任意背饰达到X星
     */
    WingsStar = 240,
    /**
     * 任意主角达到X星
     */
    LeadStar = 241,
    /**
     * 任意战车达到X星
     */
    TankStar = 242,
    /**
     * 任意藏品达到X星
     */
    CollectionStar = 243,
}
/**
 * 聊天文本
 */
export default class BaseChatText {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 类型
     */
    get type(): EnumChatTextType {
        return this.data[1];
    }

    /**
     * 参数
     */
    get value(): any[] {
        return this.data[2];
    }

    /**
     * 频道
     */
    get channel(): number {
        return this.data[3];
    }

    /**
     * 弹幕类型
     */
    get bulletChat(): EnumChatTextBulletChat {
        return this.data[4];
    }

    /**
     * 是否存储
     */
    get storage(): number {
        return this.data[5];
    }

    /**
     * 红包ID
     */
    get redPacket(): number {
        return this.data[6];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[7];
    }

    /**
     * 颜色
     */
    get color(): string {
        return this.data[8];
    }

    /**
     * 文本
     */
    get text(): string {
        return this.data[9];
    }

    /**
     * 跑马灯文本
     */
    get text2(): string {
        return this.data[10];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
