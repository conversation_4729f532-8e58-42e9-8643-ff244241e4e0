/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 箭矢表
 */
export default class BaseArrow {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[1];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
