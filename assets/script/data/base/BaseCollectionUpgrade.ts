/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 收藏品升级表
 */
export default class BaseCollectionUpgrade {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 类型id
     */
    get type(): number {
        return this.data[1];
    }

    /**
     * 精炼等级
     */
    get level(): number {
        return this.data[2];
    }

    /**
     * 附加属性值
     */
    get attribute(): any[] {
        return this.data[3];
    }

    /**
     * 升到本级消耗
     */
    get upgradeCost(): any[] {
        return this.data[4];
    }

    /**
     * 生效数量上限
     */
    get taskeffectiveLimit(): number {
        return this.data[5];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
