/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 道具加成表
 */
export default class BaseItemAttribute {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 属性
     */
    get attribute(): any[] {
        return this.data[1];
    }

    /**
     * 经济属性
     */
    get economyAttribute(): any[] {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
