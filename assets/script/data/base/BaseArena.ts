/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 参数
 */
export enum EnumArenaPara {
    /**
     * 初始积分
     */
    InitialPoint = 1,
    /**
     * 挑战消耗
     */
    PkCost = 2,
    /**
     * 每日免费挑战次数
     */
    PkFreeTimes = 3,
    /**
     * 胜利积分：填[获得积分]（挑战/防守）
     */
    ChallengeWinPoint = 4,
    /**
     * 挑战获胜奖励
     */
    ChallengeWinReward = 5,
    /**
     * 失败扣分：填[获得积分]（挑战/防守）
     */
    DefendFailPoint = 6,
    /**
     * 挑战失败奖励
     */
    ChallengeFailReward = 7,
    /**
     * 榜单结算时间
     */
    EndTime = 10,
    /**
     * 商店刷新重置时间（0-6天）
     */
    RefreshTime = 12,
    /**
     * 战斗时间（单位：s）
     */
    PkTime = 13,
    /**
     * 自动释放技能时间间隔（单位：s）
     */
    PkAutomaticRelease = 14,
    /**
     * 战斗记录最高条数
     */
    PkMaxNumber = 15,
    /**
     * 每场参与奖励
     */
    ParticipationReward = 16,
    /**
     * 血量放大倍数
     */
    PkHpTimes = 17,
    /**
     * 排行榜最高名次（初始均为500+）
     */
    Ranking = 18,
    /**
     * 结算自动关闭时间（单位：s）
     */
    PkCloseTime = 20,
    /**
     * 最低积分
     */
    LowestPoint = 21,
    /**
     * 直接购买挑战令的商店表ID
     */
    PVPTicket = 22,
    /**
     * 第三号位匹配本服pk玩家范围（当前玩家竞技积分的前X名和后Y名）
     */
    PkLimitThird = 23,
    /**
     * 第一号位匹配本服PK玩家范围（当前玩家竞技积分的前X名）
     */
    PkLimitFirst = 24,
    /**
     * 第二号位匹配本服PK玩家范围（当前玩家竞技积分的前X名和后Y名）
     */
    PkLimitSecond = 25,
    /**
     * 匹配本服PK玩家范围（当前玩家竞技积分的后X名）
     */
    PkLimitPoints = 26,
}
/**
 * 竞技场
 */
export default class BaseArena {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 参数
     */
    get para(): EnumArenaPara {
        return this.data[1];
    }

    /**
     * 值
     */
    get value(): string {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
