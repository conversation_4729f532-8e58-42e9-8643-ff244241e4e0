/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 伙伴品质
 */
export enum EnumArcherQuality {
    /**
     * 普通
     */
    Quality1 = 1,
    /**
     * 精良
     */
    Quality2 = 2,
    /**
     * 优秀
     */
    Quality3 = 3,
    /**
     * 稀有
     */
    Quality4 = 4,
    /**
     * 史诗
     */
    Quality5 = 5,
    /**
     * 传说
     */
    Quality6 = 6,
}
/**
 * 伙伴表
 */
export default class BaseArcher {
    // data
    private readonly data: any[];

    /**
     * 伙伴ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[1];
    }

    /**
     * 碎片ID
     */
    get shardId(): number {
        return this.data[2];
    }

    /**
     * 伙伴品质
     */
    get quality(): EnumArcherQuality {
        return this.data[3];
    }

    /**
     * 伙伴名称
     */
    get name(): string {
        return this.data[4];
    }

    /**
     * 伙伴描述
     */
    get desc(): string {
        return this.data[5];
    }

    /**
     * 激活奖励
     */
    get activateAward(): any[] {
        return this.data[6];
    }

    /**
     * 主动技能ID
     */
    get skillId(): number[] {
        return this.data[7];
    }

    /**
     * 普攻技能ID
     */
    get attackId(): number {
        return this.data[8];
    }

    /**
     * 跳转ID
     */
    get jumpId(): number {
        return this.data[9];
    }

    /**
     * 属性系数值
     */
    get factor(): any[] {
        return this.data[10];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
