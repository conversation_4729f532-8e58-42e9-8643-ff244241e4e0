/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 显示类型
 */
export enum EnumEconomyAttributeShowType {
    /**
     * 固定值
     */
    Regular = 1,
    /**
     * 百分比
     */
    Percentage = 2,
}
/**
 * 属性类型
 */
export enum EnumEconomyAttributeNature {
    /**
     * 箭矢属性
     */
    ArrowAttribute = 1,
    /**
     * 研究所属性
     */
    InstituteAttribute = 2,
    /**
     * 停车场属性
     */
    ParkingAttribute = 3,
    /**
     * 其他属性
     */
    OtherAttribute = 4,
}
/**
 * 类型
 */
export enum EnumEconomyAttributeType {
    /**
     * 箭矢制作等级
     */
    ArrowProduceLevel = 101,
    /**
     * 自动制作
     */
    AutoArrowProduce = 102,
    /**
     * 自动合并
     */
    AutoArrowMerge = 103,
    /**
     * 幸运制作
     */
    LuckyProduce = 104,
    /**
     * 幸运合成
     */
    LuckyMerge = 105,
    /**
     * 无损制作
     */
    FreeProduce = 106,
    /**
     * 无损合成
     */
    FreeMerge = 107,
    /**
     * 金币收益
     */
    GoldIncomePercent = 201,
    /**
     * 双倍金币
     */
    DoubleGold = 202,
    /**
     * 重生石收益
     */
    RebirthStoneIncomePercent = 203,
    /**
     * 离线收益
     */
    OfflineReward = 204,
    /**
     * 研究速度提升
     */
    AcceleratedResearch = 301,
    /**
     * 矿石获取量（百分比）
     */
    MineralAcquisition = 302,
    /**
     * 铁镐补充速度
     */
    PickaxeRecoverySpeed = 303,
    /**
     * 铁镐恢复上限
     */
    PickaxeRecoveryLimit = 304,
    /**
     * 家园币收益
     */
    ParkingLotGoldEarnings = 401,
    /**
     * 停车经验收益
     */
    ParkingLotExpEarnings = 402,
    /**
     * 订单收益
     */
    ParkingLotOrderEarnings = 403,
    /**
     * 派遣速度提升
     */
    DispatchSpeed = 404,
    /**
     * 声望加成
     */
    ReputationBonus = 601,
}
/**
 * 经济属性表
 */
export default class BaseEconomyAttribute {
    // data
    private readonly data: any[];

    /**
     * 属性ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[1];
    }

    /**
     * 类型
     */
    get type(): EnumEconomyAttributeType {
        return this.data[2];
    }

    /**
     * 名称
     */
    get name(): string {
        return this.data[3];
    }

    /**
     * 描述
     */
    get desc(): string {
        return this.data[4];
    }

    /**
     * 属性类型
     */
    get nature(): EnumEconomyAttributeNature {
        return this.data[5];
    }

    /**
     * 显示类型
     */
    get showType(): EnumEconomyAttributeShowType {
        return this.data[6];
    }

    /**
     * 小数位数
     */
    get decimal(): number {
        return this.data[7];
    }

    /**
     * 系数初值
     */
    get factor(): number {
        return this.data[8];
    }

    /**
     * 初始值
     */
    get initial(): number {
        return this.data[9];
    }

    /**
     * 参数
     */
    get para(): number[] {
        return this.data[10];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
