/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 类型
 */
export enum EnumPrincessChildGroupType {
    /**
     * 获得子嗣
     */
    GetSon = 1,
    /**
     * 获得道具奖励
     */
    GetItemReward = 2,
}
/**
 * 无效表王储获取表
 */
export default class BasePrincessChildGroup {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 类型
     */
    get type(): EnumPrincessChildGroupType {
        return this.data[1];
    }

    /**
     * 组
     */
    get group(): number {
        return this.data[2];
    }

    /**
     * 等级
     */
    get level(): number {
        return this.data[3];
    }

    /**
     * 台词
     */
    get text(): string {
        return this.data[4];
    }

    /**
     * 获得奖励
     */
    get reward(): any[] {
        return this.data[5];
    }

    /**
     * 获得概率
     */
    get rewardShow(): number[] {
        return this.data[6];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
