/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 品质
 */
export enum EnumCollectionQuality {
    /**
     * 蓝色
     */
    Blue = 3,
    /**
     * 紫色
     */
    Purple = 4,
    /**
     * 橙色
     */
    Orange = 5,
    /**
     * 红色
     */
    Red = 6,
}
/**
 * 类型
 */
export enum EnumCollectionType {
    /**
     * 限定
     */
    Limit = 1,
    /**
     * 非限定
     */
    UnLimit = 2,
}
/**
 * 升级类型
 */
export enum EnumCollectionUpgradeType {
    /**
     * 普通升级
     */
    OrdinaryUpgrade = 1,
    /**
     * 带任务的升级
     */
    TaskUpgrade = 2,
}
/**
 * 收藏品
 */
export default class BaseCollection {
    // data
    private readonly data: any[];

    /**
     * 收藏品id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[1];
    }

    /**
     * 品质
     */
    get quality(): EnumCollectionQuality {
        return this.data[2];
    }

    /**
     * 类型
     */
    get type(): EnumCollectionType {
        return this.data[3];
    }

    /**
     * 收藏品名字
     */
    get name(): string {
        return this.data[4];
    }

    /**
     * 收藏品故事描述
     */
    get desc(): string {
        return this.data[5];
    }

    /**
     * 碎片ID
     */
    get shardId(): number {
        return this.data[6];
    }

    /**
     * 合成需要的碎片
     */
    get needFragment(): number {
        return this.data[7];
    }

    /**
     * 升级类型
     */
    get upgradeType(): EnumCollectionUpgradeType {
        return this.data[8];
    }

    /**
     * 调用任务类型
     */
    get taskType(): number {
        return this.data[9];
    }

    /**
     * 任务参数
     */
    get taskPara(): number {
        return this.data[10];
    }

    /**
     * 升级调用类型
     */
    get upgradeCall(): number {
        return this.data[11];
    }

    /**
     * 升星调用类型
     */
    get starType(): number {
        return this.data[12];
    }

    /**
     * 组合id
     */
    get group(): number {
        return this.data[13];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
