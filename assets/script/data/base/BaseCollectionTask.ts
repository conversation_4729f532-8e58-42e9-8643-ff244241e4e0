/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 收藏品特殊任务条件
 */
export default class BaseCollectionTask {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 任务类型
     */
    get type(): number {
        return this.data[1];
    }

    /**
     * 任务参数
     */
    get taskValue(): number[] {
        return this.data[2];
    }

    /**
     * 任务描述
     */
    get desc(): string {
        return this.data[3];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
