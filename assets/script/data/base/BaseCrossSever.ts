/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 跨服类型
 */
export enum EnumCrossSeverCrossType {
    /**
     * 联盟乱斗
     */
    UnionBrawl = 1,
}
/**
 * 无效表跨服
 */
export default class BaseCrossSever {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 跨服类型
     */
    get crossType(): EnumCrossSeverCrossType {
        return this.data[1];
    }

    /**
     * 活动ID
     */
    get activityID(): number {
        return this.data[2];
    }

    /**
     * 排行榜ID
     */
    get rankID(): number[] {
        return this.data[3];
    }

    /**
     * 跨服
     */
    get sever(): string[] {
        return this.data[4];
    }

    /**
     * 时间
     */
    get time(): string {
        return this.data[5];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
