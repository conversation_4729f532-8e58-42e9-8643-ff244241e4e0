/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 无效表活动副本
 */
export default class BaseActivityBoss {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 怪物名称
     */
    get name(): string {
        return this.data[1];
    }

    /**
     * 副本类型
     */
    get type(): number {
        return this.data[2];
    }

    /**
     * 活动ID
     */
    get activityId(): number {
        return this.data[3];
    }

    /**
     * 组别
     */
    get group(): number {
        return this.data[4];
    }

    /**
     * 怪物血量
     */
    get hp(): number[] {
        return this.data[5];
    }

    /**
     * 准备界面展示怪物
     */
    get showBoss(): number {
        return this.data[6];
    }

    /**
     * 试用对象
     */
    get tryObject(): number {
        return this.data[7];
    }

    /**
     * 试用等级
     */
    get tryGrade(): number {
        return this.data[8];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
