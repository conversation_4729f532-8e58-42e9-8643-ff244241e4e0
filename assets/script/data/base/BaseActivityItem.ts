/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 活动类型
 */
export enum EnumActivityItemType {
    /**
     * 任务
     */
    Task = 1,
    /**
     * 团购礼包
     */
    GroupGift = 2,
    /**
     * 累充
     */
    SumRecharge = 3,
    /**
     * 排行
     */
    Rank = 4,
    /**
     * 商店&礼包
     */
    Shop = 5,
    /**
     * 累天
     */
    RechargeDay = 6,
    /**
     * 签到
     */
    Login = 7,
    /**
     * 基金
     */
    Fund = 8,
    /**
     * 兑换
     */
    Exchange = 9,
    /**
     * 抽奖
     */
    DrawCard = 14,
    /**
     * 进度奖励/成就奖励
     */
    Progress = 19,
    /**
     * 掉落（周活动）
     */
    Drop = 21,
    /**
     * 普通礼包
     */
    Pack = 22,
    /**
     * PVE挑战
     */
    BossTrial = 23,
    /**
     * 活动小游戏
     */
    ActivityLittleGame = 24,
    /**
     * 游历笔记玩法
     */
    Expedition = 25,
    /**
     * 公会攻防战主界面
     */
    UnionDefense = 26,
    /**
     * 公会攻防战-对战界面
     */
    UnionDefenseBattle = 27,
}
/**
 * 活动总表
 */
export default class BaseActivityItem {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 活动规则
     */
    get desc(): string {
        return this.data[1];
    }

    /**
     * 活动类型
     */
    get type(): EnumActivityItemType {
        return this.data[2];
    }

    /**
     * 参数
     */
    get typeValue(): any[] {
        return this.data[3];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
