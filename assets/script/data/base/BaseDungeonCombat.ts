/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 副本战斗表
 */
export default class BaseDungeonCombat {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 副本类型
     */
    get type(): number {
        return this.data[1];
    }

    /**
     * 跳过战斗开关
     */
    get skipSwitch(): number {
        return this.data[2];
    }

    /**
     * 跳过解锁
     */
    get skipUnlock(): number[] {
        return this.data[3];
    }

    /**
     * 战斗加速开关
     */
    get accelerationSwitch(): number {
        return this.data[4];
    }

    /**
     * 加速解锁
     */
    get battleAcceleration(): number[] {
        return this.data[5];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
