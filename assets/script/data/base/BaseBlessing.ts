/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 祝福表
 */
export default class BaseBlessing {
    // data
    private readonly data: any[];

    /**
     * 培养ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[1];
    }

    /**
     * 名称
     */
    get name(): string {
        return this.data[2];
    }

    /**
     * 描述
     */
    get desc(): string {
        return this.data[3];
    }

    /**
     * 位置
     */
    get position(): number {
        return this.data[4];
    }

    /**
     * 每次激活获得的经验
     */
    get gainExperience(): number {
        return this.data[5];
    }

    /**
     * 每次升级所需经验
     */
    get experienceBar(): number {
        return this.data[6];
    }

    /**
     * 属性值
     */
    get stepAttribute(): any[] {
        return this.data[7];
    }

    /**
     * 最大等级
     */
    get maxLevel(): number {
        return this.data[8];
    }

    /**
     * 广告id
     */
    get adId(): number {
        return this.data[9];
    }

    /**
     * 持续时长
     */
    get time(): number {
        return this.data[10];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
