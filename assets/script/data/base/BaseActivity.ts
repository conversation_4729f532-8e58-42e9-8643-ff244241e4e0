/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 开启类型
 */
export enum EnumActivityBeginType {
    /**
     * 明确时间开启
     */
    SpecificTime = 1,
    /**
     * 注册时间开启
     */
    Register = 2,
    /**
     * 每日开启
     */
    DailyTime = 3,
    /**
     * 每周开启
     */
    WeeklyTime = 4,
    /**
     * 每月开启（预留）
     */
    MonthlyTime = 5,
    /**
     * 开服开启
     */
    OpeningTime = 7,
    /**
     * 开服开启（循环开启）
     */
    CirculateTime = 8,
    /**
     * 每周开启（多天数）
     */
    DailyTimeMany = 9,
}
/**
 * 活动配置
 */
export default class BaseActivity {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[1];
    }

    /**
     * 活动ID组
     */
    get activityIDGroup(): number[] {
        return this.data[2];
    }

    /**
     * 开启类型
     */
    get beginType(): EnumActivityBeginType {
        return this.data[3];
    }

    /**
     * 开始时间
     */
    get beginTime(): string {
        return this.data[4];
    }

    /**
     * 间隔开启时间(单位：天)
     */
    get intervalTime(): number {
        return this.data[5];
    }

    /**
     * 准备时间
     */
    get prepareTime(): number {
        return this.data[6];
    }

    /**
     * 结束时间
     */
    get settlementTime(): number {
        return this.data[7];
    }

    /**
     * 展示时间
     */
    get showTime(): number {
        return this.data[8];
    }

    /**
     * 总时间
     */
    get totalTime(): number {
        return this.data[9];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
