/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 参数
 */
export enum EnumUnionPara {
    /**
     * 创建消耗
     */
    FoundCost = 1,
    /**
     * 改名消耗
     */
    RenameCost = 2,
    /**
     * 公会名称字数上限#公会宣言字数上限#公会说明字数上限
     */
    WordsLimit = 3,
    /**
     * 申请列表上限：公会的申请上限，达上限时申请，提示：该公会已达申请上限
     */
    ApplyList = 4,
    /**
     * 列表显示数量：未加入公会时列表可见数量
     */
    ListShow = 5,
    /**
     * Boss挑战次数
     */
    BossChallengeCount = 6,
    /**
     * Boss挑战结束时间，填整点
     */
    BossChallengeEndTime = 7,
    /**
     * 珍宝阁每日重置时间：填整点，24小时制
     */
    TreasureShopResetTime = 8,
    /**
     * 每日入盟人数限制
     */
    JoinCount = 9,
    /**
     * 填天数。即盟主超过3天未上线将自动转让给3天内有上线的成员，转让顺序：职位>贡献>随机
     */
    LeaderAutoTransfer = 10,
    /**
     * 公会头像
     */
    Flag = 11,
    /**
     * 每日公会Boss讨伐伤害与奖励
     */
    BossDamage = 12,
    /**
     * 第一次使用计算的砍价人数
     */
    FirstBargainPriceNum = 13,
    /**
     * 副本的挑战时间（单位为秒）
     */
    UnionBossTime = 14,
    /**
     * 副本的boss（怪物表id）
     */
    UnionBossId = 15,
    /**
     * 副本的boss的额外属性，[属性id,属性值],[属性id,属性值]
     */
    BossAttribute = 16,
    /**
     * 捐赠奖励（【道具ID，道具数量】）
     */
    DonateReward = 17,
    /**
     * 免费次数
     */
    FreeTimes = 18,
    /**
     * 捐赠次数上限
     */
    DonateNumber = 19,
    /**
     * 【道具ID，初始数量，每次递增数量，最大消耗数量】
     */
    DonateConsume = 20,
    /**
     * 珍宝阁补偿邮件id
     */
    Mail = 21,
    /**
     * 王权等级条件选择[填入王权表ID]
     */
    UnionCondition = 22,
    /**
     * 副本怪物阵型
     */
    UnionFormation = 23,
    /**
     * 挣扎动画的播放，【消耗的血条数，播放的动作】
     */
    BlowTrigger = 24,
    /**
     * 互助奖励
     */
    HelpReward = 26,
    /**
     * 互助奖励次数上限
     */
    HelpRewardLimit = 27,
    /**
     * 第一次重新加公会时间，第二次重新加公会时间，第三次重新加公会时间
     */
    UnionCD = 28,
    /**
     * 公会攻防战-对决期-战斗期间时间
     */
    UnionDefenseBattleTime = 2001,
    /**
     * 公会攻防战-对决期-每日挑战次数
     */
    UnionDefenseBattleNumber = 2002,
    /**
     * 公会攻防战-挑战奖励-胜利
     */
    UnionDefenseChallengeWin = 2003,
    /**
     * 公会攻防战-挑战奖励-失败
     */
    UnionDefenseChallengeFail = 2004,
    /**
     * 公会攻防战-扫荡奖励
     */
    UnionDefenseChallengeSweeps = 2005,
    /**
     * 公会攻防战-胜利方奖励
     */
    UnionDefenseWin = 2006,
    /**
     * 公会攻防战-失败方奖励
     */
    UnionDefenseFail = 2007,
    /**
     * 公会攻防战-轮空奖励
     */
    UnionDefenseBye = 2008,
    /**
     * 公会攻防战-公会补给包胜方奖励（填入随机奖励Id）
     */
    UnionDefenseSupplyWin = 2009,
    /**
     * 公会攻防战-公会补给包败方奖励（填入随机奖励Id）
     */
    UnionDefenseSupplyFail = 2010,
    /**
     * 公会攻防战-参战条件（填入公会人数，累计活跃度值）
     */
    UnionDefenseCondition = 2011,
    /**
     * 公会攻防战-对战模式中的实力难度【普通，困难，噩梦】
     */
    UnionDefenseDifficulty = 2012,
    /**
     * 公会攻防战-个人活跃度判断值【填入活度值】
     */
    PersonEnliven = 2013,
    /**
     * 公会攻防战-公会补给包胜方奖励预览
     */
    UnionDefenseSupplyWinPreview = 2014,
    /**
     * 公会攻防战-公会补给包败方奖励预览
     */
    UnionDefenseSupplyFailPreview = 2015,
    /**
     * 公会攻防战-防守人员数量
     */
    UnionDefenseDefensivePersonnel = 2016,
    /**
     * 公会攻防战-战斗结果请求服务端时间（单位：秒）
     */
    UnionDefensePVPTime = 2017,
    /**
     * 公会攻防战-个人活跃度判断值的时间【填入1-7（1表示星期1的零点开始，不可超过7）】
     */
    UnionDefensePersonEnlivenTime = 2018,
    /**
     * 公会攻防战-日志-全部日志可显示的日志条数上限（填入条数）
     */
    UnionDefenseLogLimit = 2019,
    /**
     * 公会攻防战-可参与匹配的另一个条件，开服X天后的服才可参与
     */
    UnionDefenseMatchServerTime = 2020,
}
/**
 * 联盟
 */
export default class BaseUnion {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 参数
     */
    get para(): EnumUnionPara {
        return this.data[1];
    }

    /**
     * 值
     */
    get value(): string {
        return this.data[2];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
