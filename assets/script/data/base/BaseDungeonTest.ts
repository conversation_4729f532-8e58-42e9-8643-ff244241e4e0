/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 测试副本
 */
export default class BaseDungeonTest {
    // data
    private readonly data: any[];

    /**
     * 组合ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 上阵属性
     */
    get attribute(): any[] {
        return this.data[1];
    }

    /**
     * 魔法
     */
    get magic(): any[] {
        return this.data[2];
    }

    /**
     * 伙伴
     */
    get archer(): any[] {
        return this.data[3];
    }

    /**
     * 主角
     */
    get lead(): number[] {
        return this.data[4];
    }

    /**
     * 神器
     */
    get weapon(): number[] {
        return this.data[5];
    }

    /**
     * 背饰
     */
    get wing(): number[] {
        return this.data[6];
    }

    /**
     * 战车
     */
    get tank(): number[] {
        return this.data[7];
    }

    /**
     * 宠物
     */
    get pet(): number[] {
        return this.data[8];
    }

    /**
     * 刷怪
     */
    get monster(): any[] {
        return this.data[9];
    }

    /**
     * 怪物阵型
     */
    get formation(): number[] {
        return this.data[10];
    }

    /**
     * 怪物属性
     */
    get monsterAttribute(): any[] {
        return this.data[11];
    }

    /**
     * 场次
     */
    get number(): number {
        return this.data[12];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
