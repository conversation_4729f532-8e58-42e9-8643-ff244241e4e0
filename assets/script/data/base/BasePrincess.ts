/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 品质
 */
export enum EnumPrincessQuality {
    /**
     * 丑陋
     */
    Ugly = 1,
    /**
     * 平庸
     */
    Mediocre = 2,
    /**
     * 漂亮
     */
    Pretty = 3,
    /**
     * 绝美
     */
    ExtremelyBeautiful = 4,
    /**
     * 倾国
     */
    RuinCountry = 5,
    /**
     * 无与伦比
     */
    Incomparable = 6,
}
/**
 * 无效表知己表
 */
export default class BasePrincess {
    // data
    private readonly data: any[];

    /**
     * ID
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 美术资源
     */
    get res(): string {
        return this.data[1];
    }

    /**
     * 品质
     */
    get quality(): EnumPrincessQuality {
        return this.data[2];
    }

    /**
     * 名称
     */
    get name(): string {
        return this.data[3];
    }

    /**
     * 信件
     */
    get unLock(): any[] {
        return this.data[4];
    }

    /**
     * 等级上限
     */
    get levelLimit(): number {
        return this.data[5];
    }

    /**
     * 升级所需
     */
    get upgradeRequired(): number[] {
        return this.data[6];
    }

    /**
     * 属性
     */
    get attribute(): any[] {
        return this.data[7];
    }

    /**
     * 随机宠幸获得王储
     */
    get randomGetChild(): any[] {
        return this.data[8];
    }

    /**
     * 指定宠幸获得王储
     */
    get getChild(): number {
        return this.data[9];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
