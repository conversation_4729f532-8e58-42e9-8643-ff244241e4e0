/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 收藏品图鉴等级
 */
export default class BaseCollectionHandbook {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 等级
     */
    get level(): number {
        return this.data[1];
    }

    /**
     * 图标资源
     */
    get res(): string {
        return this.data[2];
    }

    /**
     * 升到本级所需经验
     */
    get upExp(): number {
        return this.data[3];
    }

    /**
     * 升到本级给的奖励
     */
    get reward(): any[] {
        return this.data[4];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
