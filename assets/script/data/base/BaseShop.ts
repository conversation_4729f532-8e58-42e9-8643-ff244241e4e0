/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * 商店类型
 */
export enum EnumShopType {
    /**
     * 杂货铺
     */
    RechargeShop = 1,
    /**
     * 活动商店
     */
    ActivityShop = 2,
    /**
     * 竞技场商店
     */
    PVPShop = 3,
    /**
     * 公会商店
     */
    UnionShop = 4,
    /**
     * 广告商店
     */
    ADShop = 5,
    /**
     * 遗迹商店
     */
    EquipmentShop = 6,
    /**
     * 停车场商店
     */
    ParkShop = 7,
    /**
     * 宠物商店
     */
    PetShop = 8,
    /**
     * 竞速商店
     */
    SprintShop = 10,
    /**
     * 不竭之境
     */
    DungeonBoss = 11,
    /**
     * 米德云端
     */
    DungeonCloud = 12,
    /**
     * 怪盗积德
     */
    DungeonThief = 13,
    /**
     * 天虹之塔
     */
    DungeonTower = 14,
    /**
     * 丰裕宝匣
     */
    DungeonBox = 15,
    /**
     * 累充累天
     */
    TotalRecharge = 16,
    /**
     * 限时玩法商店
     */
    LimitedPlayShow = 17,
    /**
     * 竞技场挑战令
     */
    PVPTicket = 18,
    /**
     * 商业化抽奖
     */
    ActivityLottery = 19,
    /**
     * 竞速商店（循环）
     */
    PersonSprint = 20,
    /**
     * 公会攻防战商店
     */
    UnionDefenseShop = 21,
}
/**
 * 商品类型
 */
export enum EnumShopBuyType {
    /**
     * 购买获得
     */
    Buy = 1,
    /**
     * 免费获得
     */
    Free = 2,
    /**
     * 看广告获得
     */
    Advertisement = 3,
}
/**
 * 解锁条件
 */
export enum EnumShopUnlockCondition {
    /**
     * 无需解锁
     */
    NoneLock = 1,
    /**
     * 联盟等级
     */
    UnionLevel = 2,
}
/**
 * 显示页签
 */
export enum EnumShopTag {
    /**
     * 不显示
     */
    NoDisplay = 1,
    /**
     * 主页签
     */
    MainTag = 2,
}
/**
 * 限购类型
 */
export enum EnumShopLimitType {
    /**
     * 不限购
     */
    NoneLimit = 1,
    /**
     * 每日限购
     */
    DailyLimit = 2,
    /**
     * 每周限购
     */
    WeeklyLimit = 3,
    /**
     * 每月限购
     */
    MonthLimit = 4,
    /**
     * 永久限购
     */
    ForeverLimit = 5,
    /**
     * 活动限购
     */
    ActivityLimit = 6,
}
/**
 * 消耗类型
 */
export enum EnumShopCostType {
    /**
     * 正常消耗
     */
    Consumption = 1,
    /**
     * 递增消耗
     */
    IncrementalConsumption = 2,
}
/**
 * 商店表
 */
export default class BaseShop {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 商店类型
     */
    get type(): EnumShopType {
        return this.data[1];
    }

    /**
     * 商品类型
     */
    get buyType(): EnumShopBuyType {
        return this.data[2];
    }

    /**
     * 名称
     */
    get name(): string {
        return this.data[3];
    }

    /**
     * 解锁条件
     */
    get unlockCondition(): EnumShopUnlockCondition {
        return this.data[4];
    }

    /**
     * 解锁参数
     */
    get shopUnlockValue(): number {
        return this.data[5];
    }

    /**
     * 显示页签
     */
    get tag(): EnumShopTag {
        return this.data[6];
    }

    /**
     * 活动ID
     */
    get activityId(): number {
        return this.data[7];
    }

    /**
     * 广告ID
     */
    get advertisementID(): number {
        return this.data[8];
    }

    /**
     * 商品
     */
    get goods(): any[] {
        return this.data[9];
    }

    /**
     * 礼包
     */
    get pack(): number[] {
        return this.data[10];
    }

    /**
     * 消耗类型
     */
    get costType(): EnumShopCostType {
        return this.data[11];
    }

    /**
     * 购买消耗
     */
    get buyCost(): any[] {
        return this.data[12];
    }

    /**
     * 限购类型
     */
    get limitType(): EnumShopLimitType {
        return this.data[13];
    }

    /**
     * 限购数量
     */
    get shopLimitValue(): number {
        return this.data[14];
    }

    /**
     * 展示期可否购买
     */
    get showTime(): number {
        return this.data[15];
    }

    /**
     * 排序
     */
    get order(): number {
        return this.data[16];
    }

    /**
     * 商品折扣
     */
    get discount(): number {
        return this.data[17];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
