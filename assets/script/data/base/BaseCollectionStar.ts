/* eslint-disable @typescript-eslint/naming-convention */

// 由导表工具导出，不可更改

/**
 * undefined
 */
export enum EnumCollectionStarQuality {
    /**
     * 蓝色
     */
    Blue = 3,
    /**
     * 紫色
     */
    Purple = 4,
    /**
     * 橙色
     */
    Orange = 5,
    /**
     * 红色
     */
    Red = 6,
}
/**
 * 操作类型
 */
export enum EnumCollectionStarOperationType {
    /**
     * 升星
     */
    Star = 1,
    /**
     * 觉醒
     */
    Wake = 2,
}
/**
 * 收藏品升星等级
 */
export default class BaseCollectionStar {
    // data
    private readonly data: any[];

    /**
     * id
     */
    get id(): number {
        return this.data[0];
    }

    /**
     * 升星类型
     */
    get type(): number {
        return this.data[1];
    }

    /**
     * 星级
     */
    get level(): number {
        return this.data[2];
    }

    /**
     * 操作类型
     */
    get operationType(): EnumCollectionStarOperationType {
        return this.data[3];
    }

    /**
     * 星级表现
     */
    get star(): number {
        return this.data[4];
    }

    /**
     * 基础属性值
     */
    get attribute(): any[] {
        return this.data[5];
    }

    /**
     * 星级效果
     */
    get starEffect(): number {
        return this.data[6];
    }

    /**
     * 消耗碎片数量
     */
    get costFragment(): number {
        return this.data[7];
    }

    /**
     * 消耗道具数量
     */
    get costItem(): any[] {
        return this.data[8];
    }

    /**
     * 获得经验
     */
    get exp(): number {
        return this.data[9];
    }

    /**
     * 构造函数
     * @param data
     */
    constructor(data: any[]) {
        this.data = data;
    }
}
