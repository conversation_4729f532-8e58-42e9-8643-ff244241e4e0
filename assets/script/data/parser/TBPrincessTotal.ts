import DataParser from "../../../nsn/data/DataParser";
import { EnumPrincessTotalPara } from "../base/BasePrincessTotal";
import DataPrincessTotal from "../extend/DataPrincessTotal";

export default class TBPrincessTotal extends DataParser<DataPrincessTotal> {
    /**
     * 获取对应值
     * @param para 参数
     * @returns
     */
    public getValueByPara(para: EnumPrincessTotalPara): any {
        const data = this.dataList.find((v) => v.para === para);
        if (data) {
            return JSON.parse(data.value);
        }
    }
}
