import DataParser from "../../../nsn/data/DataParser";
import MathUtils from "../../../nsn/util/MathUtils";
import { IAttributeInfo } from "../../../protobuf/proto";
import NumberUtils from "../../utils/NumberUtils";
import { EnumAttributeFigureType, EnumAttributeScoreType, EnumAttributeType } from "../base/BaseAttribute";
import DataAttribute from "../extend/DataAttribute";

export interface IAttrInfo {
    type: number;
    value: number;
    log?: string; // 日志
}

export default class TBAttribute extends DataParser<DataAttribute> {
    /**
     * 格式化属性（包括显示小数点后一位和加%）
     */
    public formatAttribute(attribute: IAttrInfo | IAttributeInfo | number[]): {
        name: string;
        value: string;
    } {
        const e: IAttrInfo = { type: 0, value: 0 };
        if (Array.isArray(attribute)) {
            e.type = attribute[0];
            e.value = attribute[1];
        } else {
            e.type = attribute.type;
            e.value = attribute.value;
        }
        const data = this.getDataById(e.type);
        if (data.figureType === EnumAttributeFigureType.Percentage) {
            e.value = e.value * 100;
        }
        e.value = MathUtils.round(e.value, data.decimal);
        let valueStr = NumberUtils.format(e.value, data.decimal, data.decimal);
        valueStr = valueStr.replace(/(?:\.0*|(\.\d+?)0+)$/, "$1");

        if (data.figureType === EnumAttributeFigureType.Percentage) {
            valueStr += "%";
        }
        return { name: data.name, value: valueStr };
    }

    /**
     * 合并重复属性
     * @param attr 属性
     */
    public mergeAttr(attr: IAttrInfo[]): void {
        for (let i = attr.length - 1; i >= 0; i--) {
            const tempAttr = attr.find((v, i2) => v.type === attr[i].type && i2 !== i);
            if (tempAttr) {
                tempAttr.value = tempAttr.value + attr[i].value;

                attr.splice(i, 1);
            }
        }
    }

    /**
     * 通过属性获取评分
     * @param attr
     * @returns
     */
    public getScoreByAttr(attr: IAttrInfo[] | IAttributeInfo[]): number {
        let fixedValue = 0;
        let percentValue = 0;
        for (const e of attr) {
            const attrData = TBAttribute.getInstance().getDataById(e.type);
            switch (attrData.scoreType) {
                case EnumAttributeScoreType.FixedValue:
                    fixedValue += e.value * attrData.scoreValue;
                    break;
                case EnumAttributeScoreType.Percentage:
                    percentValue += e.value * attrData.scoreValue;
                    break;
                default:
                    break;
            }
        }
        return fixedValue * (1 + percentValue);
    }

    /**
     * 获取天命属性
     * @param attr 属性
     * @returns
     */
    public getFateAttrs(attr: number[][]): number[] {
        return attr.find((e) => e[0] === EnumAttributeType.Fate);
    }
}
