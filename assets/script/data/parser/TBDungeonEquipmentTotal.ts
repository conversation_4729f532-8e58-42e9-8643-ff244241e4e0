import DataParser from "../../../nsn/data/DataParser";
import { EnumDungeonEquipmentTotalPara } from "../base/BaseDungeonEquipmentTotal";
import DataDungeonEquipmentTotal from "../extend/DataDungeonEquipmentTotal";

export default class TBDungeonEquipmentTotal extends DataParser<DataDungeonEquipmentTotal> {
    /**
     * 获取对应值
     * @param para 参数
     */
    public getValueByPara(para: EnumDungeonEquipmentTotalPara): any {
        const info = this.dataList.find((v) => v.para === para);
        if (info) {
            return JSON.parse(info.value);
        }
    }
}
