import DataParser from "../../../nsn/data/DataParser";
import { EnumRankConditionType, EnumRankSeverDivide } from "../base/BaseRank";
import DataRank from "../extend/DataRank";

export const RANK_ID = {
    ARENA: 10001, // 竞技场
    DUNGEON_TOWER: 10002, // 副本-天虹之塔
    DUNGEON_MAIN: 10005, // 主线副本
    DUNGEON_THIEF: 10007, // 副本-怪盗积德
    UNION_BOSS: 10006, // 公会-boss
    POWER: 10008, // 王权
    UNION_COMBAT: 10009, // 公会-战力排行榜
    UNION_DEFENSE: 201005, // 公会对决
};

export default class TBRank extends DataParser<DataRank> {
    /**
     * 根据排行榜类型获取排行榜配置
     * @param type 条件类型
     * @returns { DataRank[] }
     */
    public getDataByType(type: EnumRankConditionType): DataRank[] {
        return this.dataList.filter((item) => item.conditionType === type);
    }

    /**
     * 通过条件类型和分服类型来获取排行榜数据
     * @param type
     * @param server
     * @returns
     */
    public getDataByTypeAndServer(type: EnumRankConditionType, server: EnumRankSeverDivide): DataRank {
        return this.dataList.find((item) => item.conditionType === type && item.severDivide === server);
    }

    /**
     * 根据活动id获取排行榜id
     * @param activityId
     * @returns
     */
    public getDataByActivityId(activityId: number): DataRank {
        return this.dataList.find((v) => v.activityId === activityId);
    }
}
