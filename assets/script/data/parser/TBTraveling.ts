import DataParser from "../../../nsn/data/DataParser";
import { EnumTravelingType } from "../base/BaseTraveling";
import DataTraveling from "../extend/DataTraveling";

export default class TBTraveling extends DataParser<DataTraveling> {
    /**
     * 获取对应数据
     * @param type 地点类型
     * @returns
     */
    public getDataByType(type: EnumTravelingType): DataTraveling {
        return this.dataList.find((e) => e.type === type);
    }
}
