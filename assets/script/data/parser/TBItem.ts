import DataParser from "../../../nsn/data/DataParser";
import i18n from "../../config/i18n/I18n";
import { EnumItemEffectType, EnumItemQuality, EnumItemTag, EnumItemType } from "../base/BaseItem";
import DataItem from "../extend/DataItem";

/**
 * 道具id
 */
export const ITEM_ID = {
    TICKET: 100,
    DIAMOND: 101,
    COIN: 102,
    POWER_UPGRADE: 103,
    MAGIC_CRYSTAL_STONE: 205002, // 魔法水晶石
    MAGIC_DRAW_CARD_TICKET: 205003, // 魔法抽奖卷
    EQUIP_STRENGTHEN_STONE: 302001, // 装备强化石
    PARTNER_DRAW_CARD_TICKET: 603001, // 伙伴抽奖卷
    TANK_UP_ITEM: 802001, // 战车强化道具
    MAKE_ARROW_STONE: 901101, // 锻造石
    ARENA_TICKET: 901401, // 竞技场门票
    ARENA_SCORE: 901402, // 竞技场积分
    ARENA_COIN: 901403, // 竞技币
    HOME_COIN: 901602, // 停车场-家园币
    PARK_EXP: 901601, // 停车场-经验
    UNION_COIN: 901703, // 公会币
    UNION_DEFENSE_COIN: 901704, // 公会攻防战币
    RESEARCH_STONE: 901801, // 采矿研究所 - 矿石
    MINING_IRON_PICKAXE: 901802, // 采矿系统 - 铁镐
    MINING_GOLD_PICKAXE: 901803, // 采矿系统 - 金镐
    MINING_BIT: 901804, // 采矿系统 - 钻头
    MINING_EXPLOSIVE: 901805, // 采矿系统 - 炸药
    FIVE_ACC_TICKET: 901806, // 五分钟加速卷
    TASK_ACTIVE_FUND: 901903, // 活跃度基金道具
    DUNGEON_BOX_TICKET: 902101, // 丰裕宝匣体力
    OPENING_CELEBRATION: 902401, // 开服庆典-积分
    SPRINT_SHOP_COIN: 902501, // 竞速-兑换道具
    DEFAULT_AVATAR_FRAME: 903001, // 默认头像框
    DEFAULT_TITLE: 904201, // 默认称号
    TIME_BACK_TICKET: 905101, // 时空回溯主角抽奖卷
    TANK_TREASURE_TICKET: 905102, // 战车抽奖卷
    WING_DRAW_CARD_TICKET: 905103, // 背饰抽奖卷
    WEAPON_DRAW_CARD_TICKET: 905104, // 神器抽奖卷
    NEWBIE_TRIAL_TICKET: 905201, // 新手试炼门票
    FORGE_CUL: 906301, // 锻造经验
    FORGE_ADD_ITEM: 906302, // 锻造台加速道具
    COLLECTION_MIN_QUALITY_ITEM: 910002, // 藏品最低品质升级材料
    EXPEDITION_FOOD: 906501, // 游历干粮
    LIMIT_GAME_PLAY_COIN: 906502, // 游历金币
    COLLECTION_TICKET: 910001, // 藏品抽奖卷
};

const QUALITY_COLOR = [
    { quality: EnumItemQuality.Green, color: cc.color(74, 231, 85), colorText: "#4AE755", name: i18n.common0031 },
    { quality: EnumItemQuality.Blue, color: cc.color(35, 213, 226), colorText: "#23D5E2", name: i18n.common0032 },
    { quality: EnumItemQuality.Purple, color: cc.color(173, 109, 250), colorText: "#AD6DFA", name: i18n.common0033 },
    { quality: EnumItemQuality.Orange, color: cc.color(255, 114, 0), colorText: "#FF7200", name: i18n.common0034 },
    { quality: EnumItemQuality.Red, color: cc.color(255, 0, 0), colorText: "#FF0000", name: i18n.common0035 },
];

export default class TBItem extends DataParser<DataItem> {
    /**
     * 根据类型获取数据
     * @param type
     * @returns
     */
    public getDataByType(type: EnumItemType): DataItem[] {
        return this.dataList.filter((v) => v.type === type);
    }

    /**
     * 根据类型和道具品质获取数据
     * @param type
     * @returns
     */
    public getDataByTypeQuality(type: EnumItemType, quality: EnumItemQuality): DataItem[] {
        return this.dataList.filter((v) => v.type === type && v.quality === quality);
    }

    /**
     * 根据品质获得颜色
     */
    public getColorByQuality(quality: EnumItemQuality): cc.Color {
        for (const data of QUALITY_COLOR) {
            if (data.quality === quality) {
                return data.color;
            }
        }
    }

    /**
     * 根据品质获得颜色码
     */
    public getColorTextByQuality(quality: EnumItemQuality): string {
        for (const data of QUALITY_COLOR) {
            if (data.quality === quality) {
                return data.colorText;
            }
        }
    }

    /**
     * 根据品质获得名称
     */
    public getNameByQuality(quality: EnumItemQuality): string {
        for (const data of QUALITY_COLOR) {
            if (data.quality === quality) {
                return data.name;
            }
        }
        return "";
    }

    /**
     * 根据效果类型和使用效果获得数据
     */
    public getDataByEffectTypeItemEffect(effectType: EnumItemEffectType, itemEffect: number): DataItem {
        for (const item of this.getList()) {
            if (item.effectType === effectType) {
                if (item.itemEffect && item.itemEffect.length > 0) {
                    if (item.itemEffect[0] === itemEffect) {
                        return item;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据标签获得数据
     */
    public getDataListByTag(tag: EnumItemTag): DataItem[] {
        return this.dataList.filter((v) => v.tag === tag);
    }
}
