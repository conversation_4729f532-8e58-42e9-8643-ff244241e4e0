import DataParser from "../../../nsn/data/DataParser";
import DataPrincessChildGroup from "../extend/DataPrincessChildGroup";

export default class TBPrincessChildGroup extends DataParser<DataPrincessChildGroup> {
    /**
     * 获取对应数据
     * @param group 王储获取组
     * @param level 知己等级
     * @returns
     */
    public getDataByGroupAndLevel(group: number, level: number): DataPrincessChildGroup {
        return this.dataList.find((e) => e.group === group && e.level === level);
    }
}
