import DataParser from "../../../nsn/data/DataParser";
import DataRule from "../extend/DataRule";

export const RULE_ID = {
    DUNGEON_TOWER: 50, // 副本-爬塔
    TANK: 10111, // 战车系统
    PARTNER: 10121, // 伙伴系统
    COLLECTION_HOME: 10131, // 藏品大厅
    COLLECTION_DRAW: 10132, // 藏品抽奖
    PET: 10141, // 宠物
};

export default class TBRule extends DataParser<DataRule> {
    /**
     * 通过窗口名称获取规则
     * @param name
     * @returns
     */
    public getDataByUI(name: string): DataRule {
        return this.dataList.find((v) => v.popup === name);
    }

    /**
     * 通过窗口名称获取所有规则
     * @param name
     * @returns
     */
    public getDatasByUI(name: string): DataRule[] {
        return this.dataList.filter((v) => v.popup === name);
    }
}
