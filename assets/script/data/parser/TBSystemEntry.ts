import DataParser from "../../../nsn/data/DataParser";
import { EnumSystemEntryPlace } from "../base/BaseSystemEntry";
import DataSystemEntry from "../extend/DataSystemEntry";

/**
 * 系统入口id
 */
export const SYSTEM_ENTRY_ID = {
    BLESS: 1010,
    DUNGEON_CHAPTER_MAIN: 1011,
    MAIL: 1012,
    GAME_SWITCH: 1013,
    DAILY_TASK: 1015,
    RANK: 1018,
    BULLETIN: 1020,
    FRIEND: 1022,
    BAG: 1023,
    SOCIAL: 1024,
    IDLE_INCOME: 1025,
    GAME_CLUB: 1033,
    VIP_SERVICE: 1035,

    TIME_BACK1: 2003,
    TANK_TREASURE1: 2005,
    WING_DRAW_CARD: 2007,
    WEAPON_DRAW_CARD: 2009,

    COMMON_GIFT: 3001,
    OPENING_CELEBRATION: 3002,
    ACTIVITY_NEWCOMER: 3003,
    REC<PERSON>R<PERSON>_GIFT: 3004,
    CRAZY_DRAW: 3005,
    SPRINT: 3007,
    FIRST_RECHARGE: 3008,
    SIGN_IN: 3009,
    POWER_FUND: 3019,
    LINK_GIFT: 3100,

    PRIVILEGE: 3011,
    TOTAL_RECHARGE: 3012,
    NEWBIE_TRIAL: 3013,
    TANK_MINI_GAME: 3015,

    PERSON_SPRINT: 3200,

    ARENA: 4006,

    PET: 5051,
    TANK: 5091,
    BOX: 5131,
};

export default class TBSystemEntry extends DataParser<DataSystemEntry> {
    /**
     * 根据位置获取信息
     * @param place
     * @returns
     */
    public getDataByPlace(place: EnumSystemEntryPlace): DataSystemEntry[] {
        return this.getList().filter((v) => v.place === place);
    }

    /**
     * 根据活动id获取信息
     */
    public getDataByActivityID(activityId: number): DataSystemEntry {
        return this.getList().find((v) => v.activityId === activityId);
    }
}
