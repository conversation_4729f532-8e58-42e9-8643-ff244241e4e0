import DataParser from "../../../nsn/data/DataParser";
import { EnumRechargeGoodsType, EnumRechargeSaleWay } from "../base/BaseRecharge";
import { EnumShopBuyType } from "../base/BaseShop";
import DataRecharge from "../extend/DataRecharge";

/**
 * id
 */
export const RECHARGE_ID = {
    TEST: 100, // 测试
    CARD_MONTHLY: 201, // 月卡
    CARD_FOREVER: 202, // 终身卡
    BLESS: 203, // 祝福
    CARD_AUTO_MINING: 204, // 自动挖矿特权卡
    ACTIVITY_OC_FUND: 209, // 开服庆典基金
    FUND_1: 225, // 箭矢等级基金
    FUND_2: 226, // 冒险计划基金
    FUND_3: 227, // boss试炼基金
    FUND_4: 228, // 矿工基金
    POWER_FUND: 300, // 王权基金
    WEEK_FUND: 301, // 周基金

    FIRST_RECHARGE_1: 701, // 首充1
    FIRST_RECHARGE_2: 702, // 首充2
    FIRST_RECHARGE_3: 703, // 首充3

    FUND_5: 6001, // 钻石基金
};

/**
 * 购买类型
 */
export enum RechargeShopItemType {
    Shop = "shop",
    Recharge = "recharge",
}

/**
 * 充值商店商品
 */
export interface IRechargeShopItem {
    type: RechargeShopItemType; // 类型
    id: number; // id
    name: string; // 名称
    buyTime: number; // 购买次数
    saleWay: EnumRechargeSaleWay; // 充值购买方式
    pack: number[]; // 礼包
    buyCost: number[][]; // 购买消耗
    saleType: number;
    buyType: EnumShopBuyType; // 商店购买方式
    isRedPointAvailable?: boolean; // 红点是否有效
}

export default class TBRecharge extends DataParser<DataRecharge> {
    /**
     * 通过活动id获取数据
     * @param activityId
     * @returns
     */
    public getDataByActivityId(activityId: number): DataRecharge[] {
        return this.dataList.filter((v) => {
            return v.activityId === activityId;
        });
    }

    /**
     * 根据商品类型获取数据
     * @returns {DataRecharge[]}
     */
    public getDataByGoodsType(goodsType: EnumRechargeGoodsType): DataRecharge[] {
        return this.dataList.filter((v) => v.goodsType === goodsType);
    }

    /**
     * 获取链式充值数据
     * @param rechargeId
     * @returns
     */
    public getLinkDataByFirstRechargeId(rechargeId: number): DataRecharge[] {
        const linkData: DataRecharge[] = [];
        const index = this.dataList.findIndex((v) => v.id === rechargeId);
        if (index !== -1) {
            linkData.push(this.getDataById(rechargeId));
            for (let i = index + 1; i < this.dataList.length; i++) {
                const { limitGift, goodsType } = this.dataList[i];
                if (goodsType !== EnumRechargeGoodsType.ChainGift) {
                    break;
                }
                if (limitGift[0] !== linkData[linkData.length - 1].id) {
                    break;
                }
                linkData.push(this.dataList[i]);
            }
        }
        return linkData;
    }
}
