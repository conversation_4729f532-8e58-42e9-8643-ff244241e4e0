import DataParser from "../../../nsn/data/DataParser";
import { EnumActivityItemType } from "../base/BaseActivityItem";
import DataActivity from "../extend/DataActivity";
import TBActivityItem from "./TBActivityItem";

/**
 * 活动id
 */
export const ACTIVITY_ID = {
    SIGN_IN: 101,
    OPENING_CELEBRATION: 102,
    CRAZY_DRAW: 110,
    NEWBIE_TRIAL: 130,
    DAILY_TASK: 201,
    DAILY_PROGRESS_TASK: 202,
    WEEKLY_TASK: 301,
    WEEKLY_PROGRESS_TASK: 302,
    WEEK_FUND: 401,
    WEEKLY_EXCHANGE: 2201,

    EXPEDITION: 10011,

    SPRINT_ADVENTURE: 50011,
    SPRINT_WING1: 50021,
    SPRINT_PARTNER1: 50031,
    SPRINT_EQUIP1: 50041,
    SPRINT_MAGIC_DRAW1: 50051,
    SPRINT_WEAPON1: 50061,
    SPRINT_PET1: 50071,
    SPRINT_MINING1: 50081,
    SPRINT_TANK1: 50091,
    SPRINT_COLLECTION_DRAW1: 50101,
    SPRINT_COLLECTION_CUL1: 50111,

    SPRINT_WING2: 90021,
    SPRINT_PARTNER2: 90031,
    SPRINT_EQUIP2: 90041,
    SPRINT_MAGIC_DRAW2: 90051,
    SPRINT_WEAPON2: 90061,
    SPRINT_PET2: 90071,
    SPRINT_MINING2: 90081,
    SPRINT_TANK2: 90091,
    SPRINT_COLLECTION_DRAW2: 90101,
    SPRINT_COLLECTION_CUL2: 90111,

    HAMSTER_PARKOUR: 103001,
    MATCH_GAME: 104001,

    SPRINT_PERSON_ADVENTURE: 500110,
    SPRINT_PERSON_WING1: 500210,
    SPRINT_PERSON_PARTNER1: 500310,
    SPRINT_PERSON_EQUIP1: 500410,
    SPRINT_PERSON_MAGIC_DRAW1: 500510,
    SPRINT_PERSON_WEAPON1: 500610,
    SPRINT_PERSON_PET1: 500710,
    SPRINT_PERSON_MINING1: 500810,
    SPRINT_PERSON_TANK1: 500910,
    SPRINT_PERSON_COLLECTION_DRAW1: 501010,
    SPRINT_PERSON_COLLECTION_CUL1: 501110,

    SPRINT_PERSON_WING2: 900210,
    SPRINT_PERSON_PARTNER2: 900310,
    SPRINT_PERSON_EQUIP2: 900410,
    SPRINT_PERSON_MAGIC_DRAW2: 900510,
    SPRINT_PERSON_WEAPON2: 900610,
    SPRINT_PERSON_PET2: 900710,
    SPRINT_PERSON_MINING2: 900810,
    SPRINT_PERSON_TANK2: 900910,
    SPRINT_PERSON_COLLECTION_DRAW2: 901010,
    SPRINT_PERSON_COLLECTION_CUL2: 901110,

    UNION_DEFENSE: 201001,
};

/**
 * 限时竞速(排行榜)
 */
export const ACTIVITY_SPRINT_ID = [
    ACTIVITY_ID.SPRINT_ADVENTURE,
    ACTIVITY_ID.SPRINT_WING1,
    ACTIVITY_ID.SPRINT_PARTNER1,
    ACTIVITY_ID.SPRINT_EQUIP1,
    ACTIVITY_ID.SPRINT_MAGIC_DRAW1,
    ACTIVITY_ID.SPRINT_WEAPON1,
    ACTIVITY_ID.SPRINT_PET1,
    ACTIVITY_ID.SPRINT_MINING1,
    ACTIVITY_ID.SPRINT_TANK1,
    ACTIVITY_ID.SPRINT_COLLECTION_DRAW1,
    ACTIVITY_ID.SPRINT_COLLECTION_CUL1,

    ACTIVITY_ID.SPRINT_WING2,
    ACTIVITY_ID.SPRINT_PARTNER2,
    ACTIVITY_ID.SPRINT_EQUIP2,
    ACTIVITY_ID.SPRINT_MAGIC_DRAW2,
    ACTIVITY_ID.SPRINT_WEAPON2,
    ACTIVITY_ID.SPRINT_PET2,
    ACTIVITY_ID.SPRINT_MINING2,
    ACTIVITY_ID.SPRINT_TANK2,
    ACTIVITY_ID.SPRINT_COLLECTION_DRAW2,
    ACTIVITY_ID.SPRINT_COLLECTION_CUL2,
];

/**
 * 个人竞速
 */
export const ACTIVITY_PERSON_SPRINT_ID = [
    ACTIVITY_ID.SPRINT_PERSON_ADVENTURE,
    ACTIVITY_ID.SPRINT_PERSON_WING1,
    ACTIVITY_ID.SPRINT_PERSON_PARTNER1,
    ACTIVITY_ID.SPRINT_PERSON_EQUIP1,
    ACTIVITY_ID.SPRINT_PERSON_MAGIC_DRAW1,
    ACTIVITY_ID.SPRINT_PERSON_WEAPON1,
    ACTIVITY_ID.SPRINT_PERSON_PET1,
    ACTIVITY_ID.SPRINT_PERSON_MINING1,
    ACTIVITY_ID.SPRINT_PERSON_TANK1,
    ACTIVITY_ID.SPRINT_PERSON_COLLECTION_DRAW1,
    ACTIVITY_ID.SPRINT_PERSON_COLLECTION_CUL1,

    ACTIVITY_ID.SPRINT_PERSON_WING2,
    ACTIVITY_ID.SPRINT_PERSON_PARTNER2,
    ACTIVITY_ID.SPRINT_PERSON_EQUIP2,
    ACTIVITY_ID.SPRINT_PERSON_MAGIC_DRAW2,
    ACTIVITY_ID.SPRINT_PERSON_WEAPON2,
    ACTIVITY_ID.SPRINT_PERSON_PET2,
    ACTIVITY_ID.SPRINT_PERSON_MINING2,
    ACTIVITY_ID.SPRINT_PERSON_TANK2,
    ACTIVITY_ID.SPRINT_PERSON_COLLECTION_DRAW2,
    ACTIVITY_ID.SPRINT_PERSON_COLLECTION_CUL2,
];

export default class TBActivity extends DataParser<DataActivity> {
    /**
     * 根据活动id和类型获取子活动
     * @param id
     * @param type
     * @returns
     */
    public getItemByIdAndType(id: number, type: EnumActivityItemType): number {
        const data = this.getDataById(id);
        return data.activityIDGroup.find((v) => {
            const config = TBActivityItem.getInstance().getDataById(v);
            return config.type === type;
        });
    }

    /**
     * 根据活动id和类型获取子活动
     * @param id
     * @param type
     * @returns
     */
    public getItemsByIdAndType(id: number, type: EnumActivityItemType): number[] {
        const data = this.getDataById(id);
        const activityIds = [];
        for (const id of data.activityIDGroup) {
            const config = TBActivityItem.getInstance().getDataById(id);
            if (config.type === type) {
                activityIds.push(id);
            }
        }
        return activityIds;
    }

    /**
     * 根据活动子id获得活动id
     */
    public getIdByChildId(childId: number): number {
        const data = this.dataList.find((v) => v.activityIDGroup.includes(childId));
        return data.id;
    }

    /**
     * 获取对应数据
     * @param activityId 活动id
     * @returns
     */
    public getDataByActivityId(activityId: number): DataActivity {
        return this.dataList.find((e) => e.activityIDGroup.includes(activityId));
    }
}
