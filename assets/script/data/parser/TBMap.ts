/**
 * 导表工具生成
 * 请勿手动修改
 */

import TBActivity from "./TBActivity";
import TBActivityGame from "./TBActivityGame";
import TBActivityItem from "./TBActivityItem";
import TBActivityLogin from "./TBActivityLogin";
import TBActivityUniversal from "./TBActivityUniversal";
import TBAdvertisement from "./TBAdvertisement";
import TBArcher from "./TBArcher";
import TBArcher<PERSON>evel from "./TBArcherLevel";
import TBArcherStar from "./TBArcherStar";
import TBArena from "./TBArena";
import TBArrow from "./TBArrow";
import TBAttribute from "./TBAttribute";
import TBBlessing from "./TBBlessing";
import TBBlessingLevel from "./TBBlessingLevel";
import TBBoxDiy from "./TBBoxDiy";
import TBBoxMagnification from "./TBBoxMagnification";
import TBBoxRandom from "./TBBoxRandom";
import TBBoxSystem from "./TBBoxSystem";
import TBBoxSystemProgress from "./TBBoxSystemProgress";
import TBChat from "./TBChat";
import TBChatText from "./TBChatText";
import TBCollection from "./TBCollection";
import TBCollectionGamePara from "./TBCollectionGamePara";
import TBCollectionHandbook from "./TBCollectionHandbook";
import TBCollectionStar from "./TBCollectionStar";
import TBCollectionTask from "./TBCollectionTask";
import TBCollectionUpgrade from "./TBCollectionUpgrade";
import TBCombatDialogue from "./TBCombatDialogue";
import TBCombatDialogueGroup from "./TBCombatDialogueGroup";
import TBCrossSever from "./TBCrossSever";
import TBDraw from "./TBDraw";
import TBDrawGroup from "./TBDrawGroup";
import TBDungeon from "./TBDungeon";
import TBDungeonBoss from "./TBDungeonBoss";
import TBDungeonBox from "./TBDungeonBox";
import TBDungeonCloud from "./TBDungeonCloud";
import TBDungeonCombat from "./TBDungeonCombat";
import TBDungeonTest from "./TBDungeonTest";
import TBDungeonThief from "./TBDungeonThief";
import TBDungeonTower from "./TBDungeonTower";
import TBEconomyAttribute from "./TBEconomyAttribute";
import TBEntry from "./TBEntry";
import TBExpand from "./TBExpand";
import TBExpedition from "./TBExpedition";
import TBExpeditionEvent from "./TBExpeditionEvent";
import TBForgeCultivateLevel from "./TBForgeCultivateLevel";
import TBForgeLevel from "./TBForgeLevel";
import TBForgeSkin from "./TBForgeSkin";
import TBFundMarkup from "./TBFundMarkup";
import TBGameLog from "./TBGameLog";
import TBGameSwitch from "./TBGameSwitch";
import TBGiftCenter from "./TBGiftCenter";
import TBGridBigReward from "./TBGridBigReward";
import TBGroup from "./TBGroup";
import TBGroupEffect from "./TBGroupEffect";
import TBGuideGroup from "./TBGuideGroup";
import TBGuideItem from "./TBGuideItem";
import TBHelp from "./TBHelp";
import TBIdleEarningsTotal from "./TBIdleEarningsTotal";
import TBItem from "./TBItem";
import TBItemAttribute from "./TBItemAttribute";
import TBItemChange from "./TBItemChange";
import TBJump from "./TBJump";
import TBLeadEquip from "./TBLeadEquip";
import TBLeadEquipCoefficient from "./TBLeadEquipCoefficient";
import TBLeadEquipMaster from "./TBLeadEquipMaster";
import TBLeadEquipQuality from "./TBLeadEquipQuality";
import TBLeadEquipStrengthen from "./TBLeadEquipStrengthen";
import TBLeadEquipSuit from "./TBLeadEquipSuit";
import TBLeadSkin from "./TBLeadSkin";
import TBLeadSkinLevel from "./TBLeadSkinLevel";
import TBLeadSkinStar from "./TBLeadSkinStar";
import TBLimitedPlay from "./TBLimitedPlay";
import TBMagicSkill from "./TBMagicSkill";
import TBMagicSkillLevel from "./TBMagicSkillLevel";
import TBMagicSkillStar from "./TBMagicSkillStar";
import TBMail from "./TBMail";
import TBMainBarrier from "./TBMainBarrier";
import TBMainChapter from "./TBMainChapter";
import TBMainMap from "./TBMainMap";
import TBMainOtherReward from "./TBMainOtherReward";
import TBMainRandomDrop from "./TBMainRandomDrop";
import TBMainRewardGroup from "./TBMainRewardGroup";
import TBMiniGame from "./TBMiniGame";
import TBMiningGroup from "./TBMiningGroup";
import TBMiningProp from "./TBMiningProp";
import TBMiningSpace from "./TBMiningSpace";
import TBMonsterBase from "./TBMonsterBase";
import TBMonsterCallFormation from "./TBMonsterCallFormation";
import TBMonsterFormation from "./TBMonsterFormation";
import TBMonsterGroup from "./TBMonsterGroup";
import TBOfficialMall from "./TBOfficialMall";
import TBOfficialShop from "./TBOfficialShop";
import TBOutJoin from "./TBOutJoin";
import TBPack from "./TBPack";
import TBParkingDispatch from "./TBParkingDispatch";
import TBParkingLot from "./TBParkingLot";
import TBParkingLotBuff from "./TBParkingLotBuff";
import TBParkingLotLevel from "./TBParkingLotLevel";
import TBParkingLotSkin from "./TBParkingLotSkin";
import TBParkingSkinLevel from "./TBParkingSkinLevel";
import TBPet from "./TBPet";
import TBPetLevel from "./TBPetLevel";
import TBPetStar from "./TBPetStar";
import TBPetSynthesis from "./TBPetSynthesis";
import TBPetTotal from "./TBPetTotal";
import TBPopup from "./TBPopup";
import TBPower from "./TBPower";
import TBPowerLevel from "./TBPowerLevel";
import TBPowerPeakedness from "./TBPowerPeakedness";
import TBPowerPromotion from "./TBPowerPromotion";
import TBPriceTemplate from "./TBPriceTemplate";
import TBPrivilegeConfig from "./TBPrivilegeConfig";
import TBPrivilegeGroup from "./TBPrivilegeGroup";
import TBProgressReward from "./TBProgressReward";
import TBRandomNickname from "./TBRandomNickname";
import TBRandomReward from "./TBRandomReward";
import TBRandomShop from "./TBRandomShop";
import TBRandomShopConfig from "./TBRandomShopConfig";
import TBRank from "./TBRank";
import TBRankReward from "./TBRankReward";
import TBRecharge from "./TBRecharge";
import TBRechargeGift from "./TBRechargeGift";
import TBRechargeTab from "./TBRechargeTab";
import TBRedPacket from "./TBRedPacket";
import TBRefresh from "./TBRefresh";
import TBRule from "./TBRule";
import TBShop from "./TBShop";
import TBSkill from "./TBSkill";
import TBSkillEffect from "./TBSkillEffect";
import TBSkillShow from "./TBSkillShow";
import TBSystemEntry from "./TBSystemEntry";
import TBTalentLeaf from "./TBTalentLeaf";
import TBTalentLeafGroup from "./TBTalentLeafGroup";
import TBTalentTree from "./TBTalentTree";
import TBTank from "./TBTank";
import TBTankLevel from "./TBTankLevel";
import TBTankStar from "./TBTankStar";
import TBTaskDetail from "./TBTaskDetail";
import TBTaskGroup from "./TBTaskGroup";
import TBTaskSeq from "./TBTaskSeq";
import TBTextLimit from "./TBTextLimit";
import TBTextSystem from "./TBTextSystem";
import TBThiefBloodReward from "./TBThiefBloodReward";
import TBTotalRecharge from "./TBTotalRecharge";
import TBTrialBoss from "./TBTrialBoss";
import TBUnion from "./TBUnion";
import TBUnionBoss from "./TBUnionBoss";
import TBUnionLevel from "./TBUnionLevel";
import TBUnionPosition from "./TBUnionPosition";
import TBUniversal from "./TBUniversal";
import TBWeapon from "./TBWeapon";
import TBWeaponLevel from "./TBWeaponLevel";
import TBWeaponStar from "./TBWeaponStar";
import TBWing from "./TBWing";
import TBWingEnchant from "./TBWingEnchant";
import TBWingLevel from "./TBWingLevel";
import TBWingLevelRewards from "./TBWingLevelRewards";
import TBWingStar from "./TBWingStar";

export const TB_MAP = {
    activity: TBActivity,
    activityGame: TBActivityGame,
    activityItem: TBActivityItem,
    activityLogin: TBActivityLogin,
    activityUniversal: TBActivityUniversal,
    advertisement: TBAdvertisement,
    archer: TBArcher,
    archerLevel: TBArcherLevel,
    archerStar: TBArcherStar,
    arena: TBArena,
    arrow: TBArrow,
    attribute: TBAttribute,
    blessing: TBBlessing,
    blessingLevel: TBBlessingLevel,
    boxDiy: TBBoxDiy,
    boxMagnification: TBBoxMagnification,
    boxRandom: TBBoxRandom,
    boxSystem: TBBoxSystem,
    boxSystemProgress: TBBoxSystemProgress,
    chat: TBChat,
    chatText: TBChatText,
    collection: TBCollection,
    collectionGamePara: TBCollectionGamePara,
    collectionHandbook: TBCollectionHandbook,
    collectionStar: TBCollectionStar,
    collectionTask: TBCollectionTask,
    collectionUpgrade: TBCollectionUpgrade,
    combatDialogue: TBCombatDialogue,
    combatDialogueGroup: TBCombatDialogueGroup,
    crossSever: TBCrossSever,
    draw: TBDraw,
    drawGroup: TBDrawGroup,
    dungeon: TBDungeon,
    dungeonBoss: TBDungeonBoss,
    dungeonBox: TBDungeonBox,
    dungeonCloud: TBDungeonCloud,
    dungeonCombat: TBDungeonCombat,
    dungeonTest: TBDungeonTest,
    dungeonThief: TBDungeonThief,
    dungeonTower: TBDungeonTower,
    economyAttribute: TBEconomyAttribute,
    entry: TBEntry,
    expand: TBExpand,
    expedition: TBExpedition,
    expeditionEvent: TBExpeditionEvent,
    forgeCultivateLevel: TBForgeCultivateLevel,
    forgeLevel: TBForgeLevel,
    forgeSkin: TBForgeSkin,
    fundMarkup: TBFundMarkup,
    gameLog: TBGameLog,
    gameSwitch: TBGameSwitch,
    giftCenter: TBGiftCenter,
    gridBigReward: TBGridBigReward,
    group: TBGroup,
    groupEffect: TBGroupEffect,
    guideGroup: TBGuideGroup,
    guideItem: TBGuideItem,
    help: TBHelp,
    idleEarningsTotal: TBIdleEarningsTotal,
    item: TBItem,
    itemAttribute: TBItemAttribute,
    itemChange: TBItemChange,
    jump: TBJump,
    leadEquip: TBLeadEquip,
    leadEquipCoefficient: TBLeadEquipCoefficient,
    leadEquipMaster: TBLeadEquipMaster,
    leadEquipQuality: TBLeadEquipQuality,
    leadEquipStrengthen: TBLeadEquipStrengthen,
    leadEquipSuit: TBLeadEquipSuit,
    leadSkin: TBLeadSkin,
    leadSkinLevel: TBLeadSkinLevel,
    leadSkinStar: TBLeadSkinStar,
    limitedPlay: TBLimitedPlay,
    magicSkill: TBMagicSkill,
    magicSkillLevel: TBMagicSkillLevel,
    magicSkillStar: TBMagicSkillStar,
    mail: TBMail,
    mainBarrier: TBMainBarrier,
    mainChapter: TBMainChapter,
    mainMap: TBMainMap,
    mainOtherReward: TBMainOtherReward,
    mainRandomDrop: TBMainRandomDrop,
    mainRewardGroup: TBMainRewardGroup,
    miniGame: TBMiniGame,
    miningGroup: TBMiningGroup,
    miningProp: TBMiningProp,
    miningSpace: TBMiningSpace,
    monsterBase: TBMonsterBase,
    monsterCallFormation: TBMonsterCallFormation,
    monsterFormation: TBMonsterFormation,
    monsterGroup: TBMonsterGroup,
    officialMall: TBOfficialMall,
    officialShop: TBOfficialShop,
    outJoin: TBOutJoin,
    pack: TBPack,
    parkingDispatch: TBParkingDispatch,
    parkingLot: TBParkingLot,
    parkingLotBuff: TBParkingLotBuff,
    parkingLotLevel: TBParkingLotLevel,
    parkingLotSkin: TBParkingLotSkin,
    parkingSkinLevel: TBParkingSkinLevel,
    pet: TBPet,
    petLevel: TBPetLevel,
    petStar: TBPetStar,
    petSynthesis: TBPetSynthesis,
    petTotal: TBPetTotal,
    popup: TBPopup,
    power: TBPower,
    powerLevel: TBPowerLevel,
    powerPeakedness: TBPowerPeakedness,
    powerPromotion: TBPowerPromotion,
    priceTemplate: TBPriceTemplate,
    privilegeConfig: TBPrivilegeConfig,
    privilegeGroup: TBPrivilegeGroup,
    progressReward: TBProgressReward,
    randomNickname: TBRandomNickname,
    randomReward: TBRandomReward,
    randomShop: TBRandomShop,
    randomShopConfig: TBRandomShopConfig,
    rank: TBRank,
    rankReward: TBRankReward,
    recharge: TBRecharge,
    rechargeGift: TBRechargeGift,
    rechargeTab: TBRechargeTab,
    redPacket: TBRedPacket,
    refresh: TBRefresh,
    rule: TBRule,
    shop: TBShop,
    skill: TBSkill,
    skillEffect: TBSkillEffect,
    skillShow: TBSkillShow,
    systemEntry: TBSystemEntry,
    talentLeaf: TBTalentLeaf,
    talentLeafGroup: TBTalentLeafGroup,
    talentTree: TBTalentTree,
    tank: TBTank,
    tankLevel: TBTankLevel,
    tankStar: TBTankStar,
    taskDetail: TBTaskDetail,
    taskGroup: TBTaskGroup,
    taskSeq: TBTaskSeq,
    textLimit: TBTextLimit,
    textSystem: TBTextSystem,
    thiefBloodReward: TBThiefBloodReward,
    totalRecharge: TBTotalRecharge,
    trialBoss: TBTrialBoss,
    union: TBUnion,
    unionBoss: TBUnionBoss,
    unionLevel: TBUnionLevel,
    unionPosition: TBUnionPosition,
    universal: TBUniversal,
    weapon: TBWeapon,
    weaponLevel: TBWeaponLevel,
    weaponStar: TBWeaponStar,
    wing: TBWing,
    wingEnchant: TBWingEnchant,
    wingLevel: TBWingLevel,
    wingLevelRewards: TBWingLevelRewards,
    wingStar: TBWingStar,
}
