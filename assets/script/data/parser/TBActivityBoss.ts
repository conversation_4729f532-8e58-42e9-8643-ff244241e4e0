import DataParser from "../../../nsn/data/DataParser";
import DataActivityBoss from "../extend/DataActivityBoss";

export default class TBActivityBoss extends DataParser<DataActivityBoss> {
    /**
     * 根据活动id获取对应数据
     * @param activityId
     * @returns
     */
    public getDataByActivityId(activityId: number): DataActivityBoss {
        return this.dataList.find((item) => item.activityId === activityId);
    }
}
