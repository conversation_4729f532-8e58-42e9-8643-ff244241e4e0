import DataParser from "../../../nsn/data/DataParser";
import { DungeonType } from "../../game/Combat";
import DataDungeonCombat from "../extend/DataDungeonCombat";

export default class TBDungeonCombat extends DataParser<DataDungeonCombat> {
    /**
     * 获取对应数据
     * @param type
     * @returns
     */
    public getDataByType(type: DungeonType): DataDungeonCombat {
        return this.dataList.find((e) => e.type === type);
    }
}
