/*
 * @Author: linyb
 * @Date: 2022-08-01 11:28:34
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-08-15 15:20:01
 */
import Loader from "../../../core/Loader";
import Pool from "../../../core/Pool";
import StaticCache from "../../../recycle/StaticCache";
import Logger from "../../../util/Logger";
import ListView, { SelectedType, SlideType, TemplateType } from "./ListView";
import ListViewItem from "./ListViewItem";

// 初始化各种..
ListView.prototype._init = function (): void {
    const t: any = this;
    if (t._inited) {
        return;
    }

    t._scrollView = t.node.getComponent(cc.ScrollView);

    t.content = t._scrollView.content;
    if (!t.content) {
        cc.error(t.node.name + "'s cc.ScrollView unset content!");
        return;
    }

    t._layout = t.content.getComponent(cc.Layout);

    t._align = t._layout.type; // 排列模式
    t._resizeMode = t._layout.resizeMode; // 自适应模式
    t._startAxis = t._layout.startAxis;

    t._topGap = t._layout.paddingTop; // 顶边距
    t._rightGap = t._layout.paddingRight; // 右边距
    t._bottomGap = t._layout.paddingBottom; // 底边距
    t._leftGap = t._layout.paddingLeft; // 左边距

    t._columnGap = t._layout.spacingX; // 列距
    t._lineGap = t._layout.spacingY; // 行距

    t._verticalDir = t._layout.verticalDirection; // 垂直排列子节点的方向
    t._horizontalDir = t._layout.horizontalDirection; // 水平排列子节点的方向

    t.setTemplateItem(
        Loader.getInstance().instantiate(t.templateType === TemplateType.PREFAB ? t.tmpPrefab : t.tmpNode, null, true)
    );

    if (t.templateType === TemplateType.PREFAB) {
        if (Pool.getInstance().has(this.tmpPrefab.name)) {
            StaticCache.getInstance().setPersistent(this.tmpPrefab);
        }
    }
    if (t._slideMode === SlideType.ADHERING || t._slideMode === SlideType.PAGE) {
        // 特定的滑动模式处理
        t._scrollView.inertia = false;
        t._scrollView._onMouseWheel = () => {
            return;
        };
    }
    if (!t.virtual) {
        // lackCenter 仅支持 Virtual 模式
        t.lackCenter = false;
    }

    t._lastDisplayData = []; // 最后一次刷新的数据
    t.displayData = []; // 当前数据
    t._pool = new cc.NodePool(this._itemTmp.name); // 这是个池子..
    t._forceUpdate = false; // 是否强制更新
    t._updateCounter = 0; // 当前分帧渲染帧数
    t._updateDone = true; // 分帧渲染是否完成

    t.curPageNum = 0; // 当前页数

    if (t.cyclic || 0) {
        t._scrollView._processAutoScrolling = this._processAutoScrolling.bind(t);
        t._scrollView._startBounceBackIfNeeded = () => {
            return false;
        };
        /*
         * t._scrollView._scrollChildren = function () {
         *     return false;
         * }
         */
    }

    switch (t._align) {
        case cc.Layout.Type.HORIZONTAL: {
            switch (t._horizontalDir) {
                case cc.Layout.HorizontalDirection.LEFT_TO_RIGHT:
                    t._alignCalcType = 1;
                    break;
                case cc.Layout.HorizontalDirection.RIGHT_TO_LEFT:
                    t._alignCalcType = 2;
                    break;
                default:
                    break;
            }
            break;
        }
        case cc.Layout.Type.VERTICAL: {
            switch (t._verticalDir) {
                case cc.Layout.VerticalDirection.TOP_TO_BOTTOM:
                    t._alignCalcType = 3;
                    break;
                case cc.Layout.VerticalDirection.BOTTOM_TO_TOP:
                    t._alignCalcType = 4;
                    break;
                default:
                    break;
            }
            break;
        }
        case cc.Layout.Type.GRID: {
            switch (t._startAxis) {
                case cc.Layout.AxisDirection.HORIZONTAL:
                    switch (t._verticalDir) {
                        case cc.Layout.VerticalDirection.TOP_TO_BOTTOM:
                            t._alignCalcType = 3;
                            break;
                        case cc.Layout.VerticalDirection.BOTTOM_TO_TOP:
                            t._alignCalcType = 4;
                            break;
                        default:
                            break;
                    }
                    break;
                case cc.Layout.AxisDirection.VERTICAL:
                    switch (t._horizontalDir) {
                        case cc.Layout.HorizontalDirection.LEFT_TO_RIGHT:
                            t._alignCalcType = 1;
                            break;
                        case cc.Layout.HorizontalDirection.RIGHT_TO_LEFT:
                            t._alignCalcType = 2;
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
            break;
        }
        default:
            break;
    }
    /*
     * 清空 content
     * t.content.children.forEach((child: cc.Node) => {
     *     child.removeFromParent();
     *     if (child != t.tmpNode && child.isValid)
     *         child.destroy();
     * });
     */
    t.content.removeAllChildren();
    t._inited = true;
};

/**
 * 创建或更新Item（虚拟列表用）
 * @param {Object} data 数据
 */
ListView.prototype._createOrUpdateItem = function (data: any): void {
    let item: any = this.getItemByListId(data.id);
    const index = data.id % this._actualNumItems;
    if (!item) {
        const nodeItem: cc.Node = this._itemTmp;
        const name = nodeItem.name;
        let canGet = false;
        if (Pool.getInstance().has(name)) {
            const size = Pool.getInstance().size(name);
            canGet = size > 0;
            if (canGet) {
                item = Pool.getInstance().get(name);
            } else {
                item = Loader.getInstance().instantiate(nodeItem);
            }
        } else {
            // 如果不存在
            canGet = this._pool.size() > 0;
            if (canGet) {
                item = this._pool.get();
            } else {
                item = Loader.getInstance().instantiate(this._itemTmp);
            }
        }
        if (item._listId !== data.id) {
            item._listId = data.id;
            item.setContentSize(this._itemSize);
        }
        item.setPosition(cc.v2(data.x, data.y));
        this._resetItemSize(item);
        this.content.addChild(item);
        if (canGet && this._needUpdateWidget) {
            const widget: cc.Widget = item.getComponent(cc.Widget);
            if (widget) {
                widget.updateAlignment();
            }
        }
        item.setSiblingIndex(this.content.childrenCount - 1);

        const listItem = item.getComponent(item.name);
        item.listItem = listItem;
        if (listItem) {
            listItem.listId = data.id;
            listItem.list = this;
            listItem._registerEvent();
            listItem.updateData &&
                listItem.updateData(
                    this.listData[index],
                    Array.isArray(this.listExtraData) ? this.listExtraData[index] : null
                );
        }
        if (this.renderEvent) {
            cc.Component.EventHandler.emitEvents([this.renderEvent], item, index);
        }
    } else if (this._forceUpdate && this.renderEvent) {
        // 强制更新
        item.setPosition(cc.v2(data.x, data.y));
        const listItem = item.getComponent(item.name);
        if (listItem) {
            listItem.updateData &&
                listItem.updateData(
                    this.listData[index],
                    Array.isArray(this.listExtraData) ? this.listExtraData[index] : null
                );
        }
        this._resetItemSize(item);
        if (this.renderEvent) {
            cc.Component.EventHandler.emitEvents([this.renderEvent], item, index);
        }
    }
    this._resetItemSize(item);

    this._updateListItem(item.listItem);
    if (this._lastDisplayData.indexOf(data.id) < 0) {
        this._lastDisplayData.push(data.id);
    }
};

/**
 * 删除显示区域以外的Item
 */
ListView.prototype._delRedundantItem = function (): void {
    if (this._virtual) {
        const arr: any[] = this._getOutsideItem();
        for (let n: number = arr.length - 1; n >= 0; n--) {
            const item: any = arr[n];
            if (this._scrollItem && item._listId === this._scrollItem._listId) {
                continue;
            }
            if (Pool.getInstance().has(item.name)) {
                Pool.getInstance().put(item);
            } else {
                this._pool.put(item);
            }
            for (let m: number = this._lastDisplayData.length - 1; m >= 0; m--) {
                if (this._lastDisplayData[m] === item._listId) {
                    this._lastDisplayData.splice(m, 1);
                    break;
                }
            }
        }
    } else {
        while (this.content.childrenCount > this._numItems) {
            this._delSingleItem(this.content.children[this.content.childrenCount - 1]);
        }
    }
};

/**
 * 设置列表数据
 * @param data
 */
ListView.prototype.setListData = function (data: any[]): void {
    this.listData = data;
    this.numItems = data.length;
};

/**
 * 获取列表数据
 * @returns
 */
ListView.prototype.getListData = function (): any[] {
    return this.listData || [];
};

/**
 * 获取列表数据
 * @returns
 */
ListView.prototype.getListDataByIndex = function (index: number): any {
    return index >= 0 ? (this.listData ? (index < this.listData.length ? this.listData[index] : null) : null) : null;
};

/**
 * 设置列表数据
 * @param data
 */
ListView.prototype.setListExtraData = function (data: any[]): void {
    this.listExtraData = data;
};

/**
 * 获取列表数据
 * @returns
 */
ListView.prototype.getListExtraData = function (): any[] {
    return this.listExtraData || [];
};

/**
 * 清理列表数据
 */
ListView.prototype.cleanList = function (): void {
    this.listData = []; // 数据清理
    this.listExtraData = []; //额外数据清理
    this.numItems = 0; // 子节点清理
};

/**
 * 设置多选开关
 */
ListView.prototype.setMultiple = function (isOpen: boolean): void {
    if (this.selectedMode !== SelectedType.MULT) {
        Logger.warn("ListView", "需设置ListView选择模式:SelectedType.MULT");
        return;
    }
    const children: cc.Node[] = this._scrollView.content.children;
    this.isMultiple = isOpen;
    // 操作当前展示的节点
    for (const node of children) {
        const comp = node.getComponent(ListViewItem);
        comp.setMultiple();
    }
};

/**
 * 设置全选
 * @param selected
 */
ListView.prototype.allSelected = function (isSelected: boolean): void {
    if (this.selectedMode !== SelectedType.MULT) {
        return;
    }
    if (!this.isMultiple) {
        return;
    }
    const count: number = this._scrollView.content.childrenCount;
    const selecteds: number[] = this.getMultSelected();
    for (let i = count; i >= 0; i--) {
        if (isSelected) {
            if (!selecteds.includes(i)) {
                this.setSelectedId(i);
            }
        } else {
            if (selecteds.includes(i)) {
                this.setSelectedId(i);
            }
        }
    }
};

/**
 * 获取多选数据
 */
ListView.prototype.getMultSelected = function (): number[] {
    return this.multSelected;
};

/**
 * 设置已选
 * @param listId
 */
ListView.prototype.setSelectedId = function (listId: number): void {
    if (this.selectedMode === SelectedType.MULT) {
        if (!this.isMultiple) {
            return;
        }
    }
    this.selectedId = listId;
};

/**
 * 清理选择
 */
ListView.prototype.cleanSelected = function (): void {
    this.allSelected(false);
    this.multSelected = []; // 多选数据清理
    this._selectedId = -1; // 当前选择下标清理
    this.isMultiple = false; // 关闭多选
};

/**
 * 关闭界面、节点回收
 */
ListView.prototype.recycleWhenClosed = function (): void {
    const nodeItem: cc.Node = this._itemTmp;
    const name = nodeItem.name;
    if (!Pool.getInstance().has(name)) {
        // 不属于全局对象池不执行全局回收
        Logger.error("ListView", "不属于全局对象池不可执行全局回收");
        return;
    }
    const children: cc.Node[] = this._scrollView.content.children;
    for (let i = children.length - 1; i >= 0; i--) {
        const item = children[i];
        Pool.getInstance().put(item);
    }
};

ListView.prototype.update = function (): void {
    if (this.frameByFrameRenderNum <= 0 || this._updateDone) {
        return;
    }
    // cc.log(this.displayData.length, this._updateCounter, this.displayData[this._updateCounter]);
    if (this._virtual) {
        const len: number =
            this._updateCounter + this.frameByFrameRenderNum > this.displayItemNum
                ? this.displayItemNum
                : this._updateCounter + this.frameByFrameRenderNum;
        for (let n: number = this._updateCounter; n < len; n++) {
            const data: any = this.displayData[n];
            if (data) {
                this._createOrUpdateItem(data);
            }
        }

        if (this._updateCounter >= this.displayItemNum - 1) {
            // 最后一个
            this._updateDone = true;
            this._delRedundantItem();
            this._forceUpdate = false;
            this._calcNearestItem();
            if (this.slideMode === SlideType.PAGE) {
                this.curPageNum = this.nearestListId;
            }
            this.frameByFrameRenderNum = 0;
        } else {
            this._updateCounter += this.frameByFrameRenderNum;
        }
    } else {
        if (this._updateCounter < this._numItems) {
            const len: number =
                this._updateCounter + this.frameByFrameRenderNum > this._numItems
                    ? this._numItems
                    : this._updateCounter + this.frameByFrameRenderNum;
            for (let n: number = this._updateCounter; n < len; n++) {
                this._createOrUpdateItem2(n);
            }
            this._updateCounter += this.frameByFrameRenderNum;
        } else {
            this._updateDone = true;
            this._calcNearestItem();
            if (this.slideMode === SlideType.PAGE) {
                this.curPageNum = this.nearestListId;
            }
        }
    }
};

ListView.prototype.onDestroy = function (): void {
    if (this._itemTmp && this._itemTmp.isValid) {
        this._itemTmp.destroy();
    }
    if (this.tmpNode && this.tmpNode.isValid) {
        this.tmpNode.destroy();
    }
    this._pool && this._pool.clear();
};
/**
 * 清理touchUp未触发时，引起删除单元异常的问题
 */
ListView.prototype.cleanScrollItem = function (): void {
    const t: any = this;
    t._scrollPos = null;
    if (t._slideMode === SlideType.ADHERING) {
        if (this.adhering) {
            this._adheringBarrier = true;
        }
        t.adhere();
    } else if (t._slideMode === SlideType.PAGE) {
        if (t._beganPos !== null) {
            this._pageAdhere();
        } else {
            t.adhere();
        }
    }
    this._scrollItem = null;
};

/**
 * 下一页
 */
ListView.prototype.nextPage = function (timeInSecond: number = 0.5): void {
    const t: any = this;
    if (!t.checkInited()) {
        return;
    }
    if (t.nearestListId === t._numItems - 1) {
        return;
    }
    const nextPage = t.nearestListId + 1;
    this.skipPage(nextPage, timeInSecond);
};

/**
 * 上一页
 */
ListView.prototype.prePage = function (timeInSecond: number = 0.5): void {
    const t: any = this;
    if (!t.checkInited()) {
        return;
    }
    if (t.nearestListId === 0) {
        return;
    }
    let prePage = 0;
    if (t.nearestListId === t._numItems - 1) {
        let showItemCount = 0;
        if (t._align === cc.Layout.Type.HORIZONTAL) {
            showItemCount = (t.node.width - t._leftGap) / (t._itemSize.width + t._columnGap);
        } else if (t._align === cc.Layout.Type.VERTICAL) {
            showItemCount = (t.node.height - t._topGap) / (t._itemSize.height + t._lineGap);
        }
        prePage = t.nearestListId - showItemCount;
    } else {
        prePage = t.nearestListId - 1;
    }
    t.skipPage(prePage, timeInSecond);
};

/**
 * 跳转到第几页
 */
ListView.prototype.skipPage = function (pageNum: number, timeInSecond: number): void {
    const t: any = this;
    if (!t.checkInited()) {
        return;
    }
    if (t._slideMode === SlideType.NORMAL) {
        return cc.error("This function is not allowed to be called, Must SlideMode !== NORMAL!");
    }
    if (pageNum < 0 || pageNum >= t._numItems) {
        return;
    }
    t.curPageNum = pageNum;
    if (t.pageChangeEvent) {
        cc.Component.EventHandler.emitEvents([t.pageChangeEvent], pageNum);
    }
    t.scrollTo(pageNum, timeInSecond);
};
