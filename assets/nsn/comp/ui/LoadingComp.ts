/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-07 10:29:32
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 16:08:56
 */
import { Base64 } from "js-base64";
import pako from "pako";
import { HttpPath } from "../../../script/config/HttpConfig";
import i18n from "../../../script/config/i18n/I18n";
import {
    LOADING_PERCENT,
    PRELOAD_ATLAS,
    PRELOAD_CLIENT_CONFIG,
    PRELOAD_ITEM,
    PRELOAD_PREFAB,
    PRELOAD_SCENE,
} from "../../../script/config/LoadingConfig";
import ClientConfig from "../../core/ClientConfig";
import Http, { HttpParams, IHttpResp } from "../../core/Http";
import Language from "../../core/Language";
import Loader from "../../core/Loader";
import Loading, {
    ILoadingOptions,
    LoadingErrorTypeEvent,
    LoadingEvent,
    LoadingProgressEvent,
    LoadingType,
} from "../../core/Loading";
import Pool from "../../core/Pool";
import Socket, { SocketEvent } from "../../core/Socket";
import Data from "../../data/Data";
import { IDataConfig } from "../../data/DataInterface";
import Recycle from "../../recycle/Recycle";
import Logger from "../../util/Logger";

const { ccclass, disallowMultiple, menu } = cc._decorator;

/**
 * 加载阶段
 */
enum LoadingPhrase {
    Unknown, // 未知
    Unloading, // 未加载
    Loading, // 加载中
    Failed, // 加载失败
    Success, // 加载成功
}

// 服务器连接超时
const SOCKET_CONNECT_TIME_OUT = 10;

// 本地数据分块尺寸
const LOCAL_DATA_TRUNK_SIZE = 1024 * 64;
const LOCAL_DATA_PERCENT = 0.9;
const REMOTE_DATA_PERCENT = 0.1;

@ccclass
@disallowMultiple()
@menu("nsn/LoadingComp")
export default class LoadingComp extends cc.Component {
    private options: ILoadingOptions = null;
    // 加载阶段
    private loadingResPhrase: LoadingPhrase = LoadingPhrase.Unknown;
    // 加载类型
    private loadingType: LoadingType = LoadingType.LocalData;
    // 网络状态
    private loadingNetworkPhrase: LoadingPhrase = LoadingPhrase.Unknown;
    // 进度
    private progress: number = 0;
    // 单资源加载进度
    private loadingIndicator: { res: any[]; index: number } = null;
    // 服务器连接超时
    private connectTime: number = 0;

    protected onLoad(): void {
        this.registerHandler();
    }

    protected registerHandler(): void {
        Socket.getInstance().on(
            SocketEvent.Error,
            () => {
                this.loadNetworkFailed();
            },
            this
        );
        Loading.getInstance().on(
            LoadingEvent.Start,
            (options: ILoadingOptions) => {
                this.begin(options);
            },
            this
        );
        Loading.getInstance().on(
            LoadingEvent.PlayerDataInitSuccess,
            () => {
                this.loadNetworkSuccess();
            },
            this
        );
        Loading.getInstance().on(
            LoadingEvent.PlayerDataInitFailed,
            (err: string) => {
                this.loadNetworkFailed(err);
            },
            this
        );
    }

    /**
     * 开始加载
     * @param socketUrl
     * @param httpUrl
     * @param args
     * @returns
     */
    public begin(options: ILoadingOptions): void {
        this.options = options;
        this.loadingResPhrase = LoadingPhrase.Unloading;
        this.loadingType = LoadingType.LocalData;
        this.loadingNetworkPhrase = LoadingPhrase.Unloading;
        this.progress = 0;
        this.loadingIndicator = null;
        this.connectTime = 0;

        Loading.getInstance().emit(LoadingProgressEvent.ProgressStart);
    }

    protected update(dt: number): void {
        if (this.loadingResPhrase === LoadingPhrase.Unloading) {
            switch (this.loadingType) {
                case LoadingType.LocalData:
                    this.loadLocalData();
                    break;
                case LoadingType.RemoteData:
                    this.loadRemoteData();
                    break;
                case LoadingType.Scene:
                    this.loadScene();
                    break;
                case LoadingType.Atlas:
                    this.loadAtlas();
                    break;
                case LoadingType.Prefab:
                    this.loadPrefab();
                    break;
                case LoadingType.Item:
                    this.loadItem();
                    break;
                case LoadingType.ClientConfig:
                    this.loadClientConfig();
                    break;
                default:
                    break;
            }
        }
        if (this.loadingNetworkPhrase === LoadingPhrase.Loading) {
            this.updateLoadingNetwork(dt);
        }
    }

    /**
     * 用户数据初始化失败
     * @param message
     * @returns
     */
    public loadNetworkFailed(message: string = i18n.nsn0007): void {
        Logger.info("游戏加载", "连接服务器失败...");
        this.loadingNetworkPhrase = LoadingPhrase.Failed;
        if (this.loadingResPhrase === LoadingPhrase.Failed) {
            return;
        }
        Loading.getInstance().emit(
            LoadingProgressEvent.ProgressError,
            LoadingErrorTypeEvent.Error,
            LoadingType.Network,
            message
        );
        Socket.getInstance().close();
    }

    /**
     * 用户数据初始化成功
     */
    public loadNetworkSuccess(): void {
        Logger.info("游戏加载", "连接服务器成功...");
        this.loadingNetworkPhrase = LoadingPhrase.Success;
        if (this.loadingResPhrase === LoadingPhrase.Success) {
            Loading.getInstance().emit(LoadingProgressEvent.ProgressEnd);
        }
    }

    /**
     * 用户数据初始化超时
     */
    private updateLoadingNetwork(dt: number): void {
        this.connectTime += dt;
        if (this.connectTime >= SOCKET_CONNECT_TIME_OUT) {
            this.loadNetworkFailed(i18n.nsn0005);
        }
        if (this.loadingResPhrase === LoadingPhrase.Success) {
            Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.Network);
        }
    }

    /**
     * 加载网络数据
     */
    public loadNetwork(): void {
        Logger.info("游戏加载", "连接服务器...");
        this.loadingNetworkPhrase = LoadingPhrase.Loading;
        Socket.getInstance().connect(this.options.socketUrl, ...this.options.socketParams);
    }

    /**
     * 加载本地配置
     */
    private loadLocalData(): void {
        this.loadingResPhrase = LoadingPhrase.Loading;
        if (!this.loadingIndicator) {
            Logger.info("游戏加载", "加载本地配置...");
            const path = "data/gameConfig.bin";
            Loader.getInstance().loadBinary(
                path,
                (res: cc.BufferAsset) => {
                    // @ts-ignore
                    const buffer = res._buffer;
                    const chunks: Uint8Array[] = [];
                    for (let i = 0; i < buffer.byteLength; i += LOCAL_DATA_TRUNK_SIZE) {
                        const chunk = new Uint8Array(buffer, i, Math.min(LOCAL_DATA_TRUNK_SIZE, buffer.byteLength - i));
                        chunks.push(chunk);
                    }
                    const inflator = new pako.Inflate({ to: "string" });
                    this.loadingIndicator = { res: [], index: 0 };
                    // 逐块解压
                    chunks.forEach((chunk, index) => {
                        this.loadingIndicator.res.push([chunk, inflator]);
                    });
                    this.loadingResPhrase = LoadingPhrase.Unloading;
                    Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.LocalData);
                },
                () => {
                    this.loadingResPhrase = LoadingPhrase.Failed;
                    Loading.getInstance().emit(
                        LoadingProgressEvent.ProgressError,
                        LoadingErrorTypeEvent.Retry,
                        LoadingType.LocalData,
                        i18n.nsn0008
                    );
                }
            );
        } else {
            const count = this.loadingIndicator.res.length;
            const chunk: Uint8Array = this.loadingIndicator.res[this.loadingIndicator.index][0];
            const inflator: pako.Inflate = this.loadingIndicator.res[this.loadingIndicator.index][1];
            inflator.push(chunk, this.loadingIndicator.index === count - 1);
            this.loadingIndicator.index++;
            if (inflator.err) {
                this.loadingResPhrase = LoadingPhrase.Failed;
                Loading.getInstance().emit(
                    LoadingProgressEvent.ProgressError,
                    LoadingErrorTypeEvent.Retry,
                    LoadingType.LocalData,
                    i18n.nsn0008
                );
            } else {
                this.progress += (LOADING_PERCENT.DATA * LOCAL_DATA_PERCENT) / count;
                this.loadingResPhrase = LoadingPhrase.Unloading;
                Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.LocalData);
                if (this.loadingIndicator.index === count) {
                    this.loadingIndicator = null;
                    const data: IDataConfig[] = JSON.parse(inflator.result as string);
                    for (const e of data) {
                        Data.getInstance().setConfig(e.name, e);
                    }
                    this.loadingType = LoadingType.RemoteData;

                    // 释放inflator
                    inflator.result = null;
                    inflator.header = null;
                    // @ts-ignore
                    inflator.strm = null;
                    // 手动gc
                    Recycle.getInstance().gc();
                }
            }
        }
    }

    /**
     * 加载远程配置
     * @returns
     */
    private loadRemoteData(): void {
        Logger.info("游戏加载", "加载远程配置...");
        this.loadingResPhrase = LoadingPhrase.Loading;
        const pa = new HttpParams();
        pa.setParam("language", Language.getInstance().getLanguage());
        pa.setParam("logicServerId", this.options.serverId);
        Http.post(
            this.options.httpUrl,
            HttpPath.GetConfs,
            pa,
            (result: IHttpResp) => {
                const decode: (val: string) => IDataConfig[] = (val: string) => {
                    if (!val) {
                        return [];
                    }
                    const str = Base64.atob(val);
                    const buffer = new ArrayBuffer(str.length);

                    const view = new Uint8Array(buffer);
                    for (let i = 0; i < str.length; i++) {
                        view[i] = str.charCodeAt(i);
                    }

                    const data = pako.inflate(buffer, { to: "string" });
                    return Object.values<IDataConfig>(JSON.parse(data));
                };

                const remote = decode(result.data?.remote);
                for (const e of remote) {
                    Data.getInstance().setConfig(e.name, e, true);
                }

                const diff = decode(result.data?.diff);
                for (const e of diff) {
                    const data = Data.getInstance().getConfig(e.name);
                    for (const key in e.data) {
                        data.data[key] = e.data[key];
                    }
                }

                Data.getInstance().parse();

                this.progress += LOADING_PERCENT.DATA * REMOTE_DATA_PERCENT;
                this.loadingResPhrase = LoadingPhrase.Unloading;
                this.loadingType = LoadingType.ClientConfig;
                Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.RemoteData);

                // 发起网络请求
                this.loadNetwork();
            },
            () => {
                this.loadingResPhrase = LoadingPhrase.Failed;
                Loading.getInstance().emit(
                    LoadingProgressEvent.ProgressError,
                    LoadingErrorTypeEvent.Retry,
                    LoadingType.RemoteData,
                    i18n.nsn0009
                );
            }
        );
    }

    /**
     * 加载客户端配置
     */
    private loadClientConfig(): void {
        this.loadingResPhrase = LoadingPhrase.Loading;
        const count = PRELOAD_CLIENT_CONFIG.length;
        if (count === 0) {
            this.progress += LOADING_PERCENT.CLIENT_CONFIG;
            this.loadingResPhrase = LoadingPhrase.Unloading;
            this.loadingType = LoadingType.Scene;
            Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.ClientConfig);
        } else {
            if (!this.loadingIndicator) {
                Logger.info("游戏加载", "加载客户端配置...");
                this.loadingIndicator = { res: PRELOAD_CLIENT_CONFIG, index: 0 };
            }
            const [path, name] = this.loadingIndicator.res[this.loadingIndicator.index];
            Loader.getInstance().loadJson(
                path,
                (json: any) => {
                    ClientConfig.getInstance().setConfig(name, json);

                    this.loadingIndicator.index++;
                    this.progress += LOADING_PERCENT.CLIENT_CONFIG / count;
                    this.loadingResPhrase = LoadingPhrase.Unloading;
                    Loading.getInstance().emit(
                        LoadingProgressEvent.Progressing,
                        this.progress,
                        LoadingType.ClientConfig
                    );
                    if (this.loadingIndicator.index === count) {
                        this.loadingIndicator = null;
                        this.loadingType = LoadingType.Scene;
                    }
                },
                () => {
                    this.loadingResPhrase = LoadingPhrase.Failed;
                    Loading.getInstance().emit(
                        LoadingProgressEvent.ProgressError,
                        LoadingErrorTypeEvent.Retry,
                        LoadingType.ClientConfig,
                        i18n.nsn0045
                    );
                }
            );
        }
    }

    /**
     * 加载场景
     */
    private loadScene(): void {
        Logger.info("游戏加载", "加载场景...");
        this.loadingResPhrase = LoadingPhrase.Loading;
        cc.director.preloadScene(PRELOAD_SCENE, (err) => {
            if (err) {
                this.loadingResPhrase = LoadingPhrase.Failed;
                Loading.getInstance().emit(
                    LoadingProgressEvent.ProgressError,
                    LoadingErrorTypeEvent.Retry,
                    LoadingType.Scene,
                    i18n.nsn0010
                );
            } else {
                this.progress += LOADING_PERCENT.SCENE;
                this.loadingResPhrase = LoadingPhrase.Unloading;
                this.loadingType = LoadingType.Atlas;
                Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.Scene);
            }
        });
    }

    /**
     * 加载图集
     */
    private loadAtlas(): void {
        this.loadingResPhrase = LoadingPhrase.Loading;
        const count = PRELOAD_ATLAS.length;
        if (count === 0) {
            this.progress += LOADING_PERCENT.ATLAS;
            this.loadingResPhrase = LoadingPhrase.Unloading;
            this.loadingType = LoadingType.Prefab;
            Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.Atlas);
        } else {
            if (!this.loadingIndicator) {
                Logger.info("游戏加载", "加载图集...");
                this.loadingIndicator = { res: PRELOAD_ATLAS, index: 0 };
            }
            Loader.getInstance().loadSpriteAtlas(
                this.loadingIndicator.res[this.loadingIndicator.index],
                () => {
                    this.loadingIndicator.index++;
                    this.progress += LOADING_PERCENT.ATLAS / count;
                    this.loadingResPhrase = LoadingPhrase.Unloading;
                    Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.Atlas);
                    if (this.loadingIndicator.index === count) {
                        this.loadingIndicator = null;
                        this.loadingType = LoadingType.Prefab;
                    }
                },
                () => {
                    this.loadingResPhrase = LoadingPhrase.Failed;
                    Loading.getInstance().emit(
                        LoadingProgressEvent.ProgressError,
                        LoadingErrorTypeEvent.Retry,
                        LoadingType.Atlas,
                        i18n.nsn0011
                    );
                }
            );
        }
    }

    /**
     * 加载预制体
     */
    private loadPrefab(): void {
        this.loadingResPhrase = LoadingPhrase.Loading;
        const count = PRELOAD_PREFAB.length;
        if (count === 0) {
            this.progress += LOADING_PERCENT.PREFAB;
            this.loadingResPhrase = LoadingPhrase.Unloading;
            this.loadingType = LoadingType.Item;
            Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.Prefab);
        } else {
            if (!this.loadingIndicator) {
                Logger.info("游戏加载", "加载预制体...");
                this.loadingIndicator = { res: PRELOAD_PREFAB, index: 0 };
            }
            Loader.getInstance().loadPrefab(
                this.loadingIndicator.res[this.loadingIndicator.index],
                () => {
                    this.loadingIndicator.index++;
                    this.progress += LOADING_PERCENT.PREFAB / count;
                    this.loadingResPhrase = LoadingPhrase.Unloading;
                    Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.Prefab);
                    if (this.loadingIndicator.index === count) {
                        this.loadingIndicator = null;
                        this.loadingType = LoadingType.Item;
                    }
                },
                () => {
                    this.loadingResPhrase = LoadingPhrase.Failed;
                    Loading.getInstance().emit(
                        LoadingProgressEvent.ProgressError,
                        LoadingErrorTypeEvent.Retry,
                        LoadingType.Prefab,
                        i18n.nsn0012
                    );
                }
            );
        }
    }

    /**
     * 加载缓冲池对象
     */
    private loadItem(): void {
        this.loadingResPhrase = LoadingPhrase.Loading;
        const count = PRELOAD_ITEM.length;
        if (count === 0) {
            this.progress += LOADING_PERCENT.ITEM;
            this.loadingResPhrase = LoadingPhrase.Success;
            Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.Item);
            if (this.loadingNetworkPhrase === LoadingPhrase.Success) {
                Loading.getInstance().emit(LoadingProgressEvent.ProgressEnd);
            }
        } else {
            if (!this.loadingIndicator) {
                Logger.info("游戏加载", "加载缓冲池对象...");
                this.loadingIndicator = { res: PRELOAD_ITEM, index: 0 };
            }
            const [name, num] = this.loadingIndicator.res[this.loadingIndicator.index];
            Loader.getInstance().loadPrefab(
                name,
                (prefab: cc.Prefab) => {
                    for (let j = 0; j < num; j++) {
                        Pool.getInstance().preload(Loader.getInstance().instantiate(prefab));
                    }
                    this.loadingIndicator.index++;
                    this.progress += LOADING_PERCENT.ITEM / count;
                    this.loadingResPhrase = LoadingPhrase.Unloading;
                    Loading.getInstance().emit(LoadingProgressEvent.Progressing, this.progress, LoadingType.Item);
                    if (this.loadingIndicator.index === count) {
                        this.loadingIndicator = null;
                        this.loadingResPhrase = LoadingPhrase.Success;
                        if (this.loadingNetworkPhrase === LoadingPhrase.Success) {
                            Loading.getInstance().emit(LoadingProgressEvent.ProgressEnd);
                        }
                    }
                },
                () => {
                    this.loadingResPhrase = LoadingPhrase.Failed;
                    Loading.getInstance().emit(
                        LoadingProgressEvent.ProgressError,
                        LoadingErrorTypeEvent.Retry,
                        LoadingType.Item,
                        i18n.nsn0013
                    );
                }
            );
        }
    }
}
