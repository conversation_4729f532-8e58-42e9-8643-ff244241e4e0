/*
 * @Author: chenx
 * @Date: 2025-07-07 16:35:47
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-21 16:03:14
 */
import Logic from "../../nsn/core/Logic";

/**
 * 客户端配置
 */
export default class ClientConfig extends Logic {
    private configMap: Map<string, any> = new Map<string, any>(); // 配置

    public clear(): void {
        this.configMap.clear();
    }

    /**
     * 设置客户端配置
     * @param name 配置名称
     * @param config 配置
     */
    public setConfig(name: string, config: any): void {
        this.configMap.set(name, config);
    }

    /**
     * 获取客户端配置
     * @param name 配置名称
     * @returns
     */
    public getConfig(name: string): any {
        return this.configMap.get(name);
    }
}
