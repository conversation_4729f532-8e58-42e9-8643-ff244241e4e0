/*
 * @Author: JackyF<PERSON>
 * @Date: 2020-10-07 10:29:32
 * @Last Modified by: chenx
 * @Last Modified time: 2025-07-07 15:40:47
 */
import Logic from "./Logic";

export enum LoadingEvent {
    Start = "start",
    PlayerDataInitFailed = "player-data-init-failed",
    PlayerDataInitSuccess = "player-data-init-success",
}

/**
 * 加载进度条的事件
 */
export enum LoadingProgressEvent {
    ProgressStart = "progress-start", // 开始
    Progressing = "progressing", // 进行中
    ProgressEnd = "progress-end", // 结束
    ProgressError = "progress-error", // 错误
}

/**
 * 加载错误事件类型
 */
export enum LoadingErrorTypeEvent {
    Error = 0, // 错误
    Retry = 1, // 重试
}

/**
 * 加载进度的类型
 */
export enum LoadingType {
    LocalData, // 本地配置
    RemoteData, // 远程配置
    Scene, // 场景
    Atlas, // 合图
    Prefab, // 预制体
    Item, // 节点
    ClientConfig, // 客户端配置
    Network, // 网络
}

export interface ILoadingOptions {
    socketUrl: string;
    httpUrl: string;
    serverId: string;
    socketParams: string[];
}

export default class Loading extends Logic {
    /**
     * 开始加载
     * @param options
     */
    public start(options: ILoadingOptions): void {
        this.emit(LoadingEvent.Start, options);
    }
}
